<?php

use <PERSON><PERSON>\LaravelSettings\Migrations\SettingsMigration;

return new class extends SettingsMigration
{
    public function up(): void
    {
        $this->migrator->add('general.terms_and_conditions', '');
        $this->migrator->add('general.privacy_policy', '');
        $this->migrator->add('general.blocking_policy', '');
        $this->migrator->add('general.app_logo', '');
        $this->migrator->add('general.support_email', 'support.bigshah.com');
        $this->migrator->add('general.help_email', 'help.bigshah.com');
        $this->migrator->add('general.official_website', 'bigshah.com');
    }
};
