<?php

namespace Database\Seeders;

use App\Models\Common\FazaaServiceType;
use App\Models\Common\FazaaSpecificType;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class FazaaSpecificTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $fazaaServiceTypes = FazaaServiceType::all()->keyBy('key');

        $fazaaSpecificTypes = [
            [
                'id' => snowflake(),
                'name_ar' => 'شراء ادوية',
                'name_en' => 'Buying Medicines',
                'fazaa_service_type_id' => $fazaaServiceTypes['buying_product']->id,
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'شراء مواد منزلية',
                'name_en' => 'Buying Home Goods',
                'fazaa_service_type_id' => $fazaaServiceTypes['buying_product']->id,
            ],
            [
                'id' => snowflake(),
                'name_ar' => '⁠شراء اجهزة اكترونية',
                'name_en' => 'Buying Electronics',
                'fazaa_service_type_id' => $fazaaServiceTypes['buying_product']->id,
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'شراء ⁠ملابس',
                'name_en' => 'Buying clothes',
                'fazaa_service_type_id' => $fazaaServiceTypes['buying_product']->id,
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'شراء اخرى',
                'name_en' => 'Buying Others',
                'fazaa_service_type_id' => $fazaaServiceTypes['buying_product']->id,
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'شحنة ادوية',
                'name_en' => 'Receive Medicines',
                'fazaa_service_type_id' => $fazaaServiceTypes['receive_shipment']->id,
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'شحنة مواد منزلية',
                'name_en' => 'Receive Home Goods',
                'fazaa_service_type_id' => $fazaaServiceTypes['receive_shipment']->id,
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'شحنة اجهزة اكترونية',
                'name_en' => 'Receive Electronics',
                'fazaa_service_type_id' => $fazaaServiceTypes['receive_shipment']->id,
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'شحنة ملابس',
                'name_en' => 'Receive clothes',
                'fazaa_service_type_id' => $fazaaServiceTypes['receive_shipment']->id,
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'شحنة اخرى',
                'name_en' => 'Receive Others',
                'fazaa_service_type_id' => $fazaaServiceTypes['receive_shipment']->id,
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'شحن ادوية',
                'name_en' => 'Send Medicines',
                'fazaa_service_type_id' => $fazaaServiceTypes['send_shipment']->id,
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'شحن مواد منزلية',
                'name_en' => 'Send Home Goods',
                'fazaa_service_type_id' => $fazaaServiceTypes['send_shipment']->id,
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'شحن اجهزة اكترونية',
                'name_en' => 'Send Electronics',
                'fazaa_service_type_id' => $fazaaServiceTypes['send_shipment']->id,
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'شحن ملابس',
                'name_en' => 'Send clothes',
                'fazaa_service_type_id' => $fazaaServiceTypes['send_shipment']->id,
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'شحن اخرى',
                'name_en' => 'Send Others',
                'fazaa_service_type_id' => $fazaaServiceTypes['send_shipment']->id,
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'مراجعة سفارة',
                'name_en' => 'Review Courier',
                'fazaa_service_type_id' => $fazaaServiceTypes['review']->id,
            ],
            [
                'id' => snowflake(),
                'name_ar' => '⁠مراجعة جامعة',
                'name_en' => 'Review University',
                'fazaa_service_type_id' => $fazaaServiceTypes['review']->id,
            ],
            [
                'id' => snowflake(),
                'name_ar' => '⁠مراجعة مستشفى',
                'name_en' => 'Review Hospital',
                'fazaa_service_type_id' => $fazaaServiceTypes['review']->id,
            ],
            [
                'id' => snowflake(),
                'name_ar' => '⁠مراجعة عامة',
                'name_en' => 'General Review',
                'fazaa_service_type_id' => $fazaaServiceTypes['review']->id,
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'مراجعة أخرى',
                'name_en' => 'Others Review',
                'fazaa_service_type_id' => $fazaaServiceTypes['review']->id,
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'أخرى',
                'name_en' => 'Others',
                'fazaa_service_type_id' => $fazaaServiceTypes['others']->id,
            ],
        ];

        FazaaSpecificType::insert($fazaaSpecificTypes);
    }
}
