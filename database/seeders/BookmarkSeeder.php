<?php

namespace Database\Seeders;

use App\Models\Request\FazaaRequest;
use App\Models\Request\ShippingRequest;
use App\Models\Request\TripRequest;
use App\Models\Service\FazaaService;
use App\Models\Service\ShippingService;
use App\Models\Service\TripService;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Arr;
class BookmarkSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::all();

        // Get random services to bookmark
        $tripServices = TripService::inRandomOrder()->limit(10)->pluck('id')->toArray();
        $shippingServices = ShippingService::inRandomOrder()->limit(10)->pluck('id')->toArray();
        $fazaaServices = FazaaService::inRandomOrder()->limit(10)->pluck('id')->toArray();
        $tripRequests = TripRequest::inRandomOrder()->limit(10)->pluck('id')->toArray();
        $shippingRequests = ShippingRequest::inRandomOrder()->limit(10)->pluck('id')->toArray();
        $fazaaRequests = FazaaRequest::inRandomOrder()->limit(10)->pluck('id')->toArray();

        foreach ($users as $user) {
            // Bookmark random trip services
            $selectedTripServices = Arr::random($tripServices, rand(1, min(5, count($tripServices))));
            foreach ($selectedTripServices as $serviceId) {
                $user->bookmarks()->create([
                    'bookmarkable_type' => TripService::class,
                    'bookmarkable_id' => $serviceId
                ]);
            }

            // Bookmark random shipping services
            $selectedShippingServices = Arr::random($shippingServices, rand(1, min(5, count($shippingServices))));
            foreach ($selectedShippingServices as $serviceId) {
                $user->bookmarks()->create([
                    'bookmarkable_type' => ShippingService::class,
                    'bookmarkable_id' => $serviceId
                ]);
            }

            // Bookmark random fazaa services
            $selectedFazaaServices = Arr::random($fazaaServices, rand(1, min(5, count($fazaaServices))));
            foreach ($selectedFazaaServices as $serviceId) {
                $user->bookmarks()->create([
                    'bookmarkable_type' => FazaaService::class,
                    'bookmarkable_id' => $serviceId
                ]);
            }

            // Bookmark random trip requests
            $selectedTripRequests = Arr::random($tripRequests, rand(1, min(5, count($tripRequests))));
            foreach ($selectedTripRequests as $requestId) {
                $user->bookmarks()->create([
                    'bookmarkable_type' => TripRequest::class,
                    'bookmarkable_id' => $requestId
                ]);
            }

            // Bookmark random shipping requests
            $selectedShippingRequests = Arr::random($shippingRequests, rand(1, min(5, count($shippingRequests))));
            foreach ($selectedShippingRequests as $requestId) {
                $user->bookmarks()->create([
                    'bookmarkable_type' => ShippingRequest::class,
                    'bookmarkable_id' => $requestId
                ]);
            }

            // Bookmark random fazaa requests
            $selectedFazaaRequests = Arr::random($fazaaRequests, rand(1, min(5, count($fazaaRequests))));
            foreach ($selectedFazaaRequests as $requestId) {
                $user->bookmarks()->create([
                    'bookmarkable_type' => FazaaRequest::class,
                    'bookmarkable_id' => $requestId
                ]);
            }
        }
    }
}
