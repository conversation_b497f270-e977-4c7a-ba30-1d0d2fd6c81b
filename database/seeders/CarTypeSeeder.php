<?php

namespace Database\Seeders;

use App\Models\Common\CarType;
use Illuminate\Database\Seeder;

class CarTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $carTypes = [
            [
                'id' => snowflake(),
                'name_en' => 'Sedan',
                'name_ar' => 'سيدان',
                'number_of_seats' => 4,
            ],
            [
                'id' => snowflake(),
                'name_en' => 'Coupe',
                'name_ar' => 'كوب',
                'number_of_seats' => 2,
            ],
            [
                'id' => snowflake(),
                'name_en' => 'SUV',
                'name_ar' => 'دفع رباعي',
                'number_of_seats' => 5,
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'هاتشباك',
                'name_en' => 'Hatchback',
                'number_of_seats' => 4,
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'بيك أب',
                'name_en' => 'Pickup',
                'number_of_seats' => 4,
            ],
            [
                'id' => snowflake(),
                'name_en' => 'Sports',
                'name_ar' => 'رياضي',
                'number_of_seats' => 4,
            ],
            [
                'id' => snowflake(),
                'name_en' => 'Minivan',
                'name_ar' => 'باص صغير',
                'number_of_seats' => 7,
            ]
        ];

        CarType::insert($carTypes);
    }
}
