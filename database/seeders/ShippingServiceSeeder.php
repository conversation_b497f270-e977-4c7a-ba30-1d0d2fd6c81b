<?php

namespace Database\Seeders;

use App\Enums\Travel\TransportationTypeEnum;
use App\Models\Common\Car;
use App\Models\Common\City;
use App\Models\Service\ShippingService;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Arr;

class ShippingServiceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $citiesId = City::all()->pluck('id')->toArray();

        $users = User::all()->pluck('id')->toArray();

        $shippingServices = [];
        foreach ($users as $user) {
            $cars = Car::where('user_id', $user)->pluck('id')->toArray();

            for ($i = 0; $i < 4; $i++) {
                $shippingService = ShippingService::factory()->make([
                    'from_city_id' => Arr::random($citiesId),
                    'to_city_id' => Arr::random($citiesId),
                    'user_id' => $user,
                ])->toArray();

                $shippingService['car_id'] = TransportationTypeEnum::CAR() === $shippingService['transportation_type'] ? Arr::random($cars) : null;
                $shippingServices[] = $shippingService;
            }
        }

        ShippingService::insert($shippingServices);
    }
}
