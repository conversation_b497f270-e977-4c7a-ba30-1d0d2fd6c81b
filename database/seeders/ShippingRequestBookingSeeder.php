<?php

namespace Database\Seeders;

use App\Enums\Bookings\BookingStatusEnum;
use App\Models\Request\ShippingRequest;
use App\Models\User;
use Illuminate\Database\Seeder;

class ShippingRequestBookingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::all();
        $shippingRequests = ShippingRequest::available()->upcoming()->get();

        foreach ($shippingRequests as $shippingRequest) {
            // Skip if already has a booking (since shipping requests can only have one accepted booking)
            if ($shippingRequest->bookings()->where('status', BookingStatusEnum::ACCEPTED())->exists()) {
                continue;
            }

            // 90% chance to create bookings
            if (fake()->boolean(90)) {
                // Create 1-3 bookings for this shipping request
                for ($i = 0; $i < fake()->numberBetween(1, 3); $i++) {
                    $user = $users->random();

                    // Create booking with random status (70% pending, 20% accepted, 10% rejected)
                    $status = match (fake()->randomElement([1, 1, 1, 1, 1, 1, 1, 2, 2, 3])) {
                        1 => BookingStatusEnum::PENDING(),
                        2 => BookingStatusEnum::ACCEPTED(),
                        3 => BookingStatusEnum::REJECTED(),
                    };

                    $bookingData = [
                        'user_id' => $user->id,
                        'note' => fake()->optional()->sentence(),
                        'status' => $status,
                        'rejection_reason' => $status === BookingStatusEnum::REJECTED()
                            ? fake()->sentence()
                            : null,
                    ];

                    // Add prices based on shipping capabilities
                    if ($shippingRequest->can_ship_packages) {
                        $bookingData['package_price'] = fake()->numberBetween(50, 500);
                    }
                    if ($shippingRequest->can_ship_documents) {
                        $bookingData['document_price'] = fake()->numberBetween(30, 200);
                    }
                    if ($shippingRequest->can_ship_furniture) {
                        $bookingData['furniture_price'] = fake()->numberBetween(100, 1000);
                    }

                    $shippingRequest->bookings()->create($bookingData);
                }
            }
        }
    }
}
