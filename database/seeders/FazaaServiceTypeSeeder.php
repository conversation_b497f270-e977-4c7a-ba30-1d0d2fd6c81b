<?php

namespace Database\Seeders;

use App\Models\Common\FazaaServiceType;
use Illuminate\Database\Seeder;

class FazaaServiceTypeSeeder extends Seeder
{
    public function run(): void
    {
        $types = [
            ['name_en' => 'Buying product', 'name_ar' => 'شراء منتج', 'key' => 'buying_product'],
            ['name_en' => 'Receive shipment', 'name_ar' => 'استلام شحنة', 'key' => 'receive_shipment'],
            ['name_en' => 'Send shipment', 'name_ar' => 'شحن', 'key' => 'send_shipment'],
            ['name_en' => 'Review', 'name_ar' => 'مراجعة', 'key' => 'review'],
            ['name_en' => 'Others', 'name_ar' => 'أخرى', 'key' => 'others'],
        ];

        foreach ($types as $type) {
            FazaaServiceType::create($type);
        }
    }
}
