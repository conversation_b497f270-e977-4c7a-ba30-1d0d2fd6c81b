<?php

namespace Database\Seeders;

use App\Enums\TransportStatus;
use App\Models\TransportationOffice;
use App\Models\TransportationOfficeUser;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class TransportationOfficeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a sample transportation office
        $office = TransportationOffice::create([
            'name' => 'City Transport Services',
            'email' => '<EMAIL>',
            'phone' => '+**********',
            'address' => '123 Main Street, Downtown',
            'city' => 'New York',
            'country' => 'USA',
            'license_number' => 'LIC-2024-001',
            'status' => TransportStatus::INACTIVE->value,
            'description' => 'Leading transportation services in the city',
            'office_logo' => null,
            'license_document' => null,
            'registration_documents' => null,
        ]);

        // Create admin user for the transportation office
        TransportationOfficeUser::create([
            'transportation_office_id' => $office->id,
            'name' => 'Transport Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('12345678'),
            'phone' => '+**********',
            'role' => 'admin',
            'status' => 'active',
        ]);
    }
}
