<?php

namespace Database\Seeders;

use App\Enums\Bookings\BookingStatusEnum;
use App\Enums\Complaints\ComplaintStatusEnum;
use App\Models\Booking\TripServiceBooking;
use App\Models\Complaint;
use App\Models\Service\TripService;
use App\Models\User;
use Illuminate\Database\Seeder;

class ComplaintSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::take(5)->get();

        if ($users->isEmpty()) {
            $this->command->error('No users found. Please run UserSeeder first.');
            return;
        }

        $bookingTypes = [
            'pending' => BookingStatusEnum::PENDING(),
            'accepted' => BookingStatusEnum::ACCEPTED(),
            'rejected' => BookingStatusEnum::REJECTED(),
            'cancelled_by_user' => BookingStatusEnum::CANCELLED(),
            'cancelled_by_owner' => BookingStatusEnum::CANCELLED_BY_OWNER(),
        ];

        $bookingsByStatus = [];
        $missingStatuses = [];

        foreach ($bookingTypes as $key => $status) {
            $bookings = TripServiceBooking::where('status', $status)->take(3)->get();

            if ($bookings->isEmpty()) {
                $missingStatuses[] = $key;
            } else {
                $bookingsByStatus[$key] = $bookings;
            }
        }

        if (!empty($missingStatuses)) {
            $this->command->warn('Could not find bookings with these statuses: ' . implode(', ', $missingStatuses));
            $this->command->info('The seeder will continue with available booking types.');
        }

        if (empty($bookingsByStatus)) {
            $this->command->error('No bookings of any required status found. Please ensure there are bookings in the database.');
            return;
        }

        $complaintStatuses = [
            ComplaintStatusEnum::PENDING(),
            ComplaintStatusEnum::RESOLVED(),
            ComplaintStatusEnum::REJECTED(),
        ];

        $complaintDescriptions = [
            'The service provider cancelled at the last minute',
            'Unexpected price changes after booking',
            'The service was not as described',
            'Poor communication from the service provider',
            'Service provider did not show up',
            'Booking was rejected without a valid reason',
            'Vehicle was not in good condition',
            'Driver was rude and unprofessional',
            'My items were damaged during transportation',
            'Significant delay without notification',
            'Unprofessional behavior by the service provider',
            'The driver arrived very late without notice',
            'The car was different from what was promised',
            'The driver took a unnecessarily long route',
            'Did not honor the agreed-upon pickup location',
        ];

        $totalComplaints = 0;

        foreach ($bookingsByStatus as $statusType => $bookings) {
            foreach ($bookings as $index => $booking) {
                // Create 1-3 complaints per booking based on status
                $complaintsPerBooking = $statusType === 'cancelled_by_owner' || $statusType === 'rejected' ?
                    rand(1, 3) : rand(0, 2);

                for ($i = 0; $i < $complaintsPerBooking; $i++) {
                    $status = $complaintStatuses[array_rand($complaintStatuses)];
                    $description = $complaintDescriptions[array_rand($complaintDescriptions)];

                    if ($i > 0) {
                        $description .= ". Additionally, " . strtolower($complaintDescriptions[array_rand($complaintDescriptions)]);
                    }

                    $complaint = Complaint::firstOrCreate(
                        [
                            'user_id' => $booking->user_id,
                            'booking_id' => $booking->id,
                            'booking_type' => TripServiceBooking::class,
                            'description' => $description,
                        ],
                        [
                            'status' => $status,
                            'admin_response' => $status !== ComplaintStatusEnum::PENDING()
                                ? 'Thank you for your feedback. We have reviewed your complaint and taken appropriate action.'
                                : null,
                            'admin_id' => $status !== ComplaintStatusEnum::PENDING()
                                ? User::where('id', '!=', $booking->user_id)->first()?->id
                                : null,
                            'resolved_at' => $status === ComplaintStatusEnum::RESOLVED()
                                ? now()
                                : null,
                        ]
                    );

                    $totalComplaints++;
                }
            }
        }
    }
}
