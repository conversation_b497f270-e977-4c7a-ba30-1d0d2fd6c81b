<?php

namespace Database\Seeders;

use App\Models\Common\CarType;
use App\Models\Common\City;
use App\Models\Request\TripRequest;
use App\Models\Request\TripRequestSeat;
use App\Models\Service\AdditionalService;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Arr;

class TripRequestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $citiesId = City::pluck('id')->toArray();

        $users = User::pluck('id')->toArray();

        $additionalServices = AdditionalService::all()->pluck('id')->toArray();

        $carTypesId = CarType::pluck('id')->toArray();

        foreach ($users as $user) {
            for ($j = 0; $j < 6; $j++) {
                $tripRequest = TripRequest::factory()->create([
                    'from_city_id' => Arr::random($citiesId),
                    'to_city_id' => Arr::random($citiesId),
                    'car_type_id' => Arr::random($carTypesId),
                    'user_id' => $user,
                ]);

                $tripRequest->additionalServices()->sync(
                    Arr::random($additionalServices, rand(1, 3))
                );

                // Create seats with unique numbers, skipping "A1" and "A2"
                $seatNumbers = collect([
                    'A3',
                    'B1',
                    'B2',
                    'B3',
                    'C1',
                    'C2',
                    'C3',
                ])->shuffle();

                for ($j = 0; $j < $tripRequest->number_of_seats; $j++) {
                    TripRequestSeat::factory()->create([
                        'trip_request_id' => $tripRequest->id,
                        'number' => $seatNumbers->pop(),
                    ]);
                }
            }
        }
    }
}
