<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $essentialSeeders = [
            CountrySeeder::class,
            CitySeeder::class,
            CarTypeSeeder::class,
            AdditionalServiceSeeder::class,
            FazaaServiceTypeSeeder::class,
            FazaaSpecificTypeSeeder::class,
            SeatSeeder::class,
            RolesAndPermissionsSeeder::class,
            AdminSeeder::class,
        ];

        $developmentSeeders = [
            UserSeeder::class,
            CarSeeder::class,
            TripServiceSeeder::class,
            ShippingServiceSeeder::class,
            FazaaServiceSeeder::class,
            TripRequestSeeder::class,
            ShippingRequestSeeder::class,
            FazaaRequestSeeder::class,
            BookmarkSeeder::class,
            TripServiceBookingSeeder::class,
            TripRequestBookingSeeder::class,
            ShippingRequestBookingSeeder::class,
            ShippingServiceBookingSeeder::class,
            FazaaServiceBookingSeeder::class,
            FazaaRequestBookingSeeder::class,
            NotificationSeeder::class,
            RolesAndPermissionsSeeder::class,
            ComplaintSeeder::class,
            TransportationOfficeSeeder::class,
        ];

        $this->call($essentialSeeders);

        if (app()->environment('local', 'development')) {
            $this->call($developmentSeeders);
        }
    }
}
