<?php

namespace Database\Seeders;

use App\Models\Common\City;
use Illuminate\Support\Arr;
use App\Models\Common\Country;
use Illuminate\Database\Seeder;

class CitySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $countriesKeys = Country::query()->get()->modelKeys();

        $weather = [
            'Clear',
        //     'Clouds',
        //     'Rain',
        //     'Snow',
        //     'Drizzle',
        //     'Foggy',
        //     'Dust',
        //     'Tornado',
        ];

        $cities = [
            [
                'id' => snowflake(),
                'name_ar' => 'البكيرية',
                'name_en' => 'Al Bukayriyah',
                'current_weather' => Arr::random($weather),
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'بلجرشي',
                'name_en' => 'Biljurashi',
                'current_weather' => Arr::random($weather),
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'بيشة',
                'name_en' => 'Bishah',
                'current_weather' => Arr::random($weather),
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'بريدة',
                'name_en' => 'Buraydah',
                'current_weather' => Arr::random($weather),
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'الباحة',
                'name_en' => 'Al Baha',
                'current_weather' => Arr::random($weather),
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'الدمام',
                'name_en' => 'Dammam',
                'current_weather' => Arr::random($weather),
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'الظهران',
                'name_en' => 'Dhahran',
                'current_weather' => Arr::random($weather),
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'الدرعية',
                'name_en' => 'Ad Diriyah',
                'current_weather' => Arr::random($weather),
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'الدوادمي',
                'name_en' => 'Ad Duwadimi',
                'current_weather' => Arr::random($weather),
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'القويعية',
                'name_en' => 'Al Quwayiyah',
                'current_weather' => Arr::random($weather),
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'حوطة سدير',
                'name_en' => 'Hawtat Sudair',
                'current_weather' => Arr::random($weather),
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'الحريق',
                'name_en' => 'Al Hariq',
                'current_weather' => Arr::random($weather),
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'حوطة بني تميم',
                'name_en' => 'Hawtat Bani Tamim',
                'current_weather' => Arr::random($weather),
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'جدة',
                'name_en' => 'Jeddah',
                'current_weather' => Arr::random($weather),
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'مكة المكرمة',
                'name_en' => 'Mecca',
                'current_weather' => Arr::random($weather),
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'المدينة المنورة',
                'name_en' => 'Medina',
                'current_weather' => Arr::random($weather),
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'تبوك',
                'name_en' => 'Tabuk',
                'current_weather' => Arr::random($weather),
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'أبها',
                'name_en' => 'Abha',
                'current_weather' => Arr::random($weather),
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'نجران',
                'name_en' => 'Najran',
                'current_weather' => Arr::random($weather),
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'جازان',
                'name_en' => 'Jazan',
                'current_weather' => Arr::random($weather),
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'حائل',
                'name_en' => 'Hail',
                'current_weather' => Arr::random($weather),
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'الطائف',
                'name_en' => 'Taif',
                'current_weather' => Arr::random($weather),
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'الخبر',
                'name_en' => 'Khobar',
                'current_weather' => Arr::random($weather),
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'ينبع',
                'name_en' => 'Yanbu',
                'current_weather' => Arr::random($weather),
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'الجبيل',
                'name_en' => 'Jubail',
                'current_weather' => Arr::random($weather),
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'الخرج',
                'name_en' => 'Al Kharj',
                'current_weather' => Arr::random($weather),
            ],
            [
                'id' => snowflake(),
                'name_ar' => 'القطيف',
                'name_en' => 'Qatif',
                'current_weather' => Arr::random($weather),
            ]
        ];

        City::insert($cities);
    }
}
