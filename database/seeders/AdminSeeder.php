<?php

namespace Database\Seeders;

use App\Models\Dashboard\Admin;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Admin::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('12345678'),
        ]);
    }
}
