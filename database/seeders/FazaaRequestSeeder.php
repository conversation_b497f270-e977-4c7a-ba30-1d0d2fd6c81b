<?php

namespace Database\Seeders;

use App\Models\Common\City;
use App\Models\Common\FazaaServiceType;
use App\Models\Common\FazaaSpecificType;
use App\Models\Request\FazaaRequest;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Arr;

class FazaaRequestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $citiesId = City::pluck('id')->toArray();
        $users = User::pluck('id')->toArray();
        $fazaaServiceTypes = FazaaServiceType::all();
        $fazaaSpecificTypes = FazaaSpecificType::all()->groupBy('fazaa_service_type_id');

        $fazaaRequests = [];

        foreach ($users as $user) {
            for ($i = 0; $i < 4; $i++) {
                $fazaaServiceType = $fazaaServiceTypes->random();

                $fazaaRequest = FazaaRequest::factory()->make([
                    'from_city_id' => Arr::random($citiesId),
                    'to_city_id' => Arr::random($citiesId),
                    'user_id' => $user,
                    'fazaa_service_type_id' => $fazaaServiceType->id,
                ]);

                $compatibleTypes = $fazaaSpecificTypes[$fazaaServiceType->id];
                $fazaaRequest->specific_type_id = $compatibleTypes->random()->id;
                $fazaaRequests[] = $fazaaRequest->toArray();
            }
        }

        FazaaRequest::insert($fazaaRequests);
    }
}
