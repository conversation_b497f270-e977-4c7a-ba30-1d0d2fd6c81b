<?php

namespace Database\Seeders;

use App\Enums\Bookings\BookingStatusEnum;
use App\Models\Request\FazaaRequest;
use App\Models\User;
use Illuminate\Database\Seeder;

class FazaaRequestBookingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::all();
        $fazaaRequests = FazaaRequest::available()->upcoming()->get();

        foreach ($fazaaRequests as $fazaaRequest) {
            // Skip if already has a booking (since fazaa requests can only have one booking)
            if ($fazaaRequest->bookings()->exists()) {
                continue;
            }

            // 90% chance to create bookings
            if (fake()->boolean(90)) {
                // Create 1-3 bookings for this fazaa request
                for ($i = 0; $i < fake()->numberBetween(1, 3); $i++) {
                    $user = $users->random();

                    // Create booking with random status (70% pending, 20% accepted, 10% rejected)
                    $status = match (fake()->randomElement([1, 1, 1, 1, 1, 1, 1, 2, 2, 3])) {
                        1 => BookingStatusEnum::PENDING(),
                        2 => BookingStatusEnum::ACCEPTED(),
                        3 => BookingStatusEnum::REJECTED(),
                    };

                    $bookingData = [
                        'user_id' => $user->id,
                        'note' => fake()->optional(0.7)->sentence(), // 70% chance of having a note
                        'price' => fake()->numberBetween(50, 500),
                        'status' => $status,
                        'rejection_reason' => $status === BookingStatusEnum::REJECTED()
                            ? fake()->sentence()
                            : null,
                    ];

                    $fazaaRequest->bookings()->create($bookingData);
                }
            }
        }
    }
}
