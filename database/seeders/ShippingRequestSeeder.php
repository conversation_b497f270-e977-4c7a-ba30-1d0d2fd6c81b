<?php

namespace Database\Seeders;

use App\Models\Common\City;
use App\Models\Request\ShippingRequest;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Arr;

class ShippingRequestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $citiesId = City::all()->pluck('id')->toArray();

        $users = User::all()->pluck('id')->toArray();

        foreach ($users as $user) {
            $shippingRequests[] = ShippingRequest::factory(4)->make([
                'from_city_id' => Arr::random($citiesId),
                'to_city_id' => Arr::random($citiesId),
                'user_id' => $user,
            ])->toArray();
        }

        ShippingRequest::insert(Arr::collapse($shippingRequests));
    }
}
