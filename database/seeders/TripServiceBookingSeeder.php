<?php

namespace Database\Seeders;

use App\Enums\Bookings\BookingStatusEnum;
use App\Enums\Travel\SeatStatusEnum;
use App\Events\TripServiceSeatBooked;
use App\Models\Service\TripService;
use App\Models\User;
use Illuminate\Database\Seeder;

class TripServiceBookingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::all();

        foreach ($users as $user) {
            $tripServices = $user->tripServices()->get();

            foreach ($tripServices as $tripService) {
                $numberOfBookings = rand(2, 5);

                for ($i = 0; $i < $numberOfBookings; $i++) {
                    // Skip if no more available seats
                    if ($tripService->isFullyBooked()) {
                        continue;
                    }

                    $user = $users->random();
                    $availableSeats = $tripService->seats()
                        ->where('status', SeatStatusEnum::AVAILABLE())
                        ->take(rand(1, min(3, $tripService->number_of_available_seats)))
                        ->get();

                    if ($availableSeats->isEmpty()) {
                        continue;
                    }

                    $booking = $tripService->bookings()->create([
                        'user_id' => $user->id,
                        'note' => fake()->optional()->sentence(),
                        'has_fragile_items' => fake()->boolean(),
                        'number_of_seats' => $availableSeats->count(),
                        'status' => fake()->randomElement(BookingStatusEnum::values()),
                    ]);

                    foreach ($availableSeats as $seat) {
                        $booking->seats()->create([
                            'seat_id' => $seat->id,
                        ]);
                        TripServiceSeatBooked::dispatch($booking->id);

                        $seat->update(['status' => SeatStatusEnum::RESERVED()]);
                    }

                    $tripService->decrement('number_of_available_seats', $availableSeats->count());
                    $tripService->refresh();
                }
            }
        }
    }
}
