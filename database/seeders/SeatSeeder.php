<?php

namespace Database\Seeders;

use App\Enums\Travel\SeatStatusEnum;
use App\Models\Common\Seat;
use Illuminate\Database\Seeder;

class SeatSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $seatsNumbers = [
            'A1',
            'A2',
            'A3',
            'B1',
            'B2',
            'B3',
            'C1',
            'C2',
            'C3',
            'D1',
            'D2',
            'D3',
        ];

        foreach ($seatsNumbers as $seatNumber) {
            $status = in_array($seatNumber, ['A1', 'A2']) ? SeatStatusEnum::UNAVAILABLE() : SeatStatusEnum::AVAILABLE();

            $seats = Seat::factory()->make([
                'number' => $seatNumber,
                'status' => $status,
            ])->toArray();

            Seat::insert($seats);
        }
    }
}
