<?php

namespace Database\Seeders;

use App\Enums\Bookings\BookingStatusEnum;
use App\Models\Request\TripRequest;
use App\Models\User;
use Illuminate\Database\Seeder;

class TripRequestBookingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::all();
        $tripRequests = TripRequest::available()->upcoming()->get();

        foreach ($tripRequests as $tripRequest) {
            // Skip if already has a booking (since trip requests can only have one booking)
            if ($tripRequest->bookings()->where('status', BookingStatusEnum::ACCEPTED())->exists()) {
                continue;
            }

            // 50% chance to create a booking
            if (fake()->boolean(90)) {
                // Create 1-3 bookings for this trip request
                for ($i = 0; $i < fake()->numberBetween(1, 3); $i++) {
                    $user = $users->random();

                    // Create booking with random status (70% pending, 20% accepted, 10% rejected)
                    $status = match (fake()->randomElement([1, 1, 1, 1, 1, 1, 1, 2, 2, 3])) {
                        1 => BookingStatusEnum::PENDING(),
                        2 => BookingStatusEnum::ACCEPTED(),
                        3 => BookingStatusEnum::REJECTED(),
                    };

                    $booking = $tripRequest->bookings()->create([
                        'user_id' => $user->id,
                        'note' => fake()->optional()->sentence(),
                        'price' => fake()->numberBetween(50, 500),
                        'status' => $status,
                        'rejection_reason' => $status === BookingStatusEnum::REJECTED()
                            ? fake()->sentence()
                            : null,
                    ]);
                }
            }
        }
    }
}
