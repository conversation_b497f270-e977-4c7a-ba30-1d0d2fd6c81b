<?php

namespace Database\Seeders;

use App\Models\Dashboard\Admin;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\PermissionRegistrar;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions by section/feature
        $permissions = [
            // Admin management
            'view admins',
            'manage admins',

            // User management
            'view users',
            'manage users',

            // Role management
            'view roles',
            'manage roles',

            // Permission management
            'view permissions',
            'manage permissions',

            // Complaint management
            'view complaints',
            'manage complaints',

            // Request management (applies to trip, shipping, fazaa requests)
            'view requests',
            'manage requests',

            // Tracking management
            'view tracking',
            'manage tracking',

            // Booking management
            'view bookings',
            'manage bookings',

            // Offer management
            'view offers',
            'manage offers',

            // Settings management
            'view settings',
            'manage settings',

            // Rating management
            'view ratings',
            'manage ratings',
        ];

        // Create permissions if they don't exist
        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission, 'guard_name' => 'admin']);
        }

        // Create roles if they don't exist and assign permissions

        // Super Admin role - has all permissions
        $superAdminRole = Role::firstOrCreate(['name' => 'super admin', 'guard_name' => 'admin']);
        $superAdminRole->syncPermissions(Permission::all());

        // Accountant role
        $accountantRole = Role::firstOrCreate(['name' => 'accountant', 'guard_name' => 'admin']);
        $accountantRole->syncPermissions([
            'view bookings',
            'manage bookings',
            'view users',
        ]);

        // Complaint Reviewer role
        $complaintReviewerRole = Role::firstOrCreate(['name' => 'complaint reviewer', 'guard_name' => 'admin']);
        $complaintReviewerRole->syncPermissions([
            'view complaints',
            'manage complaints',
        ]);

        // Request Reviewer role
        $requestReviewerRole = Role::firstOrCreate(['name' => 'request reviewer', 'guard_name' => 'admin']);
        $requestReviewerRole->syncPermissions([
            'view requests',
            'manage requests',
        ]);

        // Service Manager role
        $serviceManagerRole = Role::firstOrCreate(['name' => 'service manager', 'guard_name' => 'admin']);
        $serviceManagerRole->syncPermissions([
            'view offers',
            'manage offers',
        ]);

        // Create a super admin user if it doesn't exist
        $admin = Admin::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Admin',
                'password' => Hash::make('12345678'),
            ]
        );

        // Assign the super admin role to the admin user
        if (!$admin->hasRole('super admin')) {
            $admin->assignRole('super admin');
        }
    }
}
