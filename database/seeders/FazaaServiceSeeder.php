<?php

namespace Database\Seeders;

use App\Models\Common\City;
use App\Models\Common\FazaaServiceType;
use App\Models\Common\FazaaSpecificType;
use App\Models\Service\FazaaService;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

class FazaaServiceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $citiesId = City::pluck('id')->toArray();
        $users = User::pluck('id')->toArray();
        $fazaaServiceTypes = FazaaServiceType::all();
        $fazaaSpecificTypes = FazaaSpecificType::all()->groupBy('fazaa_service_type_id');

        $fazaaServices = [];
        Log::info('$fazaaSpecificTypes: ' . $fazaaSpecificTypes);
        foreach ($users as $user) {
            for ($i = 0; $i < 4; $i++) {
                $fazaaServiceType = $fazaaServiceTypes->random();
                Log::info('$fazaaSpecificTypes: ' . $fazaaSpecificTypes[$fazaaServiceType->id]);
                $fazaaService = FazaaService::factory()->make([
                    'from_city_id' => Arr::random($citiesId),
                    'to_city_id' => Arr::random($citiesId),
                    'user_id' => $user,
                    'fazaa_service_type_id' => $fazaaServiceType->id,
                ]);

                $compatibleTypes = $fazaaSpecificTypes[$fazaaServiceType->id];
                $fazaaService->specific_type_id = $compatibleTypes->random()->id;
                $fazaaServices[] = $fazaaService->toArray();
            }
        }

        FazaaService::insert($fazaaServices);
    }
}
