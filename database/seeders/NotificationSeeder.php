<?php

namespace Database\Seeders;

use App\Enums\Notifications\NotificationTypeEnum;
use App\Models\Booking\TripRequestBooking;
use App\Models\Booking\TripServiceBooking;
use App\Models\Request\TripRequest;
use App\Models\Service\TripService;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class NotificationSeeder extends Seeder
{
    /**
     * Seed the application's database with realistic notifications.
     */
    public function run(): void
    {
        $users = User::all();

        // Skip if no users exist
        if ($users->isEmpty()) {
            return;
        }

        $tripRequests = TripRequest::all();
        $tripServices = TripService::all();
        $tripRequestBookings = TripRequestBooking::all();
        $tripServiceBookings = TripServiceBooking::all();

        // Create notifications for each user
        foreach ($users as $user) {
            // 1. Trip Request Notifications
            if ($tripRequests->isNotEmpty()) {
                foreach ($tripRequests->random(min(3, $tripRequests->count())) as $tripRequest) {
                    // Trip Request Cancelled
                    $user->notifications()->create([
                        'id' => Str::uuid(),
                        'type' => NotificationTypeEnum::TRIP_REQUEST_CANCELLED(),
                        'data' => [
                            'type' => NotificationTypeEnum::TRIP_REQUEST_CANCELLED(),
                            'title' => __('notifications.trip_request_cancelled.title'),
                            'content' => __('notifications.trip_request_cancelled.content'),
                            'trip_request_id' => (string) $tripRequest->id,
                        ],
                        'created_at' => now()->subHours(rand(1, 48)),
                        'read_at' => rand(0, 1) ? now()->subHours(rand(1, 24)) : null,
                    ]);

                    // Trip Request Delayed
                    $user->notifications()->create([
                        'id' => Str::uuid(),
                        'type' => NotificationTypeEnum::TRIP_REQUEST_DELAYED(),
                        'data' => [
                            'type' => NotificationTypeEnum::TRIP_REQUEST_DELAYED(),
                            'title' => __('notifications.trip_request_delayed.title'),
                            'content' => __('notifications.trip_request_delayed.content'),
                            'trip_request_id' => (string) $tripRequest->id,
                        ],
                        'created_at' => now()->subHours(rand(1, 72)),
                        'read_at' => rand(0, 1) ? now()->subHours(rand(1, 24)) : null,
                    ]);
                }
            }

            // 2. Trip Service Notifications
            if ($tripServices->isNotEmpty()) {
                foreach ($tripServices->random(min(3, $tripServices->count())) as $tripService) {
                    // Trip Service Started
                    $user->notifications()->create([
                        'id' => Str::uuid(),
                        'type' => NotificationTypeEnum::TRIP_SERVICE_STARTED(),
                        'data' => [
                            'type' => NotificationTypeEnum::TRIP_SERVICE_STARTED(),
                            'title' => __('notifications.trip_service_started.title'),
                            'content' => __('notifications.trip_service_started.content'),
                            'trip_service_id' => (string) $tripService->id,
                        ],
                        'created_at' => now()->subHours(rand(1, 24)),
                        'read_at' => rand(0, 1) ? now()->subHours(rand(1, 12)) : null,
                    ]);

                    // Trip Service Arrived
                    $user->notifications()->create([
                        'id' => Str::uuid(),
                        'type' => NotificationTypeEnum::TRIP_SERVICE_ARRIVED(),
                        'data' => [
                            'type' => NotificationTypeEnum::TRIP_SERVICE_ARRIVED(),
                            'title' => __('notifications.trip_service_arrived.title'),
                            'content' => __('notifications.trip_service_arrived.content'),
                            'trip_service_id' => (string) $tripService->id,
                        ],
                        'created_at' => now()->subHours(rand(1, 36)),
                        'read_at' => rand(0, 1) ? now()->subHours(rand(1, 12)) : null,
                    ]);

                    // Trip Service Waiting
                    $user->notifications()->create([
                        'id' => Str::uuid(),
                        'type' => NotificationTypeEnum::TRIP_SERVICE_WAITING(),
                        'data' => [
                            'type' => NotificationTypeEnum::TRIP_SERVICE_WAITING(),
                            'title' => __('notifications.trip_service_waiting.title'),
                            'content' => __('notifications.trip_service_waiting.content'),
                            'trip_service_id' => (string) $tripService->id,
                        ],
                        'created_at' => now()->subHours(rand(1, 48)),
                        'read_at' => rand(0, 1) ? now()->subHours(rand(1, 12)) : null,
                    ]);

                    // Trip Service Is Soon
                    $user->notifications()->create([
                        'id' => Str::uuid(),
                        'type' => NotificationTypeEnum::TRIP_SERVICE_IS_SOON(),
                        'data' => [
                            'type' => NotificationTypeEnum::TRIP_SERVICE_IS_SOON(),
                            'title' => __('notifications.trip_service_is_soon.title'),
                            'content' => __('notifications.trip_service_is_soon.content'),
                            'trip_service_id' => (string) $tripService->id,
                        ],
                        'created_at' => now()->subHours(rand(1, 12)),
                        'read_at' => rand(0, 1) ? now()->subHours(rand(1, 6)) : null,
                    ]);
                }
            }

            // 3. Trip Request Booking Notifications
            if ($tripRequestBookings->isNotEmpty()) {
                foreach ($tripRequestBookings->random(min(3, $tripRequestBookings->count())) as $booking) {
                    // New Trip Request Booking
                    $user->notifications()->create([
                        'id' => Str::uuid(),
                        'type' => NotificationTypeEnum::NEW_TRIP_REQUEST_BOOKING(),
                        'data' => [
                            'type' => NotificationTypeEnum::NEW_TRIP_REQUEST_BOOKING(),
                            'title' => __('notifications.new_trip_request_booking.title'),
                            'content' => __('notifications.new_trip_request_booking.content'),
                            'trip_request_id' => (string) $booking->trip_request_id,
                            'booking_id' => (string) $booking->id,
                        ],
                        'created_at' => now()->subHours(rand(1, 72)),
                        'read_at' => rand(0, 1) ? now()->subHours(rand(1, 24)) : null,
                    ]);

                    // Trip Request Booking Accepted
                    $user->notifications()->create([
                        'id' => Str::uuid(),
                        'type' => NotificationTypeEnum::TRIP_REQUEST_BOOKING_ACCEPTED(),
                        'data' => [
                            'type' => NotificationTypeEnum::TRIP_REQUEST_BOOKING_ACCEPTED(),
                            'title' => __('notifications.trip_request_booking_accepted.title'),
                            'content' => __('notifications.trip_request_booking_accepted.content'),
                            'trip_request_id' => (string) $booking->trip_request_id,
                            'booking_id' => (string) $booking->id,
                        ],
                        'created_at' => now()->subHours(rand(1, 48)),
                        'read_at' => rand(0, 1) ? now()->subHours(rand(1, 24)) : null,
                    ]);

                    // Trip Request Booking Rejected
                    $user->notifications()->create([
                        'id' => Str::uuid(),
                        'type' => NotificationTypeEnum::TRIP_REQUEST_BOOKING_REJECTED(),
                        'data' => [
                            'type' => NotificationTypeEnum::TRIP_REQUEST_BOOKING_REJECTED(),
                            'title' => __('notifications.trip_request_booking_rejected.title'),
                            'content' => __('notifications.trip_request_booking_rejected.content'),
                            'trip_request_id' => (string) $booking->trip_request_id,
                            'booking_id' => (string) $booking->id,
                        ],
                        'created_at' => now()->subHours(rand(1, 36)),
                        'read_at' => rand(0, 1) ? now()->subHours(rand(1, 24)) : null,
                    ]);
                }
            }

            // 4. Trip Service Booking Notifications
            if ($tripServiceBookings->isNotEmpty()) {
                foreach ($tripServiceBookings->random(min(3, $tripServiceBookings->count())) as $booking) {
                    // New Trip Service Booking
                    $user->notifications()->create([
                        'id' => Str::uuid(),
                        'type' => NotificationTypeEnum::NEW_TRIP_SERVICE_BOOKING(),
                        'data' => [
                            'type' => NotificationTypeEnum::NEW_TRIP_SERVICE_BOOKING(),
                            'title' => __('notifications.new_trip_service_booking.title'),
                            'content' => __('notifications.new_trip_service_booking.content'),
                            'trip_service_id' => (string) $booking->trip_service_id,
                            'booking_id' => (string) $booking->id,
                        ],
                        'created_at' => now()->subHours(rand(1, 72)),
                        'read_at' => rand(0, 1) ? now()->subHours(rand(1, 24)) : null,
                    ]);

                    // Trip Service Booking Accepted
                    $user->notifications()->create([
                        'id' => Str::uuid(),
                        'type' => NotificationTypeEnum::TRIP_SERVICE_BOOKING_ACCEPTED(),
                        'data' => [
                            'type' => NotificationTypeEnum::TRIP_SERVICE_BOOKING_ACCEPTED(),
                            'title' => __('notifications.trip_service_booking_accepted.title'),
                            'content' => __('notifications.trip_service_booking_accepted.content'),
                            'trip_service_id' => (string) $booking->trip_service_id,
                            'booking_id' => (string) $booking->id,
                        ],
                        'created_at' => now()->subHours(rand(1, 48)),
                        'read_at' => rand(0, 1) ? now()->subHours(rand(1, 24)) : null,
                    ]);

                    // Trip Service Booking Rejected
                    $user->notifications()->create([
                        'id' => Str::uuid(),
                        'type' => NotificationTypeEnum::TRIP_SERVICE_BOOKING_REJECTED(),
                        'data' => [
                            'type' => NotificationTypeEnum::TRIP_SERVICE_BOOKING_REJECTED(),
                            'title' => __('notifications.trip_service_booking_rejected.title'),
                            'content' => __('notifications.trip_service_booking_rejected.content'),
                            'trip_service_id' => (string) $booking->trip_service_id,
                            'booking_id' => (string) $booking->id,
                        ],
                        'created_at' => now()->subHours(rand(1, 36)),
                        'read_at' => rand(0, 1) ? now()->subHours(rand(1, 24)) : null,
                    ]);

                    // Trip Service Booking Cancelled By owner
                    $user->notifications()->create([
                        'id' => Str::uuid(),
                        'type' => NotificationTypeEnum::TRIP_SERVICE_BOOKING_CANCELLED_BY_OWNER(),
                        'data' => [
                            'type' => NotificationTypeEnum::TRIP_SERVICE_BOOKING_CANCELLED_BY_OWNER(),
                            'title' => __('notifications.trip_service_booking_cancelled_by_owner.title'),
                            'content' => __('notifications.trip_service_booking_cancelled_by_owner.content'),
                            'trip_service_id' => (string) $booking->trip_service_id,
                            'booking_id' => (string) $booking->id,
                        ],
                        'created_at' => now()->subHours(rand(1, 24)),
                        'read_at' => rand(0, 1) ? now()->subHours(rand(1, 12)) : null,
                    ]);
                }
            }
        }
    }
}
