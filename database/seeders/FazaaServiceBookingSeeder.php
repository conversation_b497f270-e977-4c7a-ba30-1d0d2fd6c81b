<?php

namespace Database\Seeders;

use App\Enums\Bookings\BookingStatusEnum;
use App\Models\Service\FazaaService;
use App\Models\User;
use Illuminate\Database\Seeder;

class FazaaServiceBookingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::all();
        $fazaaServices = FazaaService::active()->get();

        foreach ($fazaaServices as $fazaaService) {
            // 90% chance to create bookings
            if (fake()->boolean(90)) {
                for ($i = 0; $i < fake()->numberBetween(1, 3); $i++) {
                    if ($fazaaService->bookings()->accepted()->exists()) {
                        continue;
                    }

                    $user = $users->random();

                    // Create booking with random status (30% pending, 60% accepted, 10% rejected)
                    $status = match (fake()->randomElement([1, 1, 1, 2, 2, 2, 2, 2, 2, 3])) {
                        1 => BookingStatusEnum::PENDING(),
                        2 => BookingStatusEnum::ACCEPTED(),
                        3 => BookingStatusEnum::REJECTED(),
                    };

                    $bookingData = [
                        'user_id' => $user->id,
                        'note' => fake()->optional(0.3)->sentence(), // 70% chance of having a note
                        'status' => $status,
                        'rejection_reason' => $status === BookingStatusEnum::REJECTED()
                            ? fake()->sentence()
                            : null,
                    ];

                    $fazaaService->bookings()->create($bookingData);
                }
            }
        }
    }
}
