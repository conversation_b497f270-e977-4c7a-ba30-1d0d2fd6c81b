<?php

namespace Database\Seeders;

use App\Models\Common\City;
use App\Models\Common\Country;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $countriesKeys = Country::query()->get()->modelKeys();
        $now = now();
        $citiesKeys = City::query()->get()->modelKeys();
        $users = [
            [
                'id' => snowflake(),
                'name' => 'abdulkarim',
                'email' => '<EMAIL>',
                'mobile' => '+966501234567',
                'email_verified_at' => $now,
                'created_at' => $now,
                'country_id' => Arr::random($countriesKeys),
                'city_id' => Arr::random($citiesKeys),
                'location_lat' => '24.713554',
                'location_lng' => '46.675296'
            ],
            [
                'id' => snowflake(),
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'mobile' => '+966501234568',
                'email_verified_at' => $now,
                'created_at' => $now,
                'country_id' => Arr::random($countriesKeys),
                'city_id' => Arr::random($citiesKeys),
                'location_lat' => '24.713554',
                'location_lng' => '46.675296'
            ],
            [
                'id' => snowflake(),
                'name' => 'Test User 2',
                'email' => '<EMAIL>',
                'mobile' => '+966501234569',
                'email_verified_at' => $now,
                'created_at' => $now,
                'country_id' => Arr::random($countriesKeys),
                'city_id' => Arr::random($citiesKeys),
                'location_lat' => '24.713554',
                'location_lng' => '46.675296'
            ],
            [
                'id' => snowflake(),
                'name' => 'Test User 3',
                'email' => '<EMAIL>',
                'mobile' => '+966501234570',
                'email_verified_at' => $now,
                'created_at' => $now,
                'country_id' => Arr::random($countriesKeys),
                'city_id' => Arr::random($citiesKeys),
                'location_lat' => '24.713554',
                'location_lng' => '46.675296'
            ],
            [
                'id' => snowflake(),
                'name' => 'Abdullah Addow',
                'email' => '<EMAIL>',
                'mobile' => '+966501234571',
                'email_verified_at' => $now,
                'created_at' => $now,
                'country_id' => Arr::random($countriesKeys),
                'city_id' => Arr::random($citiesKeys),
                'location_lat' => '24.713554',
                'location_lng' => '46.675296'
            ]
        ];

        User::query()->insert($users);
    }
}
