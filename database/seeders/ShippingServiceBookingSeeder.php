<?php

namespace Database\Seeders;

use App\Enums\Bookings\BookingStatusEnum;
use App\Models\Service\ShippingService;
use App\Models\User;
use Illuminate\Database\Seeder;

class ShippingServiceBookingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::all();
        $shippingServices = ShippingService::active()->get();

        foreach ($shippingServices as $shippingService) {
            // 95% chance to create bookings
            if (fake()->boolean(95)) {
                // Create 2-5 bookings for this shipping service
                for ($i = 0; $i < fake()->numberBetween(2, 5); $i++) {
                    // Skip if fully booked
                    if ($shippingService->isFullyBooked()) {
                        continue;
                    }

                    $user = $users->random();

                    // Create booking with random status (70% pending, 20% accepted, 10% rejected)
                    $status = match (fake()->randomElement([1, 1, 1, 1, 1, 1, 1, 2, 2, 3])) {
                        1 => BookingStatusEnum::PENDING(),
                        2 => BookingStatusEnum::ACCEPTED(),
                        3 => BookingStatusEnum::REJECTED(),
                    };

                    $bookingData = [
                        'user_id' => $user->id,
                        'status' => $status,
                        'note' => fake()->optional(0.7)->sentence(), // Increased chance of having a note
                        'is_fragile' => fake()->boolean(),
                        'rejection_reason' => $status === BookingStatusEnum::REJECTED()
                            ? fake()->sentence()
                            : null,
                    ];

                    // Add volumes based on shipping service capabilities and available space
                    if ($shippingService->can_ship_packages && $shippingService->available_packages_volume > 0) {
                        $bookingData['packages_volume'] = fake()->numberBetween(
                            1,
                            min(8, $shippingService->available_packages_volume) // Increased max volume
                        );
                    }

                    if ($shippingService->can_ship_documents && $shippingService->available_document_volume > 0) {
                        $bookingData['document_volume'] = fake()->numberBetween(
                            1,
                            min(8, $shippingService->available_document_volume) // Increased max volume
                        );
                    }

                    if ($shippingService->can_ship_furniture && $shippingService->available_furniture_volume > 0) {
                        $bookingData['furniture_volume'] = fake()->numberBetween(
                            1,
                            min(8, $shippingService->available_furniture_volume) // Increased max volume
                        );
                    }

                    $booking = $shippingService->bookings()->create($bookingData);

                    // If booking is accepted, update available volumes
                    if ($status === BookingStatusEnum::ACCEPTED()) {
                        $shippingService->decrement('available_packages_volume', $bookingData['packages_volume'] ?? 0);
                        $shippingService->decrement('available_document_volume', $bookingData['document_volume'] ?? 0);
                        $shippingService->decrement('available_furniture_volume', $bookingData['furniture_volume'] ?? 0);
                        $shippingService->refresh();
                    }
                }
            }
        }
    }
}
