<?php

namespace Database\Seeders;

use App\Models\Common\Car;
use App\Models\Common\CarType;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Arr;

class CarSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::all()->pluck('id')->toArray();

        $carTypes = CarType::all()->pluck('id')->toArray();

        foreach ($users as $user) {
            for ($i = 0; $i < rand(2, 4); $i++) {
                for ($j = 0; $j < 1; $j++) {
                    $cars[] = Car::factory()->make([
                        'type_id' => Arr::random($carTypes),
                        'user_id' => $user,
                    ])->toArray();
                }
            }
        }

        Car::insert($cars);
    }
}
