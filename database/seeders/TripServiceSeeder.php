<?php

namespace Database\Seeders;

use App\Events\TripServiceCreated;
use App\Models\Common\Car;
use App\Models\Common\City;
use App\Models\Common\TripSeat;
use App\Models\Service\AdditionalService;
use App\Models\Service\TripService;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Arr;

class TripServiceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $citiesId = City::all()->pluck('id')->toArray();

        $additionalServices = AdditionalService::all()->pluck('id')->toArray();

        $users = User::all()->pluck('id')->toArray();

        foreach ($users as $user) {
            $cars = Car::where('user_id', $user)->get();

            for ($i = 0; $i < 10; $i++) {
                $car = $cars->random();
                $numberOfSeats = rand(1, $car->type->number_of_seats);

                $tripService = TripService::factory()->create([
                    'from_city_id' => Arr::random($citiesId),
                    'to_city_id' => Arr::random($citiesId),
                    'user_id' => $user,
                    'car_id' => $car->id,
                    'number_of_seats' => $numberOfSeats,
                    'number_of_available_seats' => $numberOfSeats,
                ]);

                TripServiceCreated::dispatch($tripService->id);

                $tripService->additionalServices()->sync(
                    Arr::random($additionalServices, rand(1, 3))
                );

                $seatNumbers = collect([
                    'A1',
                    'A2',
                    'A3',
                    'B1',
                    'B2',
                    'B3',
                    'C1',
                    'C2',
                    'C3',
                ])->reverse();

                for ($j = 0; $j < $tripService->number_of_seats + 2; $j++) {
                    $seatNumber = $seatNumbers->pop();
                    TripSeat::factory()->create([
                        'trip_id' => $tripService->id,
                        'number' => $seatNumber,
                        'status' => in_array($seatNumber, ['A1', 'A2']) ? 'unavailable' : 'available',
                    ]);
                }
            }
        }
    }
}
