<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('trip_service_bookings', function (Blueprint $table) {
            $table->snowflake()->primary();

            $table->foreignSnowflake('trip_service_id')->index()->constrained()->onDelete('cascade');
            $table->foreignSnowflake('user_id')->index()->constrained()->onDelete('cascade');

            $table->string('note', 512)->nullable();

            $table->integer('number_of_seats');

            $table->string('status');

            $table->boolean('has_fragile_items')->default(false);

            $table->dateTime('accepted_at')->nullable();

            $table->string('rejection_reason', 255)->nullable();
            $table->dateTime('rejected_at')->nullable();
            $table->string('rejection_note', 500)->nullable();

            $table->string('cancellation_reason', 255)->nullable();
            $table->string('cancellation_note', 500)->nullable();
            $table->dateTime('cancelled_at')->nullable();

            $table->dateTime('attendance_confirmed_at')->nullable();

            $table->dateTime('late_arrival_notified_at')->nullable();

            $table->timestamp('arrival_confirmed_at')->nullable()->after('late_arrival_notified_at');

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('trip_service_bookings');
    }
};
