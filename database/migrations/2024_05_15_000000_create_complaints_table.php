<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('complaints', function (Blueprint $table) {
            $table->snowflake()->primary();
            
            $table->foreignSnowflake('user_id');
            $table->foreignSnowflake('booking_id');

            $table->string('booking_type');
            $table->text('description');
            $table->string('status')->default('pending');
            $table->text('admin_response')->nullable();
            $table->foreignSnowflake('admin_id')->nullable();
            $table->timestamp('resolved_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('complaints');
    }
};
