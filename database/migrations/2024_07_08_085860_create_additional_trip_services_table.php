<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('additional_service_trip_service', function (Blueprint $table) {

            $table->foreignSnowflake('trip_service_id')->index()->constrained()->nullOnDelete();
            $table->foreignSnowflake('additional_service_id')->index()->onDelete('cascade');

            $table->primary(['trip_service_id', 'additional_service_id']);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('additional_service_trip_service');
    }
};
