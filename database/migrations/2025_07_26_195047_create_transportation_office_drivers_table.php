<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transportation_office_drivers', function (Blueprint $table) {
            $table->snowflake()->primary();
            $table->snowflake('transportation_office_id');
            $table->snowflake('user_id'); // Reference to users table
            $table->string('type'); // 'collaborator' or 'employee'
            $table->string('status')->default('active'); // active, inactive, suspended

            $table->timestamps();
            $table->softDeletes();

            // Foreign keys
            $table->foreign('transportation_office_id')->references('id')->on('transportation_offices')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            // Indexes
            $table->index(['transportation_office_id', 'type']);
            $table->index('status');

            // Unique constraint to prevent duplicate driver assignments
            $table->unique(['transportation_office_id', 'user_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transportation_office_drivers');
    }
};
