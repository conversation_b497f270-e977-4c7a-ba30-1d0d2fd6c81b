<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('shipping_services', function (Blueprint $table) {
            $table->string('delivery_location')->nullable()->change();
        });

        Schema::table('shipping_requests', function (Blueprint $table) {
            $table->string('delivery_location')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('shipping_services', function (Blueprint $table) {
            $table->string('delivery_location')->nullable(false)->change();
        });

        Schema::table('shipping_requests', function (Blueprint $table) {
            $table->string('delivery_location')->nullable(false)->change();
        });
    }
};
