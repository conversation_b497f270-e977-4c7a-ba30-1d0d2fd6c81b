<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shipping_request_bookings', function (Blueprint $table) {
            $table->snowflake()->primary();

            $table->foreignSnowflake('shipping_request_id')->index()->nullable()->constrained()->nullOnDelete();

            $table->foreignSnowflake('user_id')->index()->nullable()->constrained()->nullOnDelete();

            $table->string('status');

            $table->decimal('package_price')->nullable();
            $table->decimal('document_price')->nullable();
            $table->decimal('furniture_price')->nullable();

            $table->text('note')->nullable();

            $table->dateTime('accepted_at')->nullable();

            $table->string('rejection_reason', 255)->nullable();
            $table->string('rejection_note', 500)->nullable();
            $table->dateTime('rejected_at')->nullable();

            $table->string('cancellation_reason', 255)->nullable();
            $table->string('cancellation_note', 500)->nullable();
            $table->dateTime('cancelled_at')->nullable();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shipping_request_bookings');
    }
};
