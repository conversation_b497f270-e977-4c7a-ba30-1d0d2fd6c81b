<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shipping_service_bookings', function (Blueprint $table) {
            $table->snowflake()->primary();

            $table->foreignSnowflake('shipping_service_id')->index();
            $table->foreignSnowflake('user_id');

            $table->string('status');

            $table->float('packages_volume')->unsigned()->default(0);
            $table->float('document_volume')->default(0);
            $table->float('furniture_volume')->default(0);

            $table->boolean('is_fragile')->default(false);

            $table->text('note')->nullable();

            $table->dateTime('accepted_at')->nullable();

            $table->string('rejection_reason', 255)->nullable();
            $table->string('rejection_note', 500)->nullable();
            $table->dateTime('rejected_at')->nullable();

            $table->string('cancellation_reason', 255)->nullable();
            $table->string('cancellation_note', 500)->nullable();
            $table->dateTime('cancelled_at')->nullable();

            $table->dateTime('late_arrival_notified_at')->nullable();

            $table->dateTime('attendance_confirmed_at')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shipping_service_bookings');
    }
};
