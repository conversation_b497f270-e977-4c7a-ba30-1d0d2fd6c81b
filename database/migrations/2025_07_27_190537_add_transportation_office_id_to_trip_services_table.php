<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('trip_services', function (Blueprint $table) {
            $table->foreignSnowflake('transportation_office_id')
                ->nullable()
                ->after('user_id')
                ->index()
                ->constrained('transportation_offices')
                ->nullOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('trip_services', function (Blueprint $table) {
            $table->dropForeign(['transportation_office_id']);
            $table->dropColumn('transportation_office_id');
        });
    }
};
