<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('fazaa_service_bookings', function (Blueprint $table) {
            $table->snowflake()->primary();
            $table->foreignSnowflake('fazaa_service_id')->constrained('fazaa_services')->onDelete('cascade');
            $table->foreignSnowflake('user_id')->constrained('users')->onDelete('cascade');
            $table->text('note')->nullable();

            $table->string('status');

            $table->dateTime('rejected_at')->nullable();
            $table->string('rejection_reason', 255)->nullable();
            $table->string('rejection_note', 500)->nullable();

            $table->dateTime('accepted_at')->nullable();

            $table->dateTime('cancelled_at')->nullable();
            $table->string('cancellation_reason', 255)->nullable();
            $table->string('cancellation_note', 500)->nullable();

            $table->dateTime('attendance_confirmed_at')->nullable();

            $table->dateTime('late_arrival_notified_at')->nullable();

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('fazaa_service_bookings');
    }
};
