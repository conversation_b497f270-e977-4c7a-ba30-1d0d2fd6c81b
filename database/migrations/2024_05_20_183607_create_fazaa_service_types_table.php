<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('fazaa_service_types', function (Blueprint $table) {
            $table->snowflake()->primary();
            $table->string('name_en');
            $table->string('name_ar');

            $table->string('key')->unique();

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('fazaa_service_types');
    }
};
