<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Service tables (tracking)
        $serviceTables = [
            'fazaa_services',
            'shipping_services',
            'trip_services',
        ];

        // Request tables
        $requestTables = [
            'fazaa_requests',
            'shipping_requests',
            'trip_requests',
        ];

        // Booking tables
        $bookingTables = [
            'fazaa_service_bookings',
            'shipping_service_bookings',
            'trip_service_bookings',
        ];

        // Offer tables
        $offerTables = [
            'fazaa_request_bookings',
            'shipping_request_bookings',
            'trip_request_bookings',
        ];

        // Add read_at and read_by_id columns to all tables
        $allTables = array_merge($serviceTables, $requestTables, $bookingTables, $offerTables);

        foreach ($allTables as $table) {
            Schema::table($table, function (Blueprint $table) {
                $table->timestamp('read_at')->nullable();
                $table->foreignSnowflake('read_by_id')->nullable()->constrained('admins')->nullOnDelete();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Service tables (tracking)
        $serviceTables = [
            'fazaa_services',
            'shipping_services',
            'trip_services',
        ];

        // Request tables
        $requestTables = [
            'fazaa_requests',
            'shipping_requests',
            'trip_requests',
        ];

        // Booking tables
        $bookingTables = [
            'fazaa_service_bookings',
            'shipping_service_bookings',
            'trip_service_bookings',
        ];

        // Offer tables
        $offerTables = [
            'fazaa_request_bookings',
            'shipping_request_bookings',
            'trip_request_bookings',
        ];

        // Remove read_at and read_by_id columns from all tables
        $allTables = array_merge($serviceTables, $requestTables, $bookingTables, $offerTables);

        foreach ($allTables as $table) {
            Schema::table($table, function (Blueprint $table) {
                $table->dropColumn('read_at');
                $table->dropForeign(['read_by_id']);
                $table->dropColumn('read_by_id');
            });
        }
    }
};
