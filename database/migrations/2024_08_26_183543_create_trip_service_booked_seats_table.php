<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('trip_service_booked_seats', function (Blueprint $table) {
            $table->snowflake()->primary();

            $table->foreignSnowflake('trip_service_booking_id')->index()->nullable();
            $table->foreignSnowflake('seat_id')->index()->nullable();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('trip_service_booked_seats');
    }
};
