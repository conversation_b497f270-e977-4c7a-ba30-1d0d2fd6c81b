<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('transportation_offices', function (Blueprint $table) {
            $table->string('office_logo')->nullable()->after('description');
            $table->string('license_document')->nullable()->after('office_logo');
            $table->text('registration_documents')->nullable()->after('license_document');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('transportation_offices', function (Blueprint $table) {
            $table->dropColumn(['office_logo', 'license_document', 'registration_documents']);
        });
    }
};
