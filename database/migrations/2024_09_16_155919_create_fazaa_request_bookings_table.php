<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fazaa_request_bookings', function (Blueprint $table) {
            $table->snowflake()->primary();

            $table->foreignSnowflake('fazaa_request_id')->constrained('fazaa_requests')->onDelete('cascade');
            $table->foreignSnowflake('user_id')->constrained('users')->onDelete('cascade');

            $table->decimal('price');

            $table->string('status');
            $table->text('note')->nullable();

            $table->dateTime('accepted_at')->nullable();

            $table->string('rejection_reason', 255)->nullable();
            $table->string('rejection_note', 500)->nullable();
            $table->dateTime('rejected_at')->nullable();

            $table->string('cancellation_reason', 255)->nullable();
            $table->string('cancellation_note', 500)->nullable();
            $table->dateTime('cancelled_at')->nullable();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fazaa_request_bookings');
    }
};
