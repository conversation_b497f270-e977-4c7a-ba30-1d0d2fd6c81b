<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fazaa_services', function (Blueprint $table) {
            $table->snowflake()->primary();

            $table->foreignSnowflake('fazaa_service_type_id')->index();
            $table->foreignSnowflake('specific_type_id')->index();

            $table->string('transportation_type', 30);

            $table->dateTime('departure_datetime');
            $table->dateTime('arrival_datetime');

            $table->foreignSnowflake('from_city_id')->index()->nullOnDelete();
            $table->float('from_location_lat');
            $table->float('from_location_lng');

            $table->foreignSnowflake('to_city_id')->index()->nullOnDelete();
            $table->float('to_location_lat');
            $table->float('to_location_lng');

            $table->string('service_location');

            $table->decimal('price')->unsigned();

            $table->integer('shipment_volume')->unsigned()->nullable();

            $table->string('note', 512)->nullable();;

            $table->foreignSnowflake('user_id')->index()->constrained()->nullOnDelete();

            $table->timestamp('cancelled_at')->nullable();
            $table->string('cancellation_reason', 255)->nullable();
            $table->string('cancellation_note', 500)->nullable();

            $table->timestamp('attendance_confirmed_at')->nullable();

            $table->timestamp('delayed_at')->nullable();
            $table->string('delay_reason', 255)->nullable();
            $table->string('delay_note', 500)->nullable();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fazaa_services');
    }
};
