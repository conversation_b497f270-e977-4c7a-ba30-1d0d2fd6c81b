<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cars', function (Blueprint $table) {
            $table->snowflake()->primary();

            $table->foreignSnowflake('user_id')->index()->onDelete('cascade');
            $table->foreignSnowflake('type_id')->index()->constrained('car_types')->nullOnDelete();

            $table->unsignedInteger('model');
            $table->string('plate_number', 20);

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cars');
    }
};
