<?php

use App\Enums\Travel\WeatherStatuses;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $tables = [
            'trip_services',
            'shipping_services',
            'fazaa_services',
            'trip_requests',
            'shipping_requests',
            'fazaa_requests',
        ];

        foreach ($tables as $table) {
            Schema::table($table, function (Blueprint $table) {
                $table->string('weather_status')->default(WeatherStatuses::CLEAR());
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        $tables = [
            'trip_services',
            'shipping_services',
            'fazaa_services',
            'trip_requests',
            'shipping_requests',
            'fazaa_requests',
        ];

        foreach ($tables as $table) {
            Schema::table($table, function (Blueprint $table) {
                $table->dropColumn('weather_status');
            });
        }
    }
};
