<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('trip_request_bookings', function (Blueprint $table) {
            $table->snowflake()->primary();

            $table->foreignSnowflake('trip_request_id')->index()->constrained()->nullOnDelete();
            $table->foreignSnowflake('user_id')->index()->constrained()->nullOnDelete();

            $table->decimal('price')->unsigned();

            $table->string('note', 512)->nullable();

            $table->string('status');

            $table->dateTime('accepted_at')->nullable();

            $table->string('rejection_reason')->nullable();
            $table->string('rejection_note', 500)->nullable();
            $table->dateTime('rejected_at')->nullable();

            $table->string('cancellation_reason')->nullable();
            $table->string('cancellation_note', 500)->nullable();
            $table->dateTime('cancelled_at')->nullable();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('trip_request_bookings');
    }
};
