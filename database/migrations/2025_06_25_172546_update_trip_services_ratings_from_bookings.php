<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Calculate and update the average rating for each trip service using Eloquent
        $tripServices = \App\Models\Service\TripService::all();
        foreach ($tripServices as $tripService) {
            $averageRating = $tripService->bookings()
                ->whereHas('rating')
                ->with('rating')
                ->get()
                ->pluck('rating.rating')
                ->filter()
                ->avg();
            $tripService->update(['rating' => $averageRating ?? 0]);
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Optionally, set all ratings to null
        DB::table("trip_services")->update(['rating' => 0]);
    }
};
