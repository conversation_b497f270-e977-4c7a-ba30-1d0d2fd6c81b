<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('trip_services', function (Blueprint $table) {
            $table->timestamp('arrival_confirmed_at')->nullable()->after('attendance_confirmed_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('trip_services', function (Blueprint $table) {
            $table->dropColumn('arrival_confirmed_at');
        });
    }
};
