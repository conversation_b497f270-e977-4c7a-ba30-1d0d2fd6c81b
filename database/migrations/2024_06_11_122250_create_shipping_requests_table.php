<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shipping_requests', function (Blueprint $table) {
            $table->snowflake()->primary();

            $table->string('transportation_type', 30);

            $table->boolean('can_ship_packages')->default(false);
            $table->boolean('can_ship_documents')->default(false);
            $table->boolean('can_ship_furniture')->default(false);


            $table->dateTime('departure_datetime');
            $table->dateTime('arrival_datetime');

            $table->unsignedInteger('packages_volume')->nullable();

            $table->unsignedInteger('document_volume')->nullable();

            $table->unsignedInteger('furniture_volume')->nullable();

            $table->foreignSnowflake('from_city_id')->index()->nullOnDelete();
            $table->float('from_location_lat');
            $table->float('from_location_lng');

            $table->foreignSnowflake('to_city_id')->index()->nullOnDelete();
            $table->float('to_location_lat');
            $table->float('to_location_lng');

            $table->string('delivery_location');

            $table->foreignSnowflake('user_id')->index()->constrained()->nullOnDelete();

            $table->boolean('is_fragile')->default(false);

            $table->string('note', 512)->nullable();

            $table->timestamp('cancelled_at')->nullable();
            $table->string('cancellation_reason', 255)->nullable();
            $table->string('cancellation_note', 500)->nullable();

            $table->timestamp('attendance_confirmed_at')->nullable();

            $table->timestamp('delayed_at')->nullable();
            $table->string('delay_reason', 255)->nullable();
            $table->string('delay_note', 500)->nullable();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shipping_requests');
    }
};
