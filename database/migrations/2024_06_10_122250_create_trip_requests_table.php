<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('trip_requests', function (Blueprint $table) {
            $table->snowflake()->primary();

            $table->string('trip_type', 30);

            $table->dateTime('departure_datetime');
            $table->dateTime('arrival_datetime');

            $table->unsignedInteger('number_of_seats');

            $table->foreignSnowflake('from_city_id')->index()->nullOnDelete();
            $table->float('from_location_lat');
            $table->float('from_location_lng');

            $table->foreignSnowflake('to_city_id')->index()->nullOnDelete();
            $table->float('to_location_lat');
            $table->float('to_location_lng');

            $table->decimal('price')->unsigned();

            $table->boolean('allow_smoking')->default(false);

            $table->boolean('deliver_to_door')->default(false);

            $table->foreignSnowflake('user_id')->index()->constrained()->nullOnDelete();

            $table->foreignSnowflake('car_type_id')->index()->constrained()->nullOnDelete();

            $table->string('arrive_destination')->nullable();

            $table->string('note', 512)->nullable();;

            $table->timestamp('cancelled_at')->nullable();
            $table->string('cancellation_reason', 255)->nullable();
            $table->string('cancellation_note', 500)->nullable();

            $table->timestamp('attendance_confirmed_at')->nullable();

            $table->boolean('refunds_processed')->default(false);

            $table->timestamp('delayed_at')->nullable();
            $table->string('delay_reason', 255)->nullable();
            $table->string('delay_note', 500)->nullable();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('trip_requests');
    }
};
