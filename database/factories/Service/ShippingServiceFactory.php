<?php

namespace Database\Factories\Service;

use App\Enums\Travel\TransportationTypeEnum;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Travel\ShippingService>
 */
class ShippingServiceFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $departure_datetime = $this->faker->dateTimeBetween('1 day', '4 days');
        $arrival_datetime = Carbon::parse($departure_datetime)->addHours(random_int(2, 5))->format('Y-m-d H:i:s');

        $canShipPackages = $this->faker->boolean();
        $canShipDocuments = $this->faker->boolean();
        $canShipFurniture = $this->faker->boolean();

        if (fake()->boolean(10)) {
            $cancelled_at = Carbon::parse($departure_datetime)->subHours(random_int(1, 3));
            $cancelled_reason = [
                $this->faker->sentence(),
                $this->faker->sentence(),
                $this->faker->sentence(),
            ];
        }

        if (!$canShipPackages && !$canShipDocuments && !$canShipFurniture) {
            $canShipPackages = true;
        }

        if ($canShipDocuments) {
            $documentVolume = $this->faker->numberBetween(1, 11);
            $availableDocumentVolume = max(0, $documentVolume - $this->faker->numberBetween(0, 4));
            $documentPrice = $this->faker->randomFloat(2, 10, 100);
        }

        if ($canShipPackages) {
            $packageVolume = $this->faker->numberBetween(1, 11);
            $availablePackageVolume = max(0, $packageVolume - $this->faker->numberBetween(0, 4));
            $packagePrice = $this->faker->randomFloat(2, 20, 200);
        }

        if ($canShipFurniture) {
            $furnitureVolume = $this->faker->numberBetween(1, 11);
            $availableFurnitureVolume = max(0, $furnitureVolume - $this->faker->numberBetween(0, 4));
            $furniturePrice = $this->faker->randomFloat(2, 50, 500);
        }

        return [
            'id' => snowflake(),
            'transportation_type' => $this->faker->randomElement(TransportationTypeEnum::values()),
            'can_ship_packages' => $canShipPackages,
            'can_ship_documents' => $canShipDocuments,
            'can_ship_furniture' => $canShipFurniture,
            'departure_datetime' => $departure_datetime,
            'arrival_datetime' => $arrival_datetime,
            'document_volume' => $documentVolume ?? null,
            'available_document_volume' => $availableDocumentVolume ?? null,
            'packages_volume' => $packageVolume ?? null,
            'available_packages_volume' => $availablePackageVolume ?? null,
            'furniture_volume' => $furnitureVolume ?? null,
            'available_furniture_volume' => $availableFurnitureVolume ?? null,
            'from_city_id' => null,
            'to_city_id' => null,
            'from_location_lat' => $this->faker->latitude(),
            'from_location_lng' => $this->faker->longitude(),
            'to_location_lat' => $this->faker->latitude(),
            'to_location_lng' => $this->faker->longitude(),
            'delivery_location' => $this->faker->address,
            'document_price' => $documentPrice ?? null,
            'furniture_price' => $furniturePrice ?? null,
            'package_price' => $packagePrice ?? null,
            'user_id' => null,
            'cancelled_at' => $cancelled_at ?? null,
            'cancellation_reason' => $cancellation_reason ?? null,
        ];
    }
}
