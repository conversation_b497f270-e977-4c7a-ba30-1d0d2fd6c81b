<?php

namespace Database\Factories\Common;

use App\Enums\Travel\SeatStatusEnum;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Common\Seat>
 */
class SeatFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'id' => snowflake(),
            'status' => $this->faker->randomElement(SeatStatusEnum::values()),
        ];
    }
}
