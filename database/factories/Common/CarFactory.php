<?php

namespace Database\Factories\Common;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Common\Car>
 */
class CarFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'id' => snowflake(),
            'model' => $this->faker->dateTimeBetween('-12 year', 'now')->format('Y'),
            'plate_number' => $this->faker->bothify('???-####'),
        ];
    }
}
