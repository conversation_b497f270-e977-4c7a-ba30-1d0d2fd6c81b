<?php

namespace Database\Factories\Request;

use App\Enums\Travel\FazaaServiceTypeEnum;
use Illuminate\Support\Carbon;
use App\Enums\Travel\TransportationTypeEnum;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Travel\Trip>
 */
class FazaaRequestFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $departure_datetime = $this->faker->dateTimeBetween('1 day', '4 days');
        $arrival_datetime = Carbon::parse($departure_datetime)->addHours(random_int(2, 5))->format('Y-m-d H:i:s');

        if (fake()->boolean(10)) {
            $cancelled_at = Carbon::parse($departure_datetime)->subHours(random_int(1, 3));
            $cancelled_reason = [
                $this->faker->sentence(),
                $this->faker->sentence(),
                $this->faker->sentence(),
            ];
        }

        return [
            'id' => snowflake(),
            'transportation_type' => $this->faker->randomElement(TransportationTypeEnum::values()),
            'departure_datetime' => $departure_datetime,
            'arrival_datetime' => $arrival_datetime,
            'service_location' => $this->faker->address(),
            'from_city_id' => null,
            'to_city_id' => null,
            'from_location_lat' => $this->faker->latitude(),
            'from_location_lng' => $this->faker->longitude(),
            'to_location_lat' => $this->faker->latitude(),
            'to_location_lng' => $this->faker->longitude(),
            'user_id' => null,
            'note' => $this->faker->sentence(),
            'cancelled_at' => $cancelled_at ?? null,
            'cancellation_reason' => $cancellation_reason ?? null,
        ];
    }
}
