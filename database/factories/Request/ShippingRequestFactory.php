<?php

namespace Database\Factories\Request;

use App\Enums\Travel\TransportationTypeEnum;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Travel\Trip>
 */
class ShippingRequestFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $departure_datetime = $this->faker->dateTimeBetween('1 day', '4 days');
        $arrival_datetime = Carbon::parse($departure_datetime)->addHours(random_int(2, 5))->format('Y-m-d H:i:s');

        $canShipPackages = $this->faker->boolean();
        $canShipDocuments = $this->faker->boolean();
        $canShipFurniture = $this->faker->boolean();

        if (!$canShipPackages && !$canShipDocuments && !$canShipFurniture) {
            $canShipPackages = true;
        }

        if ($canShipFurniture) {
            $furnitureVolume = $this->faker->numberBetween(1, 11);
        }

        if ($canShipDocuments) {
            $documentVolume = $this->faker->numberBetween(1, 11);
        }

        if ($canShipPackages) {
            $packageVolume = $this->faker->numberBetween(1, 11);
        }

        if (fake()->boolean(10)) {
            $cancelled_at = Carbon::parse($departure_datetime)->subHours(random_int(1, 3));
            $cancelled_reason = [
                $this->faker->sentence(),
                $this->faker->sentence(),
                $this->faker->sentence(),
            ];
        }

        return [
            'id' => snowflake(),
            'transportation_type' => $this->faker->randomElement(TransportationTypeEnum::values()),
            'can_ship_packages' => $canShipPackages,
            'can_ship_documents' => $canShipDocuments,
            'can_ship_furniture' => $canShipFurniture,
            'departure_datetime' => $departure_datetime,
            'arrival_datetime' => $arrival_datetime,
            'furniture_volume' => $furnitureVolume ?? null,
            'document_volume' => $documentVolume ?? null,
            'packages_volume' => $packageVolume ?? null,
            'from_city_id' => null,
            'to_city_id' => null,
            'from_location_lat' => $this->faker->latitude(),
            'from_location_lng' => $this->faker->longitude(),
            'to_location_lat' => $this->faker->latitude(),
            'to_location_lng' => $this->faker->longitude(),
            'delivery_location' => $this->faker->address,
            'user_id' => null,
            'cancelled_at' => $cancelled_at ?? null,
            'cancellation_reason' => $cancellation_reason ?? null,
        ];
    }
}
