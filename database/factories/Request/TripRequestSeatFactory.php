<?php

namespace Database\Factories\Request;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Request\TripRequestSeat>
 */
class TripRequestSeatFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'id' => snowflake(),
            'number' => fake()->regexify('^[A-C][1-3]$'),
        ];
    }
}
