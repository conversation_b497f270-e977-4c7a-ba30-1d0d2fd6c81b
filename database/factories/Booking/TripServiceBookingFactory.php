<?php

namespace Database\Factories\Booking;

use App\Enums\Bookings\BookingStatusEnum;
use App\Models\Booking\TripServiceBooking;
use App\Models\Service\TripService;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Booking\TripServiceBooking>
 */
class TripServiceBookingFactory extends Factory
{
    protected $model = TripServiceBooking::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'trip_service_id' => null,
            'user_id' => null,
            'note' => fake()->optional()->sentence(),
            'number_of_seats' => fake()->numberBetween(1, 4),
            'has_fragile_items' => fake()->boolean(),
            'status' => BookingStatusEnum::PENDING(),
            'rejection_reason' => null,
        ];
    }

    /**
     * Set the booking status to accepted.
     */
    public function accepted(): static
    {
        return $this->state(fn(array $attributes) => [
            'status' => BookingStatusEnum::ACCEPTED(),
        ]);
    }

    /**
     * Set the booking status to rejected.
     */
    public function rejected(): static
    {
        return $this->state(fn(array $attributes) => [
            'status' => BookingStatusEnum::REJECTED(),
            'rejection_reason' => [fake()->sentence()],
        ]);
    }
}
