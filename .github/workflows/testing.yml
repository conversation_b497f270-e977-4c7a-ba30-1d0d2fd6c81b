name: Deploy to Testing

on:
  push:
    branches: [ testing ]

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v2

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.3'
          extensions: mbstring, xml, ctype, iconv, intl, pdo_sqlite, pgsql, redis

      - name: Install Dependencies
        run: composer install --no-progress --no-interaction --prefer-dist

      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/deploy_key
          chmod 600 ~/.ssh/deploy_key
          cat > ~/.ssh/config << END
          Host ${{ secrets.SSH_HOST }}
            HostName ${{ secrets.SSH_HOST }}
            User ${{ secrets.SSH_USER }}
            IdentityFile ~/.ssh/deploy_key
            StrictHostKeyChecking no
          END
          chmod 600 ~/.ssh/config

      - name: Deploy to Testing
        run: |
          # Set deployment variables
          timestamp=$(date +%Y%m%d_%H%M%S)
          release_path="/var/www/releases/${timestamp}"
          current_path="/var/www/html"
          shared_path="/var/www/shared"

          # Create necessary directories on the server
          ssh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "
            mkdir -p ${release_path}
            mkdir -p ${shared_path}/storage
            mkdir -p ${shared_path}/bootstrap/cache
          "

          # Deploy code to the new release directory
          rsync -az --delete \
            --exclude='.git*' \
            --exclude='node_modules' \
            --exclude='storage/logs/*' \
            --exclude='storage/framework/cache/*' \
            --exclude='storage/framework/sessions/*' \
            --exclude='storage/framework/views/*' \
            --exclude='.env' \
            ./ ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }}:${release_path}/

          # Configure the new release
          ssh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "
            cd ${release_path}

            # Create and setup shared storage directories
            mkdir -p ${shared_path}/storage/framework/{views,cache,sessions}
            mkdir -p ${shared_path}/storage/logs

            # Create storage symlinks
            rm -rf ${release_path}/storage
            ln -nfs ${shared_path}/storage ${release_path}/storage

            # Set correct permissions for storage
            sudo chown -R www-data:www-data ${shared_path}/storage
            sudo chmod -R 775 ${shared_path}/storage

            # Create bootstrap/cache symlink
            mkdir -p ${shared_path}/bootstrap/cache
            rm -rf ${release_path}/bootstrap/cache
            ln -nfs ${shared_path}/bootstrap/cache ${release_path}/bootstrap/cache
            sudo chown -R www-data:www-data ${shared_path}/bootstrap/cache
            sudo chmod -R 775 ${shared_path}/bootstrap/cache

            # Copy .env file
            cp ${shared_path}/.env ${release_path}/.env

            # Install dependencies and optimize
            cd ${release_path}
            composer install --no-dev --optimize-autoloader
            php artisan config:cache
            php artisan view:clear
            php artisan optimize:clear
            php artisan optimize

            # Run migrations
            php artisan migrate --force

            # Create storage symlink
            php artisan storage:link

            # Make new release live
            ln -nfs ${release_path} ${current_path}_new
            mv -Tf ${current_path}_new ${current_path}

            # Restart services
            sudo supervisorctl restart laravel-horizon:*
            sudo supervisorctl restart laravel-schedule:*

            # Cleanup old releases (keep last 5)
            # cd /var/www/releases
            # ls -dt */ | tail -n +6 | xargs -I {} rm -rf {}
          "

      - name: Verify Deployment
        run: |
          response=$(curl -s -o /dev/null -w "%{http_code}" http://${{ secrets.SSH_HOST }})
          if [ "$response" != "200" ]; then
            echo "Deployment verification failed with status $response"
            exit 1
          fi
