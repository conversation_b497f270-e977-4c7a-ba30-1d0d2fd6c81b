<?php

use App\Exceptions\Handler;
use App\Http\Middleware\ForceJsonResponse;
use App\Http\Middleware\TransformBooleans;
use App\Http\Middleware\SetLocaleMiddleware;
use Illuminate\Contracts\Debug\ExceptionHandler;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Sentry\Laravel\Integration;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__ . '/../routes/web.php',
        api: __DIR__ . '/../routes/api.php',
        commands: __DIR__ . '/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->prepend(ForceJsonResponse::class);
        $middleware->append(TransformBooleans::class);
        $middleware->append(SetLocaleMiddleware::class);
    })
    ->withEvents(discover: [
        __DIR__ . '/../app/Listeners',
        __DIR__ . '/../app/Listeners/Subscribers',
    ])
    ->withExceptions(function (Exceptions $exceptions) {
        Integration::handles($exceptions);
    })->withSingletons([
        ExceptionHandler::class => Handler::class
    ])->create();
