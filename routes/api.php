<?php

use App\Http\Controllers\API\v1\App\Requests\FazaaRequests\IndexFazaaRequestController;
use App\Http\Controllers\API\v1\App\Requests\FazaaRequests\ShowFazaaRequestController;
use App\Http\Controllers\API\v1\App\Requests\ShippingRequests\IndexShippingRequestController;
use App\Http\Controllers\API\v1\App\Requests\ShippingRequests\ShowShippingRequestController;
use App\Http\Controllers\API\v1\App\Requests\TripRequests\IndexTripRequestController;
use App\Http\Controllers\API\v1\App\Requests\TripRequests\ShowTripRequestController;
use App\Http\Controllers\API\v1\App\Services\FazaaServices\IndexFazaaServiceController;
use App\Http\Controllers\API\v1\App\Services\FazaaServices\ShowFazaaServiceController;
use App\Http\Controllers\API\v1\App\Services\ShippingServices\IndexShippingServiceController;
use App\Http\Controllers\API\v1\App\Services\ShippingServices\ShowShippingServiceController;
use App\Http\Controllers\API\v1\App\Services\TripServices\IndexTripServiceController;
use App\Http\Controllers\API\v1\App\Services\TripServices\ShowTripServiceController;
use App\Http\Controllers\API\v1\App\Users\Accounts\Authentication\FirebaseAuthenticationController;
use App\Http\Controllers\API\v1\App\Users\Accounts\Authentication\LogoutController;
use App\Http\Controllers\API\v1\App\Users\Accounts\Authentication\Otp\SendOtpController;
use App\Http\Controllers\API\v1\App\Users\Accounts\Authentication\Otp\VerifyOtpController;
use App\Http\Controllers\API\v1\App\Users\Accounts\Authentication\RegisterController;
use App\Http\Controllers\API\v1\App\Users\Accounts\Profile\ShowProfileController;
use App\Http\Controllers\API\v1\App\Users\Accounts\Profile\UpdateProfileController;
use App\Http\Controllers\API\v1\App\Users\Bookings\FazaaRequests\StoreFazaaRequestBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\FazaaServices\AcceptFazaaServiceBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\FazaaServices\CancelFazaaServiceBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\FazaaServices\CancelMyFazaaServiceBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\FazaaServices\IndexFazaaServiceBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\FazaaServices\IndexMyFazaaServiceBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\FazaaServices\RejectFazaaServiceBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\FazaaServices\ShowFazaaServiceBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\FazaaServices\ShowMyFazaaServiceBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\FazaaServices\StoreFazaaServiceBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\ShippingRequests\AcceptShippingRequestBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\ShippingRequests\CancelMyShippingRequestBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\ShippingRequests\CancelShippingRequestBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\ShippingRequests\IndexMyShippingRequestBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\ShippingRequests\IndexShippingRequestBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\ShippingRequests\RejectShippingRequestBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\ShippingRequests\ShowMyShippingRequestBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\ShippingRequests\ShowShippingRequestBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\ShippingRequests\StoreShippingRequestBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\ShippingServices\StoreShippingServiceBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\TripRequests\StoreTripRequestBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\TripServices\StoreTripServiceBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookmarks\DestroyBookmarkController;
use App\Http\Controllers\API\v1\App\Users\Bookmarks\IndexMyBookmarkController;
use App\Http\Controllers\API\v1\App\Users\Bookmarks\StoreBookmarkController;
use App\Http\Controllers\API\v1\App\Users\Cars\IndexMyCarController;
use App\Http\Controllers\API\v1\App\Users\Cars\StoreCarController;
use App\Http\Controllers\API\v1\App\Users\ServiceRequests\Fazaa\IndexMyFazaaRequestController;
use App\Http\Controllers\API\v1\App\Users\ServiceRequests\Fazaa\StoreFazaaRequestController;
use App\Http\Controllers\API\v1\App\Users\ServiceRequests\Shippings\IndexMyShippingRequestController;
use App\Http\Controllers\API\v1\App\Users\ServiceRequests\Shippings\StoreShippingRequestController;
use App\Http\Controllers\API\v1\App\Users\ServiceRequests\Trips\IndexMyTripRequestController;
use App\Http\Controllers\API\v1\App\Users\ServiceRequests\Trips\StoreTripRequestController;
use App\Http\Controllers\API\v1\App\Users\Services\Fazaa\IndexMyFazaaServiceController;
use App\Http\Controllers\API\v1\App\Users\Services\Fazaa\StoreFazaaServiceController;
use App\Http\Controllers\API\v1\App\Users\Services\Shippings\IndexMyShippingServiceController;
use App\Http\Controllers\API\v1\App\Users\Services\Shippings\StoreShippingServiceController;
use App\Http\Controllers\API\v1\App\Users\Services\Trips\IndexMyTripServiceController;
use App\Http\Controllers\API\v1\App\Users\Services\Trips\StoreTripServiceController;
use App\Http\Controllers\API\v1\App\Users\Services\Trips\UpdateTripServiceController;
use App\Http\Controllers\Common\AdditionalServices\IndexAdditionalServiceController;
use App\Http\Controllers\Common\CarTypes\IndexCarTypeController;
use App\Http\Controllers\Common\CarTypes\Seats\IndexSeatController;
use App\Http\Controllers\Common\Cities\IndexCityController;
use App\Http\Controllers\Common\Countries\IndexCountryController;
use App\Http\Controllers\Common\FazaaServiceTypes\IndexFazaaServiceTypeController;
use App\Http\Controllers\Common\FazaaSpecificTypes\IndexFazaaSpecificTypeController;
use App\Http\Controllers\API\v1\App\Users\Notifications\IndexNotificationController;
use App\Http\Controllers\API\v1\App\Users\Notifications\MarkNotificationAsReadController;
use App\Http\Controllers\API\v1\App\Users\Notifications\MarkAllNotificationsAsReadController;
use App\Http\Controllers\API\v1\App\Users\Notifications\CountUnreadNotificationsController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\v1\App\Users\Services\Trips\CancelTripServiceController;
use App\Http\Controllers\API\v1\App\Users\Bookings\TripServices\AcceptTripServiceBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\TripServices\RejectTripServiceBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\TripServices\ShowTripServiceBookingController;
use App\Http\Controllers\API\v1\App\Users\ServiceRequests\Trips\CancelTripRequestController;
use App\Http\Controllers\API\v1\App\Users\ServiceRequests\Trips\ConfirmTripRequestAttendanceController;
use App\Http\Controllers\API\v1\App\Users\ServiceRequests\Trips\DelayTripRequestController;
use App\Http\Controllers\API\v1\App\Users\Services\Trips\ConfirmTripServiceAttendanceController;
use App\Http\Controllers\API\v1\App\Users\Services\Trips\DelayTripServiceController;
use App\Http\Controllers\API\v1\App\Users\ServiceRequests\Trips\UpdateTripRequestController;
use App\Http\Controllers\API\v1\App\Users\Bookings\TripRequests\AcceptTripRequestBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\TripRequests\CancelMyTripRequestBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\TripRequests\RejectTripRequestBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\TripRequests\ShowTripRequestBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\TripRequests\CancelTripRequestBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\TripRequests\IndexMyTripRequestBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\TripRequests\IndexTripRequestBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\TripRequests\ShowMyTripRequestBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\TripServices\CancelMyTripServiceBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\TripServices\CancelTripServiceBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\TripServices\IndexMyTripServiceBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\TripServices\IndexTripServiceBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\TripServices\ShowMyTripServiceBookingController;
use App\Http\Controllers\API\v1\App\Users\ServiceRequests\Shippings\CancelShippingRequestController;
use App\Http\Controllers\API\v1\App\Users\ServiceRequests\Shippings\UpdateShippingRequestController;
use App\Http\Controllers\API\v1\App\Users\ServiceRequests\Shippings\DelayShippingRequestController;
use App\Http\Controllers\API\v1\App\Users\ServiceRequests\Shippings\ConfirmShippingRequestAttendanceController;
use App\Http\Controllers\API\v1\App\Users\Services\Shippings\CancelShippingServiceController;
use App\Http\Controllers\API\v1\App\Users\Services\Shippings\UpdateShippingServiceController;
use App\Http\Controllers\API\v1\App\Users\Services\Shippings\DelayShippingServiceController;
use App\Http\Controllers\API\v1\App\Users\Services\Shippings\ConfirmShippingServiceAttendanceController;
use App\Http\Controllers\API\v1\App\Users\Bookings\ShippingServices\IndexMyShippingServiceBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\ShippingServices\ShowMyShippingServiceBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\ShippingServices\CancelMyShippingServiceBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\ShippingServices\IndexShippingServiceBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\ShippingServices\ShowShippingServiceBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\ShippingServices\AcceptShippingServiceBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\ShippingServices\RejectShippingServiceBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\ShippingServices\CancelShippingServiceBookingController;
use App\Http\Controllers\API\v1\App\Users\Services\Fazaa\CancelFazaaServiceController;
use App\Http\Controllers\API\v1\App\Users\Services\Fazaa\ConfirmFazaaServiceAttendanceController;
use App\Http\Controllers\API\v1\App\Users\Services\Fazaa\DelayFazaaServiceController;
use App\Http\Controllers\API\v1\App\Users\Services\Fazaa\UpdateFazaaServiceController;
use App\Http\Controllers\API\v1\App\Users\ServiceRequests\Fazaa\UpdateFazaaRequestController;
use App\Http\Controllers\API\v1\App\Users\ServiceRequests\Fazaa\DelayFazaaRequestController;
use App\Http\Controllers\API\v1\App\Users\ServiceRequests\Fazaa\CancelFazaaRequestController;
use App\Http\Controllers\API\v1\App\Users\ServiceRequests\Fazaa\ConfirmFazaaRequestAttendanceController;
use App\Http\Controllers\API\v1\App\Users\Bookings\FazaaRequests\IndexMyFazaaRequestBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\FazaaRequests\ShowMyFazaaRequestBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\FazaaRequests\CancelMyFazaaRequestBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\FazaaRequests\RejectFazaaRequestBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\FazaaRequests\IndexFazaaRequestBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\FazaaRequests\ShowFazaaRequestBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\FazaaRequests\AcceptFazaaRequestBookingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\FazaaRequests\CancelFazaaRequestBookingController;
use App\Http\Controllers\API\v1\App\Users\Services\Trips\ShowMyTripServiceController;
use App\Http\Controllers\API\v1\App\Users\Services\Shippings\ShowMyShippingServiceController;
use App\Http\Controllers\API\v1\App\Users\Services\Fazaa\ShowMyFazaaServiceController;
use App\Http\Controllers\API\v1\App\Users\ServiceRequests\Trips\ShowMyTripRequestController;
use App\Http\Controllers\API\v1\App\Users\ServiceRequests\Shippings\ShowMyShippingRequestController;
use App\Http\Controllers\API\v1\App\Users\ServiceRequests\Fazaa\ShowMyFazaaRequestController;
use App\Http\Controllers\API\v1\App\Users\Bookings\TripServices\ConfirmMyTripServiceBookingAttendanceController;
use App\Http\Controllers\API\v1\App\Users\Bookings\ShippingServices\ConfirmMyShippingServiceBookingAttendanceController;
use App\Http\Controllers\API\v1\App\Users\Bookings\FazaaServices\ConfirmMyFazaaServiceBookingAttendanceController;
use App\Http\Controllers\API\v1\App\Users\Bookings\TripServices\NotifyTripServiceBookingLateArrivalController;
use App\Http\Controllers\API\v1\App\Users\Bookings\ShippingServices\NotifyShippingServiceBookingLateArrivalController;
use App\Http\Controllers\API\v1\App\Users\Bookings\FazaaServices\NotifyFazaaServiceBookingLateArrivalController;
use App\Http\Controllers\API\v1\App\Users\Devices\StoreDeviceController;
use App\Http\Controllers\API\v1\App\Users\Complaints\IndexMyComplaintsController;
use App\Http\Controllers\API\v1\App\Users\Complaints\StoreComplaintController;
use App\Http\Controllers\API\v1\App\Users\Bookings\TripServices\StoreTripServiceBookingRatingController;
use App\Http\Controllers\API\v1\App\Users\Bookings\TripServices\ConfirmMyTripServiceBookingArrivalController;
use App\Http\Controllers\API\v1\App\Users\Transactions\IndexUserTransactionsController;
use App\Http\Controllers\API\v1\App\Users\Services\Trips\ConfirmTripServiceArrivalController;
use App\Http\Controllers\API\v1\App\Users\Bookings\TripServices\MarkTripServiceBookingsAsPaidController;
use App\Http\Controllers\API\v1\App\Users\Bookings\TripServices\SendPaymentRemindersController;
use App\Http\Controllers\API\v1\App\Users\Bookings\TripServices\MarkAllTripServiceBookingsAsPaidController;
use App\Http\Controllers\API\v1\App\Users\Cars\DeleteCarController;
use App\Http\Controllers\API\v1\App\Users\Cars\UpdateCarController;

Route::name('app.')->prefix('app')->whereNumber(['id', 'bookingId'])->group(function () {
    Route::name('users.')->prefix('users')->group(function () {
        Route::name('account.')->prefix('account')->group(function () {
            Route::name('auth.')->prefix('auth')->group(function () {
                Route::post(
                    '/register',
                    RegisterController::class
                )->name('register');

                Route::name('otp.')->prefix('otp')->group(function () {
                    Route::post(
                        '/send',
                        SendOtpController::class
                    )->name('send');

                    Route::post(
                        '/verify',
                        VerifyOtpController::class
                    )->name('verify');
                });

                Route::post(
                    '/firebase',
                    FirebaseAuthenticationController::class
                )->name('firebase');

                Route::middleware('auth:api')->post(
                    '/logout',
                    LogoutController::class
                )->name('logout');
            });

            Route::middleware('auth:api')->name('profile.')->prefix('profile')->group(function () {
                Route::get(
                    '/',
                    ShowProfileController::class
                )->name('show');

                Route::patch(
                    '/',
                    UpdateProfileController::class
                )->name('update');
            });
        });

        Route::middleware('auth:api')->group(function () {
            Route::name('transactions.')->prefix('transactions')->group(function () {
                Route::get(
                    '/',
                    IndexUserTransactionsController::class
                )->name('index');
            });

            Route::name('services.')->prefix('services')->group(function () {
                Route::name('trips.')->prefix('trips')->group(function () {
                    Route::post(
                        '/',
                        StoreTripServiceController::class
                    )->name('store');

                    Route::get(
                        '/{id}',
                        ShowMyTripServiceController::class
                    )->name('show');

                    Route::patch(
                        '/{id}',
                        UpdateTripServiceController::class
                    )->name('update');

                    Route::patch(
                        '/{id}/delay',
                        DelayTripServiceController::class
                    )->name('delay');

                    Route::get(
                        '/',
                        IndexMyTripServiceController::class
                    )->name('index');

                    Route::post(
                        '/{id}/cancel',
                        CancelTripServiceController::class
                    )->name('cancel');

                    Route::post(
                        '/{id}/confirm-attendance',
                        ConfirmTripServiceAttendanceController::class
                    )->name('confirm-attendance');

                    Route::post(
                        '/{id}/confirm-arrival',
                        ConfirmTripServiceArrivalController::class
                    )->name('confirm-arrival');

                    Route::post(
                        '/{id}/mark-as-paid',
                        MarkTripServiceBookingsAsPaidController::class
                    )->name('mark-as-paid');

                    Route::post(
                        '/{id}/mark-all-as-paid',
                        MarkAllTripServiceBookingsAsPaidController::class
                    )->name('mark-all-as-paid');

                    Route::post(
                        '/{id}/send-payment-reminders',
                        SendPaymentRemindersController::class
                    )->name('send-payment-reminders');

                    Route::name('my-bookings.')->prefix('/my-bookings')->group(function () {
                        Route::get(
                            '/',
                            IndexMyTripServiceBookingController::class
                        )->name('index');

                        Route::get(
                            '/{bookingId}',
                            ShowMyTripServiceBookingController::class
                        )->name('show');

                        Route::post(
                            '/{bookingId}/cancel',
                            CancelMyTripServiceBookingController::class
                        )->name('cancel');

                        Route::post(
                            '/{bookingId}/confirm-attendance',
                            ConfirmMyTripServiceBookingAttendanceController::class
                        )->name('confirm-attendance');

                        Route::post(
                            '/{bookingId}/notify-late-arrival',
                            NotifyTripServiceBookingLateArrivalController::class
                        )->name('notify-late-arrival');

                        Route::post(
                            '/{bookingId}/confirm-arrival',
                            ConfirmMyTripServiceBookingArrivalController::class
                        )->name('confirm-arrival');

                        Route::name('rate.')->group(function () {
                            Route::post(
                                '/{bookingId}/rate',
                                StoreTripServiceBookingRatingController::class
                            )->name('store');
                        });
                    });

                    Route::get(
                        '/{id}/bookings',
                        IndexTripServiceBookingController::class
                    )->name('bookings.index');

                    Route::name('bookings.')->prefix('/bookings/{id}')->group(function () {
                        Route::get(
                            '/',
                            IndexTripServiceBookingController::class
                        )->name('index');

                        Route::get(
                            '/',
                            ShowTripServiceBookingController::class
                        )->name('show');

                        Route::post(
                            '/accept',
                            AcceptTripServiceBookingController::class
                        )->name('accept');

                        Route::post(
                            '/reject',
                            RejectTripServiceBookingController::class
                        )->name('reject');

                        Route::post(
                            '/cancel',
                            CancelTripServiceBookingController::class
                        )->name('cancel');
                    });
                });

                Route::name('fazaas.')->prefix('fazaas')->group(function () {
                    Route::post(
                        '/',
                        StoreFazaaServiceController::class
                    )->name('store');

                    Route::get(
                        '/{id}',
                        ShowMyFazaaServiceController::class
                    )->name('show');

                    Route::get(
                        '/',
                        IndexMyFazaaServiceController::class
                    )->name('index');

                    Route::patch(
                        '/{id}',
                        UpdateFazaaServiceController::class
                    )->name('update');

                    Route::patch(
                        '/{id}/delay',
                        DelayFazaaServiceController::class
                    )->name('delay');

                    Route::post(
                        '/{id}/cancel',
                        CancelFazaaServiceController::class
                    )->name('cancel');

                    Route::post(
                        '/{id}/confirm-attendance',
                        ConfirmFazaaServiceAttendanceController::class
                    )->name('confirm-attendance');

                    Route::name('my-bookings.')->prefix('/my-bookings')->group(function () {
                        Route::get(
                            '/',
                            IndexMyFazaaServiceBookingController::class
                        )->name('index');

                        Route::get(
                            '/{bookingId}',
                            ShowMyFazaaServiceBookingController::class
                        )->name('show');

                        Route::post(
                            '/{bookingId}/cancel',
                            CancelMyFazaaServiceBookingController::class
                        )->name('cancel');

                        Route::post(
                            '/{bookingId}/confirm-attendance',
                            ConfirmMyFazaaServiceBookingAttendanceController::class
                        )->name('confirm-attendance');

                        Route::post(
                            '/{bookingId}/notify-late-arrival',
                            NotifyFazaaServiceBookingLateArrivalController::class
                        )->name('notify-late-arrival');
                    });

                    Route::get(
                        '/{id}/bookings',
                        IndexFazaaServiceBookingController::class
                    )->name('bookings.index');

                    Route::name('bookings.')->prefix('/bookings/{id}')->group(function () {
                        Route::get(
                            '/',
                            ShowFazaaServiceBookingController::class
                        )->name('show');

                        Route::post(
                            '/accept',
                            AcceptFazaaServiceBookingController::class
                        )->name('accept');

                        Route::post(
                            '/reject',
                            RejectFazaaServiceBookingController::class
                        )->name('reject');

                        Route::post(
                            '/cancel',
                            CancelFazaaServiceBookingController::class
                        )->name('cancel');
                    });
                });

                Route::name('shippings.')->prefix('shippings')->group(function () {
                    Route::post(
                        '/',
                        StoreShippingServiceController::class
                    )->name('store');

                    Route::get(
                        '/{id}',
                        ShowMyShippingServiceController::class
                    )->name('show');

                    Route::get(
                        '/',
                        IndexMyShippingServiceController::class
                    )->name('index');

                    Route::patch(
                        '/{id}',
                        UpdateShippingServiceController::class
                    )->name('update');

                    Route::patch(
                        '/{id}/delay',
                        DelayShippingServiceController::class
                    )->name('delay');

                    Route::post(
                        '/{id}/cancel',
                        CancelShippingServiceController::class
                    )->name('cancel');

                    Route::post(
                        '/{id}/confirm-attendance',
                        ConfirmShippingServiceAttendanceController::class
                    )->name('confirm-attendance');

                    Route::name('my-bookings.')->prefix('/my-bookings')->group(function () {
                        Route::get(
                            '/',
                            IndexMyShippingServiceBookingController::class
                        )->name('index');

                        Route::get(
                            '/{bookingId}',
                            ShowMyShippingServiceBookingController::class
                        )->name('show');

                        Route::post(
                            '/{bookingId}/cancel',
                            CancelMyShippingServiceBookingController::class
                        )->name('cancel');

                        Route::post(
                            '/{bookingId}/confirm-attendance',
                            ConfirmMyShippingServiceBookingAttendanceController::class
                        )->name('confirm-attendance');

                        Route::post(
                            '/{bookingId}/notify-late-arrival',
                            NotifyShippingServiceBookingLateArrivalController::class
                        )->name('notify-late-arrival');
                    });

                    Route::get(
                        '/{id}/bookings',
                        IndexShippingServiceBookingController::class
                    )->name('bookings.index');

                    Route::name('bookings.')->prefix('/bookings/{id}')->group(function () {
                        Route::get(
                            '/',
                            ShowShippingServiceBookingController::class
                        )->name('show');

                        Route::post(
                            '/accept',
                            AcceptShippingServiceBookingController::class
                        )->name('accept');

                        Route::post(
                            '/reject',
                            RejectShippingServiceBookingController::class
                        )->name('reject');

                        Route::post(
                            '/cancel',
                            CancelShippingServiceBookingController::class
                        )->name('cancel');
                    });
                });
            });

            Route::name('requests.')->prefix('requests')->group(function () {
                Route::name('trips.')->prefix('trips')->group(function () {
                    Route::get(
                        '/',
                        IndexMyTripRequestController::class
                    )->name('index');

                    Route::get(
                        '/{id}',
                        ShowMyTripRequestController::class
                    )->name('show');

                    Route::post(
                        '/',
                        StoreTripRequestController::class
                    )->name('store');

                    Route::post(
                        '/{id}/cancel',
                        CancelTripRequestController::class
                    )->name('cancel');

                    Route::patch(
                        '/{id}',
                        UpdateTripRequestController::class
                    )->name('update');

                    Route::patch(
                        '/{id}/delay',
                        DelayTripRequestController::class
                    )->name('delay');

                    Route::post(
                        '/{id}/confirm-attendance',
                        ConfirmTripRequestAttendanceController::class
                    )->name('confirm-attendance');

                    Route::name('my-offers.')->prefix('/my-offers')->group(function () {
                        Route::get(
                            '/',
                            IndexMyTripRequestBookingController::class
                        )->name('index');

                        Route::get(
                            '/{bookingId}',
                            ShowMyTripRequestBookingController::class
                        )->name('show');

                        Route::post(
                            '/{bookingId}/cancel',
                            CancelMyTripRequestBookingController::class
                        )->name('cancel');
                    });

                    Route::get(
                        '/{id}/offers',
                        IndexTripRequestBookingController::class
                    )->name('offers.index');

                    Route::name('offers.')->prefix('/offers/{id}')->group(function () {
                        Route::get(
                            '/',
                            ShowTripRequestBookingController::class
                        )->name('show');

                        Route::post(
                            '/accept',
                            AcceptTripRequestBookingController::class
                        )->name('accept');

                        Route::post(
                            '/reject',
                            RejectTripRequestBookingController::class
                        )->name('reject');

                        Route::post(
                            '/cancel',
                            CancelTripRequestBookingController::class
                        )->name('cancel');
                    });
                });

                Route::name('shippings.')->prefix('shippings')->group(function () {
                    Route::get(
                        '/',
                        IndexMyShippingRequestController::class
                    )->name('index');

                    Route::get(
                        '/{id}',
                        ShowMyShippingRequestController::class
                    )->name('show');

                    Route::post(
                        '/',
                        StoreShippingRequestController::class
                    )->name('store');

                    Route::post(
                        '/{id}/cancel',
                        CancelShippingRequestController::class
                    )->name('cancel');

                    Route::patch(
                        '/{id}',
                        UpdateShippingRequestController::class
                    )->name('update');

                    Route::patch(
                        '/{id}/delay',
                        DelayShippingRequestController::class
                    )->name('delay');

                    Route::post(
                        '/{id}/confirm-attendance',
                        ConfirmShippingRequestAttendanceController::class
                    )->name('confirm-attendance');

                    Route::name('my-offers.')->prefix('/my-offers')->group(function () {
                        Route::get(
                            '/',
                            IndexMyShippingRequestBookingController::class
                        )->name('index');

                        Route::get(
                            '/{bookingId}',
                            ShowMyShippingRequestBookingController::class
                        )->name('show');

                        Route::post(
                            '/{bookingId}/cancel',
                            CancelMyShippingRequestBookingController::class
                        )->name('cancel');
                    });

                    Route::get(
                        '/{id}/offers',
                        IndexShippingRequestBookingController::class
                    )->name('offers.index');

                    Route::name('offers.')->prefix('/offers/{id}')->group(function () {
                        Route::get(
                            '/',
                            ShowShippingRequestBookingController::class
                        )->name('show');

                        Route::post(
                            '/accept',
                            AcceptShippingRequestBookingController::class
                        )->name('accept');

                        Route::post(
                            '/reject',
                            RejectShippingRequestBookingController::class
                        )->name('reject');

                        Route::post(
                            '/cancel',
                            CancelShippingRequestBookingController::class
                        )->name('cancel');
                    });
                });

                Route::name('fazaas.')->prefix('fazaas')->group(function () {
                    Route::get(
                        '/',
                        IndexMyFazaaRequestController::class
                    )->name('index');

                    Route::get(
                        '/{id}',
                        ShowMyFazaaRequestController::class
                    )->name('show');

                    Route::post(
                        '/',
                        StoreFazaaRequestController::class
                    )->name('store');

                    Route::patch(
                        '/{id}',
                        UpdateFazaaRequestController::class
                    )->name('update');

                    Route::patch(
                        '/{id}/delay',
                        DelayFazaaRequestController::class
                    )->name('delay');

                    Route::post(
                        '/{id}/cancel',
                        CancelFazaaRequestController::class
                    )->name('cancel');

                    Route::post(
                        '/{id}/confirm-attendance',
                        ConfirmFazaaRequestAttendanceController::class
                    )->name('confirm-attendance');

                    Route::name('my-offers.')->prefix('/my-offers')->group(function () {
                        Route::get(
                            '/',
                            IndexMyFazaaRequestBookingController::class
                        )->name('index');

                        Route::get(
                            '/{bookingId}',
                            ShowMyFazaaRequestBookingController::class
                        )->name('show');

                        Route::post(
                            '/{bookingId}/cancel',
                            CancelMyFazaaRequestBookingController::class
                        )->name('cancel');
                    });

                    Route::get(
                        '/{id}/offers',
                        IndexFazaaRequestBookingController::class
                    )->name('offers.index');

                    Route::name('offers.')->prefix('/offers/{id}')->group(function () {
                        Route::get(
                            '/',
                            ShowFazaaRequestBookingController::class
                        )->name('show');

                        Route::post(
                            '/accept',
                            AcceptFazaaRequestBookingController::class
                        )->name('accept');

                        Route::post(
                            '/reject',
                            RejectFazaaRequestBookingController::class
                        )->name('reject');

                        Route::post(
                            '/cancel',
                            CancelFazaaRequestBookingController::class
                        )->name('cancel');
                    });
                });
            });

            Route::name('cars.')->prefix('cars')->group(function () {
                Route::get(
                    '/',
                    IndexMyCarController::class
                )->name('index');

                Route::post(
                    '/',
                    StoreCarController::class
                )->name('store');

                Route::patch(
                    '/{id}',
                    UpdateCarController::class
                )->name('update');

                Route::delete(
                    '/{id}',
                    DeleteCarController::class
                )->name('destroy');
            });

            Route::name('bookmarks.')->prefix('bookmarks')->group(function () {
                Route::get(
                    '/',
                    IndexMyBookmarkController::class
                )->name('index');

                Route::post(
                    '/',
                    StoreBookmarkController::class
                )->name('store');

                Route::delete(
                    '/',
                    DestroyBookmarkController::class
                )->name('destroy');
            });

            Route::name('notifications.')->prefix('notifications')->group(function () {
                Route::get(
                    '/',
                    IndexNotificationController::class
                )->name('index');

                Route::get(
                    '/unread-count',
                    CountUnreadNotificationsController::class
                )->name('unread-count');

                Route::patch(
                    '/{notificationId}/read',
                    MarkNotificationAsReadController::class
                )->name('mark-as-read');

                Route::patch(
                    '/read-all',
                    MarkAllNotificationsAsReadController::class
                )->name('mark-all-as-read');
            });

            Route::name('devices.')->prefix('devices')->group(function () {
                Route::post(
                    '/',
                    StoreDeviceController::class
                )->name('store');
            });

            Route::name('complaints.')->prefix('complaints')->group(function () {
                Route::get(
                    '/',
                    IndexMyComplaintsController::class
                )->name('index');

                Route::post(
                    '/',
                    StoreComplaintController::class
                )->name('store');
            });
        });
    });

    Route::name('services.')->prefix('services')->group(function () {
        Route::name('trips.')->prefix('trips')->group(function () {
            Route::get(
                '/',
                IndexTripServiceController::class
            )->name('index');

            Route::get(
                '/{id}',
                ShowTripServiceController::class
            )->name('show');

            Route::name('book.')->middleware('auth:api')->prefix('{id}/book')->group(function () {
                Route::post(
                    '/',
                    StoreTripServiceBookingController::class
                )->name('store');
            });
        });

        Route::name('shippings.')->prefix('shippings')->group(function () {
            Route::get(
                '/',
                IndexShippingServiceController::class
            )->name('index');

            Route::get(
                '/{id}',
                ShowShippingServiceController::class
            )->name('show');

            Route::name('book.')->middleware('auth:api')->prefix('{id}/book')->group(function () {
                Route::post(
                    '/',
                    StoreShippingServiceBookingController::class
                )->name('store');
            });
        });

        Route::name('fazaas.')->prefix('fazaas')->group(function () {
            Route::get(
                '/',
                IndexFazaaServiceController::class
            )->name('index');

            Route::get(
                '/{id}',
                ShowFazaaServiceController::class
            )->name('show');

            Route::name('book.')->middleware('auth:api')->prefix('{id}/book')->group(function () {
                Route::post(
                    '/',
                    StoreFazaaServiceBookingController::class
                )->name('store');
            });
        });
    });

    Route::name('requests.')->prefix('requests')->group(function () {
        Route::name('trips.')->prefix('trips')->group(function () {
            Route::get(
                '/',
                IndexTripRequestController::class
            )->name('index');

            Route::get(
                '/{id}',
                ShowTripRequestController::class
            )->name('show');

            Route::name('book.')->middleware('auth:api')->prefix('{id}/book')->group(function () {
                Route::post(
                    '/',
                    StoreTripRequestBookingController::class
                )->name('store');
            });
        });

        Route::name('shippings.')->prefix('shippings')->group(function () {
            Route::get(
                '/',
                IndexShippingRequestController::class
            )->name('index');

            Route::get(
                '/{id}',
                ShowShippingRequestController::class
            )->name('show');

            Route::name('book.')->middleware('auth:api')->prefix('{id}/book')->group(function () {
                Route::post(
                    '/',
                    StoreShippingRequestBookingController::class
                )->name('store');
            });
        });

        Route::name('fazaas.')->prefix('fazaas')->group(function () {
            Route::get(
                '/',
                IndexFazaaRequestController::class
            )->name('index');

            Route::get(
                '/{id}',
                ShowFazaaRequestController::class
            )->name('show');

            Route::name('book.')->middleware('auth:api')->prefix('{id}/book')->group(function () {
                Route::post(
                    '/',
                    StoreFazaaRequestBookingController::class
                )->name('store');
            });
        });
    });

    Route::name('common.')->prefix('common')->group(function () {
        Route::name('cities.')->prefix('cities')->group(function () {
            Route::get(
                '/',
                IndexCityController::class
            )->name('index');
        });

        Route::name('additional-services.')->prefix('additional-services')->group(function () {
            Route::get(
                '/',
                IndexAdditionalServiceController::class
            )->name('show');
        });

        Route::name('car-types.')->prefix('car-types')->group(function () {
            Route::get(
                '/',
                IndexCarTypeController::class
            )->name('index');

            Route::name('seats.')->prefix('{id}/seats')->group(function () {
                Route::get(
                    '/',
                    IndexSeatController::class
                )->name('index');
            });
        });

        Route::name('fazaas-specific-types.')->prefix('fazaas-specific-types')->group(function () {
            Route::get(
                '/',
                IndexFazaaSpecificTypeController::class
            )->name('index');
        });

        Route::name('fazaas-service-types.')->prefix('fazaas-service-types')->group(function () {
            Route::get(
                '/',
                IndexFazaaServiceTypeController::class
            )->name('index');
        });

        Route::name('countries.')->prefix('countries')->group(function () {
            Route::get(
                '/',
                IndexCountryController::class
            )->name('index');
        });
    });
});
