<?php

namespace App\Concerns;

use App\Models\Common\City;
use Illuminate\Support\Arr;

trait IntraWithCities
{
    public function getCityId(?string $cityEn, ?string $cityAr): int
    {
        $city = City::query()
            ->where('name_en', $cityEn)
            ->orWhere('name_ar', $cityAr)
            ->first();

        if (!$city) {
            $city = City::create([
                'name_en' => $cityEn,
                'name_ar' => $cityAr,
                'current_weather' => 'Clear',
                // 'current_weather' => Arr::random([
                //     'Clear',
                //     'Clouds',
                //     'Rain',
                //     'Snow',
                //     'Drizzle',
                //     'Foggy',
                //     'Dust',
                //     'Tornado',
                // ]), //todo: remove later to use weather api
            ]);
        }

        if ($city->wasRecentlyCreated) {
            ////todo: run weather api to get city weather

        }

        return $city->id;
    }
}
