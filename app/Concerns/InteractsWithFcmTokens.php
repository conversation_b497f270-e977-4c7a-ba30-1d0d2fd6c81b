<?php

declare(strict_types=1);

namespace App\Concerns;

use App\Models\Device;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Log;

trait InteractsWithFcmTokens
{
    public function devices(): HasMany
    {
        return $this->hasMany(Device::class, 'notifiable_id');
    }

    public function getTokens(): array
    {
        return $this->devices()->pluck('token')->toArray();
    }

    public function preferredLocale(): ?string
    {
        return $this->locale;
    }

    public function routeNotificationForFcm(): array
    {
        return $this->getTokens();
    }
}
