<?php

namespace App\Concerns;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Support\Carbon;

trait HasDateTimeFormatting
{
    protected function createdAt(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => Carbon::parse($value)->format('Y-m-d H:i:s'),
        );
    }

    protected function updatedAt(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => Carbon::parse($value)->format('Y-m-d H:i:s'),
        );
    }

    protected function formattedDepartureTime(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => Carbon::parse($this->departure_time)->translatedFormat('h:i a'),
        );
    }

    protected function formattedArrivalTime(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => Carbon::parse($this->arrival_time)->translatedFormat('h:i a'),
        );
    }

    protected function departureDate(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => Carbon::parse($value)->format('Y/m/d'),
        );
    }

    protected function arrivalDate(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => Carbon::parse($value)->format('Y/m/d'),
        );
    }

    protected function departureDatetime(): Attribute
    {
        return Attribute::make(
            get: fn () => Carbon::parse($this->arrival_date . ' ' . $this->arrival_time)->format('Y-m-d H:i:s'),
        );
    }

    protected function arrivalDatetime(): Attribute
    {
        return Attribute::make(
            get: fn () => Carbon::parse($this->arrival_date . ' ' . $this->arrival_time)->format('Y-m-d H:i:s'),
        );
    }
}
