<?php

namespace App\Filament\Transport\Pages\Auth;

use App\Enums\TransportStatus;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Http\Responses\Auth\Contracts\LoginResponse;
use Filament\Models\Contracts\FilamentUser;
use Filament\Notifications\Notification;
use Filament\Pages\Auth\Login as BaseLogin;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;

class Login extends BaseLogin
{
    public function authenticate(): ?LoginResponse
    {
        try {
            $data = $this->form->getState();

            if (!Auth::guard('transport')->attempt($this->getCredentialsFromFormData($data), $data['remember'] ?? false)) {
                $this->throwFailureValidationException();
            }

            $user = Auth::guard('transport')->user();

            if (!$user instanceof FilamentUser || !$user->canAccessPanel(filament()->getCurrentPanel())) {
                Auth::guard('transport')->logout();
                $this->throwFailureValidationException();
            }

            // Check office status
            if ($user->transportationOffice) {
                $office = $user->transportationOffice;

                if (!$office->canLogin()) {
                    Auth::guard('transport')->logout();

                    $message = match ($office->status) {
                        TransportStatus::INACTIVE->value => __('messages.transport.auth.account_inactive'),
                        TransportStatus::SUSPENDED->value => __('messages.transport.auth.account_suspended'),
                        default => __('messages.transport.auth.account_not_accessible'),
                    };

                    Notification::make()
                        ->title(__('messages.transport.auth.access_denied'))
                        ->body($message)
                        ->danger()
                        ->send();

                    $this->throwFailureValidationException();
                }
            }

            session()->regenerate();

            return app(LoginResponse::class);
        } catch (ValidationException $exception) {
            throw $exception;
        }
    }

    protected function throwFailureValidationException(): never
    {
        throw ValidationException::withMessages([
            'data.email' => __('filament-panels::pages/auth/login.messages.failed'),
        ]);
    }

    protected function getCredentialsFromFormData(array $data): array
    {
        return [
            'email' => $data['email'],
            'password' => $data['password'],
        ];
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                $this->getEmailFormComponent(),
                $this->getPasswordFormComponent(),
                $this->getRememberFormComponent(),
            ])
            ->statePath('data');
    }

    protected function getEmailFormComponent(): Component
    {
        return TextInput::make('email')
            ->label(__('filament-panels::pages/auth/login.form.email.label'))
            ->email()
            ->required()
            ->autocomplete()
            ->autofocus()
            ->extraInputAttributes(['tabindex' => 1]);
    }

    protected function getPasswordFormComponent(): Component
    {
        return TextInput::make('password')
            ->label(__('filament-panels::pages/auth/login.form.password.label'))
            ->password()
            ->required()
            ->extraInputAttributes(['tabindex' => 2]);
    }

    protected function getRememberFormComponent(): Component
    {
        return \Filament\Forms\Components\Checkbox::make('remember')
            ->label(__('filament-panels::pages/auth/login.form.remember.label'));
    }
}
