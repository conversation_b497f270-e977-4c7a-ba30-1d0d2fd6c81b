<?php

namespace App\Filament\Transport\Pages;

use Filament\Pages\Dashboard as BaseDashboard;

class Dashboard extends BaseDashboard
{
    protected static ?string $navigationIcon = 'heroicon-o-home';

    protected static string $view = 'filament.transport.pages.dashboard';

    public function getTitle(): string
    {
        $user = auth('transport')->user();
        $officeName = $user?->transportationOffice?->name ?? __('models.transport.transportation_office');

        return __('messages.transport.welcome_title', ['office_name' => $officeName]);
    }

    public function getSubheading(): string
    {
        $user = auth('transport')->user();

        return __('messages.transport.welcome_subtitle', ['user_name' => $user?->name]);
    }

    protected function getHeaderWidgets(): array
    {
        return [
            // Add custom widgets here if needed
        ];
    }

    protected function getFooterWidgets(): array
    {
        return [
            // Add custom widgets here if needed
        ];
    }
}
