<?php

namespace App\Filament\Transport\Pages;

use App\Enums\TransportStatus;
use App\Models\TransportationOffice;
use App\Models\TransportationOfficeUser;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Auth\Register as BaseRegister;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class Register extends BaseRegister
{
    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(__('sections.transport.transportation_office_information'))
                    ->description(__('sections.transport.office_information_description'))
                    ->schema([
                        Forms\Components\TextInput::make('office_name')
                            ->label(__('fields.transport.name'))
                            ->required()
                            ->maxLength(255)
                            ->placeholder(__('fields.transport.office_name_placeholder')),
                        Forms\Components\TextInput::make('office_email')
                            ->label(__('fields.transport.email'))
                            ->email()
                            ->required()
                            ->maxLength(255)
                            ->unique(TransportationOffice::class, 'email')
                            ->placeholder(__('fields.transport.office_email_placeholder')),
                        Forms\Components\TextInput::make('office_phone')
                            ->label(__('fields.transport.phone'))
                            ->tel()
                            ->maxLength(255)
                            ->placeholder(__('fields.transport.office_phone_placeholder')),
                        Forms\Components\Textarea::make('office_address')
                            ->label(__('fields.transport.address'))
                            ->rows(3)
                            ->columnSpanFull()
                            ->placeholder(__('fields.transport.office_address_placeholder')),
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('office_city')
                                    ->label(__('fields.transport.city'))
                                    ->maxLength(255)
                                    ->placeholder(__('fields.transport.city_placeholder')),
                                Forms\Components\TextInput::make('office_country')
                                    ->label(__('fields.transport.country'))
                                    ->maxLength(255)
                                    ->placeholder(__('fields.transport.country_placeholder')),
                            ]),
                        Forms\Components\TextInput::make('license_number')
                            ->label(__('fields.transport.license_number'))
                            ->maxLength(255)
                            ->placeholder(__('fields.transport.license_number_placeholder')),
                        Forms\Components\Textarea::make('office_description')
                            ->label(__('fields.transport.description'))
                            ->rows(3)
                            ->columnSpanFull()
                            ->placeholder(__('fields.transport.description_placeholder')),
                    ])
                    ->columns(2),

                Forms\Components\Section::make(__('sections.transport.admin_user_information'))
                    ->description(__('sections.transport.admin_account_description'))
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label(__('fields.transport.name'))
                            ->required()
                            ->maxLength(255)
                            ->placeholder(__('fields.transport.full_name_placeholder')),
                        Forms\Components\TextInput::make('email')
                            ->label(__('fields.transport.email'))
                            ->email()
                            ->required()
                            ->maxLength(255)
                            ->unique(TransportationOfficeUser::class, 'email')
                            ->placeholder(__('fields.transport.email_placeholder')),
                        Forms\Components\TextInput::make('password')
                            ->label(__('fields.transport.password'))
                            ->password()
                            ->required()
                            ->minLength(8)
                            ->same('passwordConfirmation')
                            ->placeholder(__('fields.transport.password_placeholder'))
                            ->helperText(__('messages.transport.password_requirements')),
                        Forms\Components\TextInput::make('passwordConfirmation')
                            ->label(__('fields.transport.password_confirmation'))
                            ->password()
                            ->required()
                            ->dehydrated(false)
                            ->placeholder(__('fields.transport.password_confirmation_placeholder')),
                        Forms\Components\TextInput::make('phone')
                            ->label(__('fields.transport.phone'))
                            ->tel()
                            ->maxLength(255)
                            ->placeholder(__('fields.transport.phone_placeholder')),
                    ])
                    ->columns(2),

                Forms\Components\Section::make(__('sections.transport.documents'))
                    ->description(__('sections.transport.documents_description'))
                    ->schema([
                        Forms\Components\FileUpload::make('office_logo')
                            ->label(__('fields.transport.office_logo'))
                            ->image()
                            ->imageEditor()
                            ->imageEditorAspectRatios([
                                '16:9',
                                '4:3',
                                '1:1',
                            ])
                            ->directory('transport-offices/logos')
                            ->visibility('public')
                            ->maxSize(2048)
                            ->helperText(__('messages.transport.logo_requirements')),
                        Forms\Components\FileUpload::make('license_document')
                            ->label(__('fields.transport.license_document'))
                            ->acceptedFileTypes(['application/pdf', 'image/*'])
                            ->directory('transport-offices/documents')
                            ->visibility('private')
                            ->maxSize(5120)
                            ->helperText(__('messages.transport.license_document_requirements')),
                        Forms\Components\FileUpload::make('registration_documents')
                            ->label(__('fields.transport.registration_documents'))
                            ->acceptedFileTypes(['application/pdf', 'image/*'])
                            ->directory('transport-offices/documents')
                            ->visibility('private')
                            ->maxSize(5120)
                            ->helperText(__('messages.transport.registration_documents_requirements')),
                    ])
                    ->columns(2),
            ]);
    }

    protected function handleRegistration(array $data): Model
    {
        return DB::transaction(function () use ($data) {
            // Create the transportation office first
            $office = TransportationOffice::create([
                'name' => $data['office_name'],
                'email' => $data['office_email'],
                'phone' => $data['office_phone'] ?? null,
                'address' => $data['office_address'] ?? null,
                'city' => $data['office_city'] ?? null,
                'country' => $data['office_country'] ?? null,
                'license_number' => $data['license_number'] ?? null,
                'status' => TransportStatus::INACTIVE->value,
                'description' => $data['office_description'] ?? null,
                'office_logo' => $data['office_logo'] ?? null,
                'license_document' => $data['license_document'] ?? null,
                'registration_documents' => $data['registration_documents'] ?? null,
            ]);

            // Create the admin user for the office
            $user = TransportationOfficeUser::create([
                'transportation_office_id' => $office->id,
                'name' => $data['name'],
                'email' => $data['email'],
                'password' => Hash::make($data['password']),
                'phone' => $data['phone'] ?? null,
                'role' => 'admin',
                'status' => 'active',
            ]);

            // Send success notification
            Notification::make()
                ->title(__('messages.transport.registration_success'))
                ->body(__('messages.transport.registration_success_body'))
                ->success()
                ->send();

            return $user;
        });
    }
}
