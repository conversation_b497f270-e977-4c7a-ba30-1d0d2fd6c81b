<?php

namespace App\Filament\Transport\Pages;

use App\Models\TransportationOffice;
use Filament\Actions;
use Filament\Forms;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Support\Facades\Auth;

class ManageOfficeSettings extends Page implements HasForms
{
    use InteractsWithForms;
    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    protected static string $view = 'filament.transport.pages.manage-office-settings';

    protected static ?int $navigationSort = 99;

    public ?array $data = [];

    public static function getNavigationLabel(): string
    {
        return __('navigation.transport.office_settings');
    }

    public function getTitle(): string
    {
        return __('navigation.transport.office_settings');
    }

    public function getSubheading(): string
    {
        return __('sections.transport.documents_description');
    }

    public function mount(): void
    {
        $office = Auth::guard('transport')->user()?->transportationOffice;

        if ($office) {
            $this->form->fill([
                'name' => (string) ($office->name ?? ''),
                'email' => (string) ($office->email ?? ''),
                'phone' => (string) ($office->phone ?? ''),
                'address' => (string) ($office->address ?? ''),
                'city' => (string) ($office->city ?? ''),
                'country' => (string) ($office->country ?? ''),
                'license_number' => (string) ($office->license_number ?? ''),
                'description' => (string) ($office->description ?? ''),
                'status' => (string) ($office->status ?? 'active'),
                'office_logo' => $office->office_logo,
                'license_document' => $office->license_document,
                'registration_documents' => $office->registration_documents,
            ]);
        }
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(__('sections.transport.basic_information'))
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label(__('fields.transport.name'))
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('email')
                            ->label(__('fields.transport.email'))
                            ->email()
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('phone')
                            ->label(__('fields.transport.phone'))
                            ->tel()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('status')
                            ->label(__('fields.transport.status'))
                            ->disabled()
                            ->dehydrated(false)
                            ->formatStateUsing(function ($state) {
                                return match ($state) {
                                    'active' => __('fields.transport.active'),
                                    'inactive' => __('fields.transport.inactive'),
                                    'suspended' => __('fields.transport.suspended'),
                                    default => $state
                                };
                            })
                            ->helperText(__('messages.transport.status_readonly')),
                    ])
                    ->columns(2),

                Forms\Components\Section::make(__('sections.transport.location_information'))
                    ->schema([
                        Forms\Components\Textarea::make('address')
                            ->label(__('fields.transport.address'))
                            ->rows(3)
                            ->columnSpanFull(),
                        Forms\Components\TextInput::make('city')
                            ->label(__('fields.transport.city'))
                            ->maxLength(255),
                        Forms\Components\TextInput::make('country')
                            ->label(__('fields.transport.country'))
                            ->maxLength(255),
                    ])
                    ->columns(2),

                Forms\Components\Section::make(__('sections.transport.additional_information'))
                    ->schema([
                        Forms\Components\TextInput::make('license_number')
                            ->label(__('fields.transport.license_number'))
                            ->maxLength(255),
                        Forms\Components\Textarea::make('description')
                            ->label(__('fields.transport.description'))
                            ->rows(3)
                            ->columnSpanFull(),
                    ]),

                Forms\Components\Section::make(__('sections.transport.documents'))
                    ->schema([
                        Forms\Components\FileUpload::make('office_logo')
                            ->label(__('fields.transport.office_logo'))
                            ->image()
                            ->imageEditor()
                            ->imageEditorAspectRatios([
                                '16:9',
                                '4:3',
                                '1:1',
                            ])
                            ->directory('transport-offices/logos')
                            ->visibility('public')
                            ->maxSize(2048)
                            ->helperText(__('messages.transport.logo_requirements')),
                        Forms\Components\FileUpload::make('license_document')
                            ->label(__('fields.transport.license_document'))
                            ->acceptedFileTypes(['application/pdf', 'image/*'])
                            ->directory('transport-offices/documents')
                            ->visibility('private')
                            ->maxSize(5120)
                            ->helperText(__('messages.transport.license_document_requirements')),
                        Forms\Components\FileUpload::make('registration_documents')
                            ->label(__('fields.transport.registration_documents'))
                            ->acceptedFileTypes(['application/pdf', 'image/*'])
                            ->directory('transport-offices/documents')
                            ->visibility('private')
                            ->maxSize(5120)
                            ->helperText(__('messages.transport.registration_documents_requirements')),
                    ])
                    ->columns(2),
            ])
            ->statePath('data');
    }

    public function save(): void
    {
        $data = $this->form->getState();
        $office = Auth::guard('transport')->user()?->transportationOffice;

        if ($office) {


            $office->update($data);

            Notification::make()
                ->title(__('messages.transport.settings_saved_title'))
                ->body(__('messages.transport.settings_saved_body'))
                ->success()
                ->send();
        }
    }



    public static function canAccess(): bool
    {
        $user = Auth::guard('transport')->user();
        return $user && in_array($user->role, ['admin', 'manager']);
    }
}
