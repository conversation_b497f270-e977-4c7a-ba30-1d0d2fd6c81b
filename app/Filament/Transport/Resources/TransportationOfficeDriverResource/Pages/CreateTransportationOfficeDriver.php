<?php

namespace App\Filament\Transport\Resources\TransportationOfficeDriverResource\Pages;

use App\Enums\DriverType;
use App\Filament\Transport\Resources\TransportationOfficeDriverResource;
use App\Models\TransportationOfficeDriver;
use App\Models\User;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Auth;

class CreateTransportationOfficeDriver extends CreateRecord
{
    protected static string $resource = TransportationOfficeDriverResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['transportation_office_id'] = Auth::guard('transport')->user()?->transportation_office_id;

        // Check for duplicates before creating
        if (isset($data['user_id'])) {
            $existingDriver = TransportationOfficeDriver::where('transportation_office_id', $data['transportation_office_id'])
                ->where('user_id', $data['user_id'])
                ->first();

            if ($existingDriver) {
                Notification::make()
                    ->title('Driver Already Exists')
                    ->body('This user is already assigned as a driver to your office.')
                    ->danger()
                    ->send();

                $this->halt();
            }
        }

        // If creating an employee, create a new user first
        if ($data['type'] === DriverType::EMPLOYEE->value) {
            $user = User::create([
                'name' => $data['name'],
                'email' => $data['email'],
                'mobile' => $data['mobile'],
                'country_id' => $data['country_id'],
                'city_id' => $data['city_id'],
                'email_verified_at' => now(), // Mark email as verified for employees
            ]);

            $data['user_id'] = $user->id;

            // Remove user fields from driver data
            unset($data['name'], $data['email'], $data['mobile'], $data['country_id'], $data['city_id']);
        }

        return $data;
    }
}
