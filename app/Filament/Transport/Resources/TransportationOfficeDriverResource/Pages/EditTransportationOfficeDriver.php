<?php

namespace App\Filament\Transport\Resources\TransportationOfficeDriverResource\Pages;

use App\Filament\Transport\Resources\TransportationOfficeDriverResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditTransportationOfficeDriver extends EditRecord
{
    protected static string $resource = TransportationOfficeDriverResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
