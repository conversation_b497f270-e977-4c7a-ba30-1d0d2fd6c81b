<?php

namespace App\Filament\Transport\Resources\TransportationOfficeResource\Pages;

use App\Filament\Transport\Resources\TransportationOfficeResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListTransportationOffices extends ListRecords
{
    protected static string $resource = TransportationOfficeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
