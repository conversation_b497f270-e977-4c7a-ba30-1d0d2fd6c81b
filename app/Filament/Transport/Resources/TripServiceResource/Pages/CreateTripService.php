<?php

namespace App\Filament\Transport\Resources\TripServiceResource\Pages;

use App\Enums\Travel\SeatStatusEnum;
use App\Filament\Transport\Resources\TripServiceResource;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Auth;

class CreateTripService extends CreateRecord
{
    protected static string $resource = TripServiceResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Set transportation type to car by default
        $data['transportation_type'] = 'car';

        // Set the transportation office ID
        $data['transportation_office_id'] = Auth::guard('transport')->user()?->transportation_office_id;

        // Calculate seat counts
        $availableSeats = $data['available_seats'] ?? [];
        $data['number_of_seats'] = count($availableSeats) + 2; // +2 for A1, A2
        $data['number_of_available_seats'] = count($availableSeats);

        // Remove the available_seats from data as it's not a direct field
        unset($data['available_seats']);

        return $data;
    }

    protected function afterCreate(): void
    {
        $record = $this->record;
        $formData = $this->form->getState();

        // Always create A1 and A2 as unavailable (driver and front passenger)
        $record->seats()->create([
            'number' => 'A1',
            'status' => SeatStatusEnum::UNAVAILABLE->value,
        ]);

        $record->seats()->create([
            'number' => 'A2',
            'status' => SeatStatusEnum::UNAVAILABLE->value,
        ]);

        // Create selected available seats
        if (isset($formData['available_seats'])) {
            foreach ($formData['available_seats'] as $seatNumber) {
                $record->seats()->create([
                    'number' => $seatNumber,
                    'status' => SeatStatusEnum::AVAILABLE->value,
                ]);
            }
        }
    }
}
