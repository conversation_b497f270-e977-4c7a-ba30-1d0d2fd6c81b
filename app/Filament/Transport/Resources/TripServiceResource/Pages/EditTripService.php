<?php

namespace App\Filament\Transport\Resources\TripServiceResource\Pages;

use App\Enums\Travel\SeatStatusEnum;
use App\Filament\Transport\Resources\TripServiceResource;
use App\Models\Common\TripSeat;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditTripService extends EditRecord
{
    protected static string $resource = TripServiceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Load available seats for editing
        $availableSeats = $this->record->seats()
            ->where('status', SeatStatusEnum::AVAILABLE->value)
            ->pluck('number')
            ->toArray();

        $data['available_seats'] = $availableSeats;

        return $data;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Calculate seat counts
        $availableSeats = $data['available_seats'] ?? [];
        $data['number_of_seats'] = count($availableSeats) + 2; // +2 for A1, A2
        $data['number_of_available_seats'] = count($availableSeats);

        // Remove the available_seats from data as it's not a direct field
        unset($data['available_seats']);

        return $data;
    }

    protected function afterSave(): void
    {
        $record = $this->record;
        $formData = $this->form->getState();

        // Delete existing seats and recreate them
        $record->seats()->delete();

        // Create seats
        $seats = [];

        // Always add A1 and A2 as unavailable (driver and front passenger)
        $seats[] = [
            'trip_id' => $record->id,
            'number' => 'A1',
            'status' => SeatStatusEnum::UNAVAILABLE->value,
        ];

        $seats[] = [
            'trip_id' => $record->id,
            'number' => 'A2',
            'status' => SeatStatusEnum::UNAVAILABLE->value,
        ];

        // Add selected available seats
        if (isset($formData['available_seats'])) {
            foreach ($formData['available_seats'] as $seatNumber) {
                $seats[] = [
                    'trip_id' => $record->id,
                    'number' => $seatNumber,
                    'status' => SeatStatusEnum::AVAILABLE->value,
                ];
            }
        }

        TripSeat::insert($seats);
    }
}
