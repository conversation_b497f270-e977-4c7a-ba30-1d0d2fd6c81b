<?php

namespace App\Filament\Transport\Resources;

use App\Enums\DriverType;
use App\Filament\Transport\Resources\TransportationOfficeDriverResource\Pages;
use App\Models\TransportationOfficeDriver;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class TransportationOfficeDriverResource extends Resource
{
    protected static ?string $model = TransportationOfficeDriver::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationGroup = null;

    public static function getNavigationGroup(): ?string
    {
        return __('navigation.transport.driver_management');
    }

    protected static ?int $navigationSort = 1;

    public static function getNavigationLabel(): string
    {
        return __('navigation.transport.drivers');
    }

    public static function getModelLabel(): string
    {
        return __('models.transport.driver');
    }

    public static function getPluralModelLabel(): string
    {
        return __('models.transport.drivers');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(__('sections.transport.driver_information'))
                    ->schema([
                        Forms\Components\Select::make('type')
                            ->label(__('fields.transport.driver_type'))
                            ->options([
                                DriverType::COLLABORATOR->value => __('fields.transport.collaborator'),
                                DriverType::EMPLOYEE->value => __('fields.transport.employee'),
                            ])
                            ->required()
                            ->live()
                            ->afterStateUpdated(function (Forms\Set $set) {
                                $set('user_id', null);
                            }),

                        // Collaborator selection (existing users)
                        Forms\Components\Select::make('user_id')
                            ->label(__('fields.transport.select_driver'))
                            ->options(function () {
                                return User::whereNotNull('mobile')
                                    ->get()
                                    ->mapWithKeys(function ($user) {
                                        return [$user->id => $user->name . ' (' . $user->email . ')'];
                                    });
                            })
                            ->searchable()
                            ->preload()
                            ->visible(fn(Forms\Get $get): bool => $get('type') === DriverType::COLLABORATOR->value)
                            ->required(fn(Forms\Get $get): bool => $get('type') === DriverType::COLLABORATOR->value)
                            ->helperText('Select an existing user to add as a collaborator driver')
                            ->getSearchResultsUsing(function (string $search) {
                                return User::whereNotNull('mobile')
                                    ->where(function ($query) use ($search) {
                                        $query->where('name', 'ilike', "%{$search}%")
                                            ->orWhere('email', 'ilike', "%{$search}%")
                                            ->orWhere('mobile', 'ilike', "%{$search}%");
                                    })
                                    ->limit(50)
                                    ->get()
                                    ->mapWithKeys(function ($user) {
                                        return [$user->id => $user->name . ' (' . $user->email . ')'];
                                    });
                            })
                            ->getOptionLabelUsing(function ($value) {
                                $user = User::find($value);
                                return $user ? $user->name . ' (' . $user->email . ')' : '';
                            }),

                        // Employee fields (create new user)
                        Forms\Components\TextInput::make('name')
                            ->label(__('fields.transport.name'))
                            ->visible(fn(Forms\Get $get): bool => $get('type') === DriverType::EMPLOYEE->value)
                            ->required(fn(Forms\Get $get): bool => $get('type') === DriverType::EMPLOYEE->value)
                            ->maxLength(255),

                        Forms\Components\TextInput::make('email')
                            ->label(__('fields.transport.email'))
                            ->email()
                            ->visible(fn(Forms\Get $get): bool => $get('type') === DriverType::EMPLOYEE->value)
                            ->required(fn(Forms\Get $get): bool => $get('type') === DriverType::EMPLOYEE->value)
                            ->maxLength(255),

                        Forms\Components\TextInput::make('mobile')
                            ->label(__('fields.transport.mobile'))
                            ->tel()
                            ->visible(fn(Forms\Get $get): bool => $get('type') === DriverType::EMPLOYEE->value)
                            ->required(fn(Forms\Get $get): bool => $get('type') === DriverType::EMPLOYEE->value)
                            ->maxLength(255),

                        Forms\Components\Select::make('country_id')
                            ->label(__('fields.transport.country'))
                            ->options(function () {
                                return \App\Models\Common\Country::all()->pluck('name', 'id');
                            })
                            ->visible(fn(Forms\Get $get): bool => $get('type') === DriverType::EMPLOYEE->value)
                            ->required(fn(Forms\Get $get): bool => $get('type') === DriverType::EMPLOYEE->value)
                            ->searchable()
                            ->preload(),

                        Forms\Components\Select::make('city_id')
                            ->label(__('fields.transport.city'))
                            ->options(function () {
                                return \App\Models\Common\City::all()->pluck('name', 'id');
                            })
                            ->visible(fn(Forms\Get $get): bool => $get('type') === DriverType::EMPLOYEE->value)
                            ->required(fn(Forms\Get $get): bool => $get('type') === DriverType::EMPLOYEE->value)
                            ->searchable()
                            ->preload(),

                        Forms\Components\Select::make('status')
                            ->label(__('fields.transport.status'))
                            ->options([
                                'active' => __('fields.transport.active'),
                                'inactive' => __('fields.transport.inactive'),
                                'suspended' => __('fields.transport.suspended'),
                            ])
                            ->default('active')
                            ->required(),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('display_name')
                    ->label(__('fields.transport.name'))
                    ->searchable(['name', 'user.name'])
                    ->sortable(),

                Tables\Columns\TextColumn::make('display_email')
                    ->label(__('fields.transport.email'))
                    ->searchable(['email', 'user.email']),

                Tables\Columns\TextColumn::make('display_phone')
                    ->label(__('fields.transport.phone'))
                    ->searchable(['phone', 'user.mobile']),

                Tables\Columns\TextColumn::make('type')
                    ->label(__('fields.transport.driver_type'))
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        DriverType::COLLABORATOR->value => 'info',
                        DriverType::EMPLOYEE->value => 'success',
                        default => 'gray'
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        DriverType::COLLABORATOR->value => __('fields.transport.collaborator'),
                        DriverType::EMPLOYEE->value => __('fields.transport.employee'),
                        default => $state
                    }),



                Tables\Columns\TextColumn::make('status')
                    ->label(__('fields.transport.status'))
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'active' => 'success',
                        'inactive' => 'warning',
                        'suspended' => 'danger',
                        default => 'gray'
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'active' => __('fields.transport.active'),
                        'inactive' => __('fields.transport.inactive'),
                        'suspended' => __('fields.transport.suspended'),
                        default => $state
                    }),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('fields.transport.created_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->label(__('fields.transport.driver_type'))
                    ->options([
                        DriverType::COLLABORATOR->value => __('fields.transport.collaborator'),
                        DriverType::EMPLOYEE->value => __('fields.transport.employee'),
                    ]),

                Tables\Filters\SelectFilter::make('status')
                    ->label(__('fields.transport.status'))
                    ->options([
                        'active' => __('fields.transport.active'),
                        'inactive' => __('fields.transport.inactive'),
                        'suspended' => __('fields.transport.suspended'),
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('transportation_office_id', Auth::guard('transport')->user()?->transportation_office_id)
            ->with(['user', 'transportationOffice']);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTransportationOfficeDrivers::route('/'),
            'create' => Pages\CreateTransportationOfficeDriver::route('/create'),
            'view' => Pages\ViewTransportationOfficeDriver::route('/{record}'),
            'edit' => Pages\EditTransportationOfficeDriver::route('/{record}/edit'),
        ];
    }
}
