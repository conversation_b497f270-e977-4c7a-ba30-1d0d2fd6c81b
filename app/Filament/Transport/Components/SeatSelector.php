<?php

namespace App\Filament\Transport\Components;

use Filament\Forms\Components\Field;

class SeatSelector extends Field
{
    protected string $view = 'filament.transport.components.seat-selector';

    protected array $reservedSeats = ['A1', 'A2'];

    public function getReservedSeats(): array
    {
        return $this->reservedSeats;
    }

    public function reservedSeats(array $seats): static
    {
        $this->reservedSeats = $seats;
        return $this;
    }

    public function getSeatLayout(): array
    {
        // For now, return default layout - can be enhanced later to be dynamic
        return $this->getDefaultSeatLayout();
    }

    protected function getDefaultSeatLayout(): array
    {
        // Try to get car information from form state
        try {
            $livewire = $this->getLivewire();
            $carId = data_get($livewire->data, 'car_id');

            if ($carId) {
                $car = \App\Models\Common\Car::with('type')->find($carId);
                if ($car && $car->type) {
                    return $this->generateSeatLayoutFromCarType($car->type->number_of_seats);
                }
            }
        } catch (\Exception) {
            // Fallback if form state is not available
        }

        // Fallback to default 9-seat layout if no car selected
        return $this->generateSeatLayoutFromCarType(9);
    }

    protected function generateSeatLayoutFromCarType(int $totalSeats): array
    {
        $layout = [];
        $seatNumber = 1;
        $rows = ['A', 'B', 'C', 'D', 'E', 'F']; // Support up to 6 rows
        $seatsPerRow = 3; // Standard 3 seats per row

        $rowIndex = 0;
        while ($seatNumber <= $totalSeats && $rowIndex < count($rows)) {
            $rowLetter = $rows[$rowIndex];
            $layout[$rowLetter] = [];

            for ($col = 1; $col <= $seatsPerRow && $seatNumber <= $totalSeats; $col++) {
                $layout[$rowLetter][] = $rowLetter . $col;
                $seatNumber++;
            }

            $rowIndex++;
        }

        return $layout;
    }

    public function getViewData(): array
    {
        $data = parent::getViewData();

        // Force refresh of seat layout when car changes
        $data['seatLayout'] = $this->getDefaultSeatLayout();

        return $data;
    }

    protected function setUp(): void
    {
        parent::setUp();

        // Ensure the field has a proper name attribute
        $this->name = $this->name ?? 'available_seats';
    }
}
