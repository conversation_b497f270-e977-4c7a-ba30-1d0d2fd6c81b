<?php

namespace App\Filament\Admin\Resources\ShippingTrackingResource\Pages;

use App\Filament\Admin\Resources\ShippingTrackingResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditShippingTracking extends EditRecord
{
    protected static string $resource = ShippingTrackingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
