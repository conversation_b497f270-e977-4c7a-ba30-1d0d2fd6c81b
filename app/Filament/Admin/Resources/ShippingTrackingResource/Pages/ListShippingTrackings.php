<?php

namespace App\Filament\Admin\Resources\ShippingTrackingResource\Pages;

use App\Filament\Admin\Resources\ShippingTrackingResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;

class ListShippingTrackings extends ListRecords
{
    protected static string $resource = ShippingTrackingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // No create action needed for tracking
        ];
    }

    public function getTabs(): array
    {
        return [
            'all' => Tab::make(__('الكل')),
            'active' => Tab::make(__('مستمرة'))
                ->modifyQueryUsing(fn(Builder $query) => $query->where('departure_datetime', '>', now())->whereNull('cancelled_at')),
            'completed' => Tab::make(__('مكتملة'))
                ->modifyQueryUsing(fn(Builder $query) => $query->where('departure_datetime', '<', now())->whereNull('cancelled_at')),
            'cancelled' => Tab::make(__('ملغية'))
                ->modifyQueryUsing(fn(Builder $query) => $query->whereNotNull('cancelled_at')),
        ];
    }
}
