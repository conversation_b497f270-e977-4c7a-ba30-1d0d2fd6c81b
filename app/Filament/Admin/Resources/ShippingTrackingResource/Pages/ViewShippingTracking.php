<?php

namespace App\Filament\Admin\Resources\ShippingTrackingResource\Pages;

use App\Filament\Admin\Resources\ShippingTrackingResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewShippingTracking extends ViewRecord
{
    protected static string $resource = ShippingTrackingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
