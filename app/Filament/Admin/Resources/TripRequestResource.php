<?php

namespace App\Filament\Admin\Resources;

use App\Enums\Travel\TripStatusEnum;
use App\Filament\Admin\Resources\TripRequestResource\Pages;
use App\Models\Request\TripRequest;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use App\Traits\FilamentHasReadTrackingResource;

class TripRequestResource extends Resource
{
    use FilamentHasReadTrackingResource;

    protected static ?string $model = TripRequest::class;

    protected static ?string $navigationIcon = 'heroicon-o-map';

    protected static ?int $navigationSort = 10;

    public static function getNavigationLabel(): string
    {
        return __('navigation.trip_requests');
    }

    public static function getModelLabel(): string
    {
        return __('models.trip_request');
    }

    public static function getPluralModelLabel(): string
    {
        return __('models.trip_requests');
    }

    public static function getNavigationGroup(): string
    {
        return __('navigation.requests_management');
    }

    public static function shouldRegisterNavigation(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('view requests');
    }

    public static function canViewAny(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('view requests');
    }

    public static function canView($record): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('view requests');
    }

    public static function canCreate(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage requests');
    }

    public static function canEdit($record): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage requests');
    }

    public static function canDelete($record): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage requests');
    }

    public static function canDeleteAny(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage requests');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(__('sections.trip_request_details'))
                    ->schema([
                        Forms\Components\Select::make('user_id')
                            ->relationship('user', 'name')
                            ->searchable()
                            ->preload()
                            ->label(__('fields.user'))
                            ->required(),
                        Forms\Components\Select::make('trip_type')
                            ->options([
                                'families' => __('enums.trip_type.families'),
                                'singles' => __('enums.trip_type.singles'),
                            ])
                            ->label(__('fields.trip_type'))
                            ->required(),
                        Forms\Components\DateTimePicker::make('departure_datetime')
                            ->label(__('fields.departure_datetime'))
                            ->required(),
                        Forms\Components\DateTimePicker::make('arrival_datetime')
                            ->label(__('fields.arrival_datetime'))
                            ->required(),
                        Forms\Components\TextInput::make('number_of_seats')
                            ->label(__('fields.number_of_seats'))
                            ->numeric()
                            ->required(),
                        Forms\Components\TextInput::make('price')
                            ->label(__('fields.price'))
                            ->numeric()
                            ->required(),
                    ]),
                Forms\Components\Section::make(__('sections.location_details'))
                    ->schema([
                        Forms\Components\Select::make('from_city_id')
                            ->relationship('fromCity', 'name_' . app()->getLocale())
                            ->searchable()
                            ->preload()
                            ->label(__('fields.from_city'))
                            ->required(),
                        Forms\Components\Select::make('to_city_id')
                            ->relationship('toCity', 'name_' . app()->getLocale())
                            ->searchable()
                            ->preload()
                            ->label(__('fields.to_city'))
                            ->required(),
                        Forms\Components\TextInput::make('from_location_lat')
                            ->label(__('fields.from_latitude'))
                            ->numeric(),
                        Forms\Components\TextInput::make('from_location_lng')
                            ->label(__('fields.from_longitude'))
                            ->numeric(),
                        Forms\Components\TextInput::make('to_location_lat')
                            ->label(__('fields.to_latitude'))
                            ->numeric(),
                        Forms\Components\TextInput::make('to_location_lng')
                            ->label(__('fields.to_longitude'))
                            ->numeric(),
                    ]),
                Forms\Components\Section::make(__('sections.additional_details'))
                    ->schema([
                        Forms\Components\Select::make('car_type_id')
                            ->relationship('carType', 'name_' . app()->getLocale())
                            ->searchable()
                            ->preload()
                            ->label(__('fields.car_type')),
                        Forms\Components\Toggle::make('allow_smoking')
                            ->label(__('fields.allow_smoking'))
                            ->default(false),
                        Forms\Components\Toggle::make('deliver_to_door')
                            ->label(__('fields.deliver_to_door'))
                            ->default(false),
                        Forms\Components\Toggle::make('arrive_destination')
                            ->label(__('fields.arrive_destination'))
                            ->default(false),
                        Forms\Components\Textarea::make('note')
                            ->label(__('fields.note'))
                            ->columnSpanFull(),
                    ]),
                Forms\Components\Section::make(__('sections.status_information'))
                    ->schema([
                        Forms\Components\DateTimePicker::make('cancelled_at')
                            ->label(__('fields.cancelled_at')),
                        Forms\Components\TextInput::make('cancellation_reason')
                            ->label(__('fields.cancellation_reason')),
                        Forms\Components\Textarea::make('cancellation_note')
                            ->label(__('fields.cancellation_note')),
                        Forms\Components\DateTimePicker::make('attendance_confirmed_at')
                            ->label(__('fields.attendance_confirmed_at')),
                        Forms\Components\DateTimePicker::make('delayed_at')
                            ->label(__('fields.delayed_at')),
                        Forms\Components\TextInput::make('delay_reason')
                            ->label(__('fields.delay_reason')),
                        Forms\Components\Textarea::make('delay_note')
                            ->label(__('fields.delay_note')),
                        ...self::getReadTrackingFormFields(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->searchable()
                    ->label(__('fields.id')),
                Tables\Columns\TextColumn::make('user.name')
                    ->searchable()
                    ->sortable()
                    ->label(__('fields.user')),
                Tables\Columns\TextColumn::make('trip_type')
                    ->formatStateUsing(fn(string $state): string => __("enums.trip_type.{$state}"))
                    ->searchable()
                    ->label(__('fields.trip_type')),
                Tables\Columns\TextColumn::make('fromCity.name_' . app()->getLocale())
                    ->label(__('fields.from_city')),
                Tables\Columns\TextColumn::make('toCity.name_' . app()->getLocale())
                    ->label(__('fields.to_city')),
                Tables\Columns\TextColumn::make('departure_datetime')
                    ->dateTime()
                    ->sortable()
                    ->label(__('fields.departure_datetime')),
                Tables\Columns\TextColumn::make('number_of_seats')
                    ->label(__('fields.number_of_seats')),
                Tables\Columns\TextColumn::make('price')
                    ->label(__('fields.price')),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->formatStateUsing(fn(string $state): string => __("enums.status.{$state}"))
                    ->color(fn(string $state): string => match ($state) {
                        'active' => 'success',
                        'completed' => 'info',
                        'cancelled' => 'danger',
                        default => 'secondary',
                    })
                    ->label(__('fields.status')),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label(__('fields.created_at')),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label(__('fields.updated_at')),
                ...self::getReadTrackingTableColumns(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'active' => __('enums.status.active'),
                        'completed' => __('enums.status.completed'),
                        'cancelled' => __('enums.status.cancelled'),
                    ])
                    ->label(__('fields.status'))
                    ->query(function (Builder $query, array $data) {
                        return match ($data['value']) {
                            'active' => $query->where('departure_datetime', '>', Carbon::now())->whereNull('cancelled_at'),
                            'completed' => $query->where('departure_datetime', '<', Carbon::now())->whereNull('cancelled_at'),
                            'cancelled' => $query->whereNotNull('cancelled_at'),
                            default => $query,
                        };
                    }),
                Tables\Filters\SelectFilter::make('from_city_id')
                    ->relationship('fromCity', 'name_' . app()->getLocale())
                    ->label(__('fields.from_city')),
                Tables\Filters\SelectFilter::make('to_city_id')
                    ->relationship('toCity', 'name_' . app()->getLocale())
                    ->label(__('fields.to_city')),
                ...self::getReadTrackingTableFilters(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(function () {
                        $admin = auth('admin')->user();
                        /** @var \App\Models\Admin $admin */
                        return $admin && $admin->can('manage requests');
                    }),
                Tables\Actions\DeleteAction::make()
                    ->visible(function () {
                        $admin = auth('admin')->user();
                        /** @var \App\Models\Admin $admin */
                        return $admin && $admin->can('manage requests');
                    }),
                ...self::getReadTrackingTableActions(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(function () {
                            $admin = auth('admin')->user();
                            /** @var \App\Models\Admin $admin */
                            return $admin && $admin->can('manage requests');
                        }),
                    ...self::getReadTrackingTableBulkActions(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTripRequests::route('/'),
            'create' => Pages\CreateTripRequest::route('/create'),
            'view' => Pages\ViewTripRequest::route('/{record}'),
            'edit' => Pages\EditTripRequest::route('/{record}/edit'),
        ];
    }
}
