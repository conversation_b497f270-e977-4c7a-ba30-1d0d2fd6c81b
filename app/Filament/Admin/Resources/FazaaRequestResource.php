<?php

namespace App\Filament\Admin\Resources;

use App\Enums\Travel\TripStatusEnum;
use App\Filament\Admin\Resources\FazaaRequestResource\Pages;
use App\Models\Request\FazaaRequest;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;

class FazaaRequestResource extends Resource
{
    protected static ?string $model = FazaaRequest::class;

    protected static ?string $navigationIcon = 'heroicon-o-hand-raised';

    protected static ?int $navigationSort = 12;

    public static function getNavigationLabel(): string
    {
        return __('navigation.fazaa_requests');
    }

    public static function getModelLabel(): string
    {
        return __('models.fazaa_request');
    }

    public static function getPluralModelLabel(): string
    {
        return __('models.fazaa_requests');
    }

    public static function getNavigationGroup(): string
    {
        return __('navigation.requests_management');
    }

    public static function shouldRegisterNavigation(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('view requests');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(__('sections.fazaa_request_details'))
                    ->schema([
                        Forms\Components\Select::make('user_id')
                            ->relationship('user', 'name')
                            ->searchable()
                            ->preload()
                            ->label(__('fields.user'))
                            ->required(),
                        Forms\Components\Select::make('fazaa_service_type_id')
                            ->relationship('fazaaServiceType', 'name_' . app()->getLocale())
                            ->searchable()
                            ->preload()
                            ->label(__('fields.fazaa_service_type'))
                            ->required(),
                        Forms\Components\Select::make('specific_type_id')
                            ->relationship('specificType', 'name_' . app()->getLocale())
                            ->searchable()
                            ->preload()
                            ->label(__('fields.specific_type')),
                        Forms\Components\Select::make('transportation_type')
                            ->options([
                                'car' => __('enums.transportation_type.car'),
                                'flight' => __('enums.transportation_type.flight'),
                            ])
                            ->label(__('fields.transportation_type'))
                            ->required(),
                        Forms\Components\DateTimePicker::make('departure_datetime')
                            ->label(__('fields.departure_datetime'))
                            ->required(),
                        Forms\Components\DateTimePicker::make('arrival_datetime')
                            ->label(__('fields.arrival_datetime')),
                    ]),
                Forms\Components\Section::make(__('sections.location_details'))
                    ->schema([
                        Forms\Components\Select::make('from_city_id')
                            ->relationship('fromCity', 'name_' . app()->getLocale())
                            ->searchable()
                            ->preload()
                            ->label(__('fields.from_city'))
                            ->required(),
                        Forms\Components\Select::make('to_city_id')
                            ->relationship('toCity', 'name_' . app()->getLocale())
                            ->searchable()
                            ->preload()
                            ->label(__('fields.to_city')),
                        Forms\Components\TextInput::make('from_location_lat')
                            ->label(__('fields.from_latitude'))
                            ->numeric(),
                        Forms\Components\TextInput::make('from_location_lng')
                            ->label(__('fields.from_longitude'))
                            ->numeric(),
                        Forms\Components\TextInput::make('to_location_lat')
                            ->label(__('fields.to_latitude'))
                            ->numeric(),
                        Forms\Components\TextInput::make('to_location_lng')
                            ->label(__('fields.to_longitude'))
                            ->numeric(),
                        Forms\Components\TextInput::make('service_location')
                            ->label(__('fields.service_location'))
                            ->columnSpanFull(),
                    ]),
                Forms\Components\Section::make(__('sections.additional_details'))
                    ->schema([
                        Forms\Components\Textarea::make('note')
                            ->label(__('fields.note'))
                            ->columnSpanFull(),
                    ]),
                Forms\Components\Section::make(__('sections.status_information'))
                    ->schema([
                        Forms\Components\DateTimePicker::make('cancelled_at')
                            ->label(__('fields.cancelled_at')),
                        Forms\Components\TextInput::make('cancellation_reason')
                            ->label(__('fields.cancellation_reason')),
                        Forms\Components\Textarea::make('cancellation_note')
                            ->label(__('fields.cancellation_note')),
                        Forms\Components\DateTimePicker::make('attendance_confirmed_at')
                            ->label(__('fields.attendance_confirmed_at')),
                        Forms\Components\DateTimePicker::make('delayed_at')
                            ->label(__('fields.delayed_at')),
                        Forms\Components\TextInput::make('delay_reason')
                            ->label(__('fields.delay_reason')),
                        Forms\Components\Textarea::make('delay_note')
                            ->label(__('fields.delay_note')),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->searchable()
                    ->label(__('fields.id')),
                Tables\Columns\TextColumn::make('user.name')
                    ->searchable()
                    ->sortable()
                    ->label(__('fields.user')),
                Tables\Columns\TextColumn::make('fazaaServiceType.name_' . app()->getLocale())
                    ->label(__('fields.service_type')),
                Tables\Columns\TextColumn::make('specificType.name_' . app()->getLocale())
                    ->label(__('fields.specific_type'))
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('transportation_type')
                    ->formatStateUsing(fn(string $state): string => __("enums.transportation_type.{$state}"))
                    ->label(__('fields.transportation_type')),
                Tables\Columns\TextColumn::make('fromCity.name_' . app()->getLocale())
                    ->label(__('fields.from_city')),
                Tables\Columns\TextColumn::make('toCity.name_' . app()->getLocale())
                    ->label(__('fields.to_city'))
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('departure_datetime')
                    ->dateTime()
                    ->sortable()
                    ->label(__('fields.departure_datetime')),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->formatStateUsing(fn(string $state): string => __("enums.status.{$state}"))
                    ->color(fn(string $state): string => match ($state) {
                        'active' => 'success',
                        'completed' => 'info',
                        'cancelled' => 'danger',
                        default => 'secondary',
                    })
                    ->label(__('fields.status')),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label(__('fields.created_at')),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label(__('fields.updated_at')),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'active' => __('enums.status.active'),
                        'completed' => __('enums.status.completed'),
                        'cancelled' => __('enums.status.cancelled'),
                    ])
                    ->label(__('fields.status'))
                    ->query(function (Builder $query, array $data) {
                        return match ($data['value']) {
                            'active' => $query->where('departure_datetime', '>', Carbon::now())->whereNull('cancelled_at'),
                            'completed' => $query->where('departure_datetime', '<', Carbon::now())->whereNull('cancelled_at'),
                            'cancelled' => $query->whereNotNull('cancelled_at'),
                            default => $query,
                        };
                    }),
                Tables\Filters\SelectFilter::make('fazaa_service_type_id')
                    ->relationship('fazaaServiceType', 'name_' . app()->getLocale())
                    ->label(__('fields.service_type')),
                Tables\Filters\SelectFilter::make('from_city_id')
                    ->relationship('fromCity', 'name_' . app()->getLocale())
                    ->label(__('fields.from_city')),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFazaaRequests::route('/'),
            'create' => Pages\CreateFazaaRequest::route('/create'),
            'view' => Pages\ViewFazaaRequest::route('/{record}'),
            'edit' => Pages\EditFazaaRequest::route('/{record}/edit'),
        ];
    }
}
