<?php

namespace App\Filament\Admin\Resources\TripTrackingResource\Pages;

use App\Filament\Admin\Resources\TripTrackingResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditTripTracking extends EditRecord
{
    protected static string $resource = TripTrackingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
