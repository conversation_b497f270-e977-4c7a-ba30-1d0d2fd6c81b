<?php

namespace App\Filament\Admin\Resources\TripTrackingResource\Pages;

use App\Filament\Admin\Resources\TripTrackingResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewTripTracking extends ViewRecord
{
    protected static string $resource = TripTrackingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
