<?php

namespace App\Filament\Admin\Resources\ShippingOfferResource\Pages;

use App\Filament\Admin\Resources\ShippingOfferResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewShippingOffer extends ViewRecord
{
    protected static string $resource = ShippingOfferResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
