<?php

namespace App\Filament\Admin\Resources\ShippingOfferResource\Pages;

use App\Filament\Admin\Resources\ShippingOfferResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditShippingOffer extends EditRecord
{
    protected static string $resource = ShippingOfferResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
