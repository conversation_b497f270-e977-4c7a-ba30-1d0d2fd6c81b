<?php

namespace App\Filament\Admin\Resources\ShippingOfferResource\Pages;

use App\Filament\Admin\Resources\ShippingOfferResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListShippingOffers extends ListRecords
{
    protected static string $resource = ShippingOfferResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
