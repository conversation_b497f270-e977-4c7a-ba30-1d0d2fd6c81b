<?php

namespace App\Filament\Admin\Resources\FazaaOfferResource\Pages;

use App\Filament\Admin\Resources\FazaaOfferResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListFazaaOffers extends ListRecords
{
    protected static string $resource = FazaaOfferResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
