<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\TripServiceResource\Pages;
use App\Models\Service\TripService;
use App\Traits\FilamentHasReadTrackingResource;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class TripServiceResource extends Resource
{
    use FilamentHasReadTrackingResource;

    protected static ?string $model = TripService::class;

    protected static ?string $navigationIcon = 'heroicon-o-map';

    protected static ?int $navigationSort = 1;

    public static function getNavigationLabel(): string
    {
        return __('navigation.trip_services');
    }

    public static function getModelLabel(): string
    {
        return __('models.trip_service');
    }

    public static function getPluralModelLabel(): string
    {
        return __('models.trip_services');
    }

    public static function getNavigationGroup(): string
    {
        return __('navigation.services_management');
    }

    public static function shouldRegisterNavigation(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('view tracking');
    }

    public static function canViewAny(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('view tracking');
    }

    public static function canView($record): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('view tracking');
    }

    public static function canCreate(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage tracking');
    }

    public static function canEdit($record): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage tracking');
    }

    public static function canDelete($record): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage tracking');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(__('sections.trip_request_details'))
                    ->schema([
                        Forms\Components\Select::make('trip_type')
                            ->label(__('fields.trip_type'))
                            ->options([
                                'one_way' => __('enums.trip_type.one_way'),
                                'round_trip' => __('enums.trip_type.round_trip'),
                            ])
                            ->required(),
                        Forms\Components\Select::make('transportation_type')
                            ->label(__('fields.transportation_type'))
                            ->options([
                                'car' => __('enums.transportation_type.car'),
                                'bus' => __('enums.transportation_type.bus'),
                            ])
                            ->required(),
                        Forms\Components\DateTimePicker::make('departure_datetime')
                            ->label(__('fields.departure_datetime'))
                            ->required(),
                        Forms\Components\DateTimePicker::make('arrival_datetime')
                            ->label(__('fields.arrival_datetime'))
                            ->required(),
                        Forms\Components\TextInput::make('number_of_seats')
                            ->label(__('fields.number_of_seats'))
                            ->numeric()
                            ->required(),
                        Forms\Components\TextInput::make('price')
                            ->label(__('fields.price'))
                            ->numeric()
                            ->required(),
                    ]),
                Forms\Components\Section::make(__('sections.location_details'))
                    ->schema([
                        Forms\Components\Select::make('from_city_id')
                            ->label(__('fields.from_city'))
                            ->relationship('fromCity', 'name_' . app()->getLocale())
                            ->required(),
                        Forms\Components\Select::make('to_city_id')
                            ->label(__('fields.to_city'))
                            ->relationship('toCity', 'name_' . app()->getLocale())
                            ->required(),
                        Forms\Components\TextInput::make('from_location_lat')
                            ->label(__('fields.from_location_lat'))
                            ->numeric(),
                        Forms\Components\TextInput::make('from_location_lng')
                            ->label(__('fields.from_location_lng'))
                            ->numeric(),
                        Forms\Components\TextInput::make('to_location_lat')
                            ->label(__('fields.to_location_lat'))
                            ->numeric(),
                        Forms\Components\TextInput::make('to_location_lng')
                            ->label(__('fields.to_location_lng'))
                            ->numeric(),
                    ]),
                Forms\Components\Section::make(__('sections.additional_details'))
                    ->schema([
                        Forms\Components\Select::make('user_id')
                            ->label(__('fields.user'))
                            ->relationship('user', 'name')
                            ->required(),
                        Forms\Components\Select::make('car_id')
                            ->label(__('fields.car'))
                            ->relationship('car', 'name_' . app()->getLocale()),
                        Forms\Components\Toggle::make('allow_smoking')
                            ->label(__('fields.allow_smoking')),
                        Forms\Components\Toggle::make('deliver_to_door')
                            ->label(__('fields.deliver_to_door')),
                        Forms\Components\TextInput::make('number_of_free_cartons')
                            ->label(__('fields.number_of_free_cartons'))
                            ->numeric(),
                        Forms\Components\Textarea::make('note')
                            ->label(__('fields.note'))
                            ->rows(3),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label(__('fields.id'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.name')
                    ->label(__('fields.user'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fromCity.name_' . app()->getLocale())
                    ->label(__('fields.from_city'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('toCity.name_' . app()->getLocale())
                    ->label(__('fields.to_city'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('departure_datetime')
                    ->label(__('fields.departure_datetime'))
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('price')
                    ->label(__('fields.price'))
                    ->money('SAR')
                    ->sortable(),
                Tables\Columns\TextColumn::make('number_of_seats')
                    ->label(__('fields.seats'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->label(__('fields.status'))
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'active' => 'success',
                        'completed' => 'info',
                        'cancelled' => 'danger',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('fields.created_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('trip_type')
                    ->label(__('fields.trip_type'))
                    ->options([
                        'one_way' => __('enums.trip_type.one_way'),
                        'round_trip' => __('enums.trip_type.round_trip'),
                    ]),
                Tables\Filters\SelectFilter::make('from_city_id')
                    ->label(__('fields.from_city'))
                    ->relationship('fromCity', 'name_' . app()->getLocale()),
                Tables\Filters\SelectFilter::make('to_city_id')
                    ->label(__('fields.to_city'))
                    ->relationship('toCity', 'name_' . app()->getLocale()),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(function () {
                        $admin = auth('admin')->user();
                        /** @var \App\Models\Admin $admin */
                        return $admin && $admin->can('manage tracking');
                    }),
                Tables\Actions\DeleteAction::make()
                    ->visible(function () {
                        $admin = auth('admin')->user();
                        /** @var \App\Models\Admin $admin */
                        return $admin && $admin->can('manage tracking');
                    }),
                ...self::getReadTrackingTableActions(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(function () {
                            $admin = auth('admin')->user();
                            /** @var \App\Models\Admin $admin */
                            return $admin && $admin->can('manage tracking');
                        }),
                    ...self::getReadTrackingTableBulkActions(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTripServices::route('/'),
            'create' => Pages\CreateTripService::route('/create'),
            'view' => Pages\ViewTripService::route('/{record}'),
            'edit' => Pages\EditTripService::route('/{record}/edit'),
        ];
    }
}
