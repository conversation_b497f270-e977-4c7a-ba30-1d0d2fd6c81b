<?php

namespace App\Filament\Admin\Resources\ShippingServiceBookingResource\Pages;

use App\Filament\Admin\Resources\ShippingServiceBookingResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewShippingServiceBooking extends ViewRecord
{
    protected static string $resource = ShippingServiceBookingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
