<?php

namespace App\Filament\Admin\Resources\ShippingServiceBookingResource\Pages;

use App\Filament\Admin\Resources\ShippingServiceBookingResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditShippingServiceBooking extends EditRecord
{
    protected static string $resource = ShippingServiceBookingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
            Actions\ViewAction::make(),
        ];
    }
}
