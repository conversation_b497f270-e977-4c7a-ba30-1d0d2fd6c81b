<?php

namespace App\Filament\Admin\Resources\ShippingServiceBookingResource\Pages;

use App\Filament\Admin\Resources\ShippingServiceBookingResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListShippingServiceBookings extends ListRecords
{
    protected static string $resource = ShippingServiceBookingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
