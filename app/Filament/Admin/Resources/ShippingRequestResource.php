<?php

namespace App\Filament\Admin\Resources;

use App\Enums\Travel\TripStatusEnum;
use App\Filament\Admin\Resources\ShippingRequestResource\Pages;
use App\Models\Request\ShippingRequest;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use App\Traits\FilamentHasReadTrackingResource;

class ShippingRequestResource extends Resource
{
    use FilamentHasReadTrackingResource;

    protected static ?string $model = ShippingRequest::class;

    protected static ?string $navigationIcon = 'heroicon-o-truck';

    protected static ?int $navigationSort = 11;

    public static function getNavigationLabel(): string
    {
        return __('navigation.shipping_requests');
    }

    public static function getModelLabel(): string
    {
        return __('models.shipping_request');
    }

    public static function getPluralModelLabel(): string
    {
        return __('models.shipping_requests');
    }

    public static function getNavigationGroup(): string
    {
        return __('navigation.requests_management');
    }

    public static function shouldRegisterNavigation(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('view requests');
    }

    public static function canViewAny(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('view requests');
    }

    public static function canView($record): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('view requests');
    }

    public static function canCreate(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage requests');
    }

    public static function canEdit($record): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage requests');
    }

    public static function canDelete($record): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage requests');
    }

    public static function canDeleteAny(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage requests');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(__('sections.shipping_request_details'))
                    ->schema([
                        Forms\Components\Select::make('user_id')
                            ->relationship('user', 'name')
                            ->searchable()
                            ->preload()
                            ->label(__('fields.user'))
                            ->required(),
                        Forms\Components\Select::make('transportation_type')
                            ->options([
                                'car' => __('enums.transportation_type.car'),
                                'flight' => __('enums.transportation_type.flight'),
                            ])
                            ->label(__('fields.transportation_type'))
                            ->required(),
                        Forms\Components\DateTimePicker::make('departure_datetime')
                            ->label(__('fields.departure_datetime'))
                            ->required(),
                        Forms\Components\DateTimePicker::make('arrival_datetime')
                            ->label(__('fields.arrival_datetime')),
                    ]),
                Forms\Components\Section::make(__('sections.shipping_details'))
                    ->schema([
                        Forms\Components\Toggle::make('can_ship_packages')
                            ->label(__('fields.can_ship_packages'))
                            ->default(false),
                        Forms\Components\Toggle::make('can_ship_documents')
                            ->label(__('fields.can_ship_documents'))
                            ->default(false),
                        Forms\Components\Toggle::make('can_ship_furniture')
                            ->label(__('fields.can_ship_furniture'))
                            ->default(false),
                        Forms\Components\TextInput::make('packages_volume')
                            ->label(__('fields.packages_volume'))
                            ->numeric(),
                        Forms\Components\TextInput::make('document_volume')
                            ->label(__('fields.document_volume'))
                            ->numeric(),
                        Forms\Components\TextInput::make('furniture_volume')
                            ->label(__('fields.furniture_volume'))
                            ->numeric(),
                        Forms\Components\Toggle::make('is_fragile')
                            ->label(__('fields.is_fragile'))
                            ->default(false),
                    ]),
                Forms\Components\Section::make(__('sections.location_details'))
                    ->schema([
                        Forms\Components\Select::make('from_city_id')
                            ->relationship('fromCity', 'name_' . app()->getLocale())
                            ->searchable()
                            ->preload()
                            ->label(__('fields.from_city'))
                            ->required(),
                        Forms\Components\Select::make('to_city_id')
                            ->relationship('toCity', 'name_' . app()->getLocale())
                            ->searchable()
                            ->preload()
                            ->label(__('fields.to_city'))
                            ->required(),
                        Forms\Components\TextInput::make('from_location_lat')
                            ->label(__('fields.from_latitude'))
                            ->numeric(),
                        Forms\Components\TextInput::make('from_location_lng')
                            ->label(__('fields.from_longitude'))
                            ->numeric(),
                        Forms\Components\TextInput::make('to_location_lat')
                            ->label(__('fields.to_latitude'))
                            ->numeric(),
                        Forms\Components\TextInput::make('to_location_lng')
                            ->label(__('fields.to_longitude'))
                            ->numeric(),
                        Forms\Components\TextInput::make('delivery_location')
                            ->label(__('fields.delivery_location'))
                            ->columnSpanFull(),
                    ]),
                Forms\Components\Section::make(__('sections.additional_details'))
                    ->schema([
                        Forms\Components\Textarea::make('note')
                            ->label(__('fields.note'))
                            ->columnSpanFull(),
                    ]),
                Forms\Components\Section::make(__('sections.status_information'))
                    ->schema([
                        Forms\Components\DateTimePicker::make('cancelled_at')
                            ->label(__('fields.cancelled_at')),
                        Forms\Components\TextInput::make('cancellation_reason')
                            ->label(__('fields.cancellation_reason')),
                        Forms\Components\Textarea::make('cancellation_note')
                            ->label(__('fields.cancellation_note')),
                        Forms\Components\DateTimePicker::make('attendance_confirmed_at')
                            ->label(__('fields.attendance_confirmed_at')),
                        Forms\Components\DateTimePicker::make('delayed_at')
                            ->label(__('fields.delayed_at')),
                        Forms\Components\TextInput::make('delay_reason')
                            ->label(__('fields.delay_reason')),
                        Forms\Components\Textarea::make('delay_note')
                            ->label(__('fields.delay_note')),
                        ...self::getReadTrackingFormFields(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->searchable()
                    ->label(__('fields.id')),
                Tables\Columns\TextColumn::make('user.name')
                    ->searchable()
                    ->sortable()
                    ->label(__('fields.user')),
                Tables\Columns\TextColumn::make('transportation_type')
                    ->formatStateUsing(fn(string $state): string => __("enums.transportation_type.{$state}"))
                    ->label(__('fields.transportation_type')),
                Tables\Columns\TextColumn::make('fromCity.name_' . app()->getLocale())
                    ->label(__('fields.from_city')),
                Tables\Columns\TextColumn::make('toCity.name_' . app()->getLocale())
                    ->label(__('fields.to_city')),
                Tables\Columns\TextColumn::make('departure_datetime')
                    ->dateTime()
                    ->sortable()
                    ->label(__('fields.departure_datetime')),
                Tables\Columns\IconColumn::make('can_ship_packages')
                    ->boolean()
                    ->label(__('fields.can_ship_packages')),
                Tables\Columns\IconColumn::make('can_ship_documents')
                    ->boolean()
                    ->label(__('fields.can_ship_documents')),
                Tables\Columns\IconColumn::make('can_ship_furniture')
                    ->boolean()
                    ->label(__('fields.can_ship_furniture')),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->formatStateUsing(fn(string $state): string => __("enums.status.{$state}"))
                    ->color(fn(string $state): string => match ($state) {
                        'active' => 'success',
                        'completed' => 'info',
                        'cancelled' => 'danger',
                        default => 'secondary',
                    })
                    ->label(__('fields.status')),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label(__('fields.created_at')),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label(__('fields.updated_at')),
                ...self::getReadTrackingTableColumns(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'active' => __('enums.status.active'),
                        'completed' => __('enums.status.completed'),
                        'cancelled' => __('enums.status.cancelled'),
                    ])
                    ->label(__('fields.status'))
                    ->query(function (Builder $query, array $data) {
                        return match ($data['value']) {
                            'active' => $query->where('departure_datetime', '>', Carbon::now())->whereNull('cancelled_at'),
                            'completed' => $query->where('departure_datetime', '<', Carbon::now())->whereNull('cancelled_at'),
                            'cancelled' => $query->whereNotNull('cancelled_at'),
                            default => $query,
                        };
                    }),
                Tables\Filters\SelectFilter::make('from_city_id')
                    ->relationship('fromCity', 'name_' . app()->getLocale())
                    ->label(__('fields.from_city')),
                Tables\Filters\SelectFilter::make('to_city_id')
                    ->relationship('toCity', 'name_' . app()->getLocale())
                    ->label(__('fields.to_city')),
                Tables\Filters\Filter::make('can_ship_packages')
                    ->label(__('filters.can_ship_packages'))
                    ->query(fn(Builder $query): Builder => $query->where('can_ship_packages', true)),
                Tables\Filters\Filter::make('can_ship_documents')
                    ->label(__('filters.can_ship_documents'))
                    ->query(fn(Builder $query): Builder => $query->where('can_ship_documents', true)),
                Tables\Filters\Filter::make('can_ship_furniture')
                    ->label(__('filters.can_ship_furniture'))
                    ->query(fn(Builder $query): Builder => $query->where('can_ship_furniture', true)),
                Tables\Filters\SelectFilter::make('transportation_type')
                    ->options([
                        'car' => __('enums.transportation_type.car'),
                        'flight' => __('enums.transportation_type.flight'),
                    ])
                    ->label(__('fields.transportation_type')),
                ...self::getReadTrackingTableFilters(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(function () {
                        $admin = auth('admin')->user();
                        /** @var \App\Models\Admin $admin */
                        return $admin && $admin->can('manage requests');
                    }),
                Tables\Actions\DeleteAction::make()
                    ->visible(function () {
                        $admin = auth('admin')->user();
                        /** @var \App\Models\Admin $admin */
                        return $admin && $admin->can('manage requests');
                    }),
                ...self::getReadTrackingTableActions(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(function () {
                            $admin = auth('admin')->user();
                            /** @var \App\Models\Admin $admin */
                            return $admin && $admin->can('manage requests');
                        }),
                    ...self::getReadTrackingTableBulkActions(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListShippingRequests::route('/'),
            'create' => Pages\CreateShippingRequest::route('/create'),
            'view' => Pages\ViewShippingRequest::route('/{record}'),
            'edit' => Pages\EditShippingRequest::route('/{record}/edit'),
        ];
    }
}
