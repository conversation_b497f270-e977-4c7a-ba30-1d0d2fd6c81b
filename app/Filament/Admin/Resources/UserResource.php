<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\UserResource\Pages;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Hash;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?int $navigationSort = 50;

    public static function getNavigationLabel(): string
    {
        return __('navigation.users');
    }

    public static function getModelLabel(): string
    {
        return __('models.user');
    }

    public static function getPluralModelLabel(): string
    {
        return __('models.users');
    }

    public static function shouldRegisterNavigation(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('view users');
    }

    public static function canViewAny(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('view users');
    }

    public static function canView($record): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('view users');
    }

    public static function canCreate(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage users');
    }

    public static function canEdit($record): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage users');
    }

    public static function canDelete($record): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage users');
    }

    public static function canDeleteAny(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage users');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label(__('fields.name'))
                    ->required(),
                Forms\Components\TextInput::make('email')
                    ->label(__('fields.email'))
                    ->email()
                    ->required(),
                Forms\Components\TextInput::make('mobile')
                    ->label(__('fields.mobile'))
                    ->tel(),
                Forms\Components\Select::make('country_id')
                    ->relationship(
                        'country',
                        'name_' . app()->getLocale()
                    )
                    ->label(__('fields.country'))
                    ->searchable()
                    ->preload(),
                Forms\Components\Select::make('locale')
                    ->label(__('fields.language'))
                    ->options([
                        'ar' => 'العربية',
                        'en' => 'English'
                    ])
                    ->default('ar'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('picture')
                    ->label(__('fields.picture'))
                    ->circular(),
                Tables\Columns\TextColumn::make('name')
                    ->label(__('fields.name'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->label(__('fields.email'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('mobile')
                    ->label(__('fields.mobile'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('country.name_' . app()->getLocale())
                    ->label(__('fields.country')),
                Tables\Columns\IconColumn::make('email_verified_at')
                    ->label(__('fields.verified'))
                    ->boolean()
                    ->trueIcon('heroicon-o-star')
                    ->falseIcon('heroicon-o-x-mark'),
                Tables\Columns\TextColumn::make('services_count')
                    ->label(__('fields.services'))
                    ->getStateUsing(function ($record) {
                        return $record->tripServices()->count()
                            + $record->fazaaServices()->count()
                            + $record->shippingServices()->count();
                    }),
                Tables\Columns\TextColumn::make('requests_count')
                    ->label(__('fields.requests'))
                    ->getStateUsing(function ($record) {
                        return $record->tripRequests()->count()
                            + $record->fazaaRequests()->count()
                            + $record->shippingRequests()->count();
                    }),
            ])
            ->modifyQueryUsing(fn($query) => $query->withCount([
                'tripServices',
                'fazaaServices',
                'shippingServices',
                'tripRequests',
                'fazaaRequests',
                'shippingRequests'
            ]))
            ->filters([
                Tables\Filters\Filter::make('verified')
                    ->query(fn(Builder $query): Builder => $query->whereNotNull('email_verified_at')),
                Tables\Filters\SelectFilter::make('country')
                    ->relationship('country', 'name_' . app()->getLocale())
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(function () {
                        $admin = auth('admin')->user();
                        /** @var \App\Models\Admin $admin */
                        return $admin && $admin->can('manage users');
                    }),
                Tables\Actions\DeleteAction::make()
                    ->visible(function () {
                        $admin = auth('admin')->user();
                        /** @var \App\Models\Admin $admin */
                        return $admin && $admin->can('manage users');
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(function () {
                            $admin = auth('admin')->user();
                            /** @var \App\Models\Admin $admin */
                            return $admin && $admin->can('manage users');
                        }),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'view' => Pages\ViewUser::route('/{record}'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }
}
