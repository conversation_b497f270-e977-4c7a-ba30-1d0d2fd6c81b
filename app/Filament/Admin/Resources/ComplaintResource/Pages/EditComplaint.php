<?php

namespace App\Filament\Admin\Resources\ComplaintResource\Pages;

use App\Enums\Complaints\ComplaintStatusEnum;
use App\Filament\Admin\Resources\ComplaintResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Facades\Auth;

class EditComplaint extends EditRecord
{
    protected static string $resource = ComplaintResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function afterSave(): void
    {
        $record = $this->getRecord();

        // Auto-assign admin who updated the complaint
        if ($record->status === ComplaintStatusEnum::RESOLVED() || $record->status === ComplaintStatusEnum::REJECTED()) {
            if (empty($record->admin_id)) {
                $record->update([
                    'admin_id' => Auth::id(),
                    'resolved_at' => now(),
                ]);
            }
        }
    }
}
