<?php

namespace App\Filament\Admin\Resources\ComplaintResource\Pages;

use App\Enums\Complaints\ComplaintStatusEnum;
use App\Filament\Admin\Resources\ComplaintResource;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Auth;

class CreateComplaint extends CreateRecord
{
    protected static string $resource = ComplaintResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function afterCreate(): void
    {
        $record = $this->getRecord();

        // Auto-assign admin who created the complaint with Resolved or Rejected status
        if ($record->status === ComplaintStatusEnum::RESOLVED() || $record->status === ComplaintStatusEnum::REJECTED()) {
            if (empty($record->admin_id)) {
                $record->update([
                    'admin_id' => Auth::id(),
                    'resolved_at' => now(),
                ]);
            }
        }
    }
}
