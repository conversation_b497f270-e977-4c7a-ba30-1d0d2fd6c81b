<?php

namespace App\Filament\Admin\Resources\FazaaRequestResource\Pages;

use App\Filament\Admin\Resources\FazaaRequestResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListFazaaRequests extends ListRecords
{
    protected static string $resource = FazaaRequestResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
