<?php

namespace App\Filament\Admin\Resources\FazaaRequestResource\Pages;

use App\Filament\Admin\Resources\FazaaRequestResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewFazaaRequest extends ViewRecord
{
    protected static string $resource = FazaaRequestResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
