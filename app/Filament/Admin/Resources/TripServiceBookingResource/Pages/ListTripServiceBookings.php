<?php

namespace App\Filament\Admin\Resources\TripServiceBookingResource\Pages;

use App\Filament\Admin\Resources\TripServiceBookingResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListTripServiceBookings extends ListRecords
{
    protected static string $resource = TripServiceBookingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
