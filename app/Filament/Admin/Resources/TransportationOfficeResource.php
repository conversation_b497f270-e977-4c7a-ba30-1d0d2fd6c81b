<?php

namespace App\Filament\Admin\Resources;

use App\Enums\TransportStatus;
use App\Filament\Admin\Resources\TransportationOfficeResource\Pages;
use App\Models\TransportationOffice;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class TransportationOfficeResource extends Resource
{
    protected static ?string $model = TransportationOffice::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office';

    protected static ?int $navigationSort = 1;

    public static function getNavigationLabel(): string
    {
        return __('models.transport.transportation_offices');
    }

    public static function getModelLabel(): string
    {
        return __('models.transport.transportation_office');
    }

    public static function getPluralModelLabel(): string
    {
        return __('models.transport.transportation_offices');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(__('sections.transport.basic_information'))
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label(__('fields.transport.name'))
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('email')
                            ->label(__('fields.transport.email'))
                            ->email()
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('phone')
                            ->label(__('fields.transport.phone'))
                            ->tel()
                            ->maxLength(255),
                        Forms\Components\Select::make('status')
                            ->label(__('fields.transport.status'))
                            ->options([
                                TransportStatus::ACTIVE->value => __('fields.transport.active'),
                                TransportStatus::INACTIVE->value => __('fields.transport.inactive'),
                                TransportStatus::SUSPENDED->value => __('fields.transport.suspended'),
                            ])
                            ->required()
                            ->default(TransportStatus::INACTIVE->value),
                    ])
                    ->columns(2),

                Forms\Components\Section::make(__('sections.transport.location_information'))
                    ->schema([
                        Forms\Components\Textarea::make('address')
                            ->label(__('fields.transport.address'))
                            ->rows(3)
                            ->columnSpanFull(),
                        Forms\Components\TextInput::make('city')
                            ->label(__('fields.transport.city'))
                            ->maxLength(255),
                        Forms\Components\TextInput::make('country')
                            ->label(__('fields.transport.country'))
                            ->maxLength(255),
                    ])
                    ->columns(2),

                Forms\Components\Section::make(__('sections.transport.additional_information'))
                    ->schema([
                        Forms\Components\TextInput::make('license_number')
                            ->label(__('fields.transport.license_number'))
                            ->maxLength(255),
                        Forms\Components\Textarea::make('description')
                            ->label(__('fields.transport.description'))
                            ->rows(3)
                            ->columnSpanFull(),
                    ]),

                Forms\Components\Section::make(__('sections.transport.documents'))
                    ->schema([
                        Forms\Components\FileUpload::make('office_logo')
                            ->label(__('fields.transport.office_logo'))
                            ->image()
                            ->imageEditor()
                            ->imageEditorAspectRatios([
                                '16:9',
                                '4:3',
                                '1:1',
                            ])
                            ->directory('transport-offices/logos')
                            ->visibility('public')
                            ->maxSize(2048)
                            ->helperText(__('messages.transport.logo_requirements')),
                        Forms\Components\FileUpload::make('license_document')
                            ->label(__('fields.transport.license_document'))
                            ->acceptedFileTypes(['application/pdf', 'image/*'])
                            ->directory('transport-offices/documents')
                            ->visibility('private')
                            ->maxSize(5120)
                            ->helperText(__('messages.transport.license_document_requirements')),
                        Forms\Components\FileUpload::make('registration_documents')
                            ->label(__('fields.transport.registration_documents'))
                            ->acceptedFileTypes(['application/pdf', 'image/*'])
                            ->directory('transport-offices/documents')
                            ->visibility('private')
                            ->maxSize(5120)
                            ->helperText(__('messages.transport.registration_documents_requirements')),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('office_logo')
                    ->label(__('fields.transport.office_logo'))
                    ->circular()
                    ->defaultImageUrl(asset('images/app_logo.png')),
                Tables\Columns\TextColumn::make('name')
                    ->label(__('fields.transport.name'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('email')
                    ->label(__('fields.transport.email'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('phone')
                    ->label(__('fields.transport.phone'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('city')
                    ->label(__('fields.transport.city'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('license_number')
                    ->label(__('fields.transport.license_number'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('status')
                    ->label(__('fields.transport.status'))
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        TransportStatus::ACTIVE->value => 'success',
                        TransportStatus::INACTIVE->value => 'warning',
                        TransportStatus::SUSPENDED->value => 'danger',
                        default => 'gray'
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        TransportStatus::ACTIVE->value => __('fields.transport.active'),
                        TransportStatus::INACTIVE->value => __('fields.transport.inactive'),
                        TransportStatus::SUSPENDED->value => __('fields.transport.suspended'),
                        default => $state
                    }),
                Tables\Columns\TextColumn::make('user.name')
                    ->label(__('fields.transport.transportation_office_user'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('fields.transport.created_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label(__('fields.transport.status'))
                    ->options([
                        TransportStatus::ACTIVE->value => __('fields.transport.active'),
                        TransportStatus::INACTIVE->value => __('fields.transport.inactive'),
                        TransportStatus::SUSPENDED->value => __('fields.transport.suspended'),
                    ]),
                Tables\Filters\Filter::make('has_license')
                    ->label(__('fields.transport.licensed_offices'))
                    ->query(fn(Builder $query): Builder => $query->whereNotNull('license_number')),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTransportationOffices::route('/'),
            'create' => Pages\CreateTransportationOffice::route('/create'),
            'view' => Pages\ViewTransportationOffice::route('/{record}'),
            'edit' => Pages\EditTransportationOffice::route('/{record}/edit'),
        ];
    }
}
