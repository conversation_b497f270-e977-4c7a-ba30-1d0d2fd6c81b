<?php

namespace App\Filament\Admin\Resources\UserResource\Pages;

use App\Filament\Admin\Resources\UserResource;
use Filament\Resources\Pages\ViewRecord;
use Filament\Actions;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\IconEntry;
use Filament\Support\Enums\ActionSize;
use Filament\Infolists\Components\RepeatableEntry;
use App\Models\Booking\TripServiceBooking;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;

class ViewUser extends ViewRecord
{
    protected static string $resource = UserResource::class;

    protected ?array $cachedTransactions = null;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        $transactions = $this->getRecentTransactions();

        return $infolist
            ->schema([
                Section::make()
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                Section::make(__('الملف الشخصي'))
                                    ->icon('heroicon-o-user')
                                    ->extraAttributes(['class' => 'user-profile-section'])
                                    ->schema([
                                        ImageEntry::make('picture')
                                            ->label('')
                                            ->circular()
                                            ->defaultImageUrl('https://ui-avatars.com/api/?name=' . urlencode($this->record->name) . '&color=7F9CF5&background=EBF4FF')
                                            ->alignCenter(),
                                        TextEntry::make('name')
                                            ->label(__('fields.name'))
                                            ->alignCenter()
                                            ->weight('bold')
                                            ->size('xl')
                                            ->color('primary'),
                                        TextEntry::make('role')
                                            ->label('')
                                            ->default(__('مستخدم'))
                                            ->alignCenter(),
                                        TextEntry::make('birth_date')
                                            ->label('')
                                            ->date('Y-m-d')
                                            ->alignCenter(),
                                        Grid::make(2)
                                            ->schema([
                                                TextEntry::make('country.name_' . app()->getLocale())
                                                    ->label('')
                                                    ->prefix(__('fields.country') . ': ')
                                                    ->alignCenter(),
                                                TextEntry::make('city.name_' . app()->getLocale())
                                                    ->label('')
                                                    ->prefix(__('fields.city') . ': ')
                                                    ->alignCenter(),
                                            ]),
                                        TextEntry::make('email')
                                            ->label('')
                                            ->alignCenter()
                                            ->icon('heroicon-o-envelope'),
                                        TextEntry::make('mobile')
                                            ->label('')
                                            ->alignCenter()
                                            ->icon('heroicon-o-phone'),
                                        Grid::make(2)
                                            ->schema([
                                                TextEntry::make('whatsapp')
                                                    ->label('')
                                                    ->default(__('واتساب'))
                                                    ->url(fn($record) => "https://wa.me/" . $record->mobile)
                                                    ->openUrlInNewTab()
                                                    ->icon('heroicon-o-chat-bubble-left-ellipsis')
                                                    ->color('success')
                                                    ->alignCenter(),
                                                TextEntry::make('call')
                                                    ->label('')
                                                    ->default(__('اتصال'))
                                                    ->url(fn($record) => "tel:" . $record->mobile)
                                                    ->icon('heroicon-o-phone')
                                                    ->color('primary')
                                                    ->alignCenter(),
                                            ])
                                            ->extraAttributes(['class' => 'contact-actions']),
                                    ])
                                    ->collapsible(),
                                Section::make(__('العمليات الأخيرة'))
                                    ->icon('heroicon-o-clock')
                                    ->extraAttributes(['class' => 'recent-transactions-section'])
                                    ->schema([
                                        // Header row
                                        Grid::make(4)
                                            ->schema([
                                                TextEntry::make('header_operation')
                                                    ->label('')
                                                    ->default(__('العملية'))
                                                    ->weight('bold'),
                                                TextEntry::make('header_status')
                                                    ->label('')
                                                    ->default(__('الحالة'))
                                                    ->weight('bold'),
                                                TextEntry::make('header_price')
                                                    ->label('')
                                                    ->default(__('الإجمالي'))
                                                    ->weight('bold'),
                                                TextEntry::make('header_date')
                                                    ->label('')
                                                    ->default(__('التاريخ'))
                                                    ->weight('bold'),
                                            ])
                                            ->extraAttributes(['class' => 'transactions-header']),

                                        // No operations message or transaction rows
                                        ...empty($transactions)
                                            ? [
                                                Grid::make(1)
                                                    ->schema([
                                                        TextEntry::make('no_operations')
                                                            ->label('')
                                                            ->default(__('لا توجد عمليات'))
                                                            ->alignCenter()
                                                            ->size('lg')
                                                            ->color('gray')
                                                            ->columnSpanFull(),
                                                    ])
                                                    ->extraAttributes(['class' => 'no-operations-message']),
                                            ]
                                            : array_map(function ($index) use ($transactions) {
                                                if (isset($transactions[$index])) {
                                                    $transaction = $transactions[$index];
                                                    return Grid::make(4)
                                                        ->schema([
                                                            TextEntry::make("operation{$index}")
                                                                ->label('')
                                                                ->default($transaction['operation']),
                                                            TextEntry::make("status{$index}")
                                                                ->label('')
                                                                ->default($transaction['status']),
                                                            TextEntry::make("price{$index}")
                                                                ->label('')
                                                                ->default($transaction['price'] . ' SAR'),
                                                            TextEntry::make("date{$index}")
                                                                ->label('')
                                                                ->default($transaction['date']),
                                                        ])
                                                        ->extraAttributes(['class' => 'transaction-row']);
                                                }
                                                return null;
                                            }, range(0, count($transactions) - 1)),
                                    ])
                                    ->collapsible(),
                            ]),
                    ]),
            ]);
    }

    /**
     * Get recent transactions from user's services
     */
    protected function getRecentTransactions(): array
    {
        if ($this->cachedTransactions !== null) {
            return $this->cachedTransactions;
        }

        $transactions = collect();
        $user = $this->record;

        // Trip Services
        $user->tripServices()->with(['fromCity', 'toCity'])->latest()->take(3)->get()->each(function ($service) use ($transactions) {
            $fromCity = $service->fromCity->name_ar ?? 'الرياض';
            $toCity = $service->toCity->name_ar ?? 'الدمام';

            $transactions->push([
                'operation' => __('رحلة') . ': ' . $fromCity . ' - ' . $toCity,
                'status' => $this->formatStatus($service->status),
                'price' => $service->price ?? 400,
                'date' => $service->departure_date ? Carbon::parse($service->departure_date)->format('Y/m/d') : now()->format('Y/m/d'),
                'type' => 'trip',
            ]);
        });

        // Fazaa Services
        $user->fazaaServices()->with(['fromCity', 'toCity'])->latest()->take(3)->get()->each(function ($service) use ($transactions) {
            $fromCity = $service->fromCity->name_ar ?? 'جدة';
            $toCity = $service->toCity->name_ar ?? 'مكة';

            $transactions->push([
                'operation' => __('فزعة') . ': ' . $fromCity . ' - ' . $toCity,
                'status' => $this->formatStatus($service->status),
                'price' => $service->price ?? 400,
                'date' => $service->departure_date ? Carbon::parse($service->departure_date)->format('Y/m/d') : now()->format('Y/m/d'),
                'type' => 'fazaa',
            ]);
        });

        // Shipping Services
        $user->shippingServices()->with(['fromCity', 'toCity'])->latest()->take(3)->get()->each(function ($service) use ($transactions) {
            $fromCity = $service->fromCity->name_ar ?? 'الرياض';
            $toCity = $service->toCity->name_ar ?? 'المدينة';

            // Calculate total price for shipping service
            $totalPrice = $service->package_price ?? 0;
            $totalPrice += $service->document_price ?? 0;
            $totalPrice += $service->furniture_price ?? 0;

            $transactions->push([
                'operation' => __('شحن') . ': ' . $fromCity . ' - ' . $toCity,
                'status' => $this->formatStatus($service->status),
                'price' => $totalPrice > 0 ? $totalPrice : 0,
                'date' => $service->departure_date ? Carbon::parse($service->departure_date)->format('Y/m/d') : now()->format('Y/m/d'),
                'type' => 'shipping',
            ]);
        });

        $this->cachedTransactions = $transactions->sortByDesc('date')->take(7)->values()->all();
        return $this->cachedTransactions;
    }

    /**
     * Format the status for display
     */
    protected function formatStatus(string $status): string
    {
        return match ($status) {
            'active' => __('نشط'),
            'completed' => __('مكتمل'),
            'cancelled' => __('ملغي'),
            default => '-',
        };
    }

    public function getStylesheets(): array
    {
        return [
            'user-profile' => asset('css/filament/user-profile.css'),
        ];
    }
}
