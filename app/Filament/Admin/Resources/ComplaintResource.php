<?php

namespace App\Filament\Admin\Resources;

use App\Enums\Complaints\ComplaintStatusEnum;
use App\Filament\Admin\Resources\ComplaintResource\Pages;
use App\Models\Complaint;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class ComplaintResource extends Resource
{
    protected static ?string $model = Complaint::class;

    protected static ?string $navigationIcon = 'heroicon-o-exclamation-circle';

    protected static ?string $recordTitleAttribute = 'id';

    protected static ?int $navigationSort = 51;

    public static function getNavigationLabel(): string
    {
        return __('navigation.complaints');
    }

    public static function getPluralLabel(): string
    {
        return __('models.complaints');
    }

    public static function getLabel(): string
    {
        return __('models.complaint');
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::pending()->count();
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return 'warning';
    }

    public static function getNavigationGroup(): string
    {
        return __('navigation.complaints_management');
    }

    public static function shouldRegisterNavigation(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('view complaints');
    }

    public static function canViewAny(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('view complaints');
    }

    public static function canView($record): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('view complaints');
    }

    public static function canCreate(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage complaints');
    }

    public static function canEdit($record): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage complaints');
    }

    public static function canDelete($record): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage complaints');
    }

    public static function canDeleteAny(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage complaints');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\TextInput::make('id')
                            ->label(__('fields.id'))
                            ->disabled(),

                        Forms\Components\Select::make('user_id')
                            ->relationship('user', 'name')
                            ->searchable()
                            ->preload()
                            ->label(__('fields.user'))
                            ->disabled()
                            ->dehydrated(false),

                        Forms\Components\Select::make('status')
                            ->options([
                                ComplaintStatusEnum::PENDING() => __('enums.status.pending'),
                                ComplaintStatusEnum::RESOLVED() => __('enums.status.resolved'),
                                ComplaintStatusEnum::REJECTED() => __('enums.status.rejected'),
                            ])
                            ->required()
                            ->label(__('fields.status')),

                        Forms\Components\Textarea::make('description')
                            ->label(__('fields.description'))
                            ->required()
                            ->columnSpanFull(),

                        Forms\Components\Textarea::make('admin_response')
                            ->label(__('fields.admin_response'))
                            ->columnSpanFull(),

                        Forms\Components\DateTimePicker::make('resolved_at')
                            ->label(__('fields.resolved_at')),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label(__('fields.id'))
                    ->searchable(),

                Tables\Columns\TextColumn::make('user.name')
                    ->label(__('fields.user'))
                    ->searchable(),

                Tables\Columns\TextColumn::make('description')
                    ->label(__('fields.description'))
                    ->limit(50),

                Tables\Columns\TextColumn::make('status')
                    ->label(__('fields.status'))
                    ->formatStateUsing(fn(ComplaintStatusEnum $state) => __("enums.status." . $state->value))
                    ->badge()
                    ->color(fn(ComplaintStatusEnum $state) => match ($state->value) {
                        'pending' => 'warning',
                        'resolved' => 'success',
                        'rejected' => 'danger',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('admin.name')
                    ->label(__('fields.handled_by')),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('fields.created_at'))
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('resolved_at')
                    ->label(__('fields.resolved_at'))
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        ComplaintStatusEnum::PENDING() => __('enums.status.pending'),
                        ComplaintStatusEnum::RESOLVED() => __('enums.status.resolved'),
                        ComplaintStatusEnum::REJECTED() => __('enums.status.rejected'),
                    ])
                    ->label(__('fields.status')),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(function () {
                        $admin = auth('admin')->user();
                        /** @var \App\Models\Admin $admin */
                        return $admin && $admin->can('manage complaints');
                    }),
                Tables\Actions\Action::make('resolve')
                    ->label(__('fields.resolve'))
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->form([
                        Forms\Components\Textarea::make('admin_response')
                            ->label(__('fields.admin_response'))
                            ->required(),
                    ])
                    ->action(function (Model $record, array $data): void {
                        $record->update([
                            'status' => ComplaintStatusEnum::RESOLVED(),
                            'admin_response' => $data['admin_response'],
                            'admin_id' => Auth::id(),
                            'resolved_at' => now(),
                        ]);
                    })
                    ->visible(function (Model $record): bool {
                        $admin = auth('admin')->user();
                        /** @var \App\Models\Admin $admin */
                        return $record->status === ComplaintStatusEnum::PENDING() && $admin && $admin->can('manage complaints');
                    }),

                Tables\Actions\Action::make('reject')
                    ->label(__('fields.reject'))
                    ->icon('heroicon-o-x-circle')
                    ->color('danger')
                    ->form([
                        Forms\Components\Textarea::make('admin_response')
                            ->label(__('fields.reason_for_rejection'))
                            ->required(),
                    ])
                    ->action(function (Model $record, array $data): void {
                        $record->update([
                            'status' => ComplaintStatusEnum::REJECTED(),
                            'admin_response' => $data['admin_response'],
                            'admin_id' => Auth::id(),
                            'resolved_at' => now(),
                        ]);
                    })
                    ->visible(function (Model $record): bool {
                        $admin = auth('admin')->user();
                        /** @var \App\Models\Admin $admin */
                        return $record->status === ComplaintStatusEnum::PENDING() && $admin && $admin->can('manage complaints');
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(function () {
                            $admin = auth('admin')->user();
                            /** @var \App\Models\Admin $admin */
                            return $admin && $admin->can('manage complaints');
                        }),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListComplaints::route('/'),
            'create' => Pages\CreateComplaint::route('/create'),
            'edit' => Pages\EditComplaint::route('/{record}/edit'),
            'view' => Pages\ViewComplaint::route('/{record}'),
        ];
    }
}
