<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\FazaaOfferResource\Pages;
use App\Models\Booking\FazaaRequestBooking;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use App\Traits\FilamentHasReadTrackingResource;

class FazaaOfferResource extends Resource
{
    use FilamentHasReadTrackingResource;

    protected static ?string $model = FazaaRequestBooking::class;

    protected static ?string $navigationIcon = 'heroicon-o-tag';

    protected static ?int $navigationSort = 42;

    public static function getNavigationLabel(): string
    {
        return __('navigation.fazaa_offers');
    }

    public static function getModelLabel(): string
    {
        return __('models.fazaa_offer');
    }

    public static function getPluralModelLabel(): string
    {
        return __('models.fazaa_offers');
    }

    public static function getNavigationGroup(): string
    {
        return __('navigation.offers_management');
    }

    public static function shouldRegisterNavigation(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('view offers');
    }

    public static function canViewAny(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('view offers');
    }

    public static function canView($record): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('view offers');
    }

    public static function canCreate(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage offers');
    }

    public static function canEdit($record): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage offers');
    }

    public static function canDelete($record): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage offers');
    }

    public static function canDeleteAny(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage offers');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(__('sections.offer_details'))
                    ->schema([
                        Forms\Components\Select::make('fazaa_request_id')
                            ->relationship('fazaaRequest', 'id')
                            ->searchable()
                            ->preload()
                            ->label(__('fields.fazaa_request')),
                        Forms\Components\Select::make('user_id')
                            ->relationship('user', 'name')
                            ->searchable()
                            ->preload()
                            ->label(__('fields.user')),
                        Forms\Components\TextInput::make('price')
                            ->numeric()
                            ->label(__('fields.price')),
                        Forms\Components\Select::make('status')
                            ->options([
                                'pending' => __('fields.pending'),
                                'accepted' => __('fields.accepted'),
                                'rejected' => __('fields.rejected'),
                                'cancelled' => __('fields.cancelled'),
                            ])
                            ->label(__('fields.offer_status')),
                        Forms\Components\Textarea::make('note')
                            ->label(__('fields.note')),
                        Forms\Components\DateTimePicker::make('created_at')
                            ->label(__('fields.offer_date')),
                    ]),
                Forms\Components\Section::make(__('sections.status_information'))
                    ->schema([
                        ...self::getReadTrackingFormFields(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->searchable()
                    ->sortable()
                    ->label(__('fields.id')),
                Tables\Columns\TextColumn::make('fazaaRequest.id')
                    ->label(__('fields.fazaa_request')),
                Tables\Columns\TextColumn::make('user.name')
                    ->searchable()
                    ->sortable()
                    ->label(__('fields.user')),
                Tables\Columns\TextColumn::make('price')
                    ->label(__('fields.price')),
                Tables\Columns\TextColumn::make('status')
                    ->label(__('fields.offer_status')),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->label(__('fields.offer_date')),
                ...self::getReadTrackingTableColumns(),
            ])
            ->filters([
                ...self::getReadTrackingTableFilters(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                ...self::getReadTrackingTableActions(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    ...self::getReadTrackingTableBulkActions(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFazaaOffers::route('/'),
        ];
    }
}
