<?php

namespace App\Filament\Admin\Resources\FazaaTrackingResource\Pages;

use App\Filament\Admin\Resources\FazaaTrackingResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewFazaaTracking extends ViewRecord
{
    protected static string $resource = FazaaTrackingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
