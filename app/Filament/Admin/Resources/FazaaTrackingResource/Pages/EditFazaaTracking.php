<?php

namespace App\Filament\Admin\Resources\FazaaTrackingResource\Pages;

use App\Filament\Admin\Resources\FazaaTrackingResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditFazaaTracking extends EditRecord
{
    protected static string $resource = FazaaTrackingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
