<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\FazaaServiceResource\Pages;
use App\Models\Service\FazaaService;
use App\Traits\FilamentHasReadTrackingResource;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class FazaaServiceResource extends Resource
{
    use FilamentHasReadTrackingResource;

    protected static ?string $model = FazaaService::class;

    protected static ?string $navigationIcon = 'heroicon-o-hand-raised';

    protected static ?int $navigationSort = 3;

    public static function getNavigationLabel(): string
    {
        return __('navigation.fazaa_services');
    }

    public static function getModelLabel(): string
    {
        return __('models.fazaa_service');
    }

    public static function getPluralModelLabel(): string
    {
        return __('models.fazaa_services');
    }

    public static function getNavigationGroup(): string
    {
        return __('navigation.services_management');
    }

    public static function shouldRegisterNavigation(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('view tracking');
    }

    public static function canViewAny(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('view tracking');
    }

    public static function canView($record): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('view tracking');
    }

    public static function canCreate(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage tracking');
    }

    public static function canEdit($record): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage tracking');
    }

    public static function canDelete($record): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage tracking');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(__('sections.fazaa_request_details'))
                    ->schema([
                        Forms\Components\Select::make('fazaa_service_type_id')
                            ->label(__('fields.fazaa_service_type'))
                            ->relationship('fazaaServiceType', 'name_' . app()->getLocale())
                            ->required(),
                        Forms\Components\Select::make('specific_type_id')
                            ->label(__('fields.specific_type'))
                            ->relationship('specificType', 'name_' . app()->getLocale()),
                        Forms\Components\TextInput::make('service_location')
                            ->label(__('fields.service_location')),
                        Forms\Components\Select::make('transportation_type')
                            ->label(__('fields.transportation_type'))
                            ->options([
                                'car' => __('enums.transportation_type.car'),
                                'truck' => __('enums.transportation_type.truck'),
                                'van' => __('enums.transportation_type.van'),
                            ]),
                        Forms\Components\DateTimePicker::make('departure_datetime')
                            ->label(__('fields.departure_datetime'))
                            ->required(),
                        Forms\Components\DateTimePicker::make('arrival_datetime')
                            ->label(__('fields.arrival_datetime')),
                        Forms\Components\TextInput::make('shipment_volume')
                            ->label(__('fields.shipment_volume'))
                            ->numeric(),
                        Forms\Components\TextInput::make('price')
                            ->label(__('fields.price'))
                            ->numeric()
                            ->required(),
                    ]),
                Forms\Components\Section::make(__('sections.location_details'))
                    ->schema([
                        Forms\Components\Select::make('from_city_id')
                            ->label(__('fields.from_city'))
                            ->relationship('fromCity', 'name_' . app()->getLocale()),
                        Forms\Components\Select::make('to_city_id')
                            ->label(__('fields.to_city'))
                            ->relationship('toCity', 'name_' . app()->getLocale()),
                        Forms\Components\TextInput::make('from_location_lat')
                            ->label(__('fields.from_location_lat'))
                            ->numeric(),
                        Forms\Components\TextInput::make('from_location_lng')
                            ->label(__('fields.from_location_lng'))
                            ->numeric(),
                        Forms\Components\TextInput::make('to_location_lat')
                            ->label(__('fields.to_location_lat'))
                            ->numeric(),
                        Forms\Components\TextInput::make('to_location_lng')
                            ->label(__('fields.to_location_lng'))
                            ->numeric(),
                    ]),
                Forms\Components\Section::make(__('sections.additional_details'))
                    ->schema([
                        Forms\Components\Select::make('user_id')
                            ->label(__('fields.user'))
                            ->relationship('user', 'name')
                            ->required(),
                        Forms\Components\Textarea::make('note')
                            ->label(__('fields.note'))
                            ->rows(3),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label(__('fields.id'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.name')
                    ->label(__('fields.user'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fazaaServiceType.name_' . app()->getLocale())
                    ->label(__('fields.fazaa_service_type'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('specificType.name_' . app()->getLocale())
                    ->label(__('fields.specific_type'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fromCity.name_' . app()->getLocale())
                    ->label(__('fields.from_city'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('toCity.name_' . app()->getLocale())
                    ->label(__('fields.to_city'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('departure_datetime')
                    ->label(__('fields.departure_datetime'))
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('price')
                    ->label(__('fields.price'))
                    ->money('SAR')
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->label(__('fields.status'))
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'active' => 'success',
                        'completed' => 'info',
                        'cancelled' => 'danger',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('fields.created_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('fazaa_service_type_id')
                    ->label(__('fields.fazaa_service_type'))
                    ->relationship('fazaaServiceType', 'name_' . app()->getLocale()),
                Tables\Filters\SelectFilter::make('specific_type_id')
                    ->label(__('fields.specific_type'))
                    ->relationship('specificType', 'name_' . app()->getLocale()),
                Tables\Filters\SelectFilter::make('from_city_id')
                    ->label(__('fields.from_city'))
                    ->relationship('fromCity', 'name_' . app()->getLocale()),
                Tables\Filters\SelectFilter::make('to_city_id')
                    ->label(__('fields.to_city'))
                    ->relationship('toCity', 'name_' . app()->getLocale()),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(function () {
                        $admin = auth('admin')->user();
                        /** @var \App\Models\Admin $admin */
                        return $admin && $admin->can('manage tracking');
                    }),
                Tables\Actions\DeleteAction::make()
                    ->visible(function () {
                        $admin = auth('admin')->user();
                        /** @var \App\Models\Admin $admin */
                        return $admin && $admin->can('manage tracking');
                    }),
                ...self::getReadTrackingTableActions(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(function () {
                            $admin = auth('admin')->user();
                            /** @var \App\Models\Admin $admin */
                            return $admin && $admin->can('manage tracking');
                        }),
                    ...self::getReadTrackingTableBulkActions(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFazaaServices::route('/'),
            'create' => Pages\CreateFazaaService::route('/create'),
            'view' => Pages\ViewFazaaService::route('/{record}'),
            'edit' => Pages\EditFazaaService::route('/{record}/edit'),
        ];
    }
}
