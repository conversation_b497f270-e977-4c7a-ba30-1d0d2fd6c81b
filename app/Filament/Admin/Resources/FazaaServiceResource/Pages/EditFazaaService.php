<?php

namespace App\Filament\Admin\Resources\FazaaServiceResource\Pages;

use App\Filament\Admin\Resources\FazaaServiceResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditFazaaService extends EditRecord
{
    protected static string $resource = FazaaServiceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
