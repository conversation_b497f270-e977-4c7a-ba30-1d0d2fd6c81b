<?php

namespace App\Filament\Admin\Resources\FazaaServiceResource\Pages;

use App\Filament\Admin\Resources\FazaaServiceResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListFazaaServices extends ListRecords
{
    protected static string $resource = FazaaServiceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
