<?php

namespace App\Filament\Admin\Resources\FazaaServiceResource\Pages;

use App\Filament\Admin\Resources\FazaaServiceResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewFazaaService extends ViewRecord
{
    protected static string $resource = FazaaServiceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
