<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\RatingResource\Pages;
use App\Models\Rating;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class RatingResource extends Resource
{
    protected static ?string $model = Rating::class;

    protected static ?string $navigationIcon = 'heroicon-o-star';

    protected static ?int $navigationSort = 52;

    public static function getNavigationLabel(): string
    {
        return __('navigation.ratings');
    }

    public static function getModelLabel(): string
    {
        return __('models.rating');
    }

    public static function getPluralModelLabel(): string
    {
        return __('models.ratings');
    }

    public static function shouldRegisterNavigation(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('view ratings');
    }

    public static function canViewAny(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('view ratings');
    }

    public static function canView($record): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('view ratings');
    }

    public static function canCreate(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage ratings');
    }

    public static function canEdit($record): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage ratings');
    }

    public static function canDelete($record): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage ratings');
    }

    public static function canDeleteAny(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage ratings');
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('driver.picture')
                    ->label(__('fields.service_owner'))
                    ->circular(),
                Tables\Columns\TextColumn::make('driver.name')
                    ->label(__('fields.service_owner'))
                    ->searchable(),
                Tables\Columns\ImageColumn::make('user.picture')
                    ->label(__('fields.rating_owner'))
                    ->circular(),
                Tables\Columns\TextColumn::make('user.name')
                    ->label(__('fields.rating_owner'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('tripServiceBooking.service_type')
                    ->label(__('fields.service_type')),
                Tables\Columns\TextColumn::make('rating')
                    ->label(__('fields.rating'))
                    ->alignCenter()
                    ->formatStateUsing(function ($state) {
                        $stars = '';
                        $fullStars = floor($state);
                        $emptyStars = 5 - ceil($state);
                        $halfStar = ($state - $fullStars) >= 0.5;

                        for ($i = 0; $i < $fullStars; $i++) {
                            $stars .= '★';
                        }

                        if ($halfStar) {
                            $stars .= '★';
                            $emptyStars--;
                        }

                        for ($i = 0; $i < $emptyStars; $i++) {
                            $stars .= '☆';
                        }

                        return $stars;
                    }),
                Tables\Columns\TextColumn::make('comment')
                    ->label(__('fields.comment'))
                    ->limit(30),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('fields.created_at'))
                    ->dateTime('d M Y')
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('status')
                    ->label(__('fields.status'))
                    ->getStateUsing(fn($record) => $record->deleted_at !== null ? 'deleted' : 'available')
                    ->colors([
                        'danger' => 'deleted',
                        'success' => 'available',
                    ])
                    ->formatStateUsing(fn(string $state) => $state === 'deleted' ? __('fields.deleted') : __('fields.available'))
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->iconButton(),
                Tables\Actions\EditAction::make()
                    ->iconButton(),
                Tables\Actions\DeleteAction::make()
                    ->iconButton(),
                Tables\Actions\RestoreAction::make()
                    ->iconButton(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRatings::route('/'),
            'edit' => Pages\EditRating::route('/{record}/edit'),
            'view' => Pages\ViewRating::route('/{record}'),
        ];
    }
}
