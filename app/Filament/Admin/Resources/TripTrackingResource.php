<?php

namespace App\Filament\Admin\Resources;

use App\Enums\Travel\TripStatusEnum;
use App\Filament\Admin\Resources\TripTrackingResource\Pages;
use App\Models\Service\TripService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use App\Traits\FilamentHasReadTrackingResource;

class TripTrackingResource extends Resource
{
    use FilamentHasReadTrackingResource;

    protected static ?string $model = TripService::class;

    protected static ?string $navigationIcon = 'heroicon-o-map-pin';

    protected static ?int $navigationSort = 20;

    public static function getNavigationLabel(): string
    {
        return __('navigation.trip_tracking');
    }

    public static function getModelLabel(): string
    {
        return __('models.trip_tracking');
    }

    public static function getPluralModelLabel(): string
    {
        return __('models.trip_trackings');
    }

    public static function getNavigationGroup(): string
    {
        return __('navigation.tracking_management');
    }

    public static function shouldRegisterNavigation(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('view tracking');
    }

    public static function canViewAny(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('view tracking');
    }

    public static function canView($record): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('view tracking');
    }

    public static function canCreate(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage tracking');
    }

    public static function canEdit($record): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage tracking');
    }

    public static function canDelete($record): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage tracking');
    }

    public static function canDeleteAny(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage tracking');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(__('sections.tracking_details'))
                    ->schema([
                        Forms\Components\TextInput::make('id')
                            ->label(__('fields.tracking_number'))
                            ->disabled(),
                        Forms\Components\Select::make('user_id')
                            ->relationship('user', 'name')
                            ->searchable()
                            ->preload()
                            ->label(__('fields.service_provider'))
                            ->disabled(),
                        Forms\Components\DateTimePicker::make('departure_datetime')
                            ->label(__('fields.departure_datetime'))
                            ->disabled(),
                        Forms\Components\DateTimePicker::make('arrival_datetime')
                            ->label(__('fields.arrival_datetime'))
                            ->disabled(),
                    ]),
                Forms\Components\Section::make(__('sections.location_details'))
                    ->schema([
                        Forms\Components\Select::make('from_city_id')
                            ->relationship('fromCity', 'name_ar')
                            ->searchable()
                            ->preload()
                            ->label(__('fields.from_city'))
                            ->disabled(),
                        Forms\Components\Select::make('to_city_id')
                            ->relationship('toCity', 'name_ar')
                            ->searchable()
                            ->preload()
                            ->label(__('fields.to_city'))
                            ->disabled(),
                        Forms\Components\TextInput::make('from_location_lat')
                            ->label(__('fields.from_latitude'))
                            ->numeric()
                            ->disabled(),
                        Forms\Components\TextInput::make('from_location_lng')
                            ->label(__('fields.from_longitude'))
                            ->numeric()
                            ->disabled(),
                        Forms\Components\TextInput::make('to_location_lat')
                            ->label(__('fields.to_latitude'))
                            ->numeric()
                            ->disabled(),
                        Forms\Components\TextInput::make('to_location_lng')
                            ->label(__('fields.to_longitude'))
                            ->numeric()
                            ->disabled(),
                    ]),
                Forms\Components\Section::make(__('sections.status_information'))
                    ->schema([
                        Forms\Components\DateTimePicker::make('cancelled_at')
                            ->label(__('fields.cancelled_at'))
                            ->disabled(),
                        Forms\Components\TextInput::make('cancellation_reason')
                            ->label(__('fields.cancellation_reason'))
                            ->disabled(),
                        Forms\Components\Textarea::make('cancellation_note')
                            ->label(__('fields.cancellation_note'))
                            ->disabled(),
                        Forms\Components\DateTimePicker::make('attendance_confirmed_at')
                            ->label(__('fields.attendance_confirmed_at'))
                            ->disabled(),
                        Forms\Components\DateTimePicker::make('delayed_at')
                            ->label(__('fields.delayed_at'))
                            ->disabled(),
                        Forms\Components\TextInput::make('delay_reason')
                            ->label(__('fields.delay_reason'))
                            ->disabled(),
                        Forms\Components\Textarea::make('delay_note')
                            ->label(__('fields.delay_note'))
                            ->disabled(),
                        ...self::getReadTrackingFormFields(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->searchable()
                    ->label(__('fields.tracking')),
                Tables\Columns\TextColumn::make('user.name')
                    ->searchable()
                    ->sortable()
                    ->label(__('fields.service_provider')),
                Tables\Columns\TextColumn::make('fromCity.name_ar')
                    ->label(__('fields.current_location')),
                Tables\Columns\TextColumn::make('toCity.name_ar')
                    ->label(__('fields.destination')),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->formatStateUsing(function ($state) {
                        if ($state === 'active') {
                            return __('fields.active');
                        } elseif ($state === 'completed') {
                            return __('fields.completed');
                        } elseif ($state === 'cancelled') {
                            return __('fields.cancelled');
                        }
                        return $state;
                    })
                    ->color(fn(string $state): string => match ($state) {
                        'active' => 'primary',
                        'completed' => 'success',
                        'cancelled' => 'danger',
                        default => 'secondary',
                    })
                    ->label(__('fields.status')),
                Tables\Columns\ViewColumn::make('trip_progress')
                    ->view('filament.tables.columns.trip-progress')
                    ->label(__('fields.remaining_time_to_arrival')),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label(__('fields.created_at')),
                ...self::getReadTrackingTableColumns(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'active' => __('fields.active'),
                        'completed' => __('fields.completed'),
                    ])
                    ->label(__('fields.status'))
                    ->query(function (Builder $query, array $data) {
                        return match ($data['value']) {
                            'active' => $query->where('departure_datetime', '>', Carbon::now())->whereNull('cancelled_at'),
                            'completed' => $query->where('departure_datetime', '<', Carbon::now())->whereNull('cancelled_at'),
                            default => $query,
                        };
                    }),
                Tables\Filters\SelectFilter::make('from_city_id')
                    ->relationship('fromCity', 'name_ar')
                    ->label(__('fields.from_city')),
                Tables\Filters\SelectFilter::make('to_city_id')
                    ->relationship('toCity', 'name_ar')
                    ->label(__('fields.to_city')),
                ...self::getReadTrackingTableFilters(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                ...self::getReadTrackingTableActions(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    ...self::getReadTrackingTableBulkActions(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTripTrackings::route('/'),
            'view' => Pages\ViewTripTracking::route('/{record}'),
            'edit' => Pages\EditTripTracking::route('/{record}/edit'),
        ];
    }
}
