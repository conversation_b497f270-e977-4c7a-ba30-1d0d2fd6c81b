<?php

namespace App\Filament\Admin\Resources\TripServiceResource\Pages;

use App\Filament\Admin\Resources\TripServiceResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListTripServices extends ListRecords
{
    protected static string $resource = TripServiceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
