<?php

namespace App\Filament\Admin\Resources\ShippingServiceResource\Pages;

use App\Filament\Admin\Resources\ShippingServiceResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewShippingService extends ViewRecord
{
    protected static string $resource = ShippingServiceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
