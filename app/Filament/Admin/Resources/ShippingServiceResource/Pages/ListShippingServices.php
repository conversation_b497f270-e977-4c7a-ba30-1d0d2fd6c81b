<?php

namespace App\Filament\Admin\Resources\ShippingServiceResource\Pages;

use App\Filament\Admin\Resources\ShippingServiceResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListShippingServices extends ListRecords
{
    protected static string $resource = ShippingServiceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
