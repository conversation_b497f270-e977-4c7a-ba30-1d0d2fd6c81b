<?php

namespace App\Filament\Admin\Resources\ShippingRequestResource\Pages;

use App\Filament\Admin\Resources\ShippingRequestResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListShippingRequests extends ListRecords
{
    protected static string $resource = ShippingRequestResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
