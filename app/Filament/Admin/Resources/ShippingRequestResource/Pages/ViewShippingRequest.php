<?php

namespace App\Filament\Admin\Resources\ShippingRequestResource\Pages;

use App\Filament\Admin\Resources\ShippingRequestResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewShippingRequest extends ViewRecord
{
    protected static string $resource = ShippingRequestResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
