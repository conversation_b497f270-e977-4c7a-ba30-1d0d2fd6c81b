<?php

namespace App\Filament\Admin\Resources\TripOfferResource\Pages;

use App\Filament\Admin\Resources\TripOfferResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListTripOffers extends ListRecords
{
    protected static string $resource = TripOfferResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
