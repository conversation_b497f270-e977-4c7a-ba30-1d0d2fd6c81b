<?php

namespace App\Filament\Admin\Resources\TripOfferResource\Pages;

use App\Filament\Admin\Resources\TripOfferResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewTripOffer extends ViewRecord
{
    protected static string $resource = TripOfferResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
