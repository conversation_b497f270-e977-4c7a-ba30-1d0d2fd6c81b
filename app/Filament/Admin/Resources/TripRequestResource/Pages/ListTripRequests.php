<?php

namespace App\Filament\Admin\Resources\TripRequestResource\Pages;

use App\Filament\Admin\Resources\TripRequestResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListTripRequests extends ListRecords
{
    protected static string $resource = TripRequestResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
