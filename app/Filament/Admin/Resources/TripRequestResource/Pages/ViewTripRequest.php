<?php

namespace App\Filament\Admin\Resources\TripRequestResource\Pages;

use App\Filament\Admin\Resources\TripRequestResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewTripRequest extends ViewRecord
{
    protected static string $resource = TripRequestResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
