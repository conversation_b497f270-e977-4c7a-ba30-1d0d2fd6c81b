<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\FazaaServiceBookingResource\Pages;
use App\Models\Booking\FazaaServiceBooking;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use App\Traits\FilamentHasReadTrackingResource;

class FazaaServiceBookingResource extends Resource
{
    use FilamentHasReadTrackingResource;

    protected static ?string $model = FazaaServiceBooking::class;

    protected static ?string $navigationIcon = 'heroicon-o-calendar';

    protected static ?int $navigationSort = 32;

    public static function getNavigationLabel(): string
    {
        return __('navigation.fazaa_bookings');
    }

    public static function getModelLabel(): string
    {
        return __('models.fazaa_booking');
    }

    public static function getPluralModelLabel(): string
    {
        return __('models.fazaa_bookings');
    }

    public static function getNavigationGroup(): string
    {
        return __('navigation.bookings_management');
    }

    public static function shouldRegisterNavigation(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('view bookings');
    }

    public static function canCreate(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage bookings');
    }

    public static function canEdit($record): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage bookings');
    }

    public static function canDelete($record): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage bookings');
    }

    public static function canView($record): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('view bookings');
    }

    public static function canViewAny(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('view bookings');
    }

    public static function canDeleteAny(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage bookings');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(__('sections.booking_details'))
                    ->schema([
                        Forms\Components\Select::make('fazaa_service_id')
                            ->relationship('fazaaService', 'id')
                            ->searchable()
                            ->preload()
                            ->label(__('fields.fazaa_service')),
                        Forms\Components\Select::make('user_id')
                            ->relationship('user', 'name')
                            ->searchable()
                            ->preload()
                            ->label(__('fields.user')),
                        Forms\Components\TextInput::make('price')
                            ->numeric()
                            ->label(__('fields.price')),
                        Forms\Components\DateTimePicker::make('created_at')
                            ->label(__('fields.booking_date')),
                        Forms\Components\Textarea::make('note')
                            ->label(__('fields.note')),
                    ]),
                Forms\Components\Section::make(__('sections.status_information'))
                    ->schema([
                        ...self::getReadTrackingFormFields(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->searchable()
                    ->sortable()
                    ->label(__('fields.id')),
                Tables\Columns\TextColumn::make('fazaaService.id')
                    ->label(__('fields.fazaa_service')),
                Tables\Columns\TextColumn::make('user.name')
                    ->searchable()
                    ->sortable()
                    ->label(__('fields.user')),
                Tables\Columns\TextColumn::make('price')
                    ->label(__('fields.price')),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->label(__('fields.booking_date')),
                ...self::getReadTrackingTableColumns(),
            ])
            ->filters([
                ...self::getReadTrackingTableFilters(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(function () {
                        $admin = auth('admin')->user();
                        /** @var \App\Models\Admin $admin */
                        return $admin && $admin->can('manage bookings');
                    }),
                Tables\Actions\DeleteAction::make()
                    ->visible(function () {
                        $admin = auth('admin')->user();
                        /** @var \App\Models\Admin $admin */
                        return $admin && $admin->can('manage bookings');
                    }),
                ...self::getReadTrackingTableActions(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(function () {
                            $admin = auth('admin')->user();
                            /** @var \App\Models\Admin $admin */
                            return $admin && $admin->can('manage bookings');
                        }),
                    ...self::getReadTrackingTableBulkActions(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFazaaServiceBookings::route('/'),
            'create' => Pages\CreateFazaaServiceBooking::route('/create'),
            'view' => Pages\ViewFazaaServiceBooking::route('/{record}'),
            'edit' => Pages\EditFazaaServiceBooking::route('/{record}/edit'),
        ];
    }
}
