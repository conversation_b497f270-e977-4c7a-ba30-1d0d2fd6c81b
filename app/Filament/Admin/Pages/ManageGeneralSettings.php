<?php

namespace App\Filament\Admin\Pages;

use App\Settings\GeneralSettings;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Pages\SettingsPage;

class ManageGeneralSettings extends SettingsPage
{
    protected static ?string $navigationIcon = 'heroicon-o-cog';
    protected static string $settings = GeneralSettings::class;

    public static function getNavigationLabel(): string
    {
        return 'إدارة الإعدادات';
    }

    public function getTitle(): string
    {
        return 'إعدادات محتوى الشروط والأحكام';
    }

    public static function shouldRegisterNavigation(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage settings');
    }

    public static function canAccess(): bool
    {
        $admin = auth('admin')->user();
        /**
         * @var \App\Models\Admin $admin
         */
        return $admin && $admin->can('manage settings');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                RichEditor::make('terms_and_conditions')
                    ->label('شروط وأحكام الاستخدام')
                    ->required()
                    ->toolbarButtons([
                        'blockquote',
                        'bold',
                        'bulletList',
                        'codeBlock',
                        'h2',
                        'h3',
                        'italic',
                        'link',
                        'orderedList',
                        'redo',
                        'strike',
                        'underline',
                        'undo',
                    ])
                    ->columnSpanFull(),

                RichEditor::make('privacy_policy')
                    ->label('سياسات الخصوصية')
                    ->required()
                    ->toolbarButtons([
                        'blockquote',
                        'bold',
                        'bulletList',
                        'codeBlock',
                        'h2',
                        'h3',
                        'italic',
                        'link',
                        'orderedList',
                        'redo',
                        'strike',
                        'underline',
                        'undo',
                    ])
                    ->columnSpanFull(),

                RichEditor::make('blocking_policy')
                    ->label('سياسات الحظر')
                    ->required()
                    ->toolbarButtons([
                        'blockquote',
                        'bold',
                        'bulletList',
                        'codeBlock',
                        'h2',
                        'h3',
                        'italic',
                        'link',
                        'orderedList',
                        'redo',
                        'strike',
                        'underline',
                        'undo',
                    ])
                    ->columnSpanFull(),

                FileUpload::make('app_logo')
                    ->label('شعار التطبيق')
                    ->image()
                    ->directory('app-logo')
                    ->required()
                    ->columnSpanFull(),

                TextInput::make('support_email')
                    ->label('الدعم')
                    ->required()
                    ->columnSpanFull(),

                TextInput::make('help_email')
                    ->label('المساعدة')
                    ->required()
                    ->columnSpanFull(),

                TextInput::make('official_website')
                    ->label('الرسمي')
                    ->required()
                    ->columnSpanFull(),
            ]);
    }
}
