<?php

namespace App\Traits;

use App\Models\Dashboard\Admin;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;

trait HasReadTracking
{
    /**
     * Boot the trait
     */
    public static function bootHasReadTracking()
    {
        static::addGlobalScope('withReadByRelation', function ($query) {
            return $query->with('readBy');
        });
    }

    /**
     * Get the admin who read this record
     */
    public function readBy(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'read_by_id');
    }

    /**
     * Mark the record as read
     */
    public function markAsRead(Admin $admin = null)
    {
        $adminId = $admin ? $admin->id : auth('admin')->user()->id;

        return $this->update([
            'read_at' => now(),
            'read_by_id' => $adminId,
        ]);
    }

    /**
     * Mark the record as unread
     */
    public function markAsUnread()
    {
        return $this->update([
            'read_at' => null,
            'read_by_id' => null,
        ]);
    }

    /**
     * Check if the record is read
     */
    public function isRead(): bool
    {
        return $this->read_at !== null;
    }

    /**
     * Scope a query to only include unread records
     */
    public function scopeUnread(Builder $query): Builder
    {
        return $query->whereNull('read_at');
    }
}
