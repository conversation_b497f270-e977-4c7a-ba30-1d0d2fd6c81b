<?php

namespace App\Traits;

use Filament\Forms;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;

trait FilamentHasReadTrackingResource
{
    /**
     * Get the read tracking fields for form
     */
    public static function getReadTrackingFormFields(): array
    {
        return [
            Forms\Components\DateTimePicker::make('read_at')
                ->label(__('fields.read_at'))
                ->disabled(),
            Forms\Components\Select::make('read_by_id')
                ->relationship('readBy', 'name')
                ->label(__('fields.read_by'))
                ->disabled(),
        ];
    }

    /**
     * Get the read tracking columns for table
     */
    public static function getReadTrackingTableColumns(): array
    {
        return [
            Tables\Columns\IconColumn::make('read_at')
                ->label(__('fields.read_status'))
                ->boolean()
                ->trueIcon('heroicon-o-check-circle')
                ->falseIcon('heroicon-o-x-circle')
                ->trueColor('success')
                ->falseColor('danger')
                ->getStateUsing(fn ($record): bool => $record->read_at !== null),
            Tables\Columns\TextColumn::make('read_at')
                ->dateTime()
                ->sortable()
                ->toggleable(isToggledHiddenByDefault: true)
                ->label(__('fields.read_at')),
            Tables\Columns\TextColumn::make('readBy.name')
                ->sortable()
                ->toggleable(isToggledHiddenByDefault: true)
                ->label(__('fields.read_by')),
        ];
    }

    /**
     * Get the read tracking filters for table
     */
    public static function getReadTrackingTableFilters(): array
    {
        return [
            Tables\Filters\Filter::make('read')
                ->label(__('fields.read_status'))
                ->query(fn (Builder $query): Builder => $query->whereNotNull('read_at')),
            Tables\Filters\Filter::make('unread')
                ->label(__('fields.unread_status'))
                ->query(fn (Builder $query): Builder => $query->whereNull('read_at')),
        ];
    }

    /**
     * Get the read tracking actions for table
     */
    public static function getReadTrackingTableActions(): array
    {
        return [
            Tables\Actions\Action::make('mark_as_read')
                ->label(__('actions.mark_as_read'))
                ->icon('heroicon-o-check')
                ->visible(fn ($record) => $record->read_at === null)
                ->action(function ($record) {
                    $record->markAsRead();
                }),
            Tables\Actions\Action::make('mark_as_unread')
                ->label(__('actions.mark_as_unread'))
                ->icon('heroicon-o-x-mark')
                ->visible(fn ($record) => $record->read_at !== null)
                ->action(function ($record) {
                    $record->markAsUnread();
                }),
        ];
    }

    /**
     * Get the read tracking bulk actions for table
     */
    public static function getReadTrackingTableBulkActions(): array
    {
        return [
            Tables\Actions\BulkAction::make('mark_as_read')
                ->label(__('actions.mark_as_read'))
                ->icon('heroicon-o-check')
                ->action(function ($records) {
                    $records->each(fn ($record) => $record->markAsRead());
                }),
            Tables\Actions\BulkAction::make('mark_as_unread')
                ->label(__('actions.mark_as_unread'))
                ->icon('heroicon-o-x-mark')
                ->action(function ($records) {
                    $records->each(fn ($record) => $record->markAsUnread());
                }),
        ];
    }

    /**
     * Get the navigation badge for unread items
     */
    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::unread()->count();
    }
}
