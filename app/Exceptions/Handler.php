<?php

declare(strict_types=1);

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

final class Handler extends ExceptionHandler
{
    use ExceptionsHandler;

    /**
     * A list of the exception types that are not reported.
     *
     * @var array<int, class-string<Throwable>>
     */
    protected $dontReport = [
        LogicalException::class,
    ];


    public function render($request, Throwable $e): \Illuminate\Http\Response|JsonResponse|Response
    {
        return match (app()->environment()) {
            'testing', 'production' => $this->convertExceptionToJsonResponse($request, $e),
            default => parent::render($request, $e)
        };
    }
}
