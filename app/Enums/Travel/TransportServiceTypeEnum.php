<?php

namespace App\Enums\Travel;

use ArchTech\Enums\InvokableCases;
use ArchTech\Enums\Values;

/**
 * @method static self TRIP_SERVICE()
 * @method static self SHIPPING_SERVICE()
 * @method static self FAZAA_SERVICE()
 * @method static self TRIP_REQUEST()
 */
enum TransportServiceTypeEnum: string
{
    use InvokableCases, Values;

    case TRIP_SERVICE = 'trip_service';

    case SHIPPING_SERVICE = 'shipping_service';

    case FAZAA_SERVICE = 'fazaa_service';

    case TRIP_REQUEST = 'trip_request';

    case SHIPPING_REQUEST = 'shipping_request';

    case FAZAA_REQUEST = 'fazaa_request';
}

