<?php

namespace App\Enums\Travel;

use ArchTech\Enums\InvokableCases;
use ArchTech\Enums\Values;

/**
 * @method static self RAIN()
 * @method static self DRIZZLE()
 * @method static self SNOW()
 * @method static self FOGGY()
 * @method static self CLOUDS()
 * @method static self TORNADO()
 * @method static self STORMY()
 * @method static self CLEAR()
 */
enum WeatherStatuses: string
{
    use InvokableCases, Values;

    case RAIN = 'rain';

    case DRIZZLE = 'drizzle';

    case SNOW = 'snow';

    case FOGGY = 'foggy';

    case CLOUDS = 'clouds';

    case TORNADO = 'tornado';

    case STORMY = 'stormy';

    case CLEAR = 'clear';
}
