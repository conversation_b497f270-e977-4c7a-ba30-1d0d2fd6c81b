<?php

namespace App\Enums\Notifications;

use ArchTech\Enums\InvokableCases;
use ArchTech\Enums\Values;

/**
 * @method static self TRIP_SERVICE_IS_SOON()
 * @method static self TRIP_SERVICE_IS_IMMEDIATE()
 * @method static self TRIP_SERVICE_STARTED()
 * @method static self TRIP_SERVICE_WAITING()
 * @method static self TRIP_SERVICE_ARRIVED()
 * @method static self DRIVER_TRIP_SERVICE_ARRIVED()
 * @method static self NEW_TRIP_SERVICE_BOOKING()
 * @method static self TRIP_SERVICE_DELAYED()
 * @method static self TRIP_SERVICE_CANCELLED()
 * @method static self TRIP_SERVICE_BOOKING_ACCEPTED()
 * @method static self TRIP_SERVICE_BOOKING_REJECTED()
 * @method static self TRIP_SERVICE_BOOKING_CANCELLED_BY_OWNER()
 * @method static self TRIP_SERVICE_BOOKING_CANCELLED()
 * @method static self TRIP_REQUEST_DELAYED()
 * @method static self TRIP_REQUEST_CANCELLED()
 * @method static self TRIP_REQUEST_BOOKING_ACCEPTED()
 * @method static self TRIP_REQUEST_BOOKING_REJECTED()
 * @method static self TRIP_REQUEST_BOOKING_CANCELLED_BY_OWNER()
 * @method static self TRIP_REQUEST_BOOKING_CANCELLED()
 * @method static self NEW_TRIP_REQUEST_BOOKING()
 * @method static self TRIP_SERVICE_PAYMENT_REMINDER()
 */
enum NotificationTypeEnum: string
{
    use InvokableCases, Values;

    case TRIP_SERVICE_IS_SOON = 'trip_service_is_soon';
    case TRIP_SERVICE_IS_IMMEDIATE = 'trip_service_is_immediate';
    case TRIP_SERVICE_STARTED = 'trip_service_started';
    case TRIP_SERVICE_WAITING = 'trip_service_waiting';
    case TRIP_SERVICE_ARRIVED = 'trip_service_arrived';
    case DRIVER_TRIP_SERVICE_ARRIVED = 'driver_trip_service_arrived';
    case NEW_TRIP_SERVICE_BOOKING = 'new_trip_service_booking';
    case TRIP_SERVICE_DELAYED = 'trip_service_delayed';
    case TRIP_SERVICE_CANCELLED = 'trip_service_cancelled';
    case TRIP_SERVICE_BOOKING_ACCEPTED = 'trip_service_booking_accepted';
    case TRIP_SERVICE_BOOKING_REJECTED = 'trip_service_booking_rejected';
    case TRIP_SERVICE_BOOKING_CANCELLED_BY_OWNER = 'trip_service_booking_cancelled_by_owner';
    case TRIP_SERVICE_BOOKING_CANCELLED = 'trip_service_booking_cancelled';
    case NEW_TRIP_REQUEST_BOOKING = 'new_trip_request_booking';
    case TRIP_REQUEST_DELAYED = 'trip_request_delayed';
    case TRIP_REQUEST_CANCELLED = 'trip_request_cancelled';
    case TRIP_REQUEST_BOOKING_ACCEPTED = 'trip_request_booking_accepted';
    case TRIP_REQUEST_BOOKING_REJECTED = 'trip_request_booking_rejected';
    case TRIP_REQUEST_BOOKING_CANCELLED = 'trip_request_booking_cancelled';
    case TRIP_REQUEST_BOOKING_CANCELLED_BY_OWNER = 'trip_request_booking_cancelled_by_owner';
    case TRIP_SERVICE_PAYMENT_REMINDER = 'trip_service_payment_reminder';
}
