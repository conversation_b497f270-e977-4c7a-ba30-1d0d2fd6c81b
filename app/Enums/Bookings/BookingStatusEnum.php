<?php

namespace App\Enums\Bookings;

use ArchTech\Enums\InvokableCases;
use ArchTech\Enums\Values;

/**
 * @method static self PENDING()
 * @method static self ACCEPTED()
 * @method static self REJECTED()
 * @method static self CANCELLED()
 * @method static self CANCELLED_BY_OWNER()
 */
enum BookingStatusEnum: string
{
    use InvokableCases, Values;

    case PENDING = 'pending';
    case ACCEPTED = 'accepted';
    case REJECTED = 'rejected';
    case CANCELLED = 'cancelled';
    case CANCELLED_BY_OWNER = 'cancelled_by_owner';
}
