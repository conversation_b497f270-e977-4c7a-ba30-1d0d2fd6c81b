<?php

namespace App\Enums\Bookings;

use ArchTech\Enums\InvokableCases;
use ArchTech\Enums\Values;

/**
 * @method static self TRIP_SERVICE()
 * @method static self TRIP_REQUEST()
 * @method static self SHIPPING_SERVICE()
 * @method static self SHIPPING_REQUEST()
 * @method static self FAZAA_SERVICE()
 * @method static self FAZAA_REQUEST()
 */
enum BookingTypeEnum: string
{
    use InvokableCases, Values;

    case TRIP_SERVICE = 'trip_service';
    case TRIP_REQUEST = 'trip_request';
    case SHIPPING_SERVICE = 'shipping_service';
    case SHIPPING_REQUEST = 'shipping_request';
    case FAZAA_SERVICE = 'fazaa_service';
    case FAZAA_REQUEST = 'fazaa_request';
}
