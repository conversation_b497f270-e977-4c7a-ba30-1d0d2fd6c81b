<?php

declare(strict_types=1);

namespace App\Enums\Infrastructure;

use ArchTech\Enums\InvokableCases;
use ArchTech\Enums\Values;

/**
 * @method static self DEFAULT()
 * @method static self EMAILS()
 * @method static self IN_APP_NOTIFICATION()
 * @method static self PUSH_NOTIFICATION()
 * @method static self HIGH()
 */
enum Queues: string
{
    use InvokableCases, Values;

    case DEFAULT = 'default';
    case EMAILS = 'emails';
    case IN_APP_NOTIFICATION = 'in_app_notification';
    case PUSH_NOTIFICATION = 'push_notification';
    case HIGH = 'high';
}
