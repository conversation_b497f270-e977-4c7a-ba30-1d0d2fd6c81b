<?php

namespace App\Notifications;

use App\Enums\Infrastructure\Queues;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class RegisterOtpNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(protected string $otp)
    {
        $this->locale = 'ar';
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(User $notifiable): array
    {
        return [
            'mail',
        ];
    }

    /**
     * Get the notification's delivery queues.
     */
    public function viaQueues(): array
    {
        return [
            'mail' => Queues::EMAILS(),
        ];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject(__('mails.registration_otp.subject', [], $this->locale))
            ->view(
                'mails.users.register_otp',
                [
                    'otp' => $this->otp,
                ]
            );
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }
}
