<?php

namespace App\Notifications;

use App\Channels\DatabaseChannel;
use App\Enums\Infrastructure\Queues;
use App\Enums\Notifications\NotificationTypeEnum;
use App\Models\Service\TripService;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use NotificationChannels\Fcm\FcmChannel;
use NotificationChannels\Fcm\FcmMessage;
use NotificationChannels\Fcm\Resources\Notification as FcmNotification;

class TripServiceWaitingNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        private readonly int $tripServiceId,
        private readonly string $toCityName,
        private readonly int $bookingId,
    ) {}

    public function via(User $notifiable): array
    {
        return [
            DatabaseChannel::class,
            FcmChannel::class,
        ];
    }

    public function viaQueues(): array
    {
        return [
            DatabaseChannel::class => Queues::IN_APP_NOTIFICATION(),
            FcmChannel::class => Queues::PUSH_NOTIFICATION(),
        ];
    }

    public function toDatabase(User $notifiable): array
    {
        return [
            'type' => NotificationTypeEnum::TRIP_SERVICE_WAITING(),
            'title' => $this->getTitle(),
            'content' => $this->getContent(),
            'trip_service_id' => (string) $this->tripServiceId,
            'booking_id' => (string) $this->bookingId,
        ];
    }

    public function toFcm(User $notifiable): FcmMessage
    {
        return (new FcmMessage(notification: new FcmNotification(
            title: $this->getTitle(),
            body: $this->getContent(),
        )))->data([
            'title' => $this->getTitle(),
            'content' => $this->getContent(),
            'trip_service_id' => (string) $this->tripServiceId,
            'booking_id' => (string) $this->bookingId,
            'type' => NotificationTypeEnum::TRIP_SERVICE_WAITING(),
            'booking_id' => $this->bookingId,
        ])->custom([
            'android' => [
                'notification' => [
                    'sound' => 'default',
                ],
                'fcm_options' => [
                    'analytics_label' => 'analytics',
                ],
            ],
            'apns' => [
                'payload' => [
                    'aps' => [
                        'sound' => 'default',
                    ],
                ],
                'fcm_options' => [
                    'analytics_label' => 'analytics_ios',
                ],
            ],
        ]);
    }

    private function getTitle(?string $locale = null): string
    {
        return __('notifications.trip_service_waiting.title', [
            'to' => $this->toCityName,
        ]);
    }

    private function getContent(?string $locale = null): string
    {
        return __('notifications.trip_service_waiting.content');
    }
}
