<?php

namespace App\Notifications;

use App\Channels\DatabaseChannel;
use App\Enums\Infrastructure\Queues;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use NotificationChannels\Fcm\FcmChannel;
use NotificationChannels\Fcm\FcmMessage;
use NotificationChannels\Fcm\Resources\Notification as FcmNotification;

class ExampleNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(User $notifiable): array
    {
        return [
            'mail',
            DatabaseChannel::class,
            FcmChannel::class,
        ];
    }

    public function viaQueues(): array
    {
        return [
            'mail' => Queues::EMAILS(),
            DatabaseChannel::class => Queues::IN_APP_NOTIFICATION(),
            FcmChannel::class => Queues::PUSH_NOTIFICATION(),
        ];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->line('The introduction to the notification.')
            ->action('Notification Action', url('/'))
            ->line('Thank you for using our application!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }

    public function toDatabase(User $notifiable): array
    {
        $data = [
            //
        ];

        return $data;
    }

    public function toFcm(User $notifiable): FcmMessage
    {
        return (new FcmMessage(notification: new FcmNotification(
            title: $this->getTitle(),
            body: $this->getContent(),
        )))->data([
            //
        ])->custom([
            'android' => [
                'notification' => [
                    'sound' => 'default',
                ],
                'fcm_options' => [
                    'analytics_label' => 'analytics',
                ],
            ],
            'apns' => [
                'payload' => [
                    'aps' => [
                        'sound' => 'default',
                    ],
                ],
                'fcm_options' => [
                    'analytics_label' => 'analytics_ios',
                ],
            ],
        ]);
    }

    private function getTitle(?string $locale = null): string
    {
        return '';
    }

    private function getContent(?string $locale = null): string
    {
        return '';
    }
}
