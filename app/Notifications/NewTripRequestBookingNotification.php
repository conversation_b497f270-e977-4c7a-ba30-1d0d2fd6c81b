<?php

namespace App\Notifications;

use App\Channels\DatabaseChannel;
use App\Enums\Infrastructure\Queues;
use App\Enums\Notifications\NotificationTypeEnum;
use App\Models\Booking\TripRequestBooking;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use NotificationChannels\Fcm\FcmChannel;
use NotificationChannels\Fcm\FcmMessage;
use NotificationChannels\Fcm\Resources\Notification as FcmNotification;

class NewTripRequestBookingNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        private readonly TripRequestBooking $booking,
    ) {}

    public function via(User $notifiable): array
    {
        return [
            DatabaseChannel::class,
            FcmChannel::class,
        ];
    }

    public function viaQueues(): array
    {
        return [
            DatabaseChannel::class => Queues::IN_APP_NOTIFICATION(),
            FcmChannel::class => Queues::PUSH_NOTIFICATION(),
        ];
    }

    public function toDatabase(User $notifiable): array
    {
        return [
            'type' => NotificationTypeEnum::NEW_TRIP_REQUEST_BOOKING(),
            'title' => $this->getTitle(),
            'content' => $this->getContent(),
            'trip_request_id' => (string) $this->booking->trip_request_id,
            'booking_id' => (string) $this->booking->id,
        ];
    }

    public function toFcm(User $notifiable): FcmMessage
    {
        return (new FcmMessage(notification: new FcmNotification(
            title: $this->getTitle(),
            body: $this->getContent(),
        )))->data([
            'title' => $this->getTitle(),
            'content' => $this->getContent(),
            'trip_request_id' => (string) $this->booking->trip_request_id,
            'booking_id' => (string) $this->booking->id,
            'type' => NotificationTypeEnum::NEW_TRIP_REQUEST_BOOKING(),
        ])->custom([
            'android' => [
                'notification' => [
                    'sound' => 'default',
                ],
                'fcm_options' => [
                    'analytics_label' => 'analytics',
                ],
            ],
            'apns' => [
                'payload' => [
                    'aps' => [
                        'sound' => 'default',
                    ],
                ],
                'fcm_options' => [
                    'analytics_label' => 'analytics_ios',
                ],
            ],
        ]);
    }

    private function getTitle(): string
    {
        return __('notifications.trip_request.new_booking.title');
    }

    private function getContent(): string
    {
        return __('notifications.trip_request.new_booking.content', [
            'seats' => $this->booking->number_of_seats,
        ]);
    }
}
