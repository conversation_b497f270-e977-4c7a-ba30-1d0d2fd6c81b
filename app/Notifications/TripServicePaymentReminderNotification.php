<?php

namespace App\Notifications;

use App\Channels\DatabaseChannel;
use App\Enums\Infrastructure\Queues;
use App\Enums\Notifications\NotificationTypeEnum;
use App\Models\Service\TripService;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use NotificationChannels\Fcm\FcmChannel;
use NotificationChannels\Fcm\FcmMessage;
use NotificationChannels\Fcm\Resources\Notification as FcmNotification;

class TripServicePaymentReminderNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @param int $tripServiceId
     * @param int $bookingId
     */
    public function __construct(
        private readonly int $tripServiceId,
        private readonly int $bookingId,
        private readonly string $tripServiceFromCityName,
        private readonly string $tripServiceToCityName,
        private readonly string $tripServiceDepartureDatetime,
    ) {}

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(User $notifiable): array
    {
        return [
            DatabaseChannel::class,
            FcmChannel::class,
        ];
    }

    /**
     * Get the queue names for the notification channels.
     *
     * @return array<string, string>
     */
    public function viaQueues(): array
    {
        return [
            DatabaseChannel::class => Queues::IN_APP_NOTIFICATION(),
            FcmChannel::class => Queues::PUSH_NOTIFICATION(),
        ];
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toDatabase(User $notifiable): array
    {
        return [
            'type' => NotificationTypeEnum::TRIP_SERVICE_PAYMENT_REMINDER(),
            'title' => $this->getTitle(),
            'content' => $this->getContent(),
            'trip_service_id' => (string) $this->tripServiceId,
        ];
    }

    /**
     * Get the FCM representation of the notification.
     *
     * @param User $notifiable
     * @return FcmMessage
     */
    public function toFcm(User $notifiable): FcmMessage
    {
        return (new FcmMessage(notification: new FcmNotification(
            title: $this->getTitle(),
            body: $this->getContent(),
        )))->data([
            'title' => $this->getTitle(),
            'content' => $this->getContent(),
            'trip_service_id' => (string) $this->tripServiceId,
            'type' => NotificationTypeEnum::TRIP_SERVICE_PAYMENT_REMINDER(),
        ])->custom([
            'android' => [
                'notification' => [
                    'sound' => 'default',
                ],
                'fcm_options' => [
                    'analytics_label' => 'tripServicePaymentReminder',
                ],
            ],
            'apns' => [
                'payload' => [
                    'aps' => [
                        'sound' => 'default',
                    ],
                ],
                'fcm_options' => [
                    'analytics_label' => 'tripServicePaymentReminder_ios',
                ],
            ],
        ]);
    }

    /**
     * Get the notification title.
     *
     * @return string
     */
    private function getTitle(): string
    {
        return __('notifications.trip_service_payment_reminder.title');
    }

    /**
     * Get the notification content.
     *
     * @return string
     */
    private function getContent(): string
    {
        return __('notifications.trip_service_payment_reminder.content', [
            'from_city' => $this->tripServiceFromCityName,
            'to_city' => $this->tripServiceToCityName,
            'date' => $this->tripServiceDepartureDatetime,
        ]);
    }
}
