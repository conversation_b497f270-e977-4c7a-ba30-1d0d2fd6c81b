<?php

namespace App\Notifications;

use App\Channels\DatabaseChannel;
use App\Enums\Infrastructure\Queues;
use App\Enums\Notifications\NotificationTypeEnum;
use App\Models\Service\TripService;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use NotificationChannels\Fcm\FcmChannel;
use NotificationChannels\Fcm\FcmMessage;
use NotificationChannels\Fcm\Resources\Notification as FcmNotification;

class DriverTripServiceArrivedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        private readonly TripService $tripService,
    ) {}

    public function via(User $notifiable): array
    {
        return [
            DatabaseChannel::class,
            FcmChannel::class,
        ];
    }

    public function viaQueues(): array
    {
        return [
            DatabaseChannel::class => Queues::IN_APP_NOTIFICATION(),
            FcmChannel::class => Queues::PUSH_NOTIFICATION(),
        ];
    }

    public function toDatabase(User $notifiable): array
    {
        return [
            'type' => NotificationTypeEnum::DRIVER_TRIP_SERVICE_ARRIVED(),
            'title' => $this->getTitle(),
            'content' => $this->getContent(),
            'trip_service_id' => (string) $this->tripService->id,
        ];
    }

    public function toFcm(User $notifiable): FcmMessage
    {
        return (new FcmMessage(notification: new FcmNotification(
            title: $this->getTitle(),
            body: $this->getContent(),
        )))->data([
            'title' => $this->getTitle(),
            'content' => $this->getContent(),
            'trip_service_id' => (string) $this->tripService->id,
            'type' => NotificationTypeEnum::DRIVER_TRIP_SERVICE_ARRIVED(),
        ])->custom([
            'android' => [
                'notification' => [
                    'sound' => 'default',
                ],
                'fcm_options' => [
                    'analytics_label' => 'analytics',
                ],
            ],
            'apns' => [
                'payload' => [
                    'aps' => [
                        'sound' => 'default',
                    ],
                ],
                'fcm_options' => [
                    'analytics_label' => 'analytics_ios',
                ],
            ],
        ]);
    }

    private function getTitle(?string $locale = null): string
    {
        return __('notifications.driver_trip_service_arrived.title', [
            'to' => $this->tripService->toCity->name,
        ]);
    }

    private function getContent(?string $locale = null): string
    {
        return __('notifications.driver_trip_service_arrived.content');
    }
}
