<?php

namespace App\Notifications;

use App\Channels\DatabaseChannel;
use App\Enums\Infrastructure\Queues;
use App\Enums\Notifications\NotificationTypeEnum;
use App\Models\Service\TripService;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use NotificationChannels\Fcm\FcmChannel;
use NotificationChannels\Fcm\FcmMessage;
use NotificationChannels\Fcm\Resources\Notification as FcmNotification;

class TripServiceIsSoonNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        private readonly int $tripServiceId,
        private readonly string $fromCityName,
        private readonly string $toCityName,
    ) {
        //
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(User $notifiable): array
    {
        return [
            DatabaseChannel::class,
            FcmChannel::class,
        ];
    }

    public function viaQueues(): array
    {
        return [
            DatabaseChannel::class => Queues::IN_APP_NOTIFICATION(),
            FcmChannel::class => Queues::PUSH_NOTIFICATION(),
        ];
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }

    public function toDatabase(User $notifiable): array
    {
        return [
            'type' => NotificationTypeEnum::TRIP_SERVICE_IS_SOON(),
            'title' => $this->getTitle(),
            'content' => $this->getContent(),
            'trip_service_id' => (string) $this->tripServiceId,
        ];
    }

    public function toFcm(User $notifiable): FcmMessage
    {
        return (new FcmMessage(notification: new FcmNotification(
            title: $this->getTitle(),
            body: $this->getContent(),
        )))->data([
            'title' => $this->getTitle(),
            'content' => $this->getContent(),
            'trip_service_id' => (string) $this->tripServiceId,
            'type' => NotificationTypeEnum::TRIP_SERVICE_IS_SOON(),
        ])->custom([
            'android' => [
                'notification' => [
                    'sound' => 'default',
                ],
                'fcm_options' => [
                    'analytics_label' => 'analytics',
                ],
            ],
            'apns' => [
                'payload' => [
                    'aps' => [
                        'sound' => 'default',
                    ],
                ],
                'fcm_options' => [
                    'analytics_label' => 'analytics_ios',
                ],
            ],
        ]);
    }

    private function getTitle(?string $locale = null): string
    {
        return __('notifications.trip_service_is_soon.title', [
            'from' => $this->fromCityName,
            'to' => $this->toCityName,
        ]);
    }

    private function getContent(?string $locale = null): string
    {
        return __('notifications.trip_service_is_soon.content', [
            'from' => $this->fromCityName,
            'to' => $this->toCityName,
        ]);
    }
}
