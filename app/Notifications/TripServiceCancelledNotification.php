<?php

namespace App\Notifications;

use App\Channels\DatabaseChannel;
use App\Enums\Infrastructure\Queues;
use App\Enums\Notifications\NotificationTypeEnum;
use App\Models\Service\TripService;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use NotificationChannels\Fcm\FcmChannel;
use NotificationChannels\Fcm\FcmMessage;
use NotificationChannels\Fcm\Resources\Notification as FcmNotification;

class TripServiceCancelledNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        private readonly TripService $tripService,
    ) {}

    /**
     * Get the notification's delivery channels.
     */
    public function via(User $notifiable): array
    {
        return [
            'mail',
            DatabaseChannel::class,
            FcmChannel::class,
        ];
    }

    public function viaQueues(): array
    {
        return [
            DatabaseChannel::class => Queues::IN_APP_NOTIFICATION(),
            FcmChannel::class => Queues::PUSH_NOTIFICATION(),
        ];
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }

    public function toDatabase(User $notifiable): array
    {
        return [
            'type' => NotificationTypeEnum::TRIP_SERVICE_CANCELLED(),
            'title' => $this->getTitle(),
            'content' => $this->getContent(),
            'trip_service_id' => (string) $this->tripService->id,
        ];
    }

    public function toFcm(User $notifiable): FcmMessage
    {
        return (new FcmMessage(notification: new FcmNotification(
            title: $this->getTitle(),
            body: $this->getContent(),
        )))->data([
            'trip_service_id' => (string) $this->tripService->id,
            'type' => NotificationTypeEnum::TRIP_SERVICE_CANCELLED(),
        ])->custom([
            'android' => [
                'notification' => [
                    'sound' => 'default',
                ],
                'fcm_options' => [
                    'analytics_label' => 'analytics',
                ],
            ],
            'apns' => [
                'payload' => [
                    'aps' => [
                        'sound' => 'default',
                    ],
                ],
                'fcm_options' => [
                    'analytics_label' => 'analytics_ios',
                ],
            ],
        ]);
    }

    private function getTitle(?string $locale = null): string
    {
        return __('notifications.trip_service_cancelled.title');
    }

    private function getContent(?string $locale = null): string
    {
        return __('notifications.trip_service_cancelled.content');
    }
}
