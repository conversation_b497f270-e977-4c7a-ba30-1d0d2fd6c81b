<?php

namespace App\Settings;

use Spatie\LaravelSettings\Settings;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\TextInput;

class GeneralSettings extends Settings
{
    public string $terms_and_conditions;
    public string $privacy_policy;
    public string $blocking_policy;
    public string $app_logo;
    public string $support_email;
    public string $help_email;
    public string $official_website;

    public static function group(): string
    {
        return 'general';
    }

    public static function getFormSchema(): array
    {
        return [
            RichEditor::make('terms_and_conditions')
                ->label('شروط وأحكام الاستخدام')
                ->required()
                ->toolbarButtons([
                    'blockquote',
                    'bold',
                    'bulletList',
                    'codeBlock',
                    'h2',
                    'h3',
                    'italic',
                    'link',
                    'orderedList',
                    'redo',
                    'strike',
                    'underline',
                    'undo',
                ])
                ->columnSpanFull(),

            RichEditor::make('privacy_policy')
                ->label('سياسات الخصوصية')
                ->required()
                ->toolbarButtons([
                    'blockquote',
                    'bold',
                    'bulletList',
                    'codeBlock',
                    'h2',
                    'h3',
                    'italic',
                    'link',
                    'orderedList',
                    'redo',
                    'strike',
                    'underline',
                    'undo',
                ])
                ->columnSpanFull(),

            RichEditor::make('blocking_policy')
                ->label('سياسات الحظر')
                ->required()
                ->toolbarButtons([
                    'blockquote',
                    'bold',
                    'bulletList',
                    'codeBlock',
                    'h2',
                    'h3',
                    'italic',
                    'link',
                    'orderedList',
                    'redo',
                    'strike',
                    'underline',
                    'undo',
                ])
                ->columnSpanFull(),

            FileUpload::make('app_logo')
                ->label('شعار التطبيق')
                ->image()
                ->directory('app-logo')
                ->required()
                ->columnSpanFull(),

            TextInput::make('support_email')
                ->label('الدعم')
                ->required()
                ->columnSpanFull(),

            TextInput::make('help_email')
                ->label('المساعدة')
                ->required()
                ->columnSpanFull(),

            TextInput::make('official_website')
                ->label('الرسمي')
                ->required()
                ->columnSpanFull(),
        ];
    }
}
