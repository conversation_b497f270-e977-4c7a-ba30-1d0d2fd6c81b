<?php

declare(strict_types=1);

namespace App\Channels;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Channels\DatabaseChannel as IlluminateDatabaseChannel;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use RuntimeException;

final class DatabaseChannel extends IlluminateDatabaseChannel
{
    /**
     * Send the given notification.
     *
     * @param  mixed  $notifiable
     * @return Model
     */
    public function send($notifiable, Notification $notification): Model
    {
        return $notifiable->routeNotificationFor('database', $notification)->create(
            $this->buildPayload($notifiable, $notification)
        );
    }

    /**
     * @param array $data
     */
    public function getLocalizedContent(array $data): array
    {
        return [
            $data['notification_type'] ?? null,
            $data['title_ar'] ?? null,
            $data['title_en'] ?? null,
            $data['content_ar'] ?? null,
            $data['content_en'] ?? null,
        ];
    }

    /**
     * Build an array payload for the DatabaseNotification Model.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    protected function buildPayload($notifiable, Notification $notification): array
    {
        $data = $this->getData($notifiable, $notification);

        [
            $notification_type,
            $title_ar,
            $title_en,
            $content_ar,
            $content_en,
        ] = $this->getLocalizedContent($data);

        Arr::forget($data, [
            'notification_type',
            'title_ar',
            'title_en',
            'content_ar',
            'content_en',
        ]);

        $payload = [
            'id' => $notification->id,
            'type' => method_exists($notification, 'databaseType')
                ? $notification->databaseType($notifiable)
                : get_class($notification),
            'notification_type' => $notification_type ?? null,
            'title_ar' => $title_ar ?? null,
            'title_en' => $title_en ?? null,
            'content_ar' => $content_ar ?? null,
            'content_en' => $content_en ?? null,
            'data' => $data,
            'read_at' => null,
        ];

        Log::debug(
            'DatabaseChannel',
            $payload
        );

        return $payload;
    }

    /**
     * Get the data for the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     *
     * @throws RuntimeException
     */
    protected function getData($notifiable, Notification $notification)
    {
        if (method_exists($notification, 'toDatabase')) {
            return is_array($data = $notification->toDatabase($notifiable))
                ? $data : $data->data;
        }

        if (method_exists($notification, 'toArray')) {
            return $notification->toArray($notifiable);
        }

        throw new RuntimeException('Notification is missing toDatabase / toArray method.');
    }
}
