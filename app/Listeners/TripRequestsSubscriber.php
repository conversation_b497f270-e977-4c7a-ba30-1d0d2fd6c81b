<?php

namespace App\Listeners;

use App\Events\TripRequestBookingAccepted;
use App\Events\TripRequestBookingCancelledByOwnerEvent;
use App\Events\TripRequestBookingCancelledEvent;
use App\Events\TripRequestBookingRejected;
use App\Events\TripRequestCancelledEvent;
use App\Events\TripRequestCreated;
use App\Events\TripRequestDelayed;
use App\Events\TripRequestSeatBooked;
use App\Models\Booking\TripRequestBooking;
use App\Models\Request\TripRequest;
use App\Notifications\NewTripRequestBookingNotification;
use App\Notifications\TripRequestCancelledNotification;
use App\Notifications\TripRequestBookingAcceptedNotification;
use App\Notifications\TripRequestBookingCancelledByOwnerNotification;
use App\Notifications\TripRequestBookingCancelledNotification;
use App\Notifications\TripRequestBookingRejectedNotification;
use App\Notifications\TripRequestDelayedNotification;
use Illuminate\Support\Facades\Notification;

class TripRequestsSubscriber
{
    /**
     * Handle trip request created events.
     */
    public function handleTripRequestCreated(TripRequestCreated $event): void
    {
        //
    }

    /**
     * Handle trip request cancelled events.
     */
    public function handleTripRequestCancelled(TripRequestCancelledEvent $event): void
    {
        $tripRequest = TripRequest::findOrFail($event->tripRequestId)->load('toCity');
        $travelers = $tripRequest->travelers;
        if ($travelers?->exist()) {
            Notification::send($travelers, (new TripRequestCancelledNotification($tripRequest)));
        }
    }

    /**
     * Handle trip request delayed events.
     */
    public function handleTripRequestDelayed(TripRequestDelayed $event): void
    {
        $tripRequest = TripRequest::findOrFail($event->tripRequestId)->load('toCity');
        $travelers = $tripRequest->travelers;

        if ($travelers?->exist()) {
            Notification::send($travelers, (new TripRequestDelayedNotification($tripRequest)));
        }
    }

    /**
     * Handle trip request seat booked events.
     */
    public function handleTripRequestSeatBooked(TripRequestSeatBooked $event): void
    {
        $booking = TripRequestBooking::findOrFail($event->bookingId);
        $tripRequest = TripRequest::findOrFail($booking->trip_request_id);
        $driver = $tripRequest->user;
        $driver->notify(new NewTripRequestBookingNotification($booking));
    }

    /**
     * Handle trip request booking accepted events.
     */
    public function handleTripRequestBookingAccepted(TripRequestBookingAccepted $event): void
    {
        $booking = TripRequestBooking::findOrFail($event->bookingId);
        $user = $booking->user;
        $user->notify(new TripRequestBookingAcceptedNotification($booking));
    }

    /**
     * Handle trip request booking rejected events.
     */
    public function handleTripRequestBookingRejected(TripRequestBookingRejected $event): void
    {
        $booking = TripRequestBooking::findOrFail($event->bookingId);
        $user = $booking->user;
        $user->notify(new TripRequestBookingRejectedNotification($booking));
    }

    /**
     * Handle trip request booking cancelled events.
     */
    public function handleTripRequestBookingCancelled(TripRequestBookingCancelledEvent $event): void
    {
        $booking = TripRequestBooking::findOrFail($event->bookingId);
        $user = $booking->user;
        $user->notify(new TripRequestBookingCancelledNotification($booking));
    }

    /**
     * Handle trip request booking cancelled by owner events.
     */
    public function handleTripRequestBookingCancelledByOwner(TripRequestBookingCancelledByOwnerEvent $event): void
    {
        $booking = TripRequestBooking::findOrFail($event->bookingId);
        $user = $booking->user;
        $user->notify(new TripRequestBookingCancelledByOwnerNotification($booking));
    }

    /**
     * Register the listeners for the subscriber.
     *
     * @return array<string, string>
     */
    public function subscribe(): array
    {
        return [
            TripRequestCreated::class => 'handleTripRequestCreated',
            TripRequestCancelledEvent::class => 'handleTripRequestCancelled',
            TripRequestSeatBooked::class => 'handleTripRequestSeatBooked',
            TripRequestBookingAccepted::class => 'handleTripRequestBookingAccepted',
            TripRequestBookingRejected::class => 'handleTripRequestBookingRejected',
            TripRequestDelayed::class => 'handleTripRequestDelayed',
            TripRequestBookingCancelledEvent::class => 'handleTripRequestBookingCancelled',
            TripRequestBookingCancelledByOwnerEvent::class => 'handleTripRequestBookingCancelledByOwner',
        ];
    }
}
