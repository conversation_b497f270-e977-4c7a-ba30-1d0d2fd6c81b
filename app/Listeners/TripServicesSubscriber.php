<?php

namespace App\Listeners;

use App\Events\TripServiceBookingAccepted;
use App\Events\TripServiceBookingCancelledEvent;
use App\Events\TripServiceBookingRejected;
use App\Events\TripServiceCancelledEvent;
use App\Events\TripServiceCreated;
use App\Events\TripServiceDelayed;
use App\Events\TripServiceSeatBooked;
use App\Events\TripServiceBookingCancelledByOwnerEvent;
use App\Jobs\Travel\TripService\NotifyTravelersWaitingJob;
use App\Jobs\Travel\TripService\NotifyTravelersArrivedJob;
use App\Jobs\Travel\TripService\NotifyDriverArrivedJob;
use App\Models\Booking\TripServiceBooking;
use App\Models\Service\TripService;
use App\Notifications\NewTripServiceBookingNotification;
use App\Notifications\TripServiceCancelledNotification;
use App\Notifications\TripServiceIsImmediateNotification;
use App\Notifications\TripServiceIsSoonNotification;
use App\Notifications\TripServiceStartedNotification;
use App\Notifications\TripServiceBookingAcceptedNotification;
use App\Notifications\TripServiceBookingCancelledNotification;
use App\Notifications\TripServiceBookingRejectedNotification;
use App\Notifications\TripServiceDelayedNotification;
use App\Notifications\TripServiceBookingCancelledByOwnerNotification;
use Carbon\Carbon;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Log;

class TripServicesSubscriber
{
    /**
     * Handle trip service created events.
     */
    public function handleTripServiceCreated(TripServiceCreated $event): void
    {
        Log::info('Trip service created', ['tripServiceId' => $event->tripServiceId]);
        $tripService = TripService::findOrFail($event->tripServiceId)->load('fromCity', 'toCity');
        $now = Carbon::now();
        $departureTime = Carbon::parse($tripService->departure_datetime);
        $hoursDifference = $now->diffInHours($departureTime);

        $driver = $tripService->user;

        if ($hoursDifference <= 4) {
            $driver->notify(new TripServiceIsImmediateNotification(
                $tripService->id,
                $tripService->fromCity->name_ar,
                $tripService->toCity->name_ar
            ));
        } else {
            $notificationTime = $departureTime->copy()->subHours(4);
            $driver->notify((new TripServiceIsSoonNotification(
                $tripService->id,
                $tripService->fromCity->name_ar,
                $tripService->toCity->name_ar
            ))->delay($notificationTime));
        }

        $startNotificationTime = $departureTime->copy()->subSecond();
        if ($now->diffInSeconds($departureTime) <= 1) {
            $driver->notify(new TripServiceStartedNotification(
                $tripService->id,
                $tripService->fromCity->name_ar,
                $tripService->toCity->name_ar
            ));
        } else {
            $driver->notify((new TripServiceStartedNotification(
                $tripService->id,
                $tripService->fromCity->name_ar,
                $tripService->toCity->name_ar
            ))->delay($startNotificationTime));
        }

        // Schedule the job to send waiting notifications to accepted travelers at start time
        NotifyTravelersWaitingJob::dispatch($tripService)->delay($startNotificationTime);

        $arrivalTime = Carbon::parse($tripService->arrival_datetime);
        $deliveredNotificationTime = $arrivalTime->copy()->subSecond();
        // Schedule the job to send arrival notifications to accepted travelers
        NotifyTravelersArrivedJob::dispatch($tripService)->delay($deliveredNotificationTime);
        NotifyDriverArrivedJob::dispatch($tripService)->delay($deliveredNotificationTime);
    }

    /**
     * Handle trip service cancelled events.
     */
    public function handleTripServiceCancelled(TripServiceCancelledEvent $event): void
    {
        $tripService = TripService::findOrFail($event->tripServiceId)->load('toCity');
        $travelers = $tripService->acceptedTravelers();

        Notification::send($travelers, (new TripServiceCancelledNotification($tripService)));
    }

    /**
     * Handle trip service delayed events.
     */
    public function handleTripServiceDelayed(TripServiceDelayed $event): void
    {
        $tripService = TripService::findOrFail($event->tripServiceId)->load(['toCity', 'bookings', 'bookings.user']);

        $acceptedBookings = $tripService->bookings()->accepted()->get();
        foreach ($acceptedBookings as $booking) {
            Notification::send($booking->user, (new TripServiceDelayedNotification($booking)));
        }
    }

    /**
     * Handle trip service seat booked events.
     */
    public function handleTripServiceSeatBooked(TripServiceSeatBooked $event): void
    {
        $booking = TripServiceBooking::findOrFail($event->bookingId);
        $tripService = TripService::findOrFail($booking->trip_service_id);
        $driver = $tripService->user;
        $driver->notify(new NewTripServiceBookingNotification(
            $booking->id,
            $booking->trip_service_id,
            $booking->number_of_seats
        ));
    }

    /**
     * Handle trip service booking accepted events.
     */
    public function handleTripServiceBookingAccepted(TripServiceBookingAccepted $event): void
    {
        $booking = TripServiceBooking::findOrFail($event->bookingId);
        $user = $booking->user;
        $user->notify(new TripServiceBookingAcceptedNotification($booking));
    }

    /**
     * Handle trip service booking rejected events.
     */
    public function handleTripServiceBookingRejected(TripServiceBookingRejected $event): void
    {
        $booking = TripServiceBooking::findOrFail($event->bookingId);
        $user = $booking->user;
        $user->notify(new TripServiceBookingRejectedNotification($booking));
    }

    /**
     * Handle trip service booking cancelled events.
     */
    public function handleTripServiceBookingCancelled(TripServiceBookingCancelledEvent $event): void
    {
        $booking = TripServiceBooking::findOrFail($event->bookingId);
        $user = $booking->user;
        $user->notify(new TripServiceBookingCancelledNotification($booking));
    }

    /**
     * Handle trip service booking cancelled by owner events.
     */
    public function handleTripServiceBookingCancelledByOwner(TripServiceBookingCancelledByOwnerEvent $event): void
    {
        $booking = TripServiceBooking::findOrFail($event->bookingId);
        $user = $booking->user;
        $user->notify(new TripServiceBookingCancelledByOwnerNotification($booking));
    }

    /**
     * Register the listeners for the subscriber.
     *
     * @return array<string, string>
     */
    public function subscribe(): array
    {
        return [
            TripServiceCreated::class => 'handleTripServiceCreated',
            TripServiceCancelledEvent::class => 'handleTripServiceCancelled',
            TripServiceSeatBooked::class => 'handleTripServiceSeatBooked',
            TripServiceBookingAccepted::class => 'handleTripServiceBookingAccepted',
            TripServiceBookingRejected::class => 'handleTripServiceBookingRejected',
            TripServiceDelayed::class => 'handleTripServiceDelayed',
            TripServiceBookingCancelledEvent::class => 'handleTripServiceBookingCancelled',
            TripServiceBookingCancelledByOwnerEvent::class => 'handleTripServiceBookingCancelledByOwner',
        ];
    }
}
