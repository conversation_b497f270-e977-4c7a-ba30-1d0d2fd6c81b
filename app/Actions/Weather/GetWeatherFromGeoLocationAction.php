<?php

namespace App\Actions\Weather;

use App\Enums\Travel\WeatherStatuses;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GetWeatherFromGeoLocationAction
{

    /**
     * Get weather from geo location.
     *
     * @param float $latitude
     * @param float $longitude
     * @param string $date
     *
     * @throws \Exception
     *
     * @return string|null
     */
    public function __invoke(float $latitude, float $longitude, string $date): ?string
    {
        $apiKey = config('services.open_weather_map.api_key');
        $response = Http::get('https://api.openweathermap.org/data/2.5/forecast', [
            'lat' => $latitude,
            'lon' => $longitude,
            'appid' => $apiKey,
            'exclude' => 'current,minutely,hourly,alerts',
        ]);
        Log::info("Weather from geo location: " . $response->body());
        if ($response->failed()) {
            throw new \Exception('Failed to get weather from geo location');
        }

        $forecasts = $response->json('list');

        $departureDate = \Carbon\Carbon::parse($date)->startOfDay();

        foreach ($forecasts as $forecast) {
            $forecastDate = \Carbon\Carbon::parse($forecast['dt_txt'])->startOfDay();

            if ($forecastDate->equalTo($departureDate)) {
                $weather = $forecast['weather'][0]['main'];

                return $this->mapWeatherToStatus($weather);
            }
        }

        return null;
    }

    private function mapWeatherToStatus(string $weather): ?string
    {
        return match (strtolower($weather)) {
            'rain' => WeatherStatuses::RAIN->value,
            'drizzle' => WeatherStatuses::DRIZZLE->value,
            'snow' => WeatherStatuses::SNOW->value,
            'mist', 'smoke', 'haze', 'dust', 'fog', 'sand', 'ash', 'squall' => WeatherStatuses::FOGGY->value,
            'clouds' => WeatherStatuses::CLOUDS->value,
            'tornado' => WeatherStatuses::TORNADO->value,
            'thunderstorm' => WeatherStatuses::STORMY->value,
            'clear' => WeatherStatuses::CLEAR->value,
            default => null,
        };
    }
}
