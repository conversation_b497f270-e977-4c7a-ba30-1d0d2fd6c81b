<?php

namespace App\Data\Services;

use App\Concerns\IntraWithCities;
use <PERSON><PERSON>\LaravelData\Data;
use Spatie\LaravelData\Optional;

class UpdateFazaaServiceData extends Data
{
    use IntraWithCities;

    public function __construct(
        public Optional|int $fazaa_service_type_id,
        public Optional|int $specific_type_id,
        public Optional|string $service_location,
        public Optional|string $transportation_type,
        public Optional|string $from_city_en,
        public Optional|string $from_city_ar,
        public Optional|array $from_location,
        public Optional|string $to_city_en,
        public Optional|string $to_city_ar,
        public Optional|array $to_location,
        public Optional|string $departure_datetime,
        public Optional|string $arrival_datetime,
        public Optional|float $price,
        public Optional|int $from_city_id,
        public Optional|int $to_city_id,
        public Optional|float $from_location_lat,
        public Optional|float $from_location_lng,
        public Optional|float $to_location_lat,
        public Optional|float $to_location_lng,
        public Optional|string|null $note,
    ) {
        if ($this->from_city_en && $this->from_city_ar) {
            $this->from_city_id = $this->getCityId($this->from_city_en, $this->from_city_ar);
        }

        if ($this->to_city_en && $this->to_city_ar) {
            $this->to_city_id = $this->getCityId($this->to_city_en, $this->to_city_ar);
        }

        if ($this->from_location) {
            $this->from_location_lat = $this->from_location['lat'];
            $this->from_location_lng = $this->from_location['lng'];
        }

        if ($this->to_location) {
            $this->to_location_lat = $this->to_location['lat'];
            $this->to_location_lng = $this->to_location['lng'];
        }
    }
}
