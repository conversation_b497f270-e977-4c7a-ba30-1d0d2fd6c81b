<?php

namespace App\Data\Services;

use App\Concerns\IntraWithCities;
use App\Enums\Travel\SeatStatusEnum;
use Spatie\LaravelData\Data;

class TripServiceData extends Data
{
    use IntraWithCities;

    public function __construct(
        public string $trip_type,
        public ?string $transportation_type,
        public string $from_city_en,
        public string $from_city_ar,
        public ?int $from_city_id,
        public $from_location,
        public ?string $from_location_lat,
        public ?string $from_location_lng,
        public string $to_city_en,
        public string $to_city_ar,
        public ?int $to_city_id,
        public $to_location,
        public ?string $to_location_lat,
        public ?string $to_location_lng,
        public string $departure_datetime,
        public string $arrival_datetime,
        public float $price,
        public int $number_of_free_cartons,
        public ?array $additional_services,
        public bool $deliver_to_door,
        public bool $allow_smoking,
        public int $car_id,
        public array $seats,
        public ?int $number_of_seats,
        public ?int $number_of_available_seats,
        public ?string $note,
    ) {
        $this->transportation_type = $transportation_type ?? 'car';

        $this->number_of_seats = count($this->seats);
        $this->number_of_available_seats = $this->number_of_seats;

        $this->from_city_id = $this->getCityId($this->from_city_en, $this->from_city_ar);
        $this->to_city_id = $this->getCityId($this->to_city_en, $this->to_city_ar);

        $this->from_location_lat = $this->from_location['lat'];
        $this->from_location_lng = $this->from_location['lng'];
        $this->to_location_lat = $this->to_location['lat'];
        $this->to_location_lng = $this->to_location['lng'];
    }
}
