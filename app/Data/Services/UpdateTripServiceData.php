<?php

namespace App\Data\Services;

use App\Concerns\IntraWithCities;
use App\Enums\Travel\SeatStatusEnum;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Optional;

class UpdateTripServiceData extends Data
{
    use IntraWithCities;

    public function __construct(
        public Optional|string $trip_type,
        public Optional|string $transportation_type,
        public Optional|string $from_city_en,
        public Optional|string $from_city_ar,
        public Optional|int $from_city_id,
        public Optional|array $from_location,
        public Optional|string $from_location_lat,
        public Optional|string $from_location_lng,
        public Optional|string $to_city_en,
        public Optional|string $to_city_ar,
        public Optional|int $to_city_id,
        public Optional|array $to_location,
        public Optional|string $to_location_lat,
        public Optional|string $to_location_lng,
        public Optional|string $departure_datetime,
        public Optional|string $arrival_datetime,
        public Optional|float $price,
        public Optional|int $number_of_free_cartons,
        public Optional|array $additional_services,
        public Optional|bool $deliver_to_door,
        public Optional|bool $allow_smoking,
        public Optional|int $car_id,
        public Optional|array $seats,
        public Optional|int $number_of_seats,
        public Optional|int $number_of_available_seats,
        public Optional|string|null $note,
    ) {
        if (!$this->seats instanceof Optional) {
            $availableSeats = count(array_filter($this->seats, fn($seat) => $seat['status'] === SeatStatusEnum::AVAILABLE()));
            $this->number_of_seats = $availableSeats;
            $this->number_of_available_seats = $availableSeats;
        }

        if (!$this->from_city_en instanceof Optional || !$this->from_city_ar instanceof Optional) {
            $this->from_city_id = $this->getCityId($this->from_city_en, $this->from_city_ar);
        }

        if (!$this->to_city_en instanceof Optional || !$this->to_city_ar instanceof Optional) {
            $this->to_city_id = $this->getCityId($this->to_city_en, $this->to_city_ar);
        }

        if (!$this->from_location instanceof Optional) {
            $this->from_location_lat = $this->from_location['lat'];
            $this->from_location_lng = $this->from_location['lng'];
        }

        if (!$this->to_location instanceof Optional) {
            $this->to_location_lat = $this->to_location['lat'];
            $this->to_location_lng = $this->to_location['lng'];
        }
    }
}
