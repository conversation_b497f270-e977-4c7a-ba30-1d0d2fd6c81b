<?php

namespace App\Data\Services;

use App\Concerns\IntraWithCities;
use <PERSON><PERSON>\LaravelData\Data;

class ShippingServiceData extends Data
{
    use IntraWithCities;

    public function __construct(
        public string $transportation_type,
        public string $from_city_en,
        public string $from_city_ar,
        public ?int $from_city_id,
        public $from_location,
        public ?float $from_location_lat,
        public ?float $from_location_lng,
        public string $to_city_en,
        public string $to_city_ar,
        public ?int $to_city_id,
        public $to_location,
        public ?float $to_location_lat,
        public ?float $to_location_lng,
        public string $departure_datetime,
        public string $arrival_datetime,
        public bool $can_ship_packages,
        public bool $can_ship_documents,
        public bool $can_ship_furniture,
        public ?int $packages_volume,
        public ?int $available_packages_volume,
        public ?int $document_volume,
        public ?int $available_document_volume,
        public ?int $furniture_volume,
        public ?int $available_furniture_volume,
        public ?string $delivery_location,
        public ?float $package_price,
        public ?float $document_price,
        public ?float $furniture_price,
        public ?string $note = null,
        public ?int $car_id = null,
    ) {
        $this->available_packages_volume = $packages_volume;
        $this->available_document_volume = $document_volume;
        $this->available_furniture_volume = $furniture_volume;

        $this->from_city_id = $this->getCityId($this->from_city_en, $this->from_city_ar);
        $this->to_city_id = $this->getCityId($this->to_city_en, $this->to_city_ar);

        $this->from_location_lat = $this->from_location['lat'];
        $this->from_location_lng = $this->from_location['lng'];
        $this->to_location_lat = $this->to_location['lat'];
        $this->to_location_lng = $this->to_location['lng'];
    }
}
