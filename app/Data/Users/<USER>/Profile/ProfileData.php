<?php

namespace App\Data\Users\Accounts\Profile;

use App\Concerns\IntraWithCities;
use Illuminate\Http\UploadedFile;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Optional;

class ProfileData extends Data
{
    use IntraWithCities;

    public function __construct(
        public Optional|string $name,
        public Optional|string $email,
        public Optional|string $mobile,
        public Optional|string $city_en,
        public Optional|string $city_ar,
        public Optional|int $city_id,
        public Optional|array $location,
        public Optional|string $location_lat,
        public Optional|string $location_lng,
        public Optional|string $gender,
        public Optional|string $birth_date,
        public Optional|UploadedFile $picture,
        public Optional|string $country_id,
        public Optional|string $whatsapp_number,
    ) {

        if (!$this->city_en instanceof Optional || !$this->city_ar instanceof Optional) {
            $this->city_id = $this->getCityId($this->city_en, $this->city_ar);
        }

        if (!$this->location instanceof Optional) {
            $this->location_lat = $this->location['lat'];
            $this->location_lng = $this->location['lng'];
        }
    }
}
