<?php

namespace App\Data\ServiceRequests;

use App\Concerns\IntraWithCities;
use <PERSON><PERSON>\LaravelData\Data;

class ShippingRequestData extends Data
{
    use IntraWithCities;

    public function __construct(
        public string $transportation_type,
        public bool $can_ship_packages,
        public bool $can_ship_documents,
        public bool $can_ship_furniture,
        public string $from_city_en,
        public string $from_city_ar,
        public ?int $from_city_id,
        public $from_location,
        public ?string $from_location_lat,
        public ?string $from_location_lng,
        public string $to_city_en,
        public string $to_city_ar,
        public ?int $to_city_id,
        public $to_location,
        public ?string $to_location_lat,
        public ?string $to_location_lng,
        public string $departure_datetime,
        public string $arrival_datetime,
        public ?int $packages_volume,
        public ?int $document_volume,
        public ?int $furniture_volume,
        public ?string $delivery_location,
        public bool $is_fragile,
        public ?string $note,
    ) {
        $this->from_city_id = $this->getCityId($this->from_city_en, $this->from_city_ar);
        $this->to_city_id = $this->getCityId($this->to_city_en, $this->to_city_ar);

        $this->from_location_lat = $this->from_location['lat'];
        $this->from_location_lng = $this->from_location['lng'];
        $this->to_location_lat = $this->to_location['lat'];
        $this->to_location_lng = $this->to_location['lng'];
    }
}
