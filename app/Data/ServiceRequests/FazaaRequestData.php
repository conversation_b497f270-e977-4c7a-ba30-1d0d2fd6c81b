<?php

namespace App\Data\ServiceRequests;

use App\Concerns\IntraWithCities;
use <PERSON><PERSON>\LaravelData\Data;

class FazaaRequestData extends Data
{
    use IntraWithCities;

    public function __construct(
        public int $fazaa_service_type_id,
        public int $specific_type_id,
        public string $transportation_type,
        public string $from_city_en,
        public string $from_city_ar,
        public ?int $from_city_id,
        public $from_location,
        public ?string $from_location_lat,
        public ?string $from_location_lng,
        public string $to_city_en,
        public string $to_city_ar,
        public ?int $to_city_id,
        public $to_location,
        public ?string $to_location_lat,
        public ?string $to_location_lng,
        public string $departure_datetime,
        public string $arrival_datetime,
        public string $service_location,
        public ?string $note = null,
    ) {

        $this->from_city_id = $this->getCityId($this->from_city_en, $this->from_city_ar);
        $this->to_city_id = $this->getCityId($this->to_city_en, $this->to_city_ar);

        $this->from_location_lat = $this->from_location['lat'];
        $this->from_location_lng = $this->from_location['lng'];
        $this->to_location_lat = $this->to_location['lat'];
        $this->to_location_lng = $this->to_location['lng'];
    }
}
