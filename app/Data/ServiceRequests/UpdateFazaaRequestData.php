<?php

namespace App\Data\ServiceRequests;

use App\Concerns\IntraWithCities;
use <PERSON><PERSON>\LaravelData\Data;
use <PERSON>tie\LaravelData\Optional;

class UpdateFazaaRequestData extends Data
{
    use IntraWithCities;

    public function __construct(
        public string|Optional $transportation_type,
        public int|Optional $from_city_id,
        public string|Optional $from_city_en,
        public string|Optional $from_city_ar,
        public int|Optional $to_city_id,
        public string|Optional $to_city_en,
        public string|Optional $to_city_ar,
        public array|Optional $from_location,
        public array|Optional $to_location,
        public string|Optional $from_location_lat,
        public string|Optional $from_location_lng,
        public string|Optional $to_location_lat,
        public string|Optional $to_location_lng,
        public string|Optional $departure_datetime,
        public string|Optional $arrival_datetime,
        public string|Optional $description,
        public int|Optional $number_of_seats,
        public float|Optional $price,
        public int|Optional $fazaa_service_type_id,
        public int|Optional $specific_type_id,
        public string|Optional|null $service_location,
        public string|Optional|null $note,
    ) {
        if (!$this->from_city_id instanceof Optional) {
            $this->from_city_id = $this->getCityId($this->from_city_en, $this->from_city_ar);
        }

        if (!$this->to_city_id instanceof Optional) {
            $this->to_city_id = $this->getCityId($this->to_city_en, $this->to_city_ar);
        }

        if (!$this->from_location instanceof Optional) {
            $this->from_location_lat = $this->from_location['lat'];
            $this->from_location_lng = $this->from_location['lng'];
        }

        if (!$this->to_location instanceof Optional) {
            $this->to_location_lat = $this->to_location['lat'];
            $this->to_location_lng = $this->to_location['lng'];
        }
    }
}
