<?php

namespace App\Data\ServiceRequests;

use App\Concerns\IntraWithCities;
use <PERSON><PERSON>\LaravelData\Data;

class TripRequestData extends Data
{
    use IntraWithCities;

    public function __construct(
        public string $trip_type,
        public string $from_city_en,
        public string $from_city_ar,
        public ?int $from_city_id,
        public $from_location,
        public ?string $from_location_lat,
        public ?string $from_location_lng,
        public string $to_city_en,
        public string $to_city_ar,
        public ?int $to_city_id,
        public $to_location,
        public ?string $to_location_lat,
        public ?string $to_location_lng,
        public string $departure_datetime,
        public string $arrival_datetime,
        public ?int $number_of_seats,
        public float $price,
        public bool $allow_smoking,
        public bool $deliver_to_door,
        public array $seats,
        public ?array $additional_services,
        public int $car_type_id,
        public ?string $note,
    ) {
        $this->number_of_seats = count($this->seats);

        $this->from_city_id = $this->getCityId($this->from_city_en, $this->from_city_ar);
        $this->to_city_id = $this->getCityId($this->to_city_en, $this->to_city_ar);

        $this->from_location_lat = $this->from_location['lat'];
        $this->from_location_lng = $this->from_location['lng'];
        $this->to_location_lat = $this->to_location['lat'];
        $this->to_location_lng = $this->to_location['lng'];
    }
}
