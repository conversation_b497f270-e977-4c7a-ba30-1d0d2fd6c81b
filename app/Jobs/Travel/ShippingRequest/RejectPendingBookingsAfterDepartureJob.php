<?php

namespace App\Jobs\Travel\ShippingRequest;

use App\Enums\Bookings\BookingStatusEnum;
use App\Models\Request\ShippingRequest;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RejectPendingBookingsAfterDepartureJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public ShippingRequest $shippingRequest
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $shippingRequest = $this->shippingRequest->fresh();

        if ($shippingRequest->departure_datetime->isFuture()) {
            self::dispatch($shippingRequest)->delay($shippingRequest->departure_datetime);
            return;
        }

        $pendingBookings = $shippingRequest->bookings()->pending();

        $pendingBookings->update([
            'status' => BookingStatusEnum::REJECTED(),
            'rejected_at' => now(),
            'rejection_reason' => __(key:'messages.shipping_request.bookings.auto_rejected', locale: $shippingRequest->user->locale),
        ]);
    }
}
