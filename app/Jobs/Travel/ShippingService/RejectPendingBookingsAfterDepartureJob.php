<?php

namespace App\Jobs\Travel\ShippingService;

use App\Enums\Bookings\BookingStatusEnum;
use App\Models\Service\ShippingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RejectPendingBookingsAfterDepartureJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public ShippingService $shippingService
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $shippingService = $this->shippingService->fresh();

        if ($shippingService->departure_datetime->isFuture()) {
            self::dispatch($shippingService)->delay($shippingService->departure_datetime);
            return;
        }

        $pendingBookings = $shippingService->bookings()->pending();

        $pendingBookings->update([
            'status' => BookingStatusEnum::REJECTED(),
            'rejected_at' => now(),
            'rejection_reason' => __(key:'messages.shipping_service.bookings.auto_rejected', locale: $shippingService->user->locale),
        ]);
    }
}
