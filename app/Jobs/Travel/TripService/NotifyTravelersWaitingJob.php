<?php

namespace App\Jobs\Travel\TripService;

use App\Models\Booking\TripServiceBooking;
use App\Models\Service\TripService;
use App\Notifications\TripServiceWaitingNotification;
use App\Enums\Bookings\BookingStatusEnum;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Notification;

class NotifyTravelersWaitingJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public TripService $tripService,
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $travelers = $this->tripService->travelers;

        if ($travelers->isNotEmpty()) {
            foreach ($travelers as $traveler) {
                // Find the booking for this traveler and trip service
                $booking = TripServiceBooking::where('user_id', $traveler->id)
                    ->where('trip_service_id', $this->tripService->id)
                    ->where('status', BookingStatusEnum::ACCEPTED())
                    ->first();

                if ($booking) {
                    $traveler->notify(new TripServiceWaitingNotification(
                        $this->tripService->id,
                        $this->tripService->toCity->name,
                        $booking->id,
                    ));
                }
            }
        }
    }
}
