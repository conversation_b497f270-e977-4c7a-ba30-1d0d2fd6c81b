<?php

namespace App\Jobs\Travel\TripService;

use App\Enums\Bookings\BookingStatusEnum;
use App\Models\Service\TripService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RejectPendingBookingsAfterDepartureJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public TripService $tripService
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $tripService = $this->tripService->fresh();

        if ($tripService->departure_datetime->isFuture()) {
            self::dispatch($tripService)->delay($tripService->departure_datetime);
            return;
        }

        $pendingBookings = $tripService->bookings()->pending();

        $pendingBookings->update([
            'status' => BookingStatusEnum::REJECTED(),
            'rejected_at' => now(),
            'rejection_reason' => __(key:'messages.trip_service.bookings.auto_rejected', locale: $tripService->user->locale),
        ]);
    }
}
