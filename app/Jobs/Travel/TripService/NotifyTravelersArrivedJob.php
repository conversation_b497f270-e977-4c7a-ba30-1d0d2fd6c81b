<?php

namespace App\Jobs\Travel\TripService;

use App\Enums\Bookings\BookingStatusEnum;
use App\Models\Booking\TripServiceBooking;
use App\Models\Service\TripService;
use App\Models\User;
use App\Notifications\TripServiceArrivedNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Collection;

class NotifyTravelersArrivedJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public TripService $tripService,
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Get only accepted travelers
        $acceptedBookings = TripServiceBooking::where('trip_service_id', $this->tripService->id)
            ->where('status', BookingStatusEnum::ACCEPTED())
            ->get();

        if ($acceptedBookings->isNotEmpty()) {
            $acceptedTravelerIds = $acceptedBookings->pluck('user_id');
            $acceptedTravelers = User::whereIn('id', $acceptedTravelerIds)->get();

            if ($acceptedTravelers->isNotEmpty()) {
                Notification::send($acceptedTravelers, new TripServiceArrivedNotification($this->tripService));
            }
        }
    }
}
