<?php

namespace App\Jobs\Travel\TripService;

use App\Models\Service\TripService;
use App\Notifications\DriverTripServiceArrivedNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class NotifyDriverArrivedJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public TripService $tripService,
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $driver = $this->tripService->user;

        if ($this->tripService->attendance_confirmed_at || $this->tripService->cancelled_at) {
            // If the trip service is already confirmed, cancelled, or delayed, do not notify the driver
            return;
        }

        $driver->notify(new DriverTripServiceArrivedNotification($this->tripService));
    }
}
