<?php

namespace App\Jobs\Travel\FazaaService;

use App\Enums\Bookings\BookingStatusEnum;
use App\Models\Service\FazaaService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RejectPendingBookingsAfterDepartureJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public FazaaService $fazaaService
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $fazaaService = $this->fazaaService->fresh();

        if ($fazaaService->departure_datetime->isFuture()) {
            self::dispatch($fazaaService)->delay($fazaaService->departure_datetime);
            return;
        }

        $booking = $fazaaService->bookings()->pending();

        $booking->update([
            'status' => BookingStatusEnum::REJECTED(),
            'rejected_at' => now(),
            'rejection_reason' => 'Auto-rejected after departure time',
        ]);
    }
}
