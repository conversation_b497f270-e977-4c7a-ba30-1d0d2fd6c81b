<?php

namespace App\Jobs\Travel\FazaaRequest;

use App\Enums\Bookings\BookingStatusEnum;
use App\Models\Request\FazaaRequest;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RejectPendingBookingsAfterDepartureJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public FazaaRequest $fazaaRequest
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $fazaaRequest = $this->fazaaRequest->fresh();

        if ($fazaaRequest->departure_datetime->isFuture()) {
            self::dispatch($fazaaRequest)->delay($fazaaRequest->departure_datetime);
            return;
        }

        $pendingBookings = $fazaaRequest->bookings()->pending();

        $pendingBookings->update([
            'status' => BookingStatusEnum::REJECTED(),
            'rejected_at' => now(),
            'rejection_reason' => __(key:'messages.fazaa_request.bookings.auto_rejected', locale: $fazaaRequest->user->locale),
        ]);
    }
}
