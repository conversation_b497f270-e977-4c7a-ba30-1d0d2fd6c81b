<?php

namespace App\Jobs\Travel\Weather;

use App\Actions\Weather\GetWeatherFromGeoLocationAction;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class GetWeatherFromGeoLocationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(public Model $model)
    {
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $weatherStatus = app(GetWeatherFromGeoLocationAction::class)(
            latitude: $this->model->from_location_lat,
            longitude: $this->model->from_location_lng,
            date: $this->model->departure_datetime,
        );

        if ($weatherStatus) {
            $this->model->update(['weather_status' => $weatherStatus]);
        }
    }
}
