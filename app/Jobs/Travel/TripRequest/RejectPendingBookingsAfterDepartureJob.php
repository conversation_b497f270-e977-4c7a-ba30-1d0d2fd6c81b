<?php

namespace App\Jobs\Travel\TripRequest;

use App\Enums\Bookings\BookingStatusEnum;
use App\Models\Request\TripRequest;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RejectPendingBookingsAfterDepartureJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public TripRequest $tripRequest
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $tripRequest = $this->tripRequest->fresh();

        if ($tripRequest->departure_datetime->isFuture()) {
            self::dispatch($tripRequest)->delay($tripRequest->departure_datetime);
            return;
        }

        $pendingBookings = $tripRequest->bookings()->pending();

        $pendingBookings->update([
            'status' => BookingStatusEnum::REJECTED(),
            'rejected_at' => now(),
            'rejection_reason' => __(key:'messages.trip_request.bookings.auto_rejected', locale: $tripRequest->user->locale),
        ]);
    }
}
