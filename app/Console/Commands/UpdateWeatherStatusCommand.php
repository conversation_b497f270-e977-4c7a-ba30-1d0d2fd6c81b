<?php

namespace App\Console\Commands;

use App\Jobs\Travel\Weather\GetWeatherFromGeoLocationJob;
use App\Models\Service\FazaaService;
use App\Models\Service\ShippingService;
use App\Models\Service\TripService;
use App\Models\Request\FazaaRequest;
use App\Models\Request\ShippingRequest;
use App\Models\Request\TripRequest;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class UpdateWeatherStatusCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'weather:update';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update weather status for all services and requests';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        Log::info('Kicking off weather update');

        $models = [
            TripService::class,
            FazaaService::class,
            ShippingService::class,
            TripRequest::class,
            FazaaRequest::class,
            ShippingRequest::class,
        ];

        foreach ($models as $modelClass) {
            $modelClass::upcoming()->chunk(100, function ($records) {
                foreach ($records as $record) {
                    GetWeatherFromGeoLocationJob::dispatch($record);
                }
            });
        }

        Log::info('Weather update command finished.');
        $this->info('Weather status update command has been executed.');
    }
}
