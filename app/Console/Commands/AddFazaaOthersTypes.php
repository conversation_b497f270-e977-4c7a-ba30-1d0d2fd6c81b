<?php

namespace App\Console\Commands;

use App\Models\Common\FazaaServiceType;
use App\Models\Common\FazaaSpecificType;
use Illuminate\Console\Command;

class AddFazaaOthersTypes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fazaa:add-others-types';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add "Others" service type and its default specific type';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        // Step 1: Check and add the Others service type
        $othersType = [
            'name_en' => 'Others',
            'name_ar' => 'أخرى',
            'key' => 'others'
        ];

        $exists = FazaaServiceType::where('key', 'others')->exists();

        if ($exists) {
            $this->info('The "Others" service type already exists in the database.');
        } else {
            FazaaServiceType::create($othersType);
            $this->info('Successfully added "Others" service type to the database.');
        }

        // Step 2: Get the Others service type and add a specific type for it
        $othersServiceType = FazaaServiceType::where('key', 'others')->first();

        if (!$othersServiceType) {
            $this->error('The "Others" service type does not exist in the database.');
            return Command::FAILURE;
        }

        $exists = FazaaSpecificType::where('fazaa_service_type_id', $othersServiceType->id)
            ->where('name_en', 'Other Services')
            ->exists();

        if ($exists) {
            $this->info('A default specific type for "Others" service type already exists.');
        } else {
            FazaaSpecificType::create([
                'name_en' => 'Other Services',
                'name_ar' => 'خدمات أخرى',
                'fazaa_service_type_id' => $othersServiceType->id,
            ]);
            $this->info('Successfully added default specific type for "Others" service type.');
        }

        return Command::SUCCESS;
    }
}
