<?php

namespace App\Console\Commands;

use App\Models\Common\FazaaServiceType;
use App\Models\Common\FazaaSpecificType;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB; // Required for DB transaction

class SeedFazaaServiceTypesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fazaa:seed-service-types';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Seeds Fazaa service types and their specific types data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting Fazaa data seeding...');

        DB::transaction(function () {
            // Seed FazaaServiceTypes
            $this->info('Seeding Fazaa Service Types...');

            $serviceTypesData = [
                ['name_en' => 'Send shipment', 'name_ar' => 'شحن', 'key' => 'send_shipment'],
            ];

            foreach ($serviceTypesData as $typeData) {
                FazaaServiceType::firstOrCreate(['key' => $typeData['key']], $typeData);
            }

            $this->info('Fazaa Service Types seeded.');

            // Fetch service types again to ensure we have IDs for specific types
            $fazaaServiceTypes = FazaaServiceType::all()->keyBy('key');

            // Seed FazaaSpecificTypes
            $this->info('Seeding Fazaa Specific Types...');
            $fazaaSpecificTypesData = [
                // Buying Product
                ['name_ar' => 'شراء اخرى', 'name_en' => 'Buying Others', 'fazaa_service_type_id' => $fazaaServiceTypes['buying_product']->id],
                // Receive Shipment
                ['name_ar' => 'شحنة اخرى', 'name_en' => 'Receive Others', 'fazaa_service_type_id' => $fazaaServiceTypes['receive_shipment']->id],
                // Send Shipment
                ['name_ar' => 'شحن ادوية', 'name_en' => 'Send Medicines', 'fazaa_service_type_id' => $fazaaServiceTypes['send_shipment']->id],
                ['name_ar' => 'شحن مواد منزلية', 'name_en' => 'Send Home Goods', 'fazaa_service_type_id' => $fazaaServiceTypes['send_shipment']->id],
                ['name_ar' => 'شحن اجهزة اكترونية', 'name_en' => 'Send Electronics', 'fazaa_service_type_id' => $fazaaServiceTypes['send_shipment']->id],
                ['name_ar' => 'شحن ملابس', 'name_en' => 'Send clothes', 'fazaa_service_type_id' => $fazaaServiceTypes['send_shipment']->id],
                ['name_ar' => 'شحن اخرى', 'name_en' => 'Send Others', 'fazaa_service_type_id' => $fazaaServiceTypes['send_shipment']->id],
                // Review
                ['name_ar' => 'مراجعة أخرى', 'name_en' => 'Others Review', 'fazaa_service_type_id' => $fazaaServiceTypes['review']->id],
            ];

            foreach ($fazaaSpecificTypesData as $typeData) {
                    FazaaSpecificType::firstOrCreate(
                        [ // Attributes to find by
                            'name_en' => $typeData['name_en'],
                            'fazaa_service_type_id' => $typeData['fazaa_service_type_id'],
                        ],
                        [ // Attributes to set if creating
                            'name_ar' => $typeData['name_ar'],
                            // 'id' will be handled by model events (snowflake) if it's a new record
                        ]
                    );
            }
            $this->info('Fazaa Specific Types seeded.');
        });

        FazaaSpecificType::where('name_en', 'Travel Agency')->delete();
        FazaaServiceType::where('key', 'travel_agency')->delete();

        $this->info('Fazaa data seeding completed successfully.');
        return Command::SUCCESS;
    }
}
