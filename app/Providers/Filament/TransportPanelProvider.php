<?php

namespace App\Providers\Filament;

use App\Filament\Transport\Pages\Auth\Login;
use App\Filament\Transport\Pages\Dashboard;
use App\Filament\Transport\Pages\Register;
use App\Http\Middleware\CheckTransportOfficeStatus;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\Support\Facades\Storage;
use Illuminate\View\Middleware\ShareErrorsFromSession;

class TransportPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->id('transport')
            ->path('transport')
            ->login(Login::class)
            ->registration(Register::class)
            ->brandLogo(function () {
                $user = auth('transport')->user();
                $officeLogo = $user?->transportationOffice?->office_logo;

                if ($officeLogo && Storage::disk('public')->exists($officeLogo)) {
                    return asset('storage/' . $officeLogo);
                }

                return asset('images/app_logo.png');
            })
            ->brandLogoHeight('5rem')
            ->colors([
                'primary' => Color::Blue,
            ])
            ->authGuard('transport')
            ->discoverResources(in: app_path('Filament/Transport/Resources'), for: 'App\\Filament\\Transport\\Resources')
            ->discoverPages(in: app_path('Filament/Transport/Pages'), for: 'App\\Filament\\Transport\\Pages')
            ->pages([
                Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Transport/Widgets'), for: 'App\\Filament\\Transport\\Widgets')
            ->widgets([
                Widgets\AccountWidget::class,
                Widgets\FilamentInfoWidget::class,
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
                CheckTransportOfficeStatus::class,
            ]);
    }
}
