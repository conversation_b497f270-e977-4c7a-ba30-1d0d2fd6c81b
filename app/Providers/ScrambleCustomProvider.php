<?php

namespace App\Providers;

use Dedoc\Scramble\Scramble;
use Dedoc\Scramble\Support\RouteInfo;
use Illuminate\Support\Arr;
use Illuminate\Support\ServiceProvider;

class ScrambleCustomProvider extends ServiceProvider
{
    public function boot()
    {
        Scramble::routes(function ($route) {
            $action = $route->getAction('uses');

            if (is_string($action)) {
                if (method_exists($action, '__invoke')) {
                    return true;
                }
                return str_starts_with($route->uri, 'api');
            }

            return false;
        });

        Scramble::resolveTagsUsing(function (RouteInfo $routeInfo) {
            return array_values(array_unique(
                Arr::map(
                    $routeInfo->phpDoc()->getTagsByName('@tags'),
                    fn($tag) => trim($tag?->value?->value)
                )
            ));
        });
    }
}
