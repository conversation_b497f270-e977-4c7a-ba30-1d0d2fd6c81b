<?php

namespace App\Http\Resources\Users\Cars;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CarsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'model' => $this->model,
            'plate_number' => $this->plate_number,
            'type' => CarTypeResource::make($this->type),
        ];
    }
}
