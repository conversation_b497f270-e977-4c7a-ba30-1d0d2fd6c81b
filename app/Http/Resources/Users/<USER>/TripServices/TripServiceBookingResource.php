<?php

namespace App\Http\Resources\Users\Bookings\TripServices;

use App\Http\Resources\Seats\TripBookedSeatsResource;
use App\Http\Resources\Services\TripServices\TripServiceResource;
use App\Http\Resources\Users\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TripServiceBookingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'note' => $this->note,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'has_fragile_items' => $this->has_fragile_items,
            'accepted_at' => $this->accepted_at,
            'cancelled_at' => $this->cancelled_at,
            'cancellation_reason' => $this->cancellation_reason,
            'rejected_at' => $this->rejected_at,
            'rejection_reason' => $this->rejection_reason,
            'seats_count' => $this->number_of_seats,
            'seats' => TripBookedSeatsResource::collection($this->seats),
            'status' => $this->status,
            'attendance_confirmed_at' => $this->attendance_confirmed_at,
            'delay_requested_at' => $this->delay_requested_at,
            'trip_service' => TripServiceResource::make($this->tripService),
            'price' => $this->price,
            'user' => UserResource::make($this->user),
            'arrival_confirmed_at' => $this->arrival_confirmed_at,
            'paid_at' => $this->paid_at,
        ];
    }
}
