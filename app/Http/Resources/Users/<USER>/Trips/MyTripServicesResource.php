<?php

namespace App\Http\Resources\Users\Services\Trips;

use Illuminate\Http\Request;
use App\Http\Resources\AdditionalServices\AdditionalServicesResource;
use App\Http\Resources\Cities\CityResource;
use App\Http\Resources\Locations\FromLocationResource;
use App\Http\Resources\Locations\ToLocationResource;
use App\Http\Resources\Seats\TripSeatsResource;
use App\Http\Resources\Users\Cars\CarResource;
use Illuminate\Http\Resources\Json\JsonResource;

class MyTripServicesResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'trip_type' => $this->trip_type_formatted,
            'departure_datetime' => $this->departure_datetime,
            'arrival_datetime' => $this->arrival_datetime,
            'number_of_seats' => $this->number_of_seats,
            'number_of_available_seats' => $this->number_of_available_seats,
            'number_of_free_cartons' => $this->number_of_free_cartons,
            'from_city' => CityResource::make($this->fromCity),
            'from_location' => FromLocationResource::make($this),
            'to_city' => CityResource::make($this->toCity),
            'to_location' => ToLocationResource::make($this),
            'price' => $this->price,
            'allow_smoking' => $this->allow_smoking,
            'deliver_to_door' => $this->deliver_to_door,
            'additional_services' => AdditionalServicesResource::collection($this->AdditionalServices),
            'car' => CarResource::make($this->car),
            'seats' => TripSeatsResource::collection($this->seats),
            'note' => $this->note,
            'status' => $this->status,
            'cancelled_at' => $this->cancelled_at,
            'cancellation_reason' => $this->cancellation_reason,
        ];
    }
}
