<?php

namespace App\Http\Resources\Users\Services\Fazaa;

use App\Http\Resources\Cities\CityResource;
use App\Http\Resources\Locations\FromLocationResource;
use App\Http\Resources\Locations\ToLocationResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FazaaServiceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'fazaa_service_type' => $this->fazaaServiceType->name,
            'specific_type' => $this->specificType->name,
            'transportation_type' => $this->transportation_type_formatted,
            'departure_datetime' => $this->departure_datetime,
            'arrival_datetime' => $this->arrival_datetime,
            'from_city' => CityResource::make($this->fromCity),
            'from_location' => FromLocationResource::make($this),
            'to_city' => CityResource::make($this->toCity),
            'to_location' => ToLocationResource::make($this),
            'service_location' => $this->service_location,
            'price' => $this->price,
            'shipment_volume' => $this->shipment_volume,
            'note' => $this->note,
        ];
    }
}
