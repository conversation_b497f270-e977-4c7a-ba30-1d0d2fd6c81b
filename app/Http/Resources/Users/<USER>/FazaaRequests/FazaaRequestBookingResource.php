<?php

namespace App\Http\Resources\Users\Bookings\FazaaRequests;

use App\Http\Resources\Requests\FazaaRequests\FazaaRequestResource;
use App\Http\Resources\Users\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FazaaRequestBookingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'note' => $this->note,
            'price' => $this->price,
            'status' => $this->status,
            'accepted_at' => $this->accepted_at,
            'cancelled_at' => $this->cancelled_at,
            'cancellation_reason' => $this->cancellation_reason,
            'rejected_at' => $this->rejected_at,
            'rejection_reason' => $this->rejection_reason,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'fazaa_request' => FazaaRequestResource::make($this->fazaaRequest),
            'user' => UserResource::make($this->user),
        ];
    }
}
