<?php

namespace App\Http\Resources\Users\ServiceRequests\FazaaRequests;

use App\Http\Resources\Cities\CityResource;
use App\Http\Resources\FazaaServiceTypes\FazaaServiceTypesResource;
use App\Http\Resources\FazaaSpecificTypes\FazaaSpecificTypesResource;
use App\Http\Resources\Locations\FromLocationResource;
use App\Http\Resources\Locations\ToLocationResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ShowFazaaRequestResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'fazaa_service_type' => FazaaServiceTypesResource::make($this->fazaaServiceType),
            'specific_type' => FazaaSpecificTypesResource::make($this->specificType),

            'transportation_type' => $this->transportation_type_formatted,
            'departure_datetime' => $this->departure_datetime,
            'arrival_datetime' => $this->arrival_datetime,
            'from_city' => CityResource::make($this->fromCity),
            'from_location' => FromLocationResource::make($this),
            'to_city' => CityResource::make($this->toCity),
            'to_location' => ToLocationResource::make($this),
            'service_location' => $this->service_location,
            'note' => $this->note,
            'status' => $this->status,
            'is_cancelled' => $this->is_cancelled,
            'cancelled_at' => $this->cancelled_at,
            'cancellation_reason' => $this->cancellation_reason,
            'attendance_confirmed_at' => $this->attendance_confirmed_at,
        ];
    }
}
