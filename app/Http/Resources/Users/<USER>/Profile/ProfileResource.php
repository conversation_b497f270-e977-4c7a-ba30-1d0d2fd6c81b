<?php

namespace App\Http\Resources\Users\Accounts\Profile;

use App\Http\Resources\Cities\CityResource;
use App\Http\Resources\Common\CountryResource;
use App\Http\Resources\Locations\UserLocationResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProfileResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $trip_services_count = $this->trip_services_count + $this->trip_request_bookings_count;
        $fazaa_services_count = $this->fazaa_services_count + $this->fazaa_request_bookings_count;
        $shipping_services_count = $this->shipping_services_count + $this->shipping_request_bookings_count;
        $trip_requests_count = $this->trip_requests_count + $this->trip_service_bookings_count;
        $shipping_requests_count = $this->shipping_requests_count + $this->shipping_service_bookings_count;
        $fazaa_requests_count = $this->fazaa_requests_count + $this->fazaa_service_bookings_count;

        $services_count = $trip_services_count + $fazaa_services_count + $shipping_services_count;
        $requests_count =  $trip_requests_count + $fazaa_requests_count + $shipping_requests_count;
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'picture' => $this->picture,
            'mobile' => $this->mobile,
            'city' => CityResource::make($this->city),
            'location' => $this->latitude && $this->longitude ?
                UserLocationResource::make($this) : null,
            'gender' => $this->gender,
            'birth_date' => $this->birth_date,
            'country' => CountryResource::make($this->country),
            'total_income' => (float)($this->total_income ?? 0),
            'services_count' => $this->trip_services_count + $this->fazaa_services_count + $this->shipping_services_count,
            'requests_count' => $this->trip_requests_count + $this->shipping_requests_count + $this->fazaa_requests_count,
            'trip_services_count' => $this->trip_services_count,
            'fazaa_services_count' => $this->fazaa_services_count,
            'shipping_services_count' => $this->shipping_services_count,
            'trip_requests_count' => $this->trip_requests_count,
            'shipping_requests_count' => $this->shipping_requests_count,
            'fazaa_requests_count' => $this->fazaa_requests_count,
            'services_count' => $services_count,
            'requests_count' => $requests_count,
            'trip_services_count' => $trip_services_count,
            'fazaa_services_count' => $fazaa_services_count,
            'shipping_services_count' => $shipping_services_count,
            'trip_requests_count' => $trip_requests_count,
            'fazaa_requests_count' => $fazaa_requests_count,
            'shipping_requests_count' => $shipping_requests_count,
            'whatsapp_number' => $this->whatsapp_number,
            'is_profile_completed' => $this->is_profile_completed,
        ];
    }
}
