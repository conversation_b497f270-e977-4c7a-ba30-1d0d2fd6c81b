<?php

namespace App\Http\Resources\Users\Bookings\FazaaServices;

use App\Http\Resources\Services\FazaaServices\FazaaServiceResource;
use App\Http\Resources\Users\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FazaaServiceBookingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'note' => $this->note,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'status' => $this->status,
            'accepted_at' => $this->accepted_at,
            'cancelled_at' => $this->cancelled_at,
            'attendance_confirmed_at' => $this->attendance_confirmed_at,
            'cancellation_reason' => $this->cancellation_reason,
            'rejected_at' => $this->rejected_at,
            'rejection_reason' => $this->rejection_reason,
            'delay_requested_at' => $this->delay_requested_at,
            'fazaa_service' => FazaaServiceResource::make($this->fazaaService),
            'user' => UserResource::make($this->user),
        ];
    }
}
