<?php

namespace App\Http\Resources\Users\ServiceRequests\TripRequests;

use App\Http\Resources\AdditionalServices\AdditionalServicesResource;
use App\Http\Resources\Cities\CityResource;
use App\Http\Resources\Locations\FromLocationResource;
use App\Http\Resources\Locations\ToLocationResource;
use App\Http\Resources\Seats\TripRequestSeatsResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TripRequestResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'trip_type' => $this->tripTypeFormatted,
            'departure_datetime' => $this->departure_datetime,
            'arrival_datetime' => $this->arrival_datetime,
            'number_of_seats' => $this->number_of_seats,
            'from_city' => CityResource::make($this->fromCity),
            'from_location' => FromLocationResource::make($this),
            'to_city' => CityResource::make($this->toCity),
            'to_location' => ToLocationResource::make($this),
            'price' => $this->price,
            'allow_smoking' => $this->allow_smoking,
            'deliver_to_door' => $this->deliver_to_door,
            'seats' => TripRequestSeatsResource::collection($this->seats),
            'additional_services' => AdditionalServicesResource::collection($this->additionalServices),
            'cancelled_at' => $this->cancelled_at,
            'cancellation_reason' => $this->cancellation_reason,
            'note' => $this->note,
        ];
    }
}
