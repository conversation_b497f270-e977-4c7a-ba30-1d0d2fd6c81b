<?php

namespace App\Http\Resources\Users\Bookings\TripRequests;

use App\Http\Resources\Requests\TripRequests\TripRequestResource;
use App\Http\Resources\Users\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TripRequestsBookingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'note' => $this->note,
            'price' => $this->price,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'status' => $this->status,
            'accepted_at' => $this->accepted_at,
            'cancelled_at' => $this->cancelled_at,
            'cancellation_reason' => $this->cancellation_reason,
            'rejected_at' => $this->rejected_at,
            'rejection_reason' => $this->rejection_reason,
            'trip_request' => TripRequestResource::make($this->tripRequest),
            'user' => UserResource::make($this->user),
        ];
    }
}
