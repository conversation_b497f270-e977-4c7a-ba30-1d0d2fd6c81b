<?php

namespace App\Http\Resources\Users\Complaints;

use App\Http\Resources\Users\Bookings\FazaaRequests\FazaaRequestBookingResource;
use App\Http\Resources\Users\Bookings\FazaaServices\FazaaServiceBookingResource;
use App\Http\Resources\Users\Bookings\ShippingRequests\ShippingRequestBookingResource;
use App\Http\Resources\Users\Bookings\ShippingServices\ShippingServiceBookingResource;
use App\Http\Resources\Users\Bookings\TripRequests\TripRequestsBookingResource;
use App\Http\Resources\Users\Bookings\TripServices\TripServiceBookingResource;
use App\Models\Booking\FazaaRequestBooking;
use App\Models\Booking\FazaaServiceBooking;
use App\Models\Booking\ShippingRequestBooking;
use App\Models\Booking\ShippingServiceBooking;
use App\Models\Booking\TripRequestBooking;
use App\Models\Booking\TripServiceBooking;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ComplaintResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'description' => $this->description,
            'status' => $this->status,
            'admin_response' => $this->admin_response,
            'resolved_at' => $this->resolved_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'booking' => $this->whenLoaded('booking', function () {
                return match (get_class($this->booking)) {
                    TripServiceBooking::class => TripServiceBookingResource::make($this->booking),
                    TripRequestBooking::class => TripRequestsBookingResource::make($this->booking),
                    ShippingServiceBooking::class => ShippingServiceBookingResource::make($this->booking),
                    ShippingRequestBooking::class => ShippingRequestBookingResource::make($this->booking),
                    FazaaServiceBooking::class => FazaaServiceBookingResource::make($this->booking),
                    FazaaRequestBooking::class => FazaaRequestBookingResource::make($this->booking),
                    default => null,
                };
            }),
            'booking_type' => $this->whenLoaded('booking', function () {
                return match (get_class($this->booking)) {
                    TripServiceBooking::class => 'trip_service',
                    TripRequestBooking::class => 'trip_request',
                    ShippingServiceBooking::class => 'shipping_service',
                    ShippingRequestBooking::class => 'shipping_request',
                    FazaaServiceBooking::class => 'fazaa_service',
                    FazaaRequestBooking::class => 'fazaa_request',
                    default => null,
                };
            }),
        ];
    }
}
