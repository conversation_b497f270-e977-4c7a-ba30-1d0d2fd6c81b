<?php

namespace App\Http\Resources\Users\Services\Shipping;

use App\Http\Resources\Cities\CityResource;
use App\Http\Resources\Locations\FromLocationResource;
use App\Http\Resources\Locations\ToLocationResource;
use App\Http\Resources\Users\Cars\CarResource;
use App\Http\Resources\Users\Cars\CarTypeResource;
use App\Http\Resources\Users\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ShowShippingServiceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'transportation_type' => $this->transportation_type_formatted,
            'can_ship_packages' => $this->can_ship_packages,
            'can_ship_documents' => $this->can_ship_documents,
            'can_ship_furniture' => $this->can_ship_furniture,
            'departure_datetime' => $this->departure_datetime,
            'arrival_datetime' => $this->arrival_datetime,
            'from_city' => CityResource::make($this->fromCity),
            'from_location' => FromLocationResource::make($this),
            'to_city' => CityResource::make($this->toCity),
            'to_location' => ToLocationResource::make($this),
            'packages_volume' => $this->packages_volume,
            'available_packages_volume' => $this->available_packages_volume,

            'document_volume' => $this->document_volume,
            'available_document_volume' => $this->available_document_volume,

            'furniture_volume' => $this->furniture_volume,
            'available_furniture_volume' => $this->available_furniture_volume,
            'package_price' => $this->package_price,
            'document_price' => $this->document_price,
            'furniture_price' => $this->furniture_price,
            'from_city' => CityResource::make($this->fromCity),
            'from_location' => FromLocationResource::make($this),
            'to_city' => CityResource::make($this->toCity),
            'to_location' => ToLocationResource::make($this),
            'delivery_location' => $this->delivery_location,
            'price' => $this->price,
            'car' => CarResource::make($this->car),
            'note' => $this->note,
            'status' => $this->status,
            'cancelled_at' => $this->cancelled_at,
            'cancellation_reason' => $this->cancellation_reason,
            'attendance_confirmed_at' => $this->attendance_confirmed_at,
        ];
    }
}
