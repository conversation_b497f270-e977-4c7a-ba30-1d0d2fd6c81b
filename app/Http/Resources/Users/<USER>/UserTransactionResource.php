<?php

namespace App\Http\Resources\Users\Transactions;

use App\Http\Resources\Cities\CityResource;
use App\Http\Resources\Users\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserTransactionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // Calculate commission and profit
        $commission = $this->price * 0.10; // 10% commission
        $profit = $this->price - $commission;

        return [
            'trip_service_id' => $this->id,
            'from_city' => CityResource::make($this->fromCity),
            'to_city' => CityResource::make($this->toCity),
            'departure_datetime' => $this->departure_datetime,
            'arrival_datetime' => $this->arrival_datetime,
            'booking_count' => $this->accepted_bookings_count,
            'total' => $this->price,
            'profit' => $profit,
            'commission' => $commission,
            'tax' => 0, // Currently zero as per requirements
            'travelers' => $this->whenLoaded('travelers', fn () => UserResource::collection($this->travelers)),
        ];
    }
}
