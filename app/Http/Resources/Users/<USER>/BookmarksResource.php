<?php

namespace App\Http\Resources\Users\Bookmarks;

use App\Enums\Travel\TransportServiceTypeEnum;
use App\Http\Resources\Requests\FazaaRequests\FazaaRequestResource;
use App\Http\Resources\Requests\ShippingRequests\ShippingRequestResource;
use App\Http\Resources\Requests\TripRequests\TripRequestResource;
use App\Http\Resources\Services\FazaaServices\FazaaServiceResource;
use App\Http\Resources\Services\ShippingServices\ShippingServiceResource;
use App\Http\Resources\Services\TripServices\TripServiceResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BookmarksResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'bookmarkable_type' => $this->bookmarkable_type_formatted,
            'bookmarkable_id' => $this->bookmarkable_id,
            'bookmarkable' => match($this->bookmarkable_type_formatted) {
                TransportServiceTypeEnum::TRIP_SERVICE() => TripServiceResource::make($this->whenLoaded('bookmarkable')),
                TransportServiceTypeEnum::SHIPPING_SERVICE() => ShippingServiceResource::make($this->whenLoaded('bookmarkable')),
                TransportServiceTypeEnum::FAZAA_SERVICE() => FazaaServiceResource::make($this->whenLoaded('bookmarkable')),
                TransportServiceTypeEnum::TRIP_REQUEST() => TripRequestResource::make($this->whenLoaded('bookmarkable')),
                TransportServiceTypeEnum::SHIPPING_REQUEST() => ShippingRequestResource::make($this->whenLoaded('bookmarkable')),
                TransportServiceTypeEnum::FAZAA_REQUEST() => FazaaRequestResource::make($this->whenLoaded('bookmarkable')),
            },
            'created_at' => $this->created_at,
        ];
    }
}
