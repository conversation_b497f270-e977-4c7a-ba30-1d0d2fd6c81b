<?php

namespace App\Http\Resources\Users\Bookings\ShippingServices;

use App\Http\Resources\Services\ShippingServices\ShippingServiceResource;
use App\Http\Resources\Users\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ShippingServiceBookingsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'note' => $this->note,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'packages_volume' => $this->packages_volume,
            'document_volume' => $this->document_volume,
            'furniture_volume' => $this->furniture_volume,
            'status' => $this->status,
            'user' => UserResource::make($this->user),
        ];
    }
}
