<?php

namespace App\Http\Resources\Users\Bookings\ShippingServices;

use App\Http\Resources\Services\ShippingServices\ShippingServiceResource;
use App\Http\Resources\Users\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ShippingServiceBookingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'note' => $this->note,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'packages_volume' => $this->packages_volume,
            'document_volume' => $this->document_volume,
            'furniture_volume' => $this->furniture_volume,
            'status' => $this->status,
            'accepted_at' => $this->accepted_at,
            'cancelled_at' => $this->cancelled_at,
            'cancellation_reason' => $this->cancellation_reason,
            'rejected_at' => $this->rejected_at,
            'rejection_reason' => $this->rejection_reason,
            'attendance_confirmed_at' => $this->attendance_confirmed_at,
            'delay_requested_at' => $this->delay_requested_at,
            'shipping_service' => ShippingServiceResource::make($this->shippingService),
            'user' => UserResource::make($this->user),
        ];
    }
}
