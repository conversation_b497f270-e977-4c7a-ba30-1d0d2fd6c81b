<?php

namespace App\Http\Resources\Users\Cars\Seats;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SeatsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'number' => $this->number,
            'status' => $this->status,
        ];
    }
}
