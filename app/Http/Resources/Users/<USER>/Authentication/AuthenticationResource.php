<?php

namespace App\Http\Resources\Users\Accounts\Authentication;

use App\Http\Resources\Cities\CityResource;
use App\Http\Resources\Common\CountryResource;
use App\Http\Resources\Locations\UserLocationResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AuthenticationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'picture' => $this->picture,
            'mobile' => $this->mobile,
            'city' => CityResource::make($this->city),
            'gender' => $this->gender,
            'birth_date' => $this->birth_date,
            'country' => CountryResource::make($this->country),
            'location' => $this->latitude && $this->longitude ?
                UserLocationResource::make($this) : null,
            'token' => $this->token,
            'whatsapp_number' => $this->whatsapp_number,
            'is_profile_completed' => $this->is_profile_completed,
        ];
    }
}
