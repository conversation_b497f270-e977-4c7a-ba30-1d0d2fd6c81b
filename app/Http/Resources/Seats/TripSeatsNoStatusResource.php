<?php

namespace App\Http\Resources\Seats;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TripSeatsNoStatusResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'number' => $this->number,
        ];
    }
}
