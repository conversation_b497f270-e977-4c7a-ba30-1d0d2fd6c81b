<?php

namespace App\Http\Resources\Seats;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TripSeatsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'number' => $this->number,
            'status' => $this->status,
        ];
    }
}
