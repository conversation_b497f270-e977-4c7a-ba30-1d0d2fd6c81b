<?php

namespace App\Http\Resources\Services\TripServices;

use Illuminate\Http\Request;
use App\Http\Resources\Users\UserResource;
use App\Http\Resources\Cities\CityResource;
use Illuminate\Http\Resources\Json\JsonResource;

class TripServicesResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'trip_type' => $this->trip_type_formatted,
            'departure_datetime' => $this->departure_datetime,
            'arrival_datetime' => $this->arrival_datetime,
            'number_of_seats' => $this->number_of_seats,
            'number_of_available_seats' => $this->number_of_available_seats,
            'number_of_free_cartons' => $this->number_of_free_cartons,
            'from_city' => CityResource::make($this->fromCity),
            'to_city' => CityResource::make($this->toCity),
            'price' => $this->price,
            'allow_smoking' => $this->allow_smoking,
            'deliver_to_door' => $this->deliver_to_door,
            'weather_status' => $this->weather_status,
            'user' => UserResource::make($this->user),
        ];
    }
}
