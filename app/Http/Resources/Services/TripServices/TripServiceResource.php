<?php

namespace App\Http\Resources\Services\TripServices;

use App\Enums\Travel\SeatStatusEnum;
use Illuminate\Http\Request;
use App\Http\Resources\AdditionalServices\AdditionalServicesResource;
use App\Http\Resources\Users\UserResource;
use App\Http\Resources\Cities\CityResource;
use App\Http\Resources\Locations\FromLocationResource;
use App\Http\Resources\Locations\ToLocationResource;
use App\Http\Resources\Seats\TripSeatsNoStatusResource;
use App\Http\Resources\Users\Cars\CarResource;
use Illuminate\Http\Resources\Json\JsonResource;

class TripServiceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'trip_type' => $this->tripTypeFormatted,
            'departure_datetime' => $this->departure_datetime,
            'arrival_datetime' => $this->arrival_datetime,
            'number_of_seats' => $this->number_of_seats,
            'number_of_available_seats' => $this->number_of_available_seats,
            'number_of_free_cartons' => $this->number_of_free_cartons,
            'from_city' => CityResource::make($this->fromCity),
            'from_location' => FromLocationResource::make($this),
            'to_city' => CityResource::make($this->toCity),
            'to_location' => ToLocationResource::make($this),
            'price' => $this->price,
            'allow_smoking' => $this->allow_smoking,
            'deliver_to_door' => $this->deliver_to_door,
            'user' => UserResource::make($this->user),
            'additional_services' => AdditionalServicesResource::collection($this->additionalServices),
            'car' => CarResource::make($this->car),
            'available_seats' => TripSeatsNoStatusResource::collection($this->AvailableSeats),
            'unavailable_seats' => $this->getProcessedUnavailableSeats(),
            'note' => $this->note,
            'arrival_confirmed_at' => $this->arrival_confirmed_at,
        ];
    }

    /**
     * Get unavailable seats, ensuring A1 and A2 are always included, and sort them.
     *
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    private function getProcessedUnavailableSeats()
    {
        $unavailableSeats = $this->UnavailableSeats;

        $defaultSeatNumbers = ['A1', 'A2'];

        $currentUnavailableSeatNumbers = $unavailableSeats->pluck('number')->all();

        $missingSeatNumbers = array_diff($defaultSeatNumbers, $currentUnavailableSeatNumbers);

        $missingSeats = collect($missingSeatNumbers)->map(function ($seatNumber) {
            return (object) [
                'number' => $seatNumber,
                'status' => SeatStatusEnum::UNAVAILABLE(),
            ];
        });

        $allSeats = $unavailableSeats->concat($missingSeats);

        $sortedSeats = $allSeats->sortBy(function ($seat) {
            return $seat->number;
        })->values();

        return TripSeatsNoStatusResource::collection($sortedSeats);
    }
}
