<?php

namespace App\Http\Resources\Services\FazaaServices;

use App\Http\Resources\Cities\CityResource;
use App\Http\Resources\Users\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FazaaServicesResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'fazaa_service_type' => $this->fazaaServiceType->name,
            'specific_type' => $this->specificType->name,
            'transportation_type' => $this->transportation_type_formatted,
            'departure_datetime' => $this->departure_datetime,
            'arrival_datetime' => $this->arrival_datetime,
            'from_city' => CityResource::make($this->fromCity),
            'to_city' => CityResource::make($this->toCity),
            'service_location' => $this->service_location,
            'price' => $this->price,
            'user' => UserResource::make($this->user),
            'weather_status' => $this->weather_status,
        ];
    }
}
