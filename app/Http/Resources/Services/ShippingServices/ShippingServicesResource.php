<?php

namespace App\Http\Resources\Services\ShippingServices;

use App\Http\Resources\Cities\CityResource;
use App\Http\Resources\Users\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ShippingServicesResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'transportation_type' => $this->transportation_type_formatted,
            'can_ship_packages' => $this->can_ship_packages,
            'can_ship_documents' => $this->can_ship_documents,
            'can_ship_furniture' => $this->can_ship_furniture,
            'departure_datetime' => $this->departure_datetime,
            'arrival_datetime' => $this->arrival_datetime,

            'packages_volume' => $this->packages_volume,
            'available_packages_volume' => $this->available_packages_volume,

            'document_volume' => $this->document_volume,
            'available_document_volume' => $this->available_document_volume,

            'furniture_volume' => $this->furniture_volume,
            'available_furniture_volume' => $this->available_furniture_volume,

            'from_city' => CityResource::make($this->fromCity),
            'to_city' => CityResource::make($this->toCity),
            'delivery_location' => $this->delivery_location,
            'package_price' => $this->package_price,
            'document_price' => $this->document_price,
            'furniture_price' => $this->furniture_price,
            'user' => UserResource::make($this->user),
            'weather_status' => $this->weather_status,
        ];
    }
}
