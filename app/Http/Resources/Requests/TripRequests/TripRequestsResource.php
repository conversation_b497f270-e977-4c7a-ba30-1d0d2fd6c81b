<?php

namespace App\Http\Resources\Requests\TripRequests;

use App\Http\Resources\AdditionalServices\AdditionalServicesResource;
use App\Http\Resources\Cities\CityResource;
use App\Http\Resources\Users\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TripRequestsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'trip_type' => $this->trip_type_formatted,
            'departure_datetime' => $this->departure_datetime,
            'arrival_datetime' => $this->arrival_datetime,
            'number_of_seats' => $this->number_of_seats,
            'from_city' => CityResource::make($this->fromCity),
            'to_city' => CityResource::make($this->toCity),
            'price' => $this->price,
            'arrive_destination' => $this->arrive_destination,
            'allow_smoking' => $this->allow_smoking,
            'deliver_to_door' => $this->deliver_to_door,
            'additional_services' => AdditionalServicesResource::collection($this->additionalServices),
            'user' => UserResource::make($this->user),
            'weather_status' => $this->weather_status,
        ];
    }
}
