<?php

namespace App\Http\Resources\Requests\FazaaRequests;

use App\Http\Resources\Cities\CityResource;
use App\Http\Resources\Users\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FazaaRequestsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'transportation_type' => $this->transportation_type_formatted,
            'fazaa_service_type' => $this->fazaaServiceType->name,
            'specific_type' => $this->specificType->name,
            'departure_datetime' => $this->departure_datetime,
            'arrival_datetime' => $this->arrival_datetime,
            'from_city' => CityResource::make($this->fromCity),
            'to_city' => CityResource::make($this->toCity),
            'service_location' => $this->service_location,
            'user' => UserResource::make($this->user),
            'weather_status' => $this->weather_status,
        ];
    }
}
