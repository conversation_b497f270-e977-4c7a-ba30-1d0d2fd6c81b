<?php

namespace App\Http\Resources\Requests\ShippingRequests;

use App\Http\Resources\Cities\CityResource;
use App\Http\Resources\Users\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ShippingRequestsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'can_ship_packages' => $this->can_ship_packages,
            'can_ship_documents' => $this->can_ship_documents,
            'can_ship_furniture' => $this->can_ship_furniture,
            'departure_datetime' => $this->departure_datetime,
            'transportation_type' => $this->transportation_type,
            'arrival_datetime' => $this->arrival_datetime,
            'packages_volume' => $this->packages_volume,
            'document_volume' => $this->document_volume,
            'furniture_volume' => $this->furniture_volume,
            'from_city' => CityResource::make($this->fromCity),
            'to_city' => CityResource::make($this->toCity),
            'delivery_location' => $this->delivery_location,
            'user' => UserResource::make($this->user),
            'weather_status' => $this->weather_status,
        ];
    }
}
