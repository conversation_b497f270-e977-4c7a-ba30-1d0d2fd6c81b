<?php

namespace App\Http\Resources\Requests\ShippingRequests;

use App\Http\Resources\Cities\CityResource;
use App\Http\Resources\Locations\FromLocationResource;
use App\Http\Resources\Locations\ToLocationResource;
use App\Http\Resources\Users\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ShippingRequestResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'transportation_type' => $this->transportation_type_formatted,
            'can_ship_packages' => $this->can_ship_packages,
            'can_ship_documents' => $this->can_ship_documents,
            'can_ship_furniture' => $this->can_ship_furniture,
            'departure_datetime' => $this->departure_datetime,
            'arrival_datetime' => $this->arrival_datetime,
            'from_city' => CityResource::make($this->fromCity),
            'to_city' => CityResource::make($this->toCity),
            'from_location' => FromLocationResource::make($this),
            'to_location' => ToLocationResource ::make($this),
            'packages_volume' => $this->packages_volume,
            'document_volume' => $this->document_volume,
            'furniture_volume' => $this->furniture_volume,
            'delivery_location' => $this->delivery_location,
            'is_fragile' => $this->is_fragile,
            'note' => $this->note,
            'user' => UserResource::make($this->user),
        ];
    }
}
