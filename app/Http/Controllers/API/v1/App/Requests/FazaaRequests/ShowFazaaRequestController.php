<?php

namespace App\Http\Controllers\API\v1\App\Requests\FazaaRequests;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Requests\FazaaRequests\FazaaRequestResource;
use App\Models\Request\FazaaRequest;
use Illuminate\Http\Request;

class ShowFazaaRequestController extends Controller
{
    /**
     * Get a specific Fazaa request.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     *
     * @response FazaaRequestResource
     *
     * @tags Fazaa Requests
     */
    public function __invoke(Request $request,int $id)
    {
        $fazaaRequest = FazaaRequest::with(['fromCity', 'toCity', 'user'])
            ->findOrFail($id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            FazaaRequestResource::make($fazaaRequest)
        );
    }
}
