<?php

namespace App\Http\Controllers\API\v1\App\Requests\TripRequests;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Requests\TripRequests\TripRequestsResource;
use App\Models\Request\TripRequest;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class IndexTripRequestController extends Controller
{
    /**
     * Get the list of Trip requests.
     *
     * @param Request $request
     * @return JsonResponse
     *
     * @response Illuminate\Http\Resources\Json\AnonymousResourceCollection<Illuminate\Pagination\LengthAwarePaginator<TripRequestsResource>>
     *
     * @tags Trip Requests
     */
    public function __invoke(Request $request)
    {
        $orderBy = $request->input('order_by', 'departure_datetime');

        $sort = $request->input('sort', 'asc');

        $perPage = $request->input('per_page', 10);

        $query = TripRequest::query()->upcoming()->available()->where(
            fn(Builder $builder) => $builder->when(
                $request->input('departure_date'),
                fn(Builder $builder) => $builder->whereDate(
                    'departure_datetime',
                    $request->input('departure_date')
                )
            )->when(
                $request->input('departure_date_from') && $request->input('departure_date_to'),
                fn(Builder $builder) => $builder->whereBetween('departure_datetime', [
                    $request->input('departure_date_from'),
                    $request->input('departure_date_to')
                ])
            )->when(
                $request->input('price_from') && $request->input('price_to'),
                fn(Builder $builder) => $builder->whereBetween('price', [
                    $request->input('price_from'),
                    $request->input('price_to')
                ])
            )->when(
                $request->input('from_city'),
                fn(Builder $builder) => $builder->whereRelation('fromCity', 'name_ar', $request->input('from_city'))
                    ->orWhereRelation('fromCity', 'name_en', $request->input('from_city'))
            )->when(
                $request->input('to_city'),
                fn(Builder $builder) => $builder->whereRelation('toCity', 'name_ar', $request->input('from_city'))
                    ->orWhereRelation('toCity', 'name_en', $request->input('to_city'))
            )->when(
                $request->input('trip_type'),
                fn(Builder $builder) => $builder->where('trip_type', $request->input('trip_type'))
            )->when(
                $request->input('allow_smoking'),
                fn(Builder $builder) => $builder->where('allow_smoking', $request->input('allow_smoking'))
            )->when(
                $request->input('deliver_to_door'),
                fn(Builder $builder) => $builder->where('deliver_to_door', $request->input('deliver_to_door'))
            )
        )->with(['fromCity', 'toCity', 'user'])
            ->orderBy($orderBy, $sort)
            ->paginate($perPage);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            TripRequestsResource::collection($query)->appends($request->query())->toArray()
        );
    }
}
