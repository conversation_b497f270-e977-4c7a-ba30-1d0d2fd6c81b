<?php

namespace App\Http\Controllers\API\v1\App\Requests\TripRequests;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Requests\TripRequests\TripRequestResource;
use App\Models\Request\TripRequest;
use Illuminate\Http\Request;

class ShowTripRequestController extends Controller
{
    /**
     * Get a specific Trip request.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     *
     * @response TripRequestResource
     *
     * @tags Trip Requests
     */
    public function __invoke(Request $request, int $id)
    {
        $tripRequest = TripRequest::with(['fromCity', 'toCity', 'user', 'seats' => function ($query) {
            $query->orderBy('number');
        }])->findOrFail($id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            TripRequestResource::make($tripRequest)
        );
    }
}
