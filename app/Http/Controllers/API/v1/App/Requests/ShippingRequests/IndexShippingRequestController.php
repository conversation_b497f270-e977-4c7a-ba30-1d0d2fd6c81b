<?php

namespace App\Http\Controllers\API\v1\App\Requests\ShippingRequests;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Requests\ShippingRequests\ShippingRequestsResource;
use App\Models\Request\ShippingRequest;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class IndexShippingRequestController extends Controller
{
    /**
     * Get the list of Shipping requests.
     *
     * @param Request $request
     * @return JsonResponse
     *
     * @response Illuminate\Http\Resources\Json\AnonymousResourceCollection<Illuminate\Pagination\LengthAwarePaginator<ShippingRequestsResource>>
     *
     * @tags Shipping Requests
     */
    public function __invoke(Request $request)
    {
        $orderBy = $request->input('order_by', 'departure_datetime');

        $sort = $request->input('sort', 'asc');

        $perPage = $request->input('per_page', 10);

        $query = ShippingRequest::query()->upcoming()->where(
            fn(Builder $builder) => $builder->when(
                $request->input('departure_date'),
                fn(Builder $builder) => $builder->whereDate(
                    'departure_datetime',
                    $request->input('departure_date')
                )
            )->when(
                $request->input('departure_date_from') && $request->input('departure_date_to'),
                fn(Builder $builder) => $builder->whereBetween('departure_datetime', [
                    $request->input('departure_date_from'),
                    $request->input('departure_date_to')
                ])
            )->when(
                $request->input('from_city'),
                fn(Builder $builder) => $builder->whereRelation('fromCity', 'name_ar', $request->input('from_city'))
                ->orWhereRelation('fromCity', 'name_en', $request->input('from_city'))
            )->when(
                $request->input('to_city'),
                fn(Builder $builder) => $builder->whereRelation('toCity', 'name_ar', $request->input('from_city'))
                ->orWhereRelation('toCity', 'name_en', $request->input('to_city'))
            )
        )->with(['fromCity', 'toCity', 'user'])
            ->orderBy($orderBy, $sort)
            ->paginate($perPage);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            ShippingRequestsResource::collection($query)->appends($request->query())->toArray()
        );
    }
}
