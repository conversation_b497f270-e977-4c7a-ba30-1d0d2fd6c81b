<?php

namespace App\Http\Controllers\API\v1\App\Requests\ShippingRequests;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Requests\ShippingRequests\ShippingRequestResource;
use App\Models\Request\ShippingRequest;
use Illuminate\Http\Request;

class ShowShippingRequestController extends Controller
{
    /**
     * Get a specific Shipping request.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     *
     * @response ShippingRequestResource
     *
     * @tags Shipping Requests
     */
    public function __invoke(Request $request, int $id)
    {
        $shippingRequest = ShippingRequest::with(['fromCity', 'toCity', 'user'])
            ->findOrFail($id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            ShippingRequestResource::make($shippingRequest)
        );
    }
}
