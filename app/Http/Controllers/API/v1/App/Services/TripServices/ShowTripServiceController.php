<?php

namespace App\Http\Controllers\API\v1\App\Services\TripServices;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Services\TripServices\TripServiceResource;
use App\Models\Service\TripService;
use Illuminate\Http\Request;

class ShowTripServiceController extends Controller
{
    /**
     * Get details of a specific trip service.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     *
     * @response TripServiceResource
     *
     * @tags Trip Services
     */
    public function __invoke(Request $request, int $id)
    {
        $tripService = TripService::with([
            'fromCity',
            'toCity',
            'user' => function ($query) {
                $query->withAvg('tripServices as rating', 'rating')
                      ->withCasts(['rating' => 'float']);
            },
            'AvailableSeats',
            'UnavailableSeats'
        ])->findOrFail($id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            TripServiceResource::make($tripService)
        );
    }
}
