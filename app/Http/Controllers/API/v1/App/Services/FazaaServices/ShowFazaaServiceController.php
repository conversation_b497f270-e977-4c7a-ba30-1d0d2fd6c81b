<?php

namespace App\Http\Controllers\API\v1\App\Services\FazaaServices;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Services\FazaaServices\FazaaServiceResource;
use App\Models\Service\FazaaService;
use Illuminate\Http\Request;

class ShowFazaaServiceController extends Controller
{
    /**
     * Get a specific Fazaa service.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     *
     * @response FazaaServiceResource
     *
     * @tags Fazaa Services
     */
    public function __invoke(Request $request, int $id)
    {
        $fazaaService = FazaaService::with(['fromCity', 'toCity', 'user'])
            ->findOrFail($id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            FazaaServiceResource::make($fazaaService)
        );
    }
}
