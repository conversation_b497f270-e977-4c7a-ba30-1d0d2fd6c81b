<?php

namespace App\Http\Controllers\API\v1\App\Services\ShippingServices;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Services\ShippingServices\ShippingServicesResource;
use App\Models\Service\ShippingService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class IndexShippingServiceController extends Controller
{
    /**
     * Get a list of Shipping services.
     *
     * @param Request $request
     * @return JsonResponse
     *
     * @response Illuminate\Http\Resources\Json\AnonymousResourceCollection<Illuminate\Pagination\LengthAwarePaginator<ShippingServicesResource>>
     *
     * @tags Shipping Services
     */
    public function __invoke(Request $request)
    {
        $orderBy = $request->input('order_by', 'departure_datetime');

        $sort = $request->input('sort', 'asc');

        $perPage = $request->input('per_page', 10);

        $query = ShippingService::query()->upcoming()->availableVolumes()->where(
            fn(Builder $builder) => $builder->when(
                $request->input('departure_date'),
                fn(Builder $builder) => $builder->whereDate(
                    'departure_datetime',
                    $request->input('departure_date')
                )
            )->when(
                $request->input('departure_date_from') && $request->input('departure_date_to'),
                fn(Builder $builder) => $builder->whereBetween('departure_datetime', [
                    $request->input('departure_date_from'),
                    $request->input('departure_date_to')
                ])
            )->when(
                $request->input('from_city'),
                fn(Builder $builder) => $builder->whereRelation('fromCity', 'name_ar', $request->input('from_city'))
                ->orWhereRelation('fromCity', 'name_en', $request->input('from_city'))
            )->when(
                $request->input('to_city'),
                fn(Builder $builder) => $builder->whereRelation('toCity', 'name_ar', $request->input('from_city'))
                ->orWhereRelation('toCity', 'name_en', $request->input('to_city'))
            )
        )->with(['fromCity', 'toCity', 'user'])
            ->orderBy($orderBy, $sort)
            ->paginate($perPage);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            ShippingServicesResource::collection($query)->appends($request->query())->toArray()
        );
    }
}
