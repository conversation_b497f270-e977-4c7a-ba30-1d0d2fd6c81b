<?php

namespace App\Http\Controllers\API\v1\App\Services\ShippingServices;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Services\ShippingServices\ShippingServiceResource;
use App\Models\Service\ShippingService;
use Illuminate\Http\Request;

class ShowShippingServiceController extends Controller
{
    /**
     * Get a specific Shipping service.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     *
     * @response ShippingServiceResource
     *
     * @tags Shipping Services
     */
    public function __invoke(Request $request, int $id)
    {
        $shippingService = ShippingService::with(['fromCity', 'toCity', 'user'])
            ->findOrFail($id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            ShippingServiceResource::make($shippingService)
        );
    }
}
