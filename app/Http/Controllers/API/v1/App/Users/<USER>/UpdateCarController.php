<?php

namespace App\Http\Controllers\API\v1\App\Users\Cars;

use App\Data\Common\CarData;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Cars\UpdateCarRequest;
use App\Http\Resources\Users\Cars\CarResource;
use App\Models\Common\Car;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class UpdateCarController extends Controller
{
    /**
     * Update an existing car for the user.
     *
     * @param UpdateCarRequest $request
     * @param Car $car
     * @return JsonResponse
     *
     * @response CarResource
     *
     * @tags User Cars
     */
    public function __invoke(UpdateCarRequest $request, Car $car)
    {
        /** @var User */
        $user = Auth::user();

        $car = $user->cars()->findOrFail($car->id);

        $data = CarData::from($request);

        $car->update($data->toArray());

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.update_data'),
            CarResource::make($car->fresh())
        );
    }
}
