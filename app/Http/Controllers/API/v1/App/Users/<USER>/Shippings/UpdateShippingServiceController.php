<?php

namespace App\Http\Controllers\API\v1\App\Users\Services\Shippings;

use App\Data\Services\UpdateShippingServiceData;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Services\Shipping\UpdateShippingServiceRequest;
use App\Http\Resources\Users\Services\Shipping\ShippingServiceResource;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class UpdateShippingServiceController extends Controller
{
    /**
     * Update an existing shipping service.
     *
     * @param UpdateShippingServiceRequest $request
     * @param int $id
     * @return JsonResponse
     *
     * @response ShippingServiceResource
     *
     * @tags User Shipping Services
     */
    public function __invoke(UpdateShippingServiceRequest $request, $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $shippingService = $user->shippingServices()->findOrFail($id);

        if ($shippingService->cancelled_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.shipping_service.cannot_update_cancelled_shipping'),
                null,
                422
            );
        }

        if ($shippingService->departure_datetime <= Carbon::now()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.shipping_service.cannot_update_after_departure'),
                null,
                422
            );
        }

        $data = UpdateShippingServiceData::from($request);

        $shippingService->update($data->except(
            'from_city_en',
            'from_city_ar',
            'to_city_en',
            'to_city_ar',
            'from_location',
            'to_location',
        )->toArray());

        // Calculate and update available volumes
        if ($shippingService->can_ship_packages) {
            $shippingService->available_packages_volume = $shippingService->packages_volume - $shippingService->bookings()->sum('packages_volume');
        }
        if ($shippingService->can_ship_documents) {
            $shippingService->available_document_volume = $shippingService->document_volume - $shippingService->bookings()->sum('document_volume');
        }
        if ($shippingService->can_ship_furniture) {
            $shippingService->available_furniture_volume = $shippingService->furniture_volume - $shippingService->bookings()->sum('furniture_volume');
        }

        $shippingService->save();

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.update_data'),
            ShippingServiceResource::make($shippingService->fresh())
        );
    }
}
