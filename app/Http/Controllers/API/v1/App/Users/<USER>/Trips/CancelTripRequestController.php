<?php

namespace App\Http\Controllers\API\v1\App\Users\ServiceRequests\Trips;

use App\Events\TripRequestCancelledEvent;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\ServiceRequests\Trips\CancelTripRequestRequest;
use App\Http\Resources\Users\ServiceRequests\TripRequests\TripRequestResource;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class CancelTripRequestController extends Controller
{
    /**
     * Cancel an existing trip request.
     *
     * @param CancelTripRequestRequest $request
     * @param int $id
     * @return JsonResponse
     *
     * @response TripRequestResource
     *
     * @tags User Trip Requests
     */
    public function __invoke(CancelTripRequestRequest $request, $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $tripRequest = $user->tripRequests()->findOrFail($id);

        if ($tripRequest->cancelled_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_request.already_cancelled'),
                null,
                422
            );
        }

        if (Carbon::now()->gte($tripRequest->departure_datetime)) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_request.cannot_cancel_after_departure'),
                null,
                422
            );
        }

        if ($tripRequest->attendance_confirmed_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_request.cannot_cancel_after_attendance_confirmed'),
                null,
                422
            );
        }

        DB::beginTransaction();
        try {
            $tripRequest->update([
                'cancelled_at' => Carbon::now(),
                'cancellation_reason' => $request->cancellation_reason,
                'cancellation_note' => $request->note,
            ]);

            TripRequestCancelledEvent::dispatch($tripRequest->id);

            DB::commit();

            return ResponseHelper::sendSuccessResponse(
                __('messages.response.cancel_success'),
                TripRequestResource::make($tripRequest->fresh())
            );
        } catch (\Exception $e) {
            DB::rollBack();
            return ResponseHelper::sendFailedResponse(
                __('exceptions.internal_server_error'),
                ['error' => $e->getMessage()],
                500
            );
        }
    }
}
