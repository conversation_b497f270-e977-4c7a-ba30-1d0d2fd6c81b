<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\FazaaRequests;

use App\Data\Bookings\FazaaRequestBookingData;
use App\Enums\Bookings\BookingStatusEnum;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Bookings\FazaaRequests\StoreFazaaRequestBookingRequest;
use App\Http\Resources\Users\Bookings\FazaaRequests\FazaaRequestBookingResource;
use App\Models\Request\FazaaRequest;
use Illuminate\Support\Facades\Auth;

class StoreFazaaRequestBookingController extends Controller
{
    /**
     * Store a new Fazaa Request Booking.
     *
     * @param StoreFazaaRequestBookingRequest $request
     * @param int $id fazaa request ID
     * @return JsonResponse
     *
     * @response FazaaRequestBookingResource
     *
     * @tags User Bookings, Fazaa Requests
     */
    public function __invoke(StoreFazaaRequestBookingRequest $request, int $id)
    {
        $user = Auth::user();

        $fazaaRequest = FazaaRequest::findOrFail($id);

        $existingBooking = $fazaaRequest->bookings()->exists();

        if ($existingBooking) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.fazaa_request.booking.already_booked_fazaa_request'),
                null,
                422
            );
        }

        $data = FazaaRequestBookingData::from($request->validated());

        $booking = $fazaaRequest->bookings()->create($data->toArray() + [
            'user_id' => $user->id,
            'status' => BookingStatusEnum::PENDING(),
        ]);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.create_data'),
            FazaaRequestBookingResource::make($booking->load('fazaaRequest'))
        );
    }
}
