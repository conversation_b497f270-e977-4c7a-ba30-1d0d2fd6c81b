<?php

namespace App\Http\Controllers\API\v1\App\Users\Cars;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Cars\MyCarsResource;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class IndexMyCarController extends Controller
{
    /**
     * Get the user's cars.
     *
     * @param Request $request
     * @return JsonResponse
     *
     * @response Illuminate\Http\Resources\Json\AnonymousResourceCollection<Illuminate\Pagination\LengthAwarePaginator<MyCarsResource>>
     *
     * @tags User Cars
     */
    public function __invoke(Request $request)
    {
        /** @var User $user */
        $user = Auth::user();

        $perPage = $request->input('per_page', 10);

        $query = $user->cars()->with('type')->paginate($perPage);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            MyCarsResource::collection($query)->appends($request->query())->toArray()
        );
    }
}
