<?php

namespace App\Http\Controllers\API\v1\App\Users\Accounts\Profile;

use App\Data\Users\Accounts\Profile\ProfileData;
use App\DTOs\Users\Accounts\Profile\UpdateProfileDTO;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Accounts\Profile\UpdateProfileRequest;
use App\Http\Resources\Users\Accounts\Profile\ProfileResource;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Auth;

class UpdateProfileController extends Controller
{
    public function __invoke(UpdateProfileRequest $request): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();

        $data = ProfileData::from($request->validated());

        $profileAttributes = $data->except('picture', 'city_en', 'city_ar')->toArray();

        if ($data->picture instanceof UploadedFile) {
            $picturePath = $data->picture->storePublicly('users/pictures', 'public');
            $profileAttributes['picture'] = $picturePath;
        }

        $user->update($profileAttributes);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.update_data'),
            ProfileResource::make($user)
        );
    }
}
