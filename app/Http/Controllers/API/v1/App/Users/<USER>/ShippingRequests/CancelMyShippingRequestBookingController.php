<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\ShippingRequests;

use App\Enums\Bookings\BookingStatusEnum;
use App\Events\ShippingRequestBookingCancelled;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Bookings\ShippingRequests\CancelMyShippingRequestBookingRequest;
use App\Http\Resources\Users\Bookings\ShippingRequests\ShippingRequestBookingResource;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class CancelMyShippingRequestBookingController extends Controller
{
    /**
     * Cancel a specific shipping request booking for the authenticated user.
     *
     * @param CancelMyShippingRequestBookingRequest $request
     * @param int $bookingId
     * @return JsonResponse
     *
     * @response ShippingRequestBookingResource
     *
     * @tags User Bookings, Shipping Requests
     */
    public function __invoke(CancelMyShippingRequestBookingRequest $request, int $bookingId)
    {
        /** @var User $user */
        $user = Auth::user();

        $booking = $user->shippingRequestBookings()
            ->with(['shippingRequest'])
            ->findOrFail($bookingId);

        $booking->update([
            'status' => BookingStatusEnum::CANCELLED(),
            'cancellation_reason' => $request->validated('cancellation_reason'),
            'cancellation_note' => $request->validated('note'),
            'cancelled_at' => now(),
        ]);

        ShippingRequestBookingCancelled::dispatch($booking->id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.delete_data'),
            ShippingRequestBookingResource::make($booking)
        );
    }
}
