<?php

namespace App\Http\Controllers\API\v1\App\Users\Notifications;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class CountUnreadNotificationsController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $unreadCount = $request->user()->unreadNotifications()->count();

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            ['unread_count' => $unreadCount],
        );
    }
}
