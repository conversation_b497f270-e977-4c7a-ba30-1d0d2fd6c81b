<?php

namespace App\Http\Controllers\API\v1\App\Users\Services\Trips;

use App\Events\TripServiceDelayed;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Services\Trips\DelayTripServiceRequest;
use App\Http\Resources\Users\Services\Trips\TripServiceResource;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class DelayTripServiceController extends Controller
{
    /**
     * Delay an existing trip service.
     *
     * @param DelayTripServiceRequest $request
     * @param int $id Trip service ID
     * @return JsonResponse
     *
     * @response TripServiceResource
     *
     * @tags User Trip Services
     */
    public function __invoke(DelayTripServiceRequest $request, $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $tripService = $user->tripServices()->findOrFail($id);

        $newDepartureDateTime = Carbon::parse($request->departure_datetime);
        $newArrivalDateTime = Carbon::parse($request->arrival_datetime);

        if ($tripService->cancelled_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_service.cannot_delay_cancelled_trip'),
                null,
                422
            );
        }

        if ($tripService->departure_datetime <= Carbon::now()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_service.cannot_delay_started_trip'),
                null,
                422
            );
        }

        $tripService->update([
            'departure_datetime' => $newDepartureDateTime,
            'arrival_datetime' => $newArrivalDateTime,
            'delayed_at' => Carbon::now(),
            'delay_reason' => $request->delay_reason,
            'delay_note' => $request->note,
        ]);

        TripServiceDelayed::dispatch($tripService->id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.trip_service.delayed_successfully'),
            TripServiceResource::make($tripService->fresh())
        );
    }
}
