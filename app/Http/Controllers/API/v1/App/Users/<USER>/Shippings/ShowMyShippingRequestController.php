<?php

namespace App\Http\Controllers\API\v1\App\Users\ServiceRequests\Shippings;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\ServiceRequests\ShippingRequests\ShowShippingRequestResource;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ShowMyShippingRequestController extends Controller
{
    /**
     * Show details of a specific shipping request.
     *
     * @param Request $request
     * @param int $id Shipping request ID
     * @return JsonResponse
     *
     * @response ShowShippingRequestResource
     *
     * @tags User Shipping Requests
     */
    public function __invoke(Request $request, int $id): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();

        $shippingRequest = $user->shippingRequests()
            ->with(['fromCity', 'toCity'])
            ->findOrFail($id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            ShowShippingRequestResource::make($shippingRequest)
        );
    }
}
