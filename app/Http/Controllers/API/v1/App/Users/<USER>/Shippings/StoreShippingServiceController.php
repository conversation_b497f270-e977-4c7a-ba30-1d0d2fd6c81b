<?php

namespace App\Http\Controllers\API\v1\App\Users\Services\Shippings;

use App\Data\Services\ShippingServiceData;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Services\Shipping\StoreShippingServiceRequest;
use App\Http\Resources\Users\Services\Shipping\ShippingServiceResource;
use App\Jobs\Travel\ShippingService\RejectPendingBookingsAfterDepartureJob;
use App\Jobs\Travel\Weather\GetWeatherFromGeoLocationJob;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class StoreShippingServiceController extends Controller
{
    /**
     * Store a new shipping service.
     *
     * @param StoreShippingServiceRequest $request
     * @return JsonResponse
     *
     * @response ShippingServiceResource
     *
     * @tags User Shipping Services
     */
    public function __invoke(StoreShippingServiceRequest $request)
    {
        /** @var User */
        $user = Auth::user();

        $data = ShippingServiceData::from($request);

        $shipping = $user->shippingServices()->create($data->except(
            'from_city_en',
            'from_city_ar',
            'to_city_en',
            'to_city_ar',
            'from_location',
            'to_location',
        )->toArray());

        GetWeatherFromGeoLocationJob::dispatch($shipping);

        $shipping->load('fromCity', 'toCity', 'car');

        RejectPendingBookingsAfterDepartureJob::dispatch($shipping)->delay($shipping->departure_datetime);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.create_data'),
            ShippingServiceResource::make($shipping)
        );
    }
}
