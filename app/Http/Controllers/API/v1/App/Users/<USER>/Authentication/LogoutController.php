<?php

namespace App\Http\Controllers\API\v1\App\Users\Accounts\Authentication;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LogoutController extends Controller
{
    public function __invoke(Request $request): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();

        $notifiable_token_id = $user->currentAccessToken()->id;
        $user->devices()->where('notifiable_token_id', $notifiable_token_id)->delete();

        /** @var PersonalAccessToken $user */
        $user->currentAccessToken()->delete();

        return ResponseHelper::sendSuccessResponse(
            __('auth.logout'),
            null,
            200
        );
    }
}
