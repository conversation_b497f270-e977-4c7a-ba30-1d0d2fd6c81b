<?php

namespace App\Http\Controllers\API\v1\App\Users\ServiceRequests\Shippings;

use App\Enums\Travel\TripStatusEnum;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\ServiceRequests\ShippingRequests\MyShippingRequestsResource;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class IndexMyShippingRequestController extends Controller
{
    /**
     * Get the user's shipping requests.
     *
     * @param Request $request
     * @return JsonResponse
     *
     * @response Illuminate\Http\Resources\Json\AnonymousResourceCollection<Illuminate\Pagination\LengthAwarePaginator<MyShippingRequestsResource>>
     *
     * @tags User Shipping Requests
     */
    public function __invoke(Request $request)
    {
        /** @var User $user */
        $user = Auth::user();

        $perPage = $request->input('per_page', 10);

        $search = $request->input('search');

        $status = $request->input('status');

        $orderBy = $request->input('order_by', 'created_at');

        $sort = $request->input('sort', 'desc');

        $query = $user->shippingRequests()->with(
            ['fromCity', 'toCity']
        )->when(
            !is_null($status),
            function (Builder $query) use ($status) {
                return match ($status) {
                    TripStatusEnum::ACTIVE() => $query->active(),
                    TripStatusEnum::COMPLETED() => $query->completed(),
                    TripStatusEnum::CANCELLED() => $query->cancelled(),
                };
            }
        )->when($search, function ($query) use ($search) {
            $query->where('note', 'ilike', '%' . $search . '%')
            ->orWhereHas('fromCity', function ($query) use ($search) {
                $query->where('name_en', 'ilike', '%' . $search . '%')
                    ->orWhere('name_ar', 'ilike', '%' . $search . '%');
            })
            ->orWhereHas('toCity', function ($query) use ($search) {
                $query->where('name_en', 'ilike', '%' . $search . '%')
                    ->orWhere('name_ar', 'ilike', '%' . $search . '%');
            });
        })->orderBy($orderBy, $sort)->paginate($perPage);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            MyShippingRequestsResource::collection($query)->appends($request->query())->toArray()
        );
    }
}
