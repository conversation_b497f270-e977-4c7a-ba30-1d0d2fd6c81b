<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\TripRequests;

use App\Enums\Bookings\BookingStatusEnum;
use App\Events\TripRequestBookingAccepted;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Bookings\TripRequests\TripRequestsBookingResource;
use App\Models\Booking\TripRequestBooking;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AcceptTripRequestBookingController extends Controller
{
    /**
     * Accept a trip request booking.
     *
     * @param Request $request
     * @param int $bookingId
     * @return JsonResponse
     *
     * @response Illuminate\Http\Resources\Json\AnonymousResourceCollection<Illuminate\Pagination\LengthAwarePaginator<TripRequestsBookingResource>>
     *
     * @tags User Trip Requests Bookings
     */
    public function __invoke(Request $request, int $bookingId)
    {
        /** @var User $user */
        $user = Auth::user();

        $booking = TripRequestBooking::query()->whereHas('tripRequest', function ($query) use ($user) {
            $query->where('user_id', $user->id);
        })->findOrFail($bookingId);

        if ($booking->status !== BookingStatusEnum::PENDING()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_request.booking.booking_is_not_pending'),
                null,
                422
            );
        }

        $tripRequest = $booking->tripRequest;

        if ($tripRequest->bookings()->where('status', BookingStatusEnum::ACCEPTED())->exists()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_request.booking.another_booking_already_accepted'),
                null,
                422
            );
        }

        $booking->update([
            'status' => BookingStatusEnum::ACCEPTED(),
        ]);

        // Reject all other pending bookings for this trip request
        $booking->tripRequest->bookings()
            ->where('id', '!=', $booking->id)
            ->where('status', BookingStatusEnum::PENDING())
            ->update([
                'status' => BookingStatusEnum::REJECTED(),
                'rejected_at' => now(),
                'rejection_reason' => __(key: 'messages.trip_request.bookings.auto_rejected', locale: $booking->tripRequest->user->locale),
            ]);

        TripRequestBookingAccepted::dispatch($booking->id);

        $booking->load(['tripRequest.seats', 'tripRequest.additionalServices']);

        return ResponseHelper::sendSuccessResponse(
            __('messages.trip_request.bookings.accepted'),
            TripRequestsBookingResource::make($booking->fresh())
        );
    }
}
