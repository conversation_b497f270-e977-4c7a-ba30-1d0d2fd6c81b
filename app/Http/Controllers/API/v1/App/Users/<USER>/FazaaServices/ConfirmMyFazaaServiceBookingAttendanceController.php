<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\FazaaServices;

use App\Events\FazaaServiceBookingAttendanceConfirmedEvent;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Bookings\FazaaServices\FazaaServiceBookingResource;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ConfirmMyFazaaServiceBookingAttendanceController extends Controller
{
    /**
     * Confirm fazaa service booking attendance by the user.
     *
     * @param Request $request
     * @param int $id Booking ID
     * @return JsonResponse
     *
     * @response FazaaServiceBookingResource
     *
     * @tags User Fazaa Services Bookings
     */
    public function __invoke(Request $request, int $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $booking = $user->fazaaServiceBookings()
            ->with(['fazaaService'])
            ->findOrFail($id);

        if ($booking->attendance_confirmed_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.fazaa_service.booking.attendance_already_confirmed'),
                null,
                422
            );
        }

        if ($booking->cancelled_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.fazaa_service.booking.cannot_confirm_cancelled_booking'),
                null,
                422
            );
        }

        $now = Carbon::now();
        $departureTime = Carbon::parse($booking->fazaaService->departure_datetime);
        $minutesUntilDeparture = $now->diffInMinutes($departureTime, false);

        if ($minutesUntilDeparture > 30 || $minutesUntilDeparture < 0) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.fazaa_service.booking.attendance_confirmation_window'),
                null,
                422
            );
        }

        $booking->update([
            'attendance_confirmed_at' => $now
        ]);

        FazaaServiceBookingAttendanceConfirmedEvent::dispatch($booking->id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.fazaa_service.booking.attendance_confirmed'),
            FazaaServiceBookingResource::make($booking->fresh())
        );
    }
}
