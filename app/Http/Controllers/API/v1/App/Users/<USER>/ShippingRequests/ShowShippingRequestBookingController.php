<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\ShippingRequests;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Bookings\ShippingRequests\ShippingRequestBookingResource;
use App\Models\Booking\ShippingRequestBooking;
use Illuminate\Http\Request;

class ShowShippingRequestBookingController extends Controller
{
    /**
     * Get a specific shipping request booking.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     *
     * @response ShippingRequestBookingResource
     *
     * @tags User Bookings, Shipping Requests
     */
    public function __invoke(Request $request, int $id)
    {
        $booking = ShippingRequestBooking::with(['shippingRequest'])
            ->findOrFail($id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            ShippingRequestBookingResource::make($booking)
        );
    }
}
