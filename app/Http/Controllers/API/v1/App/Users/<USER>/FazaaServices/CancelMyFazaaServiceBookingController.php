<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\FazaaServices;

use App\Enums\Bookings\BookingStatusEnum;
use App\Events\FazaaServiceBookingCancelledEvent;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Bookings\FazaaServices\CancelMyFazaaServiceBookingRequest;
use App\Http\Resources\Users\Bookings\FazaaServices\FazaaServiceBookingResource;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class CancelMyFazaaServiceBookingController extends Controller
{
    /**
     * Cancel a Fazaa service booking.
     *
     * @param CancelMyFazaaServiceBookingRequest $request
     * @param int $bookingId
     * @return JsonResponse
     *
     * @response FazaaServiceBookingResource
     *
     * @tags User Fazaa Services Bookings
     */
    public function __invoke(CancelMyFazaaServiceBookingRequest $request, int $bookingId)
    {
        /** @var User $user */
        $user = Auth::user();

        $booking = $user->fazaaServiceBookings()
            ->with('fazaaService')
            ->findOrFail($bookingId);


        $booking->update([
            'status' => BookingStatusEnum::CANCELLED(),
            'cancellation_reason' => $request->validated('cancellation_reason'),
            'cancellation_note' => $request->validated('note'),
            'cancelled_at' => now(),
        ]);


        FazaaServiceBookingCancelledEvent::dispatch($booking->id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.fazaa_service.bookings.cancelled'),
            FazaaServiceBookingResource::make($booking->fresh())
        );
    }
}
