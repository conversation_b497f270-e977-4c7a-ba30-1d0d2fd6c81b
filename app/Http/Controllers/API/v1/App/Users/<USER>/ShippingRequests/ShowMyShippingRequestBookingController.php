<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\ShippingRequests;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Bookings\ShippingRequests\ShippingRequestBookingResource;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ShowMyShippingRequestBookingController extends Controller
{
    /**
     * Get a specific shipping request booking for the authenticated user.
     *
     * @param Request $request
     * @param int $bookingId
     * @return JsonResponse
     *
     * @response ShippingRequestBookingResource
     *
     * @tags User Bookings, Shipping Requests
     */
    public function __invoke(Request $request, int $bookingId)
    {
        /** @var User $user */
        $user = Auth::user();

        $booking = $user->shippingRequestBookings()
            ->with(['shippingRequest'])
            ->findOrFail($bookingId);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            ShippingRequestBookingResource::make($booking)
        );
    }
}
