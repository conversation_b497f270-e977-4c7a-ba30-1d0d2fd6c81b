<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\TripRequests;

use App\Enums\Bookings\BookingStatusEnum;
use App\Events\TripRequestSeatBooked;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Bookings\TripRequests\StoreTripRequestBookingRequest;
use App\Http\Resources\Users\Bookings\TripRequests\TripRequestsBookingResource;
use App\Models\Request\TripRequest;
use Illuminate\Support\Facades\Auth;

class StoreTripRequestBookingController extends Controller
{
    /**
     * Store a new Trip Request Booking.
     *
     * @param StoreTripRequestBookingRequest $request
     * @param int $id Trip request ID
     * @return JsonResponse
     *
     * @response TripRequestsBookingResource
     *
     * @tags User Bookings, Trip Requests
     */
    public function __invoke(StoreTripRequestBookingRequest $request, int $id)
    {
        $user = Auth::user();
        $tripRequest = TripRequest::query()->upcoming()->findOrFail($id);

        if ($tripRequest->bookings()->exists()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_request.booking.already_booked_trip_request'),
                null,
                422
            );
        }

        $booking = $tripRequest->bookings()->create([
            'user_id' => $user->id,
            'price' => $request->price,
            'note' => $request->note,
            'status' => BookingStatusEnum::PENDING(),
        ]);

        TripRequestSeatBooked::dispatch($booking->id);

        $booking->load(['tripRequest.additionalServices', 'tripRequest.seats']);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.create_data'),
            TripRequestsBookingResource::make($booking->load('tripRequest'))
        );
    }
}
