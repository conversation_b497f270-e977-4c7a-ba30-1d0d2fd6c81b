<?php

namespace App\Http\Controllers\API\v1\App\Users\ServiceRequests\Shippings;

use App\Events\ShippingRequestAttendanceConfirmedEvent;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\ServiceRequests\ShippingRequests\ShippingRequestResource;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class ConfirmShippingRequestAttendanceController extends Controller
{
    /**
     * Confirm attendance for a shipping request.
     *
     * @param int $id
     * @return JsonResponse
     *
     * @response ShippingRequestResource
     *
     * @tags User Shipping Requests
     */
    public function __invoke($id)
    {
        /** @var User $user */
        $user = Auth::user();

        $shippingRequest = $user->shippingRequests()->findOrFail($id);

        if ($shippingRequest->attendance_confirmed_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.shipping_request.attendance_already_confirmed'),
                null,
                422
            );
        }

        if ($shippingRequest->cancelled_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.shipping_request.cannot_confirm_cancelled_shipping'),
                null,
                422
            );
        }

        $now = Carbon::now();
        $departureTime = Carbon::parse($shippingRequest->departure_datetime);
        $minutesUntilDeparture = $now->diffInMinutes($departureTime, false);

        if ($minutesUntilDeparture > 30 || $minutesUntilDeparture < 0) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.shipping_request.attendance_confirmation_window'),
                null,
                422
            );
        }

        $shippingRequest->update([
            'attendance_confirmed_at' => $now
        ]);

        ShippingRequestAttendanceConfirmedEvent::dispatch($shippingRequest->id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.shipping_request.attendance_confirmed'),
            ShippingRequestResource::make($shippingRequest->fresh())
        );
    }
}
