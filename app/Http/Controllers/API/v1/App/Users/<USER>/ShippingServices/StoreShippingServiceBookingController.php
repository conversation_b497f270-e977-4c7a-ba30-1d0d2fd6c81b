<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\ShippingServices;

use App\Data\Bookings\ShippingServiceBookingData;
use App\Enums\Bookings\BookingStatusEnum;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Bookings\ShippingServices\StoreShippingServiceBookingRequest;
use App\Http\Resources\Users\Bookings\ShippingServices\ShippingServiceBookingResource;
use App\Models\Service\ShippingService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class StoreShippingServiceBookingController extends Controller
{
    /**
     * Store a new Shipping Service Booking.
     *
     * @param StoreShippingServiceBookingRequest $request
     * @param int $id Shipping service ID
     * @return JsonResponse
     *
     * @response ShippingServiceBookingResource
     *
     * @tags User Bookings, Shipping Services
     * @throws \Exception
     */
    public function __invoke(StoreShippingServiceBookingRequest $request, int $id)
    {
        $user = Auth::user();

        DB::beginTransaction();

        $shippingService = ShippingService::query()
            ->upcoming()
            ->lockForUpdate()
            ->findOrFail($id);

        try {
            if ($shippingService->isFullyBooked()) {
                DB::rollBack();
                return ResponseHelper::sendFailedResponse(
                    __('exceptions.shipping_service.booking.fully_booked'),
                    null,
                    422
                );
            }

            if (
                $shippingService->available_packages_volume < ($request->packages_volume ?? 0) ||
                $shippingService->available_document_volume < ($request->document_volume ?? 0) ||
                $shippingService->available_furniture_volume < ($request->furniture_volume ?? 0)
            ) {
                DB::rollBack();
                return ResponseHelper::sendFailedResponse(
                    __('exceptions.shipping_service.booking.not_enough_volume'),
                    null,
                    422
                );
            }
            $data = ShippingServiceBookingData::from($request->validated())->toArray();

            $booking = $shippingService->bookings()->create($data + [
                'user_id' => $user->id,
                'status' => $request->filled('note') ? BookingStatusEnum::PENDING()
                    : BookingStatusEnum::ACCEPTED(),
                'accepted_at' => $request->filled('note') ? null : now(),
            ]);

            DB::commit();

            return ResponseHelper::sendSuccessResponse(
                __('messages.response.create_data'),
                ShippingServiceBookingResource::make($booking->load('shippingService'))
            );
        } catch (\Exception $e) {
            DB::rollBack();
            return ResponseHelper::sendFailedResponse(__('exceptions.internal_server_error'), ['error' => $e->getMessage()], 500);
        }
    }
}
