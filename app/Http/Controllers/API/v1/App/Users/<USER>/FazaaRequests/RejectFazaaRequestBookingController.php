<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\FazaaRequests;

use App\Enums\Bookings\BookingStatusEnum;
use App\Events\FazaaRequestBookingRejected;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Bookings\FazaaRequests\RejectFazaaRequestBookingRequest;
use App\Http\Resources\Users\Bookings\FazaaRequests\FazaaRequestBookingResource;
use App\Models\Booking\FazaaRequestBooking;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class RejectFazaaRequestBookingController extends Controller
{
    /**
     * Reject a Fazaa request booking.
     *
     * @param RejectFazaaRequestBookingRequest $request
     * @param int $id Booking ID
     * @return JsonResponse
     *
     * @response FazaaRequestBookingResource
     *
     * @tags User Bookings, Fazaa Requests
     */
    public function __invoke(RejectFazaaRequestBookingRequest $request, int $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $booking = FazaaRequestBooking::with('fazaaRequest')
            ->whereHas('fazaaRequest', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->findOrFail($id);

        if ($booking->status !== BookingStatusEnum::PENDING()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.fazaa_request.booking.booking_is_not_pending'),
                null,
                422
            );
        }

        $booking->update([
            'status' => BookingStatusEnum::REJECTED(),
            'rejection_reason' => $request->validated('rejection_reason'),
            'rejection_note' => $request->note,
            'rejected_at' => now(),
        ]);

        FazaaRequestBookingRejected::dispatch($booking->id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.update_data'),
            FazaaRequestBookingResource::make($booking)
        );
    }
}
