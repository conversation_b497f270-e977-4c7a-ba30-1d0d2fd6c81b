<?php

namespace App\Http\Controllers\API\v1\App\Users\Services\Shippings;

use App\Events\ShippingServiceDelayed;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Services\Shipping\DelayShippingServiceRequest;
use App\Http\Resources\Users\Services\Shipping\ShippingServiceResource;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class DelayShippingServiceController extends Controller
{
    /**
     * Delay an existing shipping service.
     *
     * @param DelayShippingServiceRequest $request
     * @param int $id Shipping service ID
     * @return JsonResponse
     *
     * @response ShippingServiceResource
     *
     * @tags User Shipping Services
     */
    public function __invoke(DelayShippingServiceRequest $request, $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $shippingService = $user->shippingServices()->findOrFail($id);

        $newDepartureDateTime = Carbon::parse($request->departure_datetime);
        $newArrivalDateTime = Carbon::parse($request->arrival_datetime);

        if ($shippingService->cancelled_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.shipping_service.cannot_delay_cancelled_shipping'),
                null,
                422
            );
        }

        if ($shippingService->departure_datetime <= Carbon::now()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.shipping_service.cannot_delay_started_shipping'),
                null,
                422
            );
        }

        $shippingService->update([
            'departure_datetime' => $newDepartureDateTime,
            'arrival_datetime' => $newArrivalDateTime,
            'delayed_at' => Carbon::now(),
            'delay_reason' => $request->delay_reason,
            'delay_note' => $request->note,
        ]);

        ShippingServiceDelayed::dispatch($shippingService->id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.shipping_service.delayed_successfully'),
            ShippingServiceResource::make($shippingService->fresh())
        );
    }
}
