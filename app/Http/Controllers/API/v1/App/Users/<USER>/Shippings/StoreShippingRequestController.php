<?php

namespace App\Http\Controllers\API\v1\App\Users\ServiceRequests\Shippings;

use App\Data\ServiceRequests\ShippingRequestData;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\ServiceRequests\Shippings\StoreShippingRequestRequest;
use App\Http\Resources\Users\ServiceRequests\ShippingRequests\ShippingRequestResource;
use App\Jobs\Travel\ShippingRequest\RejectPendingBookingsAfterDepartureJob;
use App\Jobs\Travel\Weather\GetWeatherFromGeoLocationJob;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class StoreShippingRequestController extends Controller
{
    /**
     * Store a new shipping request.
     *
     * @param StoreShippingRequestRequest $request
     * @return JsonResponse
     *
     * @response ShippingRequestResource
     *
     * @tags User Shipping Requests
     */
    public function __invoke(StoreShippingRequestRequest $request)
    {
        /** @var User $user */
        $user = Auth::user();

        $data = ShippingRequestData::from($request->validated());

        $shippingRequest = $user->shippingRequests()->create($data->except(
            'from_city_en',
            'from_city_ar',
            'to_city_en',
            'to_city_ar',
            'from_location',
            'to_location',
        )->toArray());

        GetWeatherFromGeoLocationJob::dispatch($shippingRequest);

        $shippingRequest->refresh();

        RejectPendingBookingsAfterDepartureJob::dispatch($shippingRequest)->delay($shippingRequest->departure_datetime);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.create_data'),
            ShippingRequestResource::make($shippingRequest)
        );
    }
}
