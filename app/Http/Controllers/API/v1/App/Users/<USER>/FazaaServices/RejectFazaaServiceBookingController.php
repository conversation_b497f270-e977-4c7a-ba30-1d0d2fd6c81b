<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\FazaaServices;

use App\Enums\Bookings\BookingStatusEnum;
use App\Events\FazaaServiceBookingRejectedEvent;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Bookings\FazaaServices\RejectFazaaServiceBookingRequest;
use App\Http\Resources\Users\Bookings\FazaaServices\FazaaServiceBookingResource;
use App\Models\Booking\FazaaServiceBooking;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class RejectFazaaServiceBookingController extends Controller
{
    /**
     * Reject a Fazaa service booking.
     *
     * @param RejectFazaaServiceBookingRequest $request
     * @param int $id Booking ID
     * @return JsonResponse
     *
     * @response FazaaServiceBookingResource
     *
     * @tags User Fazaa Services Bookings
     */
    public function __invoke(RejectFazaaServiceBookingRequest $request, int $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $booking = FazaaServiceBooking::query()
            ->whereHas('fazaaService', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->with(['fazaaService'])
            ->findOrFail($id);

        if ($booking->status !== BookingStatusEnum::PENDING()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.fazaa_service.booking.booking_is_not_pending'),
                null,
                422
            );
        }

        $booking->update([
            'status' => BookingStatusEnum::REJECTED(),
            'rejection_reason' => $request->validated('rejection_reason'),
            'rejection_note' => $request->validated('note'),
            'rejected_at' => now(),
        ]);

        FazaaServiceBookingRejectedEvent::dispatch($booking->id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.fazaa_service.bookings.rejected'),
            FazaaServiceBookingResource::make($booking->fresh())
        );
    }
}
