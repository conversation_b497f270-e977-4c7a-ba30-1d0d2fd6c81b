<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\TripRequests;

use App\Enums\Bookings\BookingStatusEnum;
use App\Events\TripRequestBookingCancelledEvent;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Bookings\TripRequests\CancelMyTripRequestBookingRequest;
use App\Http\Resources\Users\Bookings\TripRequests\TripRequestsBookingResource;
use App\Models\User;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;

class CancelMyTripRequestBookingController extends Controller
{
    /**
     * Cancel a trip request booking.
     *
     * @param CancelMyTripRequestBookingRequest $request
     * @param int $bookingId
     * @return JsonResponse
     *
     * @response TripRequestsBookingResource
     *
     * @tags User Trip Requests Bookings
     */
    public function __invoke(CancelMyTripRequestBookingRequest $request, int $bookingId)
    {
        /** @var User $user */
        $user = Auth::user();

        $booking = $user->tripRequestBookings()
            ->with('tripRequest')
            ->findOrFail($bookingId);

        $booking->update([
            'status' => BookingStatusEnum::CANCELLED(),
            'cancellation_reason' => $request->validated('cancellation_reason'),
            'cancellation_note' => $request->validated('note'),
            'cancelled_at' => now(),
        ]);

        TripRequestBookingCancelledEvent::dispatch($booking->id);

        $booking->load(['tripRequest.seats', 'tripRequest.additionalServices']);

        return ResponseHelper::sendSuccessResponse(
            __('messages.trip_request.bookings.cancelled'),
            TripRequestsBookingResource::make($booking->fresh())
        );
    }
}
