<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookmarks;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Bookmarks\DestroyBookmarkRequest;
use Illuminate\Support\Facades\Auth;

class DestroyBookmarkController extends Controller
{
    /**
     * Remove a bookmark.
     *
     * @param DestroyBookmarkRequest $request
     * @return JsonResponse
     *
     * @tags User Bookmarks
     */
    public function __invoke(DestroyBookmarkRequest $request)
    {
        /** @var User $user */
        $user = Auth::user();

        $user->bookmarks()
            ->where('bookmarkable_id', $request->bookmarkable_id)
            ->firstOrFail()
            ->delete();

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.delete_data')
        );
    }
}
