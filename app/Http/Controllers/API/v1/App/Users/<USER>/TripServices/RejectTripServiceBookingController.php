<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\TripServices;

use App\Models\User;
use App\Helpers\ResponseHelper;
use Illuminate\Support\Facades\DB;
use App\Enums\Travel\SeatStatusEnum;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Enums\Bookings\BookingStatusEnum;
use App\Events\TripServiceBookingRejected;
use App\Http\Requests\App\Users\Bookings\TripServices\RejectTripServiceBookingRequest;
use App\Models\Booking\TripServiceBooking;
use App\Http\Resources\Users\Bookings\TripServices\TripServiceBookingResource;

class RejectTripServiceBookingController extends Controller
{
    public function __invoke(RejectTripServiceBookingRequest $request, int $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $booking = TripServiceBooking::query()
            ->whereHas('tripService', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->with(['tripService', 'seats.seat'])
            ->findOrFail($id);

        if ($booking->status !== BookingStatusEnum::PENDING()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_service.booking.booking_is_not_pending'),
                null,
                422
            );
        }

        DB::beginTransaction();

        try {
            $booking->update([
                'status' => BookingStatusEnum::REJECTED(),
                'rejection_reason' => $request->validated('rejection_reason'),
                'rejection_note' => $request->validated('note'),
                'rejected_at' => now(),
            ]);

            foreach ($booking->seats as $bookedSeat) {
                $bookedSeat->seat->update(['status' => SeatStatusEnum::AVAILABLE()]);
            }

            $booking->tripService->increment('number_of_available_seats', $booking->seats->count());

            DB::commit();

            TripServiceBookingRejected::dispatch($booking->id);

            $booking->load(['tripService.seats', 'tripService.additionalServices']);

            return ResponseHelper::sendSuccessResponse(
                __('messages.trip_service.bookings.rejected'),
                TripServiceBookingResource::make($booking->fresh())
            );
        } catch (\Exception $e) {
            DB::rollBack();
            return ResponseHelper::sendFailedResponse(__('exceptions.internal_server_error'), ['error' => $e->getMessage()], 500);
        }
    }
}
