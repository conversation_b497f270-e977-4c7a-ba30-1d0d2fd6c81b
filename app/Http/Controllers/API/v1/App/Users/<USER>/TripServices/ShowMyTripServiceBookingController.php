<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\TripServices;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Bookings\TripServices\TripServiceBookingResource;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ShowMyTripServiceBookingController extends Controller
{
    /**
     * Show details of a specific trip service booking made by the authenticated user.
     *
     * @param Request $request
     * @param int $id Booking ID
     * @return JsonResponse
     *
     * @response TripServiceBookingResource
     *
     * @tags User Trip Services Bookings
     */
    public function __invoke(Request $request, int $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $booking = $user->tripServiceBookings()->with([
            'tripService.fromCity',
            'tripService.toCity',
            'tripService.additionalServices',
            'tripService.seats',
            'seats.seat'
        ])->findOrFail($id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            TripServiceBookingResource::make($booking)
        );
    }
}
