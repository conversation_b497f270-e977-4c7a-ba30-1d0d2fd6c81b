<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\TripRequests;

use App\Enums\Bookings\BookingStatusEnum;
use App\Events\TripRequestBookingCancelledByOwnerEvent;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Bookings\TripRequests\CancelTripRequestBookingRequest;
use App\Http\Resources\Users\Bookings\TripRequests\TripRequestsBookingResource;
use App\Models\Booking\TripRequestBooking;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class CancelTripRequestBookingController extends Controller
{
    /**
     * Cancel a trip request booking.
     *
     * @param CancelTripRequestBookingRequest $request
     * @param int $bookingId
     * @return JsonResponse
     *
     * @response TripRequestsBookingResource
     *
     * @tags User Trip Requests Bookings
     */
    public function __invoke(CancelTripRequestBookingRequest $request, int $bookingId)
    {
        /** @var User $user */
        $user = Auth::user();

        $booking = TripRequestBooking::query()
            ->whereHas('tripRequest', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->where('id', $bookingId)
            ->firstOrFail();

        $booking->update([
            'status' => BookingStatusEnum::CANCELLED_BY_OWNER(),
            'cancellation_reason' => $request->validated('cancellation_reason'),
            'cancellation_note' => $request->validated('note'),
            'cancelled_at' => now(),
        ]);

        TripRequestBookingCancelledByOwnerEvent::dispatch($booking->id);

        $booking->load(['tripRequest.seats', 'tripRequest.additionalServices']);

        return ResponseHelper::sendSuccessResponse(
            __('messages.trip_request.bookings.cancelled'),
            TripRequestsBookingResource::make($booking->fresh())
        );
    }
}
