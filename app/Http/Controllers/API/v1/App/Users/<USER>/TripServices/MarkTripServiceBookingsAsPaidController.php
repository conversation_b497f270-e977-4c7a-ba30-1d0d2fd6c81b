<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\TripServices;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Bookings\TripServices\MarkTripServiceBookingsAsPaidRequest;
use App\Models\User;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class MarkTripServiceBookingsAsPaidController extends Controller
{
    /**
     * Mark multiple trip service bookings as paid.
     *
     * @param  MarkTripServiceBookingsAsPaidRequest  $request
     * @param  string  $id Trip service ID
     * @return JsonResponse
     */
    public function __invoke(MarkTripServiceBookingsAsPaidRequest $request, int $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $tripService = $user->tripServices()->findOrFail($id);

        $tripService->bookings()
            ->whereNull('paid_at')
            ->whereIn('id', $request->validated('booking_ids'))
            ->update(['paid_at' => Carbon::now()]);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.update_data'),
        );
    }
}
