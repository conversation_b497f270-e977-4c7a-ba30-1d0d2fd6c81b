<?php

namespace App\Http\Controllers\API\v1\App\Users\Notifications;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class MarkNotificationAsReadController extends Controller
{
    public function __invoke(string $id): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();

        $notification = $user->notifications()->where('id', $id)->first();

        if (!$notification) {
            return ResponseHelper::sendFailedResponse(
                __('messages.response.not_found'),
                null,
                404
            );
        }

        $notification->markAsRead();

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.update_data'),
            null
        );
    }
}
