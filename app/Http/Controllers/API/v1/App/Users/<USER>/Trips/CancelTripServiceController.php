<?php

namespace App\Http\Controllers\API\v1\App\Users\Services\Trips;

use App\Events\TripServiceCancelledEvent;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Services\Trips\CancelTripServiceRequest;
use App\Http\Resources\Users\Services\Trips\TripServiceResource;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class CancelTripServiceController extends Controller
{
    /**
     * Cancel an existing trip service.
     *
     * @param CancelTripServiceRequest $request
     * @param int $id
     * @return JsonResponse
     *
     * @response TripServiceResource
     *
     * @tags User Trip Services
     */
    public function __invoke(CancelTripServiceRequest $request, $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $tripService = $user->tripServices()->findOrFail($id);

        if ($tripService->cancelled_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_service.already_cancelled'),
                null,
                422
            );
        }

        if (Carbon::now()->gte($tripService->departure_datetime)) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_service.cannot_cancel_after_departure'),
                null,
                422
            );
        }

        DB::beginTransaction();
        try {
            $tripService->update([
                'cancelled_at' => Carbon::now(),
                'cancellation_reason' => $request->cancellation_reason,
                'cancellation_note' => $request->note,
            ]);

            TripServiceCancelledEvent::dispatch($tripService->id);

            DB::commit();

            return ResponseHelper::sendSuccessResponse(
                __('messages.response.cancel_success'),
                TripServiceResource::make($tripService->fresh())
            );
        } catch (\Exception $e) {
            DB::rollBack();
            return ResponseHelper::sendFailedResponse(
                __('exceptions.internal_server_error'),
                ['error' => $e->getMessage()],
                500
            );
        }
    }
}
