<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\TripServices;

use App\Events\TripServiceBookingAttendanceConfirmedEvent;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Bookings\TripServices\TripServiceBookingResource;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ConfirmMyTripServiceBookingAttendanceController extends Controller
{
    /**
     * Confirm trip service booking attendance by the user.
     *
     * @param Request $request
     * @param int $id Booking ID
     * @return JsonResponse
     *
     * @response TripServiceBookingResource
     *
     * @tags User Trip Services Bookings
     */
    public function __invoke(Request $request, int $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $booking = $user->tripServiceBookings()
            ->with(['tripService'])
            ->findOrFail($id);

        if ($booking->attendance_confirmed_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_service.booking.attendance_already_confirmed'),
                null,
                422
            );
        }

        if ($booking->cancelled_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_service.booking.cannot_confirm_cancelled_booking'),
                null,
                422
            );
        }

        $now = Carbon::now();
        $departureTime = Carbon::parse($booking->tripService->departure_datetime);
        $minutesUntilDeparture = $now->diffInMinutes($departureTime, false);

        if ($minutesUntilDeparture > 30 || $minutesUntilDeparture < 0) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_service.booking.attendance_confirmation_window'),
                null,
                422
            );
        }

        $booking->update([
            'attendance_confirmed_at' => $now
        ]);

        TripServiceBookingAttendanceConfirmedEvent::dispatch($booking->id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.trip_service.booking.attendance_confirmed'),
            TripServiceBookingResource::make($booking->fresh())
        );
    }
}
