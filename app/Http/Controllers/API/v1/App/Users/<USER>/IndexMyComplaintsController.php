<?php

namespace App\Http\Controllers\API\v1\App\Users\Complaints;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Complaints\ComplaintResource;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class IndexMyComplaintsController extends Controller
{
    /**
     * Get the list of user's complaints.
     *
     * @param Request $request
     * @return JsonResponse
     *
     * @response Illuminate\Http\Resources\Json\AnonymousResourceCollection<Illuminate\Pagination\LengthAwarePaginator<ComplaintResource>>
     *
     * @tags User Complaints
     */
    public function __invoke(Request $request)
    {
        /** @var User $user */
        $user = Auth::user();

        $sort = $request->input('sort', 'desc');
        $orderBy = $request->input('order_by', 'created_at');
        $perPage = $request->input('per_page', 10);

        $complaints = $user->complaints()
            ->with('booking')
            ->orderBy($orderBy, $sort)
            ->paginate($perPage);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            ComplaintResource::collection($complaints)->appends($request->query())->toArray()
        );
    }
}
