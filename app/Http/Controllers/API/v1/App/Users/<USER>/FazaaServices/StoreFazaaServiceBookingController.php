<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\FazaaServices;

use App\Data\Bookings\FazaaServiceBookingData;
use App\Enums\Bookings\BookingStatusEnum;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Bookings\FazaaServices\StoreFazaaServiceBookingRequest;
use App\Http\Resources\Users\Bookings\FazaaServices\FazaaServiceBookingResource;
use App\Models\Service\FazaaService;
use Illuminate\Support\Facades\Auth;

class StoreFazaaServiceBookingController extends Controller
{
    /**
     * Store a new Fazaa Service Booking.
     *
     * @param StoreFazaaServiceBookingRequest $request
     * @param int $id Fazaa service ID
     * @return JsonResponse
     *
     * @response FazaaServiceBookingResource
     *
     * @tags User Bookings, Fazaa Services
     */
    public function __invoke(StoreFazaaServiceBookingRequest $request, $id)
    {
        $user = Auth::user();

        $fazaaService = FazaaService::query()->upcoming()->findOrFail($id);

        $existingBooking = $fazaaService->bookings()->accepted()->exists();

        if ($existingBooking) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.fazaa_service.booking.already_booked_fazaa_service'),
                null,
                422
            );
        }

        $data = FazaaServiceBookingData::from($request->validated())->toArray();

        $booking = $fazaaService->bookings()->create($data + [
            'user_id' => $user->id,
            'status' => $request->filled('note') ? BookingStatusEnum::PENDING()
                : BookingStatusEnum::ACCEPTED(),
            'accepted_at' => $request->filled('note') ? null : now(),
        ]);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.create_data'),
            FazaaServiceBookingResource::make($booking->load('fazaaService'))
        );
    }
}
