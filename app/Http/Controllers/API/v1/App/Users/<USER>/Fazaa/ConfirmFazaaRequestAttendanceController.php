<?php

namespace App\Http\Controllers\API\v1\App\Users\ServiceRequests\Fazaa;

use App\Events\FazaaRequestAttendanceConfirmed;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\ServiceRequests\FazaaRequests\FazaaRequestResource;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class ConfirmFazaaRequestAttendanceController extends Controller
{
    /**
     * Confirm attendance for a fazaa request.
     *
     * @param int $id Fazaa request ID
     * @return JsonResponse
     *
     * @response FazaaRequestResource
     *
     * @tags User Fazaa Requests
     */
    public function __invoke($id)
    {
        /** @var User $user */
        $user = Auth::user();

        $fazaaRequest = $user->fazaaRequests()->findOrFail($id);

        if ($fazaaRequest->cancelled_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.fazaa_request.cannot_confirm_cancelled_request'),
                null,
                422
            );
        }

        if ($fazaaRequest->attendance_confirmed_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.fazaa_request.attendance_already_confirmed'),
                null,
                422
            );
        }

        $departureTime = Carbon::parse($fazaaRequest->departure_datetime);
        $now = Carbon::now();

        if ($now->diffInMinutes($departureTime) > 30 || $now->isAfter($departureTime)) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.fazaa_request.attendance_confirmation_window_invalid'),
                null,
                422
            );
        }

        $fazaaRequest->update([
            'attendance_confirmed_at' => $now,
        ]);

        FazaaRequestAttendanceConfirmed::dispatch($fazaaRequest->id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.fazaa_request.attendance_confirmed_successfully'),
            FazaaRequestResource::make($fazaaRequest->fresh())
        );
    }
}
