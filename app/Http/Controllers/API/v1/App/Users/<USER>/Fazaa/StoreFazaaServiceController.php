<?php

namespace App\Http\Controllers\API\v1\App\Users\Services\Fazaa;

use App\Data\Services\FazaaServiceData;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Services\Fazaa\StoreFazaaServiceRequest;
use App\Http\Resources\Users\Services\Fazaa\FazaaServiceResource;
use App\Jobs\Travel\FazaaService\RejectPendingBookingsAfterDepartureJob;
use App\Jobs\Travel\Weather\GetWeatherFromGeoLocationJob;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class StoreFazaaServiceController extends Controller
{
    /**
     * Store a new fazaa service.
     *
     * @param StoreFazaaServiceRequest $request
     * @return JsonResponse
     *
     * @response FazaaServiceResource
     *
     * @tags User Fazaa Services
     */
    public function __invoke(StoreFazaaServiceRequest $request)
    {
        /** @var User */
        $user = Auth::user();

        $data = FazaaServiceData::from($request);

        $fazaa = $user->fazaaServices()->create($data->except(
            'from_city_en',
            'from_city_ar',
            'to_city_en',
            'to_city_ar',
            'from_location',
            'to_location',
        )->toArray());

        GetWeatherFromGeoLocationJob::dispatch($fazaa);

        $fazaa->refresh();

        RejectPendingBookingsAfterDepartureJob::dispatch($fazaa)->delay($fazaa->departure_datetime);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.create_data'),
            FazaaServiceResource::make($fazaa)
        );
    }
}
