<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\ShippingRequests;

use App\Data\Bookings\ShippingRequestBookingData;
use App\Enums\Bookings\BookingStatusEnum;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Bookings\ShippingRequests\StoreShippingRequestBookingRequest;
use App\Http\Resources\Users\Bookings\ShippingRequests\ShippingRequestBookingResource;
use App\Models\Request\ShippingRequest;
use Illuminate\Support\Facades\Auth;

class StoreShippingRequestBookingController extends Controller
{
    /**
     * Store a new Shipping Request Booking.
     *
     * @param StoreShippingRequestBookingRequest $request
     * @param int $id Shipping request ID
     * @return JsonResponse
     *
     * @response ShippingRequestBookingResource
     *
     * @tags User Bookings, Shipping Requests
     */
    public function __invoke(StoreShippingRequestBookingRequest $request, int $id)
    {
        $user = Auth::user();

        $shippingRequest = ShippingRequest::query()->upcoming()->available()->findOrFail($id);

        $existingBooking = $shippingRequest->bookings()->exists();

        if ($existingBooking) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.shipping_request.booking.already_booked_shipping_request'),
                null,
                422
            );
        }

        $data = ShippingRequestBookingData::from($request->validated())->toArray();

        $booking = $shippingRequest->bookings()->create($data + [
            'user_id' => $user->id,
            'status' => BookingStatusEnum::PENDING(),
        ]);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.create_data'),
            ShippingRequestBookingResource::make($booking->load('shippingRequest'))
        );
    }
}
