<?php

namespace App\Http\Controllers\API\v1\App\Users\ServiceRequests\Shippings;

use App\Events\ShippingRequestDelayedEvent;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\ServiceRequests\Shippings\DelayShippingRequestRequest;
use App\Http\Resources\Users\ServiceRequests\ShippingRequests\ShippingRequestResource;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class DelayShippingRequestController extends Controller
{
    /**
     * Delay an existing shipping request.
     *
     * @param DelayShippingRequestRequest $request
     * @param int $id Shipping request ID
     * @return JsonResponse
     *
     * @response ShippingRequestResource
     *
     * @tags User Shipping Requests
     */
    public function __invoke(DelayShippingRequestRequest $request, $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $shippingRequest = $user->shippingRequests()->findOrFail($id);

        $newDepartureDateTime = Carbon::parse($request->departure_datetime);
        $newArrivalDateTime = Carbon::parse($request->arrival_datetime);

        if ($shippingRequest->cancelled_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.shipping_request.cannot_delay_cancelled_shipping'),
                null,
                422
            );
        }

        if ($shippingRequest->departure_datetime <= Carbon::now()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.shipping_request.cannot_delay_started_shipping'),
                null,
                422
            );
        }

        $shippingRequest->update([
            'departure_datetime' => $newDepartureDateTime,
            'arrival_datetime' => $newArrivalDateTime,
            'delayed_at' => Carbon::now(),
            'delay_reason' => $request->delay_reason,
            'delay_note' => $request->note,
        ]);

        ShippingRequestDelayedEvent::dispatch($shippingRequest->id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.shipping_request.delayed_successfully'),
            ShippingRequestResource::make($shippingRequest->fresh())
        );
    }
}
