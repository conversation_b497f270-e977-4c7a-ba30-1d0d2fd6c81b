<?php

namespace App\Http\Controllers\API\v1\App\Users\ServiceRequests\Fazaa;

use App\Events\FazaaRequestDelayed;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\ServiceRequests\Fazaa\DelayFazaaRequestRequest;
use App\Http\Resources\Users\ServiceRequests\FazaaRequests\FazaaRequestResource;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class DelayFazaaRequestController extends Controller
{
    /**
     * Delay an existing fazaa request.
     *
     * @param DelayFazaaRequestRequest $request
     * @param int $id Fazaa request ID
     * @return JsonResponse
     *
     * @response FazaaRequestResource
     *
     * @tags User Fazaa Requests
     */
    public function __invoke(DelayFazaaRequestRequest $request, $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $fazaaRequest = $user->fazaaRequests()->findOrFail($id);

        $newDepartureDateTime = Carbon::parse($request->departure_datetime);
        $newArrivalDateTime = Carbon::parse($request->arrival_datetime);
        $now = Carbon::now();

        if ($fazaaRequest->cancelled_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.fazaa_request.cannot_delay_cancelled_request'),
                null,
                422
            );
        }

        if ($fazaaRequest->departure_datetime <= $now) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.fazaa_request.cannot_delay_started_request'),
                null,
                422
            );
        }

        $fazaaRequest->update([
            'departure_datetime' => $newDepartureDateTime,
            'arrival_datetime' => $newArrivalDateTime,
            'delayed_at' => $now,
            'delay_reason' => $request->delay_reason,
            'delay_note' => $request->note,
        ]);

        FazaaRequestDelayed::dispatch($fazaaRequest->id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.fazaa_request.delayed_successfully'),
            FazaaRequestResource::make($fazaaRequest->fresh())
        );
    }
}
