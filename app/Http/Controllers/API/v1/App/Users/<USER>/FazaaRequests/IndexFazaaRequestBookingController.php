<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\FazaaRequests;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Bookings\FazaaRequests\FazaaRequestBookingsResource;
use App\Models\Request\FazaaRequest;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class IndexFazaaRequestBookingController extends Controller
{
    /**
     * Get list of Fazaa request bookings.
     *
     * @param Request $request
     * @param int $id Fazaa request ID
     * @return JsonResponse
     *
     * @response Illuminate\Http\Resources\Json\AnonymousResourceCollection<Illuminate\Pagination\LengthAwarePaginator<FazaaRequestBookingResource>>
     *
     * @tags User Bookings, Fazaa Requests
     */
    public function __invoke(Request $request, int $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $search = $request->input('search');

        $fazaaRequest = FazaaRequest::where('user_id', $user->id)
            ->findOrFail($id);

        $perPage = $request->input('per_page', 10);

        $bookings = $fazaaRequest->bookings()
            ->with('user')
            ->when($search, function ($query) use ($search) {
                $query->where('note', 'ilike', '%' . $search . '%')
                    ->orWhereRelation('user', 'name', 'ilike', '%' . $search . '%')
                    ->orWhereRelation('fromCity', 'name_ar', 'ilike', '%' . $search . '%')
                    ->orWhereRelation('fromCity', 'name_en', 'ilike', '%' . $search . '%')
                    ->orWhereRelation('toCity', 'name_ar', 'ilike', '%' . $search . '%')
                    ->orWhereRelation('toCity', 'name_en', 'ilike', '%' . $search . '%');
            })->paginate($perPage);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            FazaaRequestBookingsResource::collection($bookings)->appends($request->query())->toArray()
        );
    }
}
