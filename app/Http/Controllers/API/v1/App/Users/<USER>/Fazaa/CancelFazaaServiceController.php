<?php

namespace App\Http\Controllers\API\v1\App\Users\Services\Fazaa;

use App\Events\FazaaServiceCancelled;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Services\Fazaa\FazaaServiceResource;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CancelFazaaServiceController extends Controller
{
    /**
     * Cancel an existing fazaa service.
     *
     * @param Request $request
     * @param int $id Fazaa service ID
     * @return JsonResponse
     *
     * @response FazaaServiceResource
     *
     * @tags User Fazaa Services
     */
    public function __invoke(Request $request, $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $fazaaService = $user->fazaaServices()->findOrFail($id);

        if ($fazaaService->cancelled_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.fazaa_service.already_cancelled'),
                null,
                422
            );
        }

        if ($fazaaService->departure_datetime <= Carbon::now()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.fazaa_service.cannot_cancel_started_service'),
                null,
                422
            );
        }

        $fazaaService->update([
            'cancelled_at' => Carbon::now(),
            'cancellation_reason' => $request->cancellation_reason,
            'cancellation_note' => $request->note,
        ]);

        FazaaServiceCancelled::dispatch($fazaaService->id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.fazaa_service.cancelled_successfully'),
            FazaaServiceResource::make($fazaaService->fresh())
        );
    }
}
