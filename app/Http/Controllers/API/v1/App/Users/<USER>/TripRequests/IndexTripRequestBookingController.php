<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\TripRequests;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Bookings\TripRequests\TripRequestBookingsResource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class IndexTripRequestBookingController extends Controller
{
    /**
     * Get bookings for a specific trip request.
     *
     * @param Request $request
     * @param int $id Trip request ID
     * @return JsonResponse
     *
     * @response Illuminate\Http\Resources\Json\AnonymousResourceCollection<Illuminate\Pagination\LengthAwarePaginator<TripRequestsBookingResource>>
     *
     * @tags User Trip Request Bookings
     */
    public function __invoke(Request $request, int $id)
    {
        $perPage = $request->query('per_page', 15);

        $search = $request->input('search');

        /** @var User $user */
        $user = Auth::user();

        $tripRequest = $user->tripRequests()->findOrFail($id);

        $query = $tripRequest->bookings()
            ->with('user')
            ->when($search, function ($query) use ($search) {
                $query->where('note', 'like', '%' . $search . '%');
            })->latest()
            ->paginate($perPage);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            TripRequestBookingsResource::collection($query)->appends($request->query())->toArray()
        );
    }
}
