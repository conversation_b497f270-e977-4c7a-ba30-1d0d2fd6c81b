<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\ShippingServices;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Bookings\ShippingServices\ShippingServiceBookingsResource;
use App\Models\Service\ShippingService;
use Illuminate\Http\Request;

class IndexShippingServiceBookingController extends Controller
{
    /**
     * Get all bookings for a specific shipping service.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     *
     * @response Illuminate\Http\Resources\Json\AnonymousResourceCollection<Illuminate\Pagination\LengthAwarePaginator<ShippingServiceBookingResource>>
     *
     * @tags User Bookings, Shipping Services
     */
    public function __invoke(Request $request, int $id)
    {
        $perPage = $request->input('per_page', 10);

        $search = $request->input('search');

        $shippingService = ShippingService::findOrFail($id);

        $query = $shippingService->bookings()
            ->with('user')
            ->when($search, function ($query) use ($search) {
                $query->where('note', 'ilike', '%' . $search . '%')
                ->orWhereHas('shippingService', function ($query) use ($search) {
                    $query->where('note', 'ilike', '%' . $search . '%')
                    ->orWhereHas('fromCity', function ($query) use ($search) {
                        $query->where('name_en', 'ilike', '%' . $search . '%')
                            ->orWhere('name_ar', 'ilike', '%' . $search . '%');
                    })
                    ->orWhereHas('toCity', function ($query) use ($search) {
                        $query->where('name_en', 'ilike', '%' . $search . '%')
                            ->orWhere('name_ar', 'ilike', '%' . $search . '%');
                    });
                });
            })->paginate($perPage);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            ShippingServiceBookingsResource::collection($query)->appends($request->query())->toArray()
        );
    }
}
