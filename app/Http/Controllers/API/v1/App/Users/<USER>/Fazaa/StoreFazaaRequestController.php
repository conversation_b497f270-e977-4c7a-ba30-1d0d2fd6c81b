<?php

namespace App\Http\Controllers\API\v1\App\Users\ServiceRequests\Fazaa;

use App\Data\ServiceRequests\FazaaRequestData;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\ServiceRequests\Fazaa\StoreFazaaRequestRequest;
use App\Http\Resources\Users\ServiceRequests\FazaaRequests\FazaaRequestResource;
use App\Jobs\Travel\FazaaRequest\RejectPendingBookingsAfterDepartureJob;
use App\Jobs\Travel\Weather\GetWeatherFromGeoLocationJob;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class StoreFazaaRequestController extends Controller
{
    /**
     * Store a new Fazaa request.
     *
     * @param StoreFazaaRequestRequest $request
     * @return JsonResponse
     *
     * @response FazaaRequestResource
     *
     * @tags User Fazaa Requests
     */
    public function __invoke(StoreFazaaRequestRequest $request)
    {
        /** @var User */
        $user = Auth::user();

        $data = FazaaRequestData::from($request);

        $fazaa = $user->fazaaRequests()->create($data->except(
            'from_city_en',
            'from_city_ar',
            'to_city_en',
            'to_city_ar',
            'from_location',
            'to_location',
        )->toArray());

        GetWeatherFromGeoLocationJob::dispatch($fazaa);

        $fazaa->refresh();

        RejectPendingBookingsAfterDepartureJob::dispatch($fazaa)->delay($fazaa->departure_datetime);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.create_data'),
            FazaaRequestResource::make($fazaa)
        );
    }
}
