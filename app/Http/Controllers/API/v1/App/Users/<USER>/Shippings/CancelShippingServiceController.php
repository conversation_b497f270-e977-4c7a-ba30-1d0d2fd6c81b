<?php

namespace App\Http\Controllers\API\v1\App\Users\Services\Shippings;

use App\Events\ShippingServiceCancelledEvent;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Services\Shipping\CancelShippingServiceRequest;
use App\Http\Resources\Users\Services\Shipping\ShippingServiceResource;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class CancelShippingServiceController extends Controller
{
    /**
     * Cancel an existing shipping service.
     *
     * @param CancelShippingServiceRequest $request
     * @param int $id
     * @return JsonResponse
     *
     * @response ShippingServiceResource
     *
     * @tags User Shipping Services
     */
    public function __invoke(CancelShippingServiceRequest $request, $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $shippingService = $user->shippingServices()->findOrFail($id);

        if ($shippingService->cancelled_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.shipping_service.already_cancelled'),
                null,
                422
            );
        }

        if (Carbon::now()->gte($shippingService->departure_datetime)) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.shipping_service.cannot_cancel_after_departure'),
                null,
                422
            );
        }

        DB::beginTransaction();
        try {
            $shippingService->update([
                'cancelled_at' => Carbon::now(),
                'cancellation_reason' => $request->cancellation_reason,
                'cancellation_note' => $request->note,
            ]);

            ShippingServiceCancelledEvent::dispatch($shippingService->id);

            DB::commit();

            return ResponseHelper::sendSuccessResponse(
                __('messages.response.cancel_success'),
                ShippingServiceResource::make($shippingService->fresh())
            );
        } catch (\Exception $e) {
            DB::rollBack();
            return ResponseHelper::sendFailedResponse(
                __('exceptions.internal_server_error'),
                ['error' => $e->getMessage()],
                500
            );
        }
    }
}
