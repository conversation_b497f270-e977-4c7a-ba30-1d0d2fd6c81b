<?php

namespace App\Http\Controllers\API\v1\App\Users\Services\Shippings;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Services\Shipping\ShowShippingServiceResource;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ShowMyShippingServiceController extends Controller
{
    /**
     * Show details of a specific shipping service.
     *
     * @param Request $request
     * @param int $id Shipping service ID
     * @return JsonResponse
     *
     * @response ShowShippingServiceResource
     *
     * @tags User Shipping Services
     */
    public function __invoke(Request $request, int $id): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();

        $shippingService = $user->shippingServices()
            ->with([
                'fromCity',
                'toCity',
                'car',
                'car.type',
                'user'
            ])
            ->findOrFail($id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            ShowShippingServiceResource::make($shippingService)
        );
    }
}
