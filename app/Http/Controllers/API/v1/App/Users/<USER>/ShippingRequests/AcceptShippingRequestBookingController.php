<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\ShippingRequests;

use App\Enums\Bookings\BookingStatusEnum;
use App\Events\ShippingRequestBookingAccepted;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Bookings\ShippingRequests\ShippingRequestBookingResource;
use App\Models\Booking\ShippingRequestBooking;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AcceptShippingRequestBookingController extends Controller
{
    /**
     * Accept a shipping request booking.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     *
     * @response ShippingRequestBookingResource
     *
     * @tags User Bookings, Shipping Requests
     */
    public function __invoke(Request $request, int $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $booking = ShippingRequestBooking::query()
            ->with(['shippingRequest'])
            ->whereHas('shippingRequest', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->findOrFail($id);

        if ($booking->status !== BookingStatusEnum::PENDING()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.shipping_request.booking.booking_is_not_pending'),
                null,
                422
            );
        }

        $shippingRequest = $booking->shippingRequest;
        if ($shippingRequest->bookings()->where('status', BookingStatusEnum::ACCEPTED())->exists()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.shipping_request.booking.another_booking_already_accepted'),
                null,
                422
            );
        }

        $booking->update([
            'status' => BookingStatusEnum::ACCEPTED(),
        ]);

        // Reject all other pending bookings for this fazaa service
        $booking->shippingRequest->bookings()
            ->where('id', '!=', $booking->id)
            ->where('status', BookingStatusEnum::PENDING())
            ->update([
                'status' => BookingStatusEnum::REJECTED(),
                'rejected_at' => now(),
                'rejection_reason' => __(key: 'messages.shipping_request.bookings.auto_rejected', locale: $booking->shippingRequest->user->locale),
            ]);

        ShippingRequestBookingAccepted::dispatch($booking->id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.update_data'),
            ShippingRequestBookingResource::make($booking)
        );
    }
}
