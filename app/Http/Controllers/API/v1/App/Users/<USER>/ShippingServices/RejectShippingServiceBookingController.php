<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\ShippingServices;

use App\Enums\Bookings\BookingStatusEnum;
use App\Events\ShippingServiceBookingRejected;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Bookings\ShippingServices\RejectShippingServiceBookingRequest;
use App\Http\Resources\Users\Bookings\ShippingServices\ShippingServiceBookingResource;
use App\Models\Booking\ShippingServiceBooking;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class RejectShippingServiceBookingController extends Controller
{
    /**
     * Reject a shipping service booking.
     *
     * @param RejectShippingServiceBookingRequest $request
     * @param int $id
     * @return JsonResponse
     *
     * @response ShippingServiceBookingResource
     *
     * @tags User Bookings, Shipping Services
     */
    public function __invoke(RejectShippingServiceBookingRequest $request, int $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $booking = ShippingServiceBooking::query()
            ->with(['shippingService'])
            ->whereHas('shippingService', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->findOrFail($id);

        if ($booking->status !== BookingStatusEnum::PENDING()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.shipping_service.booking.booking_is_not_pending'),
                null,
                422
            );
        }

        $booking->update([
            'status' => BookingStatusEnum::REJECTED(),
            'rejection_reason' => $request->validated('rejection_reason'),
            'rejection_note' => $request->validated('note'),
            'rejected_at' => now(),
        ]);

        ShippingServiceBookingRejected::dispatch($booking->id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.delete_data'),
            ShippingServiceBookingResource::make($booking)
        );
    }
}
