<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\ShippingServices;

use App\Events\ShippingServiceBookingAttendanceConfirmedEvent;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Bookings\ShippingServices\ShippingServiceBookingResource;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ConfirmMyShippingServiceBookingAttendanceController extends Controller
{
    /**
     * Confirm shipping service booking attendance by the user.
     *
     * @param Request $request
     * @param int $id Booking ID
     * @return JsonResponse
     *
     * @response ShippingServiceBookingResource
     *
     * @tags User Shipping Services Bookings
     */
    public function __invoke(Request $request, int $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $booking = $user->shippingServiceBookings()
            ->with(['shippingService'])
            ->findOrFail($id);

        if ($booking->attendance_confirmed_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.shipping_service.booking.attendance_already_confirmed'),
                null,
                422
            );
        }

        if ($booking->cancelled_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.shipping_service.booking.cannot_confirm_cancelled_booking'),
                null,
                422
            );
        }

        $now = Carbon::now();
        $departureTime = Carbon::parse($booking->shippingService->departure_datetime);
        $minutesUntilDeparture = $now->diffInMinutes($departureTime, false);

        if ($minutesUntilDeparture > 30 || $minutesUntilDeparture < 0) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.shipping_service.booking.attendance_confirmation_window'),
                null,
                422
            );
        }

        $booking->update([
            'attendance_confirmed_at' => $now
        ]);

        ShippingServiceBookingAttendanceConfirmedEvent::dispatch($booking->id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.shipping_service.booking.attendance_confirmed'),
            ShippingServiceBookingResource::make($booking->fresh())
        );
    }
}
