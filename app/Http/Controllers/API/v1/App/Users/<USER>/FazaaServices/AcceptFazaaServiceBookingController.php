<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\FazaaServices;

use App\Enums\Bookings\BookingStatusEnum;
use App\Events\FazaaServiceBookingAcceptedEvent;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Bookings\FazaaServices\FazaaServiceBookingResource;
use App\Models\Booking\FazaaServiceBooking;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class AcceptFazaaServiceBookingController extends Controller
{
    /**
     * Accept a Fazaa service booking.
     *
     * @param int $id Booking ID
     * @return JsonResponse
     *
     * @response FazaaServiceBookingResource
     *
     * @tags User Fazaa Services Bookings
     */
    public function __invoke(int $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $booking = FazaaServiceBooking::query()
            ->whereHas('fazaaService', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->with(['fazaaService'])
            ->findOrFail($id);

        if ($booking->status !== BookingStatusEnum::PENDING()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.fazaa_service.booking.booking_is_not_pending'),
                null,
                422
            );
        }

        $booking->update([
            'status' => BookingStatusEnum::ACCEPTED(),
        ]);

        // Reject all other pending bookings for this fazaa service
        $booking->fazaaService->bookings()
            ->where('id', '!=', $booking->id)
            ->where('status', BookingStatusEnum::PENDING())
            ->update([
                'status' => BookingStatusEnum::REJECTED(),
                'rejected_at' => now(),
                'rejection_reason' => __(key:'messages.fazaa_service.bookings.auto_rejected', locale: $booking->fazaaService->user->locale),
            ]);

        FazaaServiceBookingAcceptedEvent::dispatch($booking->id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.fazaa_service.bookings.accepted'),
            FazaaServiceBookingResource::make($booking->fresh())
        );
    }
}
