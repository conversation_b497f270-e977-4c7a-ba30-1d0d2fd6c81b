<?php

namespace App\Http\Controllers\API\v1\App\Users\ServiceRequests\Shippings;

use App\Data\ServiceRequests\UpdateShippingRequestData;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\ServiceRequests\Shippings\UpdateShippingRequestRequest;
use App\Http\Resources\Users\ServiceRequests\ShippingRequests\ShippingRequestResource;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Spatie\LaravelData\Optional;

class UpdateShippingRequestController extends Controller
{
    /**
     * Update an existing shipping request.
     *
     * @param UpdateShippingRequestRequest $request
     * @param int $id
     * @return JsonResponse
     *
     * @response ShippingRequestResource
     *
     * @tags User Shipping Requests
     */
    public function __invoke(UpdateShippingRequestRequest $request, $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $shippingRequest = $user->shippingRequests()->findOrFail($id);

        if ($shippingRequest->cancelled_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.shipping_request.cannot_update_cancelled_shipping'),
                null,
                422
            );
        }

        if ($shippingRequest->departure_datetime <= Carbon::now()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.shipping_request.cannot_update_after_departure'),
                null,
                422
            );
        }

        $data = UpdateShippingRequestData::from($request);

        $shippingRequest->update($data->except(
            'from_city_en',
            'from_city_ar',
            'to_city_en',
            'to_city_ar',
            'from_location',
            'to_location',
        )->toArray());

        $shippingRequest->refresh();

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.update_data'),
            ShippingRequestResource::make($shippingRequest)
        );
    }
}
