<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\TripServices;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Bookings\TripServices\TripServiceBookingsResource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class IndexTripServiceBookingController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, int $id)
    {
        $perPage = $request->query('per_page', 15);

        /** @var User $user */
        $user = Auth::user();

        $tripService = $user->tripServices()->findOrFail($id);

        $query = $tripService->bookings()
            ->with(['tripService', 'user', 'seats'])
            ->latest()
            ->paginate($perPage);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            TripServiceBookingsResource::collection($query)->appends($request->query())->toArray()
        );
    }
}
