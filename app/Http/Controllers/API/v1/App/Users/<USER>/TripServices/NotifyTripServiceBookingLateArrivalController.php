<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\TripServices;

use App\Events\TripServiceBookingLateArrivalNotified;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Bookings\TripServices\TripServiceBookingResource;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NotifyTripServiceBookingLateArrivalController extends Controller
{
    /**
     * Notify that the user will be arriving late for a trip service booking.
     *
     * @param Request $request
     * @param string $bookingId Trip service booking ID
     * @return JsonResponse
     *
     * @response TripServiceBookingResource
     *
     * @tags User Trip Service Bookings
     */
    public function __invoke(Request $request, $bookingId)
    {
        /** @var User $user */
        $user = Auth::user();

        $booking = $user->tripServiceBookings()->findOrFail($bookingId);
        $tripService = $booking->tripService;

        if ($booking->cancelled_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_service.booking.cannot_notify_late_arrival_cancelled_booking'),
                null,
                422
            );
        }

        if ($booking->rejected_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_service.booking.cannot_notify_late_arrival_rejected_booking'),
                null,
                422
            );
        }

        if ($tripService->arrival_datetime <= Carbon::now()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_service.booking.cannot_notify_late_arrival_completed_trip'),
                null,
                422
            );
        }

        if ($tripService->cancelled_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_service.booking.cannot_notify_late_arrival_cancelled_trip'),
                null,
                422
            );
        }

        $booking->update([
            'late_arrival_notified_at' => Carbon::now(),
        ]);

        TripServiceBookingLateArrivalNotified::dispatch($booking->id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.trip_service.booking.late_arrival_notified_successfully'),
            TripServiceBookingResource::make($booking->fresh())
        );
    }
}
