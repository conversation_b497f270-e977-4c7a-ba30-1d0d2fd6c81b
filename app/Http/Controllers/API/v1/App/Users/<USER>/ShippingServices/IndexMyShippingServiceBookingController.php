<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\ShippingServices;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Bookings\ShippingServices\ShippingServiceBookingResource;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Enums\Bookings\BookingStatusEnum;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;

class IndexMyShippingServiceBookingController extends Controller
{
    /**
     * Get the user's shipping service bookings.
     *
     * @param Request $request
     * @return JsonResponse
     *
     * @response Illuminate\Http\Resources\Json\AnonymousResourceCollection<Illuminate\Pagination\LengthAwarePaginator<ShippingServiceBookingResource>>
     *
     * @tags User Bookings, Shipping Services
     */
    public function __invoke(Request $request)
    {
        /** @var User $user */
        $user = Auth::user();

        $perPage = $request->input('per_page', 10);

        $status = $request->input('status');

        $search = $request->input('search');

        $orderBy = $request->input('order_by', 'created_at');

        $sort = $request->input('sort', 'desc');

        $query = $user->shippingServiceBookings()
            ->with(['shippingService'])
            ->when(
                $status,
                function (Builder $query) use ($status) {
                    return match ($status) {
                        BookingStatusEnum::PENDING() => $query->where('status', BookingStatusEnum::PENDING()),
                        BookingStatusEnum::ACCEPTED() => $query->where('status', BookingStatusEnum::ACCEPTED()),
                        BookingStatusEnum::REJECTED() => $query->where('status', BookingStatusEnum::REJECTED()),
                        BookingStatusEnum::CANCELLED() => $query->where('status', BookingStatusEnum::CANCELLED())
                            ->orWhere('status', BookingStatusEnum::CANCELLED_BY_OWNER()),
                        default => $query
                    };
                }
            )->when($search, function ($query) use ($search) {
                $query->where('note', 'ilike', '%' . $search . '%')
                    ->orWhereHas('shippingService', function ($query) use ($search) {
                        $query->where('note', 'ilike', '%' . $search . '%')
                            ->orWhereHas('fromCity', function ($query) use ($search) {
                                $query->where('name_en', 'ilike', '%' . $search . '%')
                                    ->orWhere('name_ar', 'ilike', '%' . $search . '%');
                            })
                            ->orWhereHas('toCity', function ($query) use ($search) {
                                $query->where('name_en', 'ilike', '%' . $search . '%')
                                    ->orWhere('name_ar', 'ilike', '%' . $search . '%');
                            });
                    });
            })->orderBy($orderBy, $sort)->paginate($perPage);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            ShippingServiceBookingResource::collection($query)->appends($request->query())->toArray()
        );
    }
}
