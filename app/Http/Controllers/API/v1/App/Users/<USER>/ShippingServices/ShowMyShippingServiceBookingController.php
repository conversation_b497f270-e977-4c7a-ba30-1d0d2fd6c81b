<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\ShippingServices;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Bookings\ShippingServices\ShippingServiceBookingResource;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ShowMyShippingServiceBookingController extends Controller
{
    /**
     * Get a specific shipping service booking for the authenticated user.
     *
     * @param Request $request
     * @param int $bookingId
     * @return JsonResponse
     *
     * @response ShippingServiceBookingResource
     *
     * @tags User Bookings, Shipping Services
     */
    public function __invoke(Request $request, int $bookingId)
    {
        /** @var User $user */
        $user = Auth::user();

        $booking = $user->shippingServiceBookings()
            ->with(['shippingService'])
            ->findOrFail($bookingId);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            ShippingServiceBookingResource::make($booking)
        );
    }
}
