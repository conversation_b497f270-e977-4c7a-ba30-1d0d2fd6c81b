<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookmarks;

use App\Enums\Travel\TransportServiceTypeEnum;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Bookmarks\StoreBookmarkRequest;
use App\Models\Request\FazaaRequest;
use App\Models\Request\ShippingRequest;
use App\Models\Request\TripRequest;
use App\Models\Service\FazaaService;
use App\Models\Service\ShippingService;
use App\Models\Service\TripService;
use Illuminate\Support\Facades\Auth;

class StoreBookmarkController extends Controller
{
    /**
     * Store a new bookmark.
     *
     * @param StoreBookmarkRequest $request
     * @return JsonResponse
     *
     * @response null
     *
     * @tags User Bookmarks
     */
    public function __invoke(StoreBookmarkRequest $request)
    {
        /** @var User $user */
        $user = Auth::user();

        $attributes = $request->validated();

        $attributes['bookmarkable_type'] = match($attributes['bookmarkable_type']) {
            TransportServiceTypeEnum::TRIP_SERVICE() => TripService::class,
            TransportServiceTypeEnum::SHIPPING_SERVICE() => ShippingService::class,
            TransportServiceTypeEnum::FAZAA_SERVICE() => FazaaService::class,
            TransportServiceTypeEnum::TRIP_REQUEST() => TripRequest::class,
            TransportServiceTypeEnum::SHIPPING_REQUEST() => ShippingRequest::class,
            TransportServiceTypeEnum::FAZAA_REQUEST() => FazaaRequest::class,
            default => throw new \InvalidArgumentException('Invalid transportation type',422)
        };

        //find service or request
        $attributes['bookmarkable_type']::findOrFail($attributes['bookmarkable_id']);

        $user->bookmarks()->firstOrCreate($attributes);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.create_data'),
            null
        );
    }
}
