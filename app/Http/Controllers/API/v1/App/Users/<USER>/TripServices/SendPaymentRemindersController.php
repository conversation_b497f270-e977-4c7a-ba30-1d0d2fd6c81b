<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\TripServices;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\Service\TripService;
use App\Models\User;
use App\Notifications\TripServicePaymentReminderNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;

class SendPaymentRemindersController extends Controller
{
    /**
     * Send payment reminders to travelers who haven't paid yet.
     *
     * @param  string  $id Trip service ID
     * @return JsonResponse
     */
    public function __invoke(Request $request, int $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $tripService = $user->tripServices()->findOrFail($id);

        $unpaidBookingsQuery = $tripService->bookings()
            ->accepted()
            ->whereNull('paid_at');

        if ($unpaidBookingsQuery->count() === 0) {
            return ResponseHelper::sendSuccessResponse(
                __('exceptions.trip_service.bookings.no_unpaid_bookings'),
            );
        }

        $unpaidBookingsQuery->with('user')->get()->each(function ($booking) use ($tripService) {
            Notification::send($booking->user, new TripServicePaymentReminderNotification(
                $tripService->id,
                $booking->id,
                $tripService->fromCity->name,
                $tripService->toCity->name,
                $tripService->departure_datetime,
            ));
        });

        return ResponseHelper::sendSuccessResponse(
            __('messages.trip_service.bookings.payment_reminders_sent'),
        );
    }
}
