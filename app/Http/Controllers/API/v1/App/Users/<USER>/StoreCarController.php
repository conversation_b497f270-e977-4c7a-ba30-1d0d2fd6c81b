<?php

namespace App\Http\Controllers\API\v1\App\Users\Cars;

use App\Data\Common\CarData;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Cars\StoreCarRequest;
use App\Http\Resources\Users\Cars\CarResource;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class StoreCarController extends Controller
{
    /**
     * Store a new car for the user.
     *
     * @param StoreCarRequest $request
     * @return JsonResponse
     *
     * @response CarResource
     *
     * @tags User Cars
     */
    public function __invoke(StoreCarRequest $request)
    {
        /** @var User */
        $user = Auth::user();

        $data = CarData::from($request);

        $car = $user->cars()->create($data->toArray());

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.create_data'),
            CarResource::make($car)
        );
    }
}
