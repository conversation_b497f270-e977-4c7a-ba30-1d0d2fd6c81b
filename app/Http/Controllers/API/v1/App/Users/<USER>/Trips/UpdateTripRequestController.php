<?php

namespace App\Http\Controllers\API\v1\App\Users\ServiceRequests\Trips;

use App\Data\ServiceRequests\UpdateTripRequestData;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\ServiceRequests\Trips\UpdateTripRequestRequest;
use App\Http\Resources\Users\ServiceRequests\TripRequests\TripRequestResource;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Spatie\LaravelData\Optional;

class UpdateTripRequestController extends Controller
{
    /**
     * Update an existing trip request.
     *
     * @param UpdateTripRequestRequest $request
     * @param int $id
     * @return JsonResponse
     *
     * @response TripRequestResource
     *
     * @tags User Trip Requests
     */
    public function __invoke(UpdateTripRequestRequest $request, $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $tripRequest = $user->tripRequests()->findOrFail($id);

        if ($tripRequest->cancelled_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_request.cannot_update_cancelled_trip'),
                null,
                422
            );
        }

        if ($tripRequest->departure_datetime <= Carbon::now()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_request.cannot_update_after_departure'),
                null,
                422
            );
        }

        $data = UpdateTripRequestData::from($request);

        $tripRequest->update($data->except(
            'seats',
            'additional_services',
            'from_city_en',
            'from_city_ar',
            'to_city_en',
            'to_city_ar',
            'from_location',
            'to_location',
        )->toArray());

        if (!$data->additional_services instanceof Optional) {
            $tripRequest->additionalServices()->sync($data->additional_services);
        }

        if (!$data->seats instanceof Optional) {
            $tripRequest->seats()->delete();

            $formattedSeats = array_map(fn($seat) => [
                'number' => $seat,
            ], $data->seats);

            $tripRequest->seats()->createMany($formattedSeats);
        }

        $tripRequest->refresh()->load(['seats', 'additionalServices']);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.update_data'),
            TripRequestResource::make($tripRequest)
        );
    }
}
