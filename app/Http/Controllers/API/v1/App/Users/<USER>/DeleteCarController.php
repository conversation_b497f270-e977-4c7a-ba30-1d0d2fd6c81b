<?php

namespace App\Http\Controllers\API\v1\App\Users\Cars;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\Common\Car;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class DeleteCarController extends Controller
{
    /**
     * Delete a car for the user.
     *
     * @param Car $car
     * @return JsonResponse
     *
     * @tags User Cars
     */
    public function __invoke(Car $car)
    {
        /** @var User */
        $user = Auth::user();

        $car = $user->cars()->findOrFail($car->id);

        $car->delete();

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.delete_data')
        );
    }
}
