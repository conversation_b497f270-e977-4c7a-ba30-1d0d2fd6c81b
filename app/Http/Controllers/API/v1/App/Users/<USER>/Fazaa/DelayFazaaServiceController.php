<?php

namespace App\Http\Controllers\API\v1\App\Users\Services\Fazaa;

use App\Events\FazaaServiceDelayed;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Services\Fazaa\DelayFazaaServiceRequest;
use App\Http\Resources\Users\Services\Fazaa\FazaaServiceResource;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class DelayFazaaServiceController extends Controller
{
    /**
     * Delay an existing fazaa service.
     *
     * @param DelayFazaaServiceRequest $request
     * @param int $id Fazaa service ID
     * @return JsonResponse
     *
     * @response FazaaServiceResource
     *
     * @tags User Fazaa Services
     */
    public function __invoke(DelayFazaaServiceRequest $request, $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $fazaaService = $user->fazaaServices()->findOrFail($id);

        $newDepartureDateTime = Carbon::parse($request->departure_datetime);
        $newArrivalDateTime = Carbon::parse($request->arrival_datetime);

        if ($fazaaService->cancelled_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.fazaa_service.cannot_delay_cancelled_service'),
                null,
                422
            );
        }

        if ($fazaaService->departure_datetime <= Carbon::now()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.fazaa_service.cannot_delay_started_service'),
                null,
                422
            );
        }

        $fazaaService->update([
            'departure_datetime' => $newDepartureDateTime,
            'arrival_datetime' => $newArrivalDateTime,
            'delayed_at' => Carbon::now(),
            'delay_reason' => $request->delay_reason,
            'delay_note' => $request->note,
        ]);

        FazaaServiceDelayed::dispatch($fazaaService->id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.fazaa_service.delayed_successfully'),
            FazaaServiceResource::make($fazaaService->fresh())
        );
    }
}
