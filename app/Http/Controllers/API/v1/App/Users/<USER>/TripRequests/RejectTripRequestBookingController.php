<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\TripRequests;

use App\Enums\Bookings\BookingStatusEnum;
use App\Events\TripRequestBookingRejected;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Bookings\TripRequests\RejectTripRequestBookingRequest;
use App\Http\Resources\Users\Bookings\TripRequests\TripRequestsBookingResource;
use App\Models\Booking\TripRequestBooking;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class RejectTripRequestBookingController extends Controller
{
    /**
     * Reject a trip request booking.
     *
     * @param RejectTripRequestBookingRequest $request
     * @param int $bookingId
     * @return JsonResponse
     *
     * @response TripRequestsBookingResource
     *
     * @tags User Trip Requests Bookings
     */
    public function __invoke(RejectTripRequestBookingRequest $request, int $bookingId)
    {
        /** @var User $user */
        $user = Auth::user();

        $booking = TripRequestBooking::query()
            ->whereHas('tripRequest', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })->findOrFail($bookingId);

        if ($booking->status !== BookingStatusEnum::PENDING()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_request.booking.booking_is_not_pending'),
                null,
                422
            );
        }

        $booking->update([
            'status' => BookingStatusEnum::REJECTED(),
            'rejected_at' => now(),
            'rejection_reason' => $request->validated('rejection_reason'),
            'rejection_note' => $request->validated('note'),
        ]);

        TripRequestBookingRejected::dispatch($booking->id);

        $booking->load(['tripRequest.seats', 'tripRequest.additionalServices']);

        return ResponseHelper::sendSuccessResponse(
            __('messages.trip_request.bookings.rejected'),
            TripRequestsBookingResource::make($booking->fresh())
        );
    }
}
