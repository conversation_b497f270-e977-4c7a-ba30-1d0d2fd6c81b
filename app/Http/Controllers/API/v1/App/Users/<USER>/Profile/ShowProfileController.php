<?php

namespace App\Http\Controllers\API\v1\App\Users\Accounts\Profile;

use App\Enums\Bookings\BookingStatusEnum;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Accounts\Profile\ProfileResource;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ShowProfileController extends Controller
{
    public function __invoke(Request $request): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();
        //todo: load booking and offers counts

        // Calculate total income from accepted trip service bookings
        $totalIncome = $user->tripServiceBookings()
            ->where('status', BookingStatusEnum::ACCEPTED())
            ->join('trip_services', 'trip_service_bookings.trip_service_id', '=', 'trip_services.id')
            ->sum(DB::raw('trip_services.price * 0.9')); // 90% of price (after 10% commission)

        $user->total_income = $totalIncome;

        $user->loadCount([
            'tripServices' => fn($query) => $query->active(),
            'tripServiceBookings' => fn($query) => $query->active(),
            'fazaaServices' => fn($query) => $query->active(),
            'fazaaServiceBookings' => fn($query) => $query->active(),
            'shippingServices' => fn($query) => $query->active(),
            'shippingServiceBookings' => fn($query) => $query->active(),
            'tripRequests' => fn($query) => $query->active(),
            'tripRequestBookings' => fn($query) => $query->active(),
            'shippingRequests' => fn($query) => $query->active(),
            'shippingRequestBookings' => fn($query) => $query->active(),
            'fazaaRequests' => fn($query) => $query->active(),
            'fazaaRequestBookings' => fn($query) => $query->active(),
        ]);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            ProfileResource::make($user)
        );
    }
}
