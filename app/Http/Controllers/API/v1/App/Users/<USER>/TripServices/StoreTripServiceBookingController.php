<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\TripServices;

use App\Data\Bookings\TripRequestBookingData;
use App\Enums\Travel\SeatStatusEnum;
use App\Enums\Bookings\BookingStatusEnum;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Bookings\TripServices\StoreTripServiceBookingRequest;
use App\Http\Resources\Users\Bookings\TripServices\TripServiceBookingResource;
use App\Models\Service\TripService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Events\TripServiceSeatBooked;

class StoreTripServiceBookingController extends Controller
{
    /**
     * Store a new Trip Service Booking.
     *
     * @param StoreTripServiceBookingRequest $request
     * @param int $id Trip service ID
     * @return JsonResponse
     *
     * @response TripServiceBookingResource
     *
     * @tags User Bookings, Trip Services
     * @throws \Exception
     */
    public function __invoke(StoreTripServiceBookingRequest $request, int $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $tripService = TripService::query()->upcoming()->findOrFail($id);

        if ($tripService->isFullyBooked()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_service.booking.service_is_fully_booked'),
                status_code: 422
            );
        }

        $bookingData = TripRequestBookingData::from($request->validated())->toArray();

        DB::beginTransaction();

        try {
            $booking = $user->tripServiceBookings()->create(
                $bookingData + [
                    'trip_service_id' => $tripService->id,
                    'status' => $request->filled('note') ? BookingStatusEnum::PENDING()
                        : BookingStatusEnum::ACCEPTED(),
                    'accepted_at' => $request->filled('note') ? null : now(),
                ]
            );

            foreach ($request->seats as $seat) {
                $existingSeat = $tripService->seats()->where('number', $seat)->first();

                $booking->seats()->create([
                    'seat_id' => $existingSeat->id,
                ]);

                $existingSeat->update(['status' => SeatStatusEnum::RESERVED()]);
            }

            $tripService->decrement('number_of_available_seats', count($request->seats));

            TripServiceSeatBooked::dispatch($booking->id);

            DB::commit();
            $booking->load(['tripService.additionalServices', 'seats']);
            return ResponseHelper::sendSuccessResponse(
                __('messages.response.create_data'),
                TripServiceBookingResource::make($booking->load('tripService'))
            );
        } catch (\Exception $e) {
            DB::rollBack();
            return ResponseHelper::sendFailedResponse(__('exceptions.internal_server_error'), ['error' => $e->getMessage()], 500);
        }
    }
}
