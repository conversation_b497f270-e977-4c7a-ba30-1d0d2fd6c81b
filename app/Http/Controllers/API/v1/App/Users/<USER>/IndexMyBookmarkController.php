<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookmarks;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Bookmarks\BookmarksResource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class IndexMyBookmarkController extends Controller
{
    /**
     * Get user's bookmarks.
     *
     * @param Request $request
     * @return JsonResponse
     *
     * @response Illuminate\Http\Resources\Json\AnonymousResourceCollection<BookmarkResource>
     *
     * @tags User Bookmarks
     */
    public function __invoke(Request $request)
    {
        /** @var User $user */
        $user = Auth::user();

        $bookmarks = $user->bookmarks()
            ->with(['bookmarkable'])
            ->orderBy($request->input('sort_by', 'created_at'), $request->input('sort', 'desc'))
            ->paginate($request->input('per_page', 10));

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            BookmarksResource::collection($bookmarks)->appends($request->query())->toArray()
        );
    }
}
