<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\ShippingRequests;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Bookings\ShippingRequests\ShippingRequestBookingsResource;
use App\Models\Request\ShippingRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class IndexShippingRequestBookingController extends Controller
{
    /**
     * Get all bookings for a specific shipping request.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     *
     * @response Illuminate\Http\Resources\Json\AnonymousResourceCollection<Illuminate\Pagination\LengthAwarePaginator<ShippingRequestBookingResource>>
     *
     * @tags User Bookings, Shipping Requests
     */
    public function __invoke(Request $request, int $id)
    {
        $perPage = $request->input('per_page', 10);

        $search = $request->input('search');

        $shippingRequest = ShippingRequest::findOrFail($id);

        $query = $shippingRequest->bookings()
            ->with('user')
            ->when($search, function ($query) use ($search) {
                $query->where('note', 'ilike', '%' . $search . '%')
                ->orWhereHas('shippingRequest', function ($query) use ($search) {
                    $query->where('note', 'ilike', '%' . $search . '%')
                    ->orWhereHas('fromCity', function ($query) use ($search) {
                        $query->where('name_en', 'ilike', '%' . $search . '%')
                            ->orWhere('name_ar', 'ilike', '%' . $search . '%');
                    })
                    ->orWhereHas('toCity', function ($query) use ($search) {
                        $query->where('name_en', 'ilike', '%' . $search . '%')
                            ->orWhere('name_ar', 'ilike', '%' . $search . '%');
                    });
                });
            })->paginate($perPage);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            ShippingRequestBookingsResource::collection($query)->appends($request->query())->toArray()
        );
    }
}
