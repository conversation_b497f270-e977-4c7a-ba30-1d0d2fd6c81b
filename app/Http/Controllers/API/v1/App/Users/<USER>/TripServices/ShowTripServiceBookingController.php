<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\TripServices;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Bookings\TripServices\TripServiceBookingResource;
use App\Models\Booking\TripServiceBooking;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ShowTripServiceBookingController extends Controller
{
    /**
     * Show details of a specific trip service booking.
     *
     * @param Request $request
     * @param int $id Booking ID
     * @return JsonResponse
     *
     * @response TripServiceBookingResource
     *
     * @tags User Trip Services Bookings
     */
    public function __invoke(Request $request, int $id)
    {
        /** @var User $user */
        $user = Auth::user();

        //either booking owner or trip service owner
        $booking = TripServiceBooking::with([
            'tripService.fromCity',
            'tripService.toCity',
            'tripService.additionalServices',
            'tripService.seats',
            'user'
        ])->whereHas('tripService', function ($query) use ($user) {
            $query->where('user_id', $user->id);
        })->findOrFail($id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            TripServiceBookingResource::make($booking->load('tripService'))
        );
    }
}
