<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\TripServices;

use App\Events\TripServiceBookingArrivalConfirmedEvent;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Bookings\TripServices\TripServiceBookingResource;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ConfirmMyTripServiceBookingArrivalController extends Controller
{
    /**
     * Confirm trip service booking arrival by the traveler.
     *
     * @param Request $request
     * @param int $id Booking ID
     * @return JsonResponse
     *
     * @response TripServiceBookingResource
     *
     * @tags User Trip Services Bookings
     */
    public function __invoke(Request $request, int $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $booking = $user->tripServiceBookings()
            ->with(['tripService'])
            ->findOrFail($id);

        if ($booking->arrival_confirmed_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_service.booking.arrival_already_confirmed'),
                null,
                422
            );
        }

        if ($booking->cancelled_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_service.booking.cannot_confirm_arrival_cancelled_booking'),
                null,
                422
            );
        }

        if ($booking->rejected_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_service.booking.cannot_confirm_arrival_rejected_booking'),
                null,
                422
            );
        }

        if ($booking->tripService->cancelled_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_service.booking.cannot_confirm_arrival_cancelled_service'),
                null,
                422
            );
        }

        $booking->update([
            'arrival_confirmed_at' => Carbon::now(),
        ]);

        TripServiceBookingArrivalConfirmedEvent::dispatch($booking->id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.trip_service.booking.arrival_confirmed'),
            TripServiceBookingResource::make($booking->fresh())
        );
    }
}
