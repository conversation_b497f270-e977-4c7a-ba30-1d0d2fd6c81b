<?php

namespace App\Http\Controllers\API\v1\App\Users\Notifications;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class MarkAllNotificationsAsReadController extends Controller
{
    public function __invoke(): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();

        $user->unreadNotifications->markAsRead();

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.update_data'),
            null
        );
    }
}
