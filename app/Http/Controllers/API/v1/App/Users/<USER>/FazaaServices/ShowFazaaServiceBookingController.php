<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\FazaaServices;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Bookings\FazaaServices\FazaaServiceBookingResource;
use App\Models\Booking\FazaaServiceBooking;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ShowFazaaServiceBookingController extends Controller
{
    /**
     * Show details of a specific Fazaa service booking.
     *
     * @param Request $request
     * @param int $id Booking ID
     * @return JsonResponse
     *
     * @response FazaaServiceBookingResource
     *
     * @tags User Fazaa Services Bookings
     */
    public function __invoke(Request $request, int $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $booking = FazaaServiceBooking::with([
            'fazaaService.fromCity',
            'fazaaService.toCity'
        ])->whereHas('fazaaService', function ($query) use ($user) {
            $query->where('user_id', $user->id);
        })->findOrFail($id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            FazaaServiceBookingResource::make($booking)
        );
    }
}
