<?php

namespace App\Http\Controllers\API\v1\App\Users\Services\Fazaa;

use App\Data\Services\UpdateFazaaServiceData;
use App\Events\FazaaServiceUpdated;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Services\Fazaa\UpdateFazaaServiceRequest;
use App\Http\Resources\Users\Services\Fazaa\FazaaServiceResource;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class UpdateFazaaServiceController extends Controller
{
    /**
     * Update an existing fazaa service.
     *
     * @param UpdateFazaaServiceRequest $request
     * @param int $id Fazaa service ID
     * @return JsonResponse
     *
     * @response FazaaServiceResource
     *
     * @tags User Fazaa Services
     */
    public function __invoke(UpdateFazaaServiceRequest $request, $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $fazaaService = $user->fazaaServices()->findOrFail($id);

        if ($fazaaService->cancelled_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.fazaa_service.cannot_update_cancelled_service'),
                null,
                422
            );
        }

        if ($fazaaService->departure_datetime <= Carbon::now()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.fazaa_service.cannot_update_started_service'),
                null,
                422
            );
        }

        $data = UpdateFazaaServiceData::from($request);

        $fazaaService->update($data->except(
            'from_city_en',
            'from_city_ar',
            'to_city_en',
            'to_city_ar',
            'from_location',
            'to_location',
        )->toArray());

        FazaaServiceUpdated::dispatch($fazaaService->id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.fazaa_service.updated_successfully'),
            FazaaServiceResource::make($fazaaService->fresh())
        );
    }
}
