<?php

namespace App\Http\Controllers\API\v1\App\Users\ServiceRequests\Trips;

use App\Data\ServiceRequests\TripRequestData;
use App\Events\TripRequestCreated;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\ServiceRequests\Trips\StoreTripRequestRequest;
use App\Http\Resources\Users\ServiceRequests\TripRequests\TripRequestResource;
use App\Jobs\Travel\TripRequest\RejectPendingBookingsAfterDepartureJob;
use App\Jobs\Travel\Weather\GetWeatherFromGeoLocationJob;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class StoreTripRequestController extends Controller
{
    /**
     * Store a new trip request.
     *
     * @param StoreTripRequestRequest $request
     * @return JsonResponse
     *
     * @response TripRequestResource
     *
     * @tags User Trip Requests
     */
    public function __invoke(StoreTripRequestRequest $request)
    {
        /** @var User */
        $user = Auth::user();

        $data = TripRequestData::from($request);

        $tripRequest = $user->tripRequests()->create(
            $data->except(
                'seats',
                'additional_services',
                'from_city_en',
                'from_city_ar',
                'to_city_en',
                'to_city_ar',
                'from_location',
                'to_location',
            )->toArray()
        );

        GetWeatherFromGeoLocationJob::dispatch($tripRequest);

        $formattedSeats = array_map(fn($seat) => [
            'number' => $seat,
        ], $data->seats);

        $tripRequest->seats()->createMany($formattedSeats);

        if ($data->additional_services) {
            $tripRequest->additionalServices()->sync($data->additional_services);
        }

        $tripRequest->refresh()->load(['seats', 'additionalServices']);

        TripRequestCreated::dispatch($tripRequest->id);

        RejectPendingBookingsAfterDepartureJob::dispatch($tripRequest)->delay($tripRequest->departure_datetime);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.create_data'),
            TripRequestResource::make($tripRequest)
        );
    }
}
