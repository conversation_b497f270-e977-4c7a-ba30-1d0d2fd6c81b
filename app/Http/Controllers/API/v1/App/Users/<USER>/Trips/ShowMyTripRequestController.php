<?php

namespace App\Http\Controllers\API\v1\App\Users\ServiceRequests\Trips;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\ServiceRequests\TripRequests\ShowTripRequestResource;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ShowMyTripRequestController extends Controller
{
    /**
     * Show details of a specific trip request.
     *
     * @param Request $request
     * @param int $id Trip request ID
     * @return JsonResponse
     *
     * @response ShowTripRequestResource
     *
     * @tags User Trip Requests
     */
    public function __invoke(Request $request, int $id): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();

        $tripRequest = $user->tripRequests()
            ->with(['fromCity', 'toCity', 'seats', 'additionalServices'])
            ->findOrFail($id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            ShowTripRequestResource::make($tripRequest)
        );
    }
}
