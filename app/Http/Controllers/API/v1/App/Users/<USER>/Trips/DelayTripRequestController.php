<?php

namespace App\Http\Controllers\API\v1\App\Users\ServiceRequests\Trips;

use App\Events\TripRequestDelayed;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\ServiceRequests\Trips\DelayTripRequestRequest;
use App\Http\Resources\Users\ServiceRequests\TripRequests\TripRequestResource;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class DelayTripRequestController extends Controller
{
    /**
     * Delay an existing trip request.
     *
     * @param DelayTripRequestRequest $request
     * @param int $id Trip request ID
     * @return JsonResponse
     *
     * @response TripRequestResource
     *
     * @tags User Trip Requests
     */
    public function __invoke(DelayTripRequestRequest $request, $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $tripRequest = $user->tripRequests()->findOrFail($id);

        $newDepartureDateTime = Carbon::parse($request->departure_datetime);
        $newArrivalDateTime = Carbon::parse($request->arrival_datetime);

        if ($tripRequest->cancelled_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_request.cannot_delay_cancelled_trip'),
                null,
                422
            );
        }

        if ($tripRequest->departure_datetime <= Carbon::now()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_request.cannot_delay_started_trip'),
                null,
                422
            );
        }

        $tripRequest->update([
            'departure_datetime' => $newDepartureDateTime,
            'arrival_datetime' => $newArrivalDateTime,
            'delayed_at' => Carbon::now(),
            'delay_reason' => $request->delay_reason,
            'delay_note' => $request->note,
        ]);

        TripRequestDelayed::dispatch($tripRequest->id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.trip_request.delayed_successfully'),
            TripRequestResource::make($tripRequest->fresh())
        );
    }
}
