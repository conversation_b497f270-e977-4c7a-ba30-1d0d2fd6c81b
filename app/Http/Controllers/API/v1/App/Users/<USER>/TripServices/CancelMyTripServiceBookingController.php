<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\TripServices;

use App\Enums\Bookings\BookingStatusEnum;
use App\Enums\Travel\SeatStatusEnum;
use App\Events\TripServiceBookingCancelledEvent;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Bookings\TripServices\CancelMyTripServiceBookingRequest;
use App\Http\Resources\Users\Bookings\TripServices\TripServiceBookingResource;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class CancelMyTripServiceBookingController extends Controller
{
    /**
     * Cancel a trip service booking.
     *
     * @param CancelMyTripServiceBookingRequest $request
     * @param int $bookingId
     * @return JsonResponse
     *
     * @response TripServiceBookingResource
     *
     * @tags User Trip Services Bookings
     */
    public function __invoke(CancelMyTripServiceBookingRequest $request, int $bookingId)
    {
        /** @var User $user */
        $user = Auth::user();

        $booking = $user->tripServiceBookings()->with('tripService')
            ->findOrFail($bookingId);

        DB::beginTransaction();

        try {
            $booking->update([
                'status' => BookingStatusEnum::CANCELLED(),
                'cancellation_reason' => $request->validated('cancellation_reason'),
                'cancellation_note' => $request->validated('note'),
                'cancelled_at' => now(),
            ]);

            foreach ($booking->seats as $bookedSeat) {
                $bookedSeat->seat->update(['status' => SeatStatusEnum::AVAILABLE()]);
            }

            $booking->tripService->increment('number_of_available_seats', $booking->seats->count());

            DB::commit();

            TripServiceBookingCancelledEvent::dispatch($booking->id);

            return ResponseHelper::sendSuccessResponse(
                __('messages.trip_service.bookings.cancelled'),
                TripServiceBookingResource::make($booking->fresh())
            );
        } catch (\Exception $e) {
            DB::rollBack();
            return ResponseHelper::sendFailedResponse(__('exceptions.internal_server_error'), ['error' => $e->getMessage()], 500);
        }
    }
}
