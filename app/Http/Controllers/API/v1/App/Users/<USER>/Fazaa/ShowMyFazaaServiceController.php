<?php

namespace App\Http\Controllers\API\v1\App\Users\Services\Fazaa;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Services\Fazaa\ShowFazaaServiceResource;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ShowMyFazaaServiceController extends Controller
{
    /**
     * Show details of a specific Fazaa service.
     *
     * @param Request $request
     * @param int $id Fazaa service ID
     * @return JsonResponse
     *
     * @response ShowFazaaServiceResource
     *
     * @tags User Fazaa Services
     */
    public function __invoke(Request $request, int $id): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();

        $fazaaService = $user->fazaaServices()
            ->with([
                'fromCity',
                'toCity',
                'fazaaServiceType',
                'specificType',
                'user'
            ])
            ->findOrFail($id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            ShowFazaaServiceResource::make($fazaaService)
        );
    }
}
