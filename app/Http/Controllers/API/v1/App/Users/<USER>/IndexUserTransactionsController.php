<?php

namespace App\Http\Controllers\API\v1\App\Users\Transactions;

use App\Enums\Bookings\BookingStatusEnum;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Transactions\UserTransactionResource;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class IndexUserTransactionsController extends Controller
{
    /**
     * Get the user's trip service booking transactions.
     *
     * @param Request $request
     * @return JsonResponse
     *
     * @response Illuminate\Http\Resources\Json\AnonymousResourceCollection<Illuminate\Pagination\LengthAwarePaginator<UserTransactionResource>>
     *
     * @tags User Transactions
     */
    public function __invoke(Request $request): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();

        $perPage = $request->input('per_page', 10);

        $query = $user->tripServices()
            ->with(['fromCity', 'toCity', 'travelers', 'bookings.seats'])
            ->withCount(['bookings as accepted_bookings_count' => function ($query) {
                $query->where('status', BookingStatusEnum::ACCEPTED());
            }])
            ->whereHas('bookings', function ($query) use ($user) {
                $query->where('status', BookingStatusEnum::ACCEPTED());
            })
            ->latest()
            ->paginate($perPage);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            UserTransactionResource::collection($query)->appends($request->query())->toArray()
        );
    }
}
