<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\TripRequests;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Bookings\TripRequests\TripRequestsBookingResource;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use App\Enums\Bookings\BookingStatusEnum;
use Illuminate\Database\Eloquent\Builder;

class IndexMyTripRequestBookingController extends Controller
{
    /**
     * Get the user's trip request bookings.
     *
     * @param Request $request
     * @return JsonResponse
     *
     * @response Illuminate\Http\Resources\Json\AnonymousResourceCollection<Illuminate\Pagination\LengthAwarePaginator<TripRequestsBookingResource>>
     *
     * @tags User Trip Request Bookings
     */
    public function __invoke(Request $request)
    {
        /** @var User $user */
        $user = Auth::user();

        $perPage = $request->input('per_page', 10);

        $status = $request->input('status');

        $search = $request->input('search');

        $orderBy = $request->input('order_by', 'created_at');

        $sort = $request->input('sort', 'desc');

        $query = $user->tripRequestBookings()
            ->with(['tripRequest.fromCity', 'tripRequest.toCity', 'tripRequest.additionalServices'])
            ->when(
                $status,
                function (Builder $query) use ($status) {
                    return match ($status) {
                        BookingStatusEnum::PENDING() => $query->where('status', BookingStatusEnum::PENDING()),
                        BookingStatusEnum::ACCEPTED() => $query->where('status', BookingStatusEnum::ACCEPTED()),
                        BookingStatusEnum::REJECTED() => $query->where('status', BookingStatusEnum::REJECTED()),
                        BookingStatusEnum::CANCELLED() => $query->where('status', BookingStatusEnum::CANCELLED())
                            ->orWhere('status', BookingStatusEnum::CANCELLED_BY_OWNER()),
                        default => $query
                    };
                }
            )->when($search, function ($query) use ($search) {
                $query->where('note', 'ilike', '%' . $search . '%')
                    ->orWhereHas('tripRequest', function ($query) use ($search) {
                        $query->where('note', 'ilike', '%' . $search . '%')
                            ->orWhereHas('fromCity', function ($query) use ($search) {
                                $query->where('name_en', 'ilike', '%' . $search . '%')
                                    ->orWhere('name_ar', 'ilike', '%' . $search . '%');
                            })
                            ->orWhereHas('toCity', function ($query) use ($search) {
                                $query->where('name_en', 'ilike', '%' . $search . '%')
                                    ->orWhere('name_ar', 'ilike', '%' . $search . '%');
                            });
                    });
            })->orderBy($orderBy,$sort)->paginate($perPage);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            TripRequestsBookingResource::collection($query)->appends($request->query())->toArray()
        );
    }
}
