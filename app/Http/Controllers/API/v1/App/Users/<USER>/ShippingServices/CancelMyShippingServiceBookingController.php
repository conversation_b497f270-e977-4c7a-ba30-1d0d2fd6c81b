<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\ShippingServices;

use App\Enums\Bookings\BookingStatusEnum;
use App\Events\ShippingServiceBookingCancelled;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Bookings\ShippingServices\CancelMyShippingServiceBookingRequest;
use App\Http\Resources\Users\Bookings\ShippingServices\ShippingServiceBookingResource;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\Booking\ShippingServiceBooking;

class CancelMyShippingServiceBookingController extends Controller
{
    /**
     * Cancel a specific shipping service booking for the authenticated user.
     *
     * @param CancelMyShippingServiceBookingRequest $request
     * @param int $id
     * @return JsonResponse
     *
     * @response ShippingServiceBookingResource
     *
     * @tags User Bookings, Shipping Services
     */
    public function __invoke(CancelMyShippingServiceBookingRequest $request, int $id)
    {
        /** @var User $user */
        $user = Auth::user();

        DB::beginTransaction();

        $booking = ShippingServiceBooking::query()
            ->with(['shippingService'])
            ->where('user_id', $user->id)
            ->lockForUpdate()
            ->findOrFail($id);

        try {
            // If booking was accepted, restore the volumes
            if ($booking->status === BookingStatusEnum::ACCEPTED()) {
                $shippingService = $booking->shippingService;
                $shippingService->increment('available_packages_volume', $booking->packages_volume ?? 0);
                $shippingService->increment('available_document_volume', $booking->document_volume ?? 0);
                $shippingService->increment('available_furniture_volume', $booking->furniture_volume ?? 0);
            }

            $booking->update([
                'status' => BookingStatusEnum::CANCELLED(),
                'cancellation_reason' => $request->validated('cancellation_reason'),
                'cancellation_note' => $request->validated('note'),
                'cancelled_at' => now(),
            ]);

            DB::commit();

            ShippingServiceBookingCancelled::dispatch($booking->id);

            return ResponseHelper::sendSuccessResponse(
                __('messages.response.delete_data'),
                ShippingServiceBookingResource::make($booking)
            );
        } catch (\Exception $e) {
            DB::rollBack();
            return ResponseHelper::sendFailedResponse(__('exceptions.internal_server_error'), ['error' => $e->getMessage()], 500);
        }
    }
}
