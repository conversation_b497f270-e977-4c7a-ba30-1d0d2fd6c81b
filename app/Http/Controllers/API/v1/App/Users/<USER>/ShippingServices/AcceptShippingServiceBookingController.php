<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\ShippingServices;

use App\Enums\Bookings\BookingStatusEnum;
use App\Events\ShippingServiceBookingAccepted;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Bookings\ShippingServices\ShippingServiceBookingResource;
use App\Models\Booking\ShippingServiceBooking;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class AcceptShippingServiceBookingController extends Controller
{
    /**
     * Accept a shipping service booking.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     *
     * @response ShippingServiceBookingResource
     *
     * @tags User Bookings, Shipping Services
     */
    public function __invoke(Request $request, int $id)
    {
        /** @var User $user */
        $user = Auth::user();

        DB::beginTransaction();
        $booking = ShippingServiceBooking::query()
            ->with(['shippingService'])
            ->whereHas('shippingService', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->lockForUpdate()
            ->findOrFail($id);

        if ($booking->status !== BookingStatusEnum::PENDING()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.shipping_service.booking.booking_is_not_pending'),
                null,
                422
            );
        }

        $shippingService = $booking->shippingService;
        if ($shippingService->isFullyBooked()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.shipping_service.booking.service_is_fully_booked'),
                null,
                422
            );
        }

        if (
            $shippingService->available_packages_volume < ($booking->packages_volume ?? 0) ||
            $shippingService->available_document_volume < ($booking->document_volume ?? 0) ||
            $shippingService->available_furniture_volume < ($booking->furniture_volume ?? 0)
        ) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.shipping_service.booking.not_enough_volume'),
                null,
                422
            );
        }

        try {
            $booking->update([
                'status' => BookingStatusEnum::ACCEPTED(),
            ]);

            $shippingService->decrement('available_packages_volume', $booking->packages_volume ?? 0);
            $shippingService->decrement('available_document_volume', $booking->document_volume ?? 0);
            $shippingService->decrement('available_furniture_volume', $booking->furniture_volume ?? 0);

            DB::commit();

            ShippingServiceBookingAccepted::dispatch($booking->id);

            return ResponseHelper::sendSuccessResponse(
                __('messages.response.update_data'),
                ShippingServiceBookingResource::make($booking)
            );
        } catch (\Exception $e) {
            DB::rollBack();
            return ResponseHelper::sendFailedResponse(__('exceptions.internal_server_error'), ['error' => $e->getMessage()], 500);
        }
    }
}
