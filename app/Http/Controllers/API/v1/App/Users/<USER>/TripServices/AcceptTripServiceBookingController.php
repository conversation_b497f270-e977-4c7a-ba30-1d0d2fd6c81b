<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\TripServices;

use App\Enums\Bookings\BookingStatusEnum;
use App\Events\TripServiceBookingAccepted;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Bookings\TripServices\TripServiceBookingResource;
use App\Models\Booking\TripServiceBooking;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AcceptTripServiceBookingController extends Controller
{
    /**
     * Accept a trip service booking.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     *
     * @response Illuminate\Http\Resources\Json\AnonymousResourceCollection<Illuminate\Pagination\LengthAwarePaginator<MyTripServicesResource>>
     *
     * @tags User Trip Services Bookings
     */
    public function __invoke(Request $request, int $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $booking = TripServiceBooking::query()
            ->whereHas('tripService', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->with('tripService')
            ->findOrFail($id);

        if ($booking->status !== BookingStatusEnum::PENDING()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_service.booking.booking_is_not_pending'),
                null,
                422
            );
        }

        $booking->update([
            'status' => BookingStatusEnum::ACCEPTED(),
        ]);

        TripServiceBookingAccepted::dispatch($booking->id);

        $booking->load(['tripService.seats', 'tripService.additionalServices']);

        return ResponseHelper::sendSuccessResponse(
            __('messages.trip_service.bookings.accepted'),
            TripServiceBookingResource::make($booking->fresh())
        );
    }
}
