<?php

namespace App\Http\Controllers\API\v1\App\Users\ServiceRequests\Trips;

use App\Enums\Travel\TripStatusEnum;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\ServiceRequests\TripRequests\MyTripRequestsResource;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class IndexMyTripRequestController extends Controller
{
    /**
     * Get the user's trip requests.
     *
     * @param Request $request
     * @return JsonResponse
     *
     * @response Illuminate\Http\Resources\Json\AnonymousResourceCollection<Illuminate\Pagination\LengthAwarePaginator<MyTripRequestsResource>>
     *
     * @tags User Trip Requests
     */
    public function __invoke(Request $request)
    {
        /** @var User $user */
        $user = Auth::user();

        $perPage = $request->input('per_page', 10);

        $search = $request->input('search');

        $status = $request->input('status');

        $orderBy = $request->input('order_by', 'created_at');

        $sort = $request->input('sort', 'desc');

        $query = $user->tripRequests()->with(
            ['fromCity', 'toCity', 'additionalServices', 'seats', 'carType']
        )->when(
            !is_null($status),
            function (Builder $query) use ($status) {
                return match ($status) {
                    TripStatusEnum::ACTIVE() => $query->active(),
                    TripStatusEnum::COMPLETED() => $query->completed(),
                    TripStatusEnum::CANCELLED() => $query->cancelled(),
                };
            }
        )->when($search, function ($query) use ($search) {
            $query->where('note', 'ilike', '%' . $search . '%')
            ->orWhereHas('fromCity', function ($query) use ($search) {

                $query->where('name_en', 'ilike', '%' . $search . '%')
                    ->orWhere('name_ar', 'ilike', '%' . $search . '%');
            })
            ->orWhereHas('toCity', function ($query) use ($search) {
                $query->where('name_en', 'ilike', '%' . $search . '%')
                    ->orWhere('name_ar', 'ilike', '%' . $search . '%');
            });
        })->orderBy($orderBy, $sort)->paginate($perPage);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            MyTripRequestsResource::collection($query)->appends($request->query())->toArray()
        );
    }
}
