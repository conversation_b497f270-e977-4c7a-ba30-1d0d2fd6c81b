<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\FazaaServices;

use App\Events\FazaaServiceBookingLateArrivalNotified;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Bookings\FazaaServices\FazaaServiceBookingResource;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NotifyFazaaServiceBookingLateArrivalController extends Controller
{
    /**
     * Notify that the user will be arriving late for a fazaa service booking.
     *
     * @param Request $request
     * @param string $bookingId Fazaa service booking ID
     * @return JsonResponse
     *
     * @response FazaaServiceBookingResource
     *
     * @tags User Fazaa Service Bookings
     */
    public function __invoke(Request $request, $bookingId)
    {
        /** @var User $user */
        $user = Auth::user();

        $booking = $user->fazaaServiceBookings()->findOrFail($bookingId);
        $fazaaService = $booking->fazaaService;

        if ($booking->cancelled_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.fazaa_service.booking.cannot_notify_late_arrival_cancelled_booking'),
                null,
                422
            );
        }

        if ($booking->rejected_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.fazaa_service.booking.cannot_notify_late_arrival_rejected_booking'),
                null,
                422
            );
        }

        if ($fazaaService->arrival_datetime <= Carbon::now()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.fazaa_service.booking.cannot_notify_late_arrival_completed_fazaa'),
                null,
                422
            );
        }

        if ($fazaaService->cancelled_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.fazaa_service.booking.cannot_notify_late_arrival_cancelled_fazaa'),
                null,
                422
            );
        }

        $booking->update([
            'late_arrival_notified_at' => Carbon::now(),
        ]);

        FazaaServiceBookingLateArrivalNotified::dispatch($booking->id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.fazaa_service.booking.late_arrival_notified_successfully'),
            FazaaServiceBookingResource::make($booking->fresh())
        );
    }
}
