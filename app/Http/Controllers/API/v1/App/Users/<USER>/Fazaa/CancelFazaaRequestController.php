<?php

namespace App\Http\Controllers\API\v1\App\Users\ServiceRequests\Fazaa;

use App\Events\FazaaRequestCancelled;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\ServiceRequests\Fazaa\CancelFazaaRequestRequest;
use App\Http\Resources\Users\ServiceRequests\FazaaRequests\FazaaRequestResource;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CancelFazaaRequestController extends Controller
{
    /**
     * Cancel an existing fazaa request.
     *
     * @param CancelFazaaRequestRequest $request
     * @param int $id Fazaa request ID
     * @return JsonResponse
     *
     * @response FazaaRequestResource
     *
     * @tags User Fazaa Requests
     */
    public function __invoke(CancelFazaaRequestRequest $request, $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $fazaaRequest = $user->fazaaRequests()->findOrFail($id);

        if ($fazaaRequest->cancelled_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.fazaa_request.already_cancelled'),
                null,
                422
            );
        }

        if ($fazaaRequest->departure_datetime <= Carbon::now()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.fazaa_request.cannot_cancel_started_request'),
                null,
                422
            );
        }

        $fazaaRequest->update([
            'cancelled_at' => Carbon::now(),
            'cancellation_reason' => $request->cancellation_reason,
            'cancellation_note' => $request->note,
        ]);

        FazaaRequestCancelled::dispatch($fazaaRequest->id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.fazaa_request.cancelled_successfully'),
            FazaaRequestResource::make($fazaaRequest->fresh())
        );
    }
}
