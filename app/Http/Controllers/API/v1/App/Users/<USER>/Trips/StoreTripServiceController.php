<?php

namespace App\Http\Controllers\API\v1\App\Users\Services\Trips;

use App\Data\Services\TripServiceData;
use App\Events\TripServiceCreated;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Services\Trips\StoreTripServiceRequest;
use App\Http\Resources\Users\Services\Trips\TripServiceResource;
use App\Jobs\Travel\TripService\RejectPendingBookingsAfterDepartureJob;
use App\Jobs\Travel\Weather\GetWeatherFromGeoLocationJob;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use App\Enums\Travel\SeatStatusEnum;

class StoreTripServiceController extends Controller
{
    /**
     * Store a new trip service.
     *
     * @param StoreTripServiceRequest $request
     * @return JsonResponse
     *
     * @response TripServiceResource
     *
     * @tags User Trip Services
     */
    public function __invoke(StoreTripServiceRequest $request)
    {
        /** @var User $user */
        $user = Auth::user();

        $data = TripServiceData::from($request);

        $trip = $user->tripServices()->create($data->except(
            'seats',
            'additional_services',
            'from_city_en',
            'from_city_ar',
            'to_city_en',
            'to_city_ar',
            'from_location',
            'to_location',
        )->toArray());

        GetWeatherFromGeoLocationJob::dispatch($trip);

        if ($data->additional_services) {
            $trip->additionalServices()->sync($data->additional_services);
        }

        TripServiceCreated::dispatch($trip->id);

        $formattedSeats = array_map(fn($seatNumber) => [
            'number' => $seatNumber,
            'status' => SeatStatusEnum::AVAILABLE()
        ], $data->seats);
        $trip->seats()->createMany($formattedSeats);

        RejectPendingBookingsAfterDepartureJob::dispatch($trip)->delay($trip->departure_datetime);

        $trip->refresh()->load(['seats', 'additionalServices']);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.create_data'),
            TripServiceResource::make($trip)
        );
    }
}
