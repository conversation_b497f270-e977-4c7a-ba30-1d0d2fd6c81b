<?php

namespace App\Http\Controllers\API\v1\App\Users\ServiceRequests\Trips;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\ServiceRequests\TripRequests\TripRequestResource;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ConfirmTripRequestAttendanceController extends Controller
{
    /**
     * Confirm trip request attendance by the owner.
     *
     * @param int $id
     * @return JsonResponse
     *
     * @response TripRequestResource
     *
     * @tags User Trip Requests
     */
    public function __invoke(Request $request, int $id)
    {
        /** @var User $user */
        $user = Auth::user();
        $tripRequest = $user->tripRequests()->findOrFail($id);

        if ($tripRequest->attendance_confirmed_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_request.attendance_already_confirmed'),
                null,
                422
            );
        }

        if ($tripRequest->cancelled_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_request.cannot_confirm_cancelled_trip'),
                null,
                422
            );
        }

        $now = Carbon::now();
        $departureTime = Carbon::parse($tripRequest->departure_datetime);
        $minutesUntilDeparture = $now->diffInMinutes($departureTime, false);

        if ($minutesUntilDeparture > 30 || $minutesUntilDeparture < 0) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_request.booking.attendance_confirmation_window'),
                null,
                422
            );
        }

        $tripRequest->update([
            'attendance_confirmed_at' => $now
        ]);

        //TripRequestAttendanceConfirmedEvent::dispatch($tripRequest->id));

        return ResponseHelper::sendSuccessResponse(
            __('messages.trip_request.attendance_confirmed'),
            TripRequestResource::make($tripRequest->fresh())
        );
    }
}
