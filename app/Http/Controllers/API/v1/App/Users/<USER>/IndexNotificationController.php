<?php

namespace App\Http\Controllers\API\v1\App\Users\Notifications;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Notifications\NotificationResource;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class IndexNotificationController extends Controller
{
    /**
     * Get user's notifications.
     *
     * @param Request $request
     * @return JsonResponse
     *
     * @response Illuminate\Http\Resources\Json\AnonymousResourceCollection<NotificationResource>
     *
     * @tags User Notifications
     */
    public function __invoke(Request $request)
    {
        /** @var User $user */
        $user = Auth::user();

        $notifications = $user->notifications()
            ->orderBy('created_at', 'desc')
            ->paginate($request->input('per_page', 10));

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            NotificationResource::collection($notifications)->appends($request->query())->toArray()
        );
    }
}
