<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\ShippingServices;

use App\Events\ShippingServiceBookingLateArrivalNotified;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Bookings\ShippingServices\ShippingServiceBookingResource;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NotifyShippingServiceBookingLateArrivalController extends Controller
{
    /**
     * Notify that the user will be arriving late for a shipping service booking.
     *
     * @param Request $request
     * @param string $bookingId Shipping service booking ID
     * @return JsonResponse
     *
     * @response ShippingServiceBookingResource
     *
     * @tags User Shipping Service Bookings
     */
    public function __invoke(Request $request, $bookingId)
    {
        /** @var User $user */
        $user = Auth::user();

        $booking = $user->shippingServiceBookings()->findOrFail($bookingId);
        $shippingService = $booking->shippingService;

        if ($booking->cancelled_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.shipping_service.booking.cannot_notify_late_arrival_cancelled_booking'),
                null,
                422
            );
        }

        if ($booking->rejected_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.shipping_service.booking.cannot_notify_late_arrival_rejected_booking'),
                null,
                422
            );
        }

        if ($shippingService->arrival_datetime <= Carbon::now()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.shipping_service.booking.cannot_notify_late_arrival_completed_shipping'),
                null,
                422
            );
        }

        if ($shippingService->cancelled_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.shipping_service.booking.cannot_notify_late_arrival_cancelled_shipping'),
                null,
                422
            );
        }

        $booking->update([
            'late_arrival_notified_at' => Carbon::now(),
        ]);

        ShippingServiceBookingLateArrivalNotified::dispatch($booking->id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.shipping_service.booking.late_arrival_notified_successfully'),
            ShippingServiceBookingResource::make($booking->fresh())
        );
    }
}
