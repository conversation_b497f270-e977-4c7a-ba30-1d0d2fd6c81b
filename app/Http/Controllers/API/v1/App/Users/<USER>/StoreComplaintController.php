<?php

namespace App\Http\Controllers\API\v1\App\Users\Complaints;

use App\Enums\Bookings\BookingStatusEnum;
use App\Enums\Bookings\BookingTypeEnum;
use App\Enums\Complaints\ComplaintStatusEnum;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Complaints\StoreComplaintRequest;
use App\Http\Resources\Users\Complaints\ComplaintResource;
use App\Models\Booking\FazaaRequestBooking;
use App\Models\Booking\FazaaServiceBooking;
use App\Models\Booking\ShippingRequestBooking;
use App\Models\Booking\ShippingServiceBooking;
use App\Models\Booking\TripRequestBooking;
use App\Models\Booking\TripServiceBooking;
use App\Models\Complaint;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

use function Pest\Laravel\post;

class StoreComplaintController extends Controller
{
    /**
     * Store a new complaint for a booking that was cancelled by the owner.
     *
     * @param StoreComplaintRequest $request
     * @return JsonResponse
     *
     * @response ComplaintResource
     *
     * @tags User Complaints
     */
    public function __invoke(StoreComplaintRequest $request)
    {
        /** @var User $user */
        $user = Auth::user();

        $className = match ($request->post('booking_type')) {
            BookingTypeEnum::TRIP_SERVICE() => TripServiceBooking::class,
            BookingTypeEnum::TRIP_REQUEST() => TripRequestBooking::class,
            BookingTypeEnum::SHIPPING_SERVICE() => ShippingServiceBooking::class,
            BookingTypeEnum::SHIPPING_REQUEST() => ShippingRequestBooking::class,
            BookingTypeEnum::FAZAA_SERVICE() => FazaaServiceBooking::class,
            BookingTypeEnum::FAZAA_REQUEST() => FazaaRequestBooking::class,
        };

        $booking = app($className)->findOrFail($request->post('booking_id'));

        if ($booking->user_id != $user->id) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.record_not_found'),
                null,
                404
            );
        }

        if ($booking->status != BookingStatusEnum::CANCELLED_BY_OWNER()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.booking.not_cancelled_by_owner'),
                null,
                422
            );
        }

        $complaint = $user->complaints()->create([
            'description' => $request->description,
            'status' => ComplaintStatusEnum::PENDING(),
            'booking_id' => $booking->id,
            'booking_type' => $booking::class,
        ]);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.create_data'),
            ComplaintResource::make($complaint->load('booking'))
        );
    }
}
