<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\FazaaRequests;

use App\Enums\Bookings\BookingStatusEnum;
use App\Events\FazaaRequestBookingAccepted;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Bookings\FazaaRequests\FazaaRequestBookingResource;
use App\Models\Booking\FazaaRequestBooking;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AcceptFazaaRequestBookingController extends Controller
{
    /**
     * Accept a Fazaa request booking.
     *
     * @param Request $request
     * @param int $id Booking ID
     * @return JsonResponse
     *
     * @response FazaaRequestBookingResource
     *
     * @tags User Bookings, Fazaa Requests
     */
    public function __invoke(Request $request, int $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $booking = FazaaRequestBooking::with('fazaaRequest')
            ->whereHas('fazaaRequest', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->findOrFail($id);

        if ($booking->status !== BookingStatusEnum::PENDING()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.fazaa_request.booking.booking_is_not_pending'),
                null,
                422
            );
        }

        $booking->update([
            'status' => BookingStatusEnum::ACCEPTED(),
        ]);

        // Reject all other pending bookings for this fazaa request
        $booking->fazaaRequest->bookings()
            ->where('id', '!=', $booking->id)
            ->where('status', BookingStatusEnum::PENDING())
            ->update([
                'status' => BookingStatusEnum::REJECTED(),
                'rejected_at' => now(),
                'rejection_reason' => __(key: 'messages.fazaa_request.bookings.auto_rejected', locale: $booking->fazaaRequest->user->locale),
            ]);

        FazaaRequestBookingAccepted::dispatch($booking->id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.update_data'),
            FazaaRequestBookingResource::make($booking)
        );
    }
}
