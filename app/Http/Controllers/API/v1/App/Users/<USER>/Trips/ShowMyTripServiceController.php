<?php

namespace App\Http\Controllers\API\v1\App\Users\Services\Trips;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Services\Trips\ShowTripServiceResource;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ShowMyTripServiceController extends Controller
{
    /**
     * Show details of a specific trip service.
     *
     * @param Request $request
     * @param int $id Trip service ID
     * @return JsonResponse
     *
     * @response ShowTripServiceResource
     *
     * @tags User Trip Services
     */
    public function __invoke(Request $request, int $id): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();

        $tripService = $user->tripServices()
            ->with([
                'fromCity',
                'toCity',
                'car',
                'car.type',
                'seats',
                'additionalServices',
                'user'
            ])
            ->findOrFail($id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            ShowTripServiceResource::make($tripService)
        );
    }
}
