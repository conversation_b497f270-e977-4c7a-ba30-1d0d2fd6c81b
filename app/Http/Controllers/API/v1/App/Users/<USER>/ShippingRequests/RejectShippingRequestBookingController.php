<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\ShippingRequests;

use App\Enums\Bookings\BookingStatusEnum;
use App\Events\ShippingRequestBookingRejected;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Bookings\ShippingRequests\RejectShippingRequestBookingRequest;
use App\Http\Resources\Users\Bookings\ShippingRequests\ShippingRequestBookingResource;
use App\Models\Booking\ShippingRequestBooking;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class RejectShippingRequestBookingController extends Controller
{
    /**
     * Reject a shipping request booking.
     *
     * @param RejectShippingRequestBookingRequest $request
     * @param int $id
     * @return JsonResponse
     *
     * @response ShippingRequestBookingResource
     *
     * @tags User Bookings, Shipping Requests
     */
    public function __invoke(RejectShippingRequestBookingRequest $request, int $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $booking = ShippingRequestBooking::query()
            ->with(['shippingRequest'])
            ->whereHas('shippingRequest', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->findOrFail($id);

        if ($booking->status !== BookingStatusEnum::PENDING()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.shipping_request.booking.booking_is_not_pending'),
                null,
                422
            );
        }

        $booking->update([
            'status' => BookingStatusEnum::REJECTED(),
            'rejection_reason' => $request->validated('rejection_reason'),
            'rejection_note' => $request->validated('note'),
            'rejected_at' => now(),
        ]);

        ShippingRequestBookingRejected::dispatch($booking->id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.delete_data'),
            ShippingRequestBookingResource::make($booking)
        );
    }
}
