<?php

namespace App\Http\Controllers\API\v1\App\Users\Services\Trips;

use App\Data\Services\UpdateTripServiceData;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Services\Trips\UpdateTripServiceRequest;
use App\Http\Resources\Users\Services\Trips\TripServiceResource;
use App\Models\Service\TripService;
use Illuminate\Support\Facades\Auth;
use Spatie\LaravelData\Optional;

class UpdateTripServiceController extends Controller
{
    /**
     * Update an existing trip service.
     *
     * @param UpdateTripServiceRequest $request
     * @param int $id
     * @return JsonResponse
     *
     * @response TripServiceResource
     *
     * @tags User Trip Services
     */
    public function __invoke(UpdateTripServiceRequest $request, $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $tripService = $user->tripServices()->findOrFail($id);

        $data = UpdateTripServiceData::from($request);

        $tripService->update($data->except(
            'seats',
            'additional_services',
            'from_city_en',
            'from_city_ar',
            'to_city_en',
            'to_city_ar',
            'from_location',
            'to_location',
        )->toArray());

        if (!$data->additional_services instanceof Optional) {
            $tripService->additionalServices()->sync($data->additional_services);
        }

        if (!$data->seats instanceof Optional) {
            $tripService->seats()->delete();

            $formattedSeats = array_map(fn($seat) => [
                'number' => $seat['number'],
                'status' => $seat['status']
            ], $data->seats);

            $tripService->seats()->createMany($formattedSeats);
        }

        $tripService->refresh()->load(['seats', 'additionalServices']);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.update_data'),
            TripServiceResource::make($tripService)
        );
    }
}
