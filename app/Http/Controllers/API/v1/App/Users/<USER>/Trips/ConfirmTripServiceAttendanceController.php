<?php

namespace App\Http\Controllers\API\v1\App\Users\Services\Trips;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Services\Trips\TripServiceResource;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ConfirmTripServiceAttendanceController extends Controller
{
    /**
     * Confirm trip service attendance by the owner.
     *
     * @param int $id
     * @return JsonResponse
     *
     * @response TripServiceResource
     *
     * @tags User Trip Services
     */
    public function __invoke(Request $request,int $id)
    {
        /** @var User $user */
        $user = Auth::user();
        $tripService = $user->tripServices()->findOrFail($id);

        if ($tripService->attendance_confirmed_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_service.attendance_already_confirmed'),
                null,
                422
            );
        }

        if ($tripService->cancelled_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_service.cannot_confirm_cancelled_trip'),
                null,
                422
            );
        }

        $now = Carbon::now();
        $departureTime = Carbon::parse($tripService->departure_datetime);
        $minutesUntilDeparture = $now->diffInMinutes($departureTime, false);

        if ($minutesUntilDeparture > 30 || $minutesUntilDeparture < 0) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_service.booking.attendance_confirmation_window'),
                null,
                422
            );
        }

        $tripService->update([
            'attendance_confirmed_at' => $now
        ]);

        //TripServiceAttendanceConfirmedEvent::dispatch($tripService->id));

        return ResponseHelper::sendSuccessResponse(
            __('messages.trip_service.attendance_confirmed'),
            TripServiceResource::make($tripService->fresh())
        );
    }
}
