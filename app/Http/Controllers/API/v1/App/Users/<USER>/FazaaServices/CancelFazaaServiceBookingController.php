<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\FazaaServices;

use App\Enums\Bookings\BookingStatusEnum;
use App\Events\FazaaServiceBookingCancelledByOwnerEvent;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Bookings\FazaaServices\CancelFazaaServiceBookingRequest;
use App\Http\Resources\Users\Bookings\FazaaServices\FazaaServiceBookingResource;
use App\Models\Booking\FazaaServiceBooking;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class CancelFazaaServiceBookingController extends Controller
{
    /**
     * Fazaa service owner cancels a booking.
     *
     * @param CancelFazaaServiceBookingRequest $request
     * @param int $id Booking ID
     * @return JsonResponse
     *
     * @response FazaaServiceBookingResource
     *
     * @tags User Fazaa Services Bookings
     */
    public function __invoke(CancelFazaaServiceBookingRequest $request, int $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $booking = FazaaServiceBooking::query()
            ->whereHas('fazaaService', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->with(['fazaaService'])
            ->findOrFail($id);

        if ($booking->status !== BookingStatusEnum::PENDING()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.fazaa_service.booking.booking_is_not_pending'),
                null,
                422
            );
        }

        $booking->update([
            'status' => BookingStatusEnum::CANCELLED_BY_OWNER(),
            'cancellation_reason' => $request->validated('cancellation_reason'),
            'cancellation_note' => $request->validated('note'),
            'cancelled_at' => now(),
        ]);

        FazaaServiceBookingCancelledByOwnerEvent::dispatch($booking->id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.fazaa_service.bookings.cancelled'),
            FazaaServiceBookingResource::make($booking->fresh())
        );
    }
}
