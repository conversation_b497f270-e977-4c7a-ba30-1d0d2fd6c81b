<?php

namespace App\Http\Controllers\API\v1\App\Users\Devices;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Devices\StoreDeviceRequest;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class StoreDeviceController extends Controller
{
    /**
     * Store user device token
     *
     * @param StoreDeviceRequest $request
     * @return JsonResponse
     */
    public function __invoke(StoreDeviceRequest $request): JsonResponse
    {
        /** @var User */
        $user = Auth::user();

        $user->devices()->create([
            'token' => $request->input('fcm_token'),
            'notifiable_token_id' => $user->tokens->last()->id,
            'notifiable_type' => User::class,
        ]);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.create_data'),
            null
        );
    }
}
