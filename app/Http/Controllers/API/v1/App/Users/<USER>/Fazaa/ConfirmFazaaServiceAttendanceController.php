<?php

namespace App\Http\Controllers\API\v1\App\Users\Services\Fazaa;

use App\Events\FazaaServiceAttendanceConfirmed;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Services\Fazaa\FazaaServiceResource;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class ConfirmFazaaServiceAttendanceController extends Controller
{
    /**
     * Confirm attendance for a fazaa service.
     *
     * @param int $id Fazaa service ID
     * @return JsonResponse
     *
     * @response FazaaServiceResource
     *
     * @tags User Fazaa Services
     */
    public function __invoke($id)
    {
        /** @var User $user */
        $user = Auth::user();

        $fazaaService = $user->fazaaServices()->findOrFail($id);

        if ($fazaaService->cancelled_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.fazaa_service.cannot_confirm_cancelled_service'),
                null,
                422
            );
        }

        if ($fazaaService->attendance_confirmed_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.fazaa_service.attendance_already_confirmed'),
                null,
                422
            );
        }

        $departureTime = Carbon::parse($fazaaService->departure_datetime);
        $now = Carbon::now();

        if ($now->diffInMinutes($departureTime) > 30 || $now->isAfter($departureTime)) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.fazaa_service.booking.attendance_confirmation_window_invalid'),
                null,
                422
            );
        }

        $fazaaService->update([
            'attendance_confirmed_at' => $now,
        ]);

        event(new FazaaServiceAttendanceConfirmed($fazaaService->id));

        return ResponseHelper::sendSuccessResponse(
            __('messages.fazaa_service.attendance_confirmed_successfully'),
            FazaaServiceResource::make($fazaaService->fresh())
        );
    }
}
