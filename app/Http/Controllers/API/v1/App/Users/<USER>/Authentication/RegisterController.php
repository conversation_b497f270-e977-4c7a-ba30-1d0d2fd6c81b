<?php

namespace App\Http\Controllers\API\v1\App\Users\Accounts\Authentication;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Accounts\Authentication\RegisterRequest;
use App\Http\Resources\Users\Accounts\RegisterResource;
use App\Models\User;
use App\Notifications\RegisterOtpNotification;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class RegisterController extends Controller
{
    public function __invoke(RegisterRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $userAttribute = $request->validated();

            $user = User::query()->create($userAttribute)->fresh();

            $otp = app()->environment('production', 'testing') ? rand(1000, 9999) : defaultOtp();

            $verificationCode = $user->verificationCode()->create(
                [
                    'otp' => $otp,
                    'expire_at' => now()->addMinutes(20),
                ]
            );

            if (app()->environment('production', 'testing')) {
                $user->notify(new RegisterOtpNotification($verificationCode->otp));
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }

        return ResponseHelper::sendSuccessResponse(
            __('auth.otp.verification_code_sent'),
            null
        );
    }
}
