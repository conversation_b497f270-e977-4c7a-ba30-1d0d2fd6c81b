<?php

namespace App\Http\Controllers\API\v1\App\Users\ServiceRequests\Fazaa;

use App\Data\ServiceRequests\UpdateFazaaRequestData;
use App\Events\FazaaRequestUpdated;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\ServiceRequests\Fazaa\UpdateFazaaRequestRequest;
use App\Http\Resources\Users\ServiceRequests\FazaaRequests\FazaaRequestResource;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class UpdateFazaaRequestController extends Controller
{
    /**
     * Update an existing fazaa request.
     *
     * @param UpdateFazaaRequestRequest $request
     * @param int $id Fazaa request ID
     * @return JsonResponse
     *
     * @response FazaaRequestResource
     *
     * @tags User Fazaa Requests
     */
    public function __invoke(UpdateFazaaRequestRequest $request, $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $fazaaRequest = $user->fazaaRequests()->findOrFail($id);

        if ($fazaaRequest->cancelled_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.fazaa_request.cannot_update_cancelled_request'),
                null,
                422
            );
        }

        if ($fazaaRequest->departure_datetime <= Carbon::now()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.fazaa_request.cannot_update_started_request'),
                null,
                422
            );
        }

        $data = UpdateFazaaRequestData::from($request);

        $fazaaRequest->update($data->except(
            'from_city_en',
            'from_city_ar',
            'to_city_en',
            'to_city_ar',
            'from_location',
            'to_location',
        )->toArray());

        FazaaRequestUpdated::dispatch($fazaaRequest->id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.fazaa_request.updated_successfully'),
            FazaaRequestResource::make($fazaaRequest->fresh())
        );
    }
}
