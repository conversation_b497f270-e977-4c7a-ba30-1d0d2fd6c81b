<?php

namespace App\Http\Controllers\API\v1\App\Users\Services\Trips;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Services\Trips\TripServiceResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;

class ConfirmTripServiceArrivalController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function __invoke(Request $request, int $id): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();

        $tripService = $user->tripServices()->findOrFail($id);

        if ($tripService->arrival_confirmed_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_service.already_confirmed'),
                null,
                422
            );
        }

        if ($tripService->cancelled_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_service.cannot_confirm_cancelled_trip'),
                null,
                422
            );
        }
        $tripService->update([
            'arrival_confirmed_at' => Carbon::now(),
        ]);

        return ResponseHelper::sendSuccessResponse(
            __('messages.trip_service.arrival_confirmed'),
            TripServiceResource::make($tripService->fresh())
        );
    }
}
