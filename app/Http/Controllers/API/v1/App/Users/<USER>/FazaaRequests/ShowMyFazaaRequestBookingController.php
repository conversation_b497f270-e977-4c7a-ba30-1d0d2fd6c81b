<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\FazaaRequests;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Bookings\FazaaRequests\FazaaRequestBookingResource;
use App\Models\Booking\FazaaRequestBooking;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ShowMyFazaaRequestBookingController extends Controller
{
    /**
     * Show details of a specific Fazaa request booking.
     *
     * @param Request $request
     * @param int $bookingId Booking ID
     * @return JsonResponse
     *
     * @response FazaaRequestBookingResource
     *
     * @tags User Bookings, Fazaa Requests
     */
    public function __invoke(Request $request, int $bookingId)
    {
        /** @var User $user */
        $user = Auth::user();

        $booking = $user->fazaaRequestBookings()
            ->with('fazaaRequest')
            ->findOrFail($bookingId);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            FazaaRequestBookingResource::make($booking)
        );
    }
}
