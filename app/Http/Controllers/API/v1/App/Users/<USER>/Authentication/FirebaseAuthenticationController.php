<?php

namespace App\Http\Controllers\API\v1\App\Users\Accounts\Authentication;

use App\Http\Controllers\Controller;
use App\Helpers\ResponseHelper;
use App\Http\Requests\App\Users\Accounts\Authentication\FirebaseAuthenticationRequest;
use App\Http\Resources\Users\Accounts\Authentication\AuthenticationResource;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Kreait\Laravel\Firebase\Facades\Firebase;

class FirebaseAuthenticationController extends Controller
{
    /**
     * Authenticate a user with Firebase.
     *
     * @param FirebaseAuthenticationRequest $request
     * @return JsonResponse
     */
    public function __invoke(FirebaseAuthenticationRequest $request)
    {
        DB::beginTransaction();

        $verifiedIdToken = Firebase::auth()->verifyIdToken($request->input('id_token'));
        $claims = $verifiedIdToken->claims()->all();

        Log::info('Firebase user authenticated', ['claims' => $claims]);

        $user = User::where('firebase_uid', $claims['sub'])
            ->orWhere('email', $claims['email'])
            ->first();

        if ($user) {
            $user->update([
                'firebase_uid' => $claims['sub'],
                'name' => $claims['name'] ?? $user->name,
                'picture' => $claims['picture'] ?? $user->picture,
                'mobile' => $claims['phoneNumber'] ?? $user->mobile,
            ]);
        } else {
            $user = User::create([
                'firebase_uid' => $claims['sub'],
                'name' => $claims['name'] ?? null,
                'email' => $claims['email'],
                'mobile' => $claims['phoneNumber'] ?? null,
                'picture' => $claims['picture'] ?? null,
            ]);
        }

        $token = $user->createToken('Personal Access Token')->plainTextToken;
        $user->token = $token;

        DB::commit();

        return ResponseHelper::sendSuccessResponse(
            __('auth.firebase_authenticated'),
            AuthenticationResource::make($user)
        );
    }
}
