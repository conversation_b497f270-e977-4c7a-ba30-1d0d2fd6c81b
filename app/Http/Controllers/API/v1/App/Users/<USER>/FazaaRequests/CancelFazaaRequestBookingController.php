<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\FazaaRequests;

use App\Enums\Bookings\BookingStatusEnum;
use App\Events\FazaaRequestBookingCancelled;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Bookings\FazaaRequests\CancelFazaaRequestBookingRequest;
use App\Http\Resources\Users\Bookings\FazaaRequests\FazaaRequestBookingResource;
use App\Models\Booking\FazaaRequestBooking;
use App\Models\User;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;

class CancelFazaaRequestBookingController extends Controller
{
    /**
     * Cancel a Fazaa request booking.
     *
     * @param CancelFazaaRequestBookingRequest $request
     * @param int $id Booking ID
     * @return JsonResponse
     *
     * @response FazaaRequestBookingResource
     *
     * @tags User Bookings, Fazaa Requests
     */
    public function __invoke(CancelFazaaRequestBookingRequest $request, int $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $booking = FazaaRequestBooking::with('fazaaRequest')
            ->whereHas('fazaaRequest', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->findOrFail($id);

        if ($booking->status !== BookingStatusEnum::PENDING()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.fazaa_request.booking.booking_is_not_pending'),
                null,
                422
            );
        }

        $booking->update([
            'status' => BookingStatusEnum::CANCELLED_BY_OWNER(),
            'cancellation_reason' => $request->validated('cancellation_reason'),
            'cancellation_note' => $request->validated('note'),
            'cancelled_at' => now(),
        ]);

        FazaaRequestBookingCancelled::dispatch($booking->id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.update_data'),
            FazaaRequestBookingResource::make($booking)
        );
    }
}
