<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\ShippingRequests;

use App\Enums\Bookings\BookingStatusEnum;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Bookings\ShippingRequests\ShippingRequestBookingResource;
use App\Models\Booking\ShippingRequestBooking;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Events\ShippingRequestBookingCancelledByOwnerEvent;
use App\Http\Requests\App\Users\Bookings\ShippingRequests\CancelShippingRequestBookingRequest;

class CancelShippingRequestBookingController extends Controller
{
    /**
     * Cancel a shipping request booking.
     *
     * @param CancelShippingRequestBookingRequest $request
     * @param int $id
     * @return JsonResponse
     *
     * @response ShippingRequestBookingResource
     *
     * @tags User Bookings, Shipping Requests
     */
    public function __invoke(CancelShippingRequestBookingRequest $request, int $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $booking = ShippingRequestBooking::query()
            ->whereHas('shippingRequest', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->where('id', $id)
            ->firstOrFail();

        $booking->update([
            'status' => BookingStatusEnum::CANCELLED_BY_OWNER(),
            'cancellation_reason' => $request->validated('cancellation_reason'),
            'cancellation_note' => $request->validated('note'),
            'cancelled_at' => now(),
        ]);

        ShippingRequestBookingCancelledByOwnerEvent::dispatch($booking->id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.delete_data'),
            ShippingRequestBookingResource::make($booking)
        );
    }
}
