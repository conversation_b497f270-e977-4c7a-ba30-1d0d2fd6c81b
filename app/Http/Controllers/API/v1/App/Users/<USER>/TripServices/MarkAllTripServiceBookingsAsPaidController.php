<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\TripServices;

use App\Enums\Bookings\BookingStatusEnum;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class MarkAllTripServiceBookingsAsPaidController extends Controller
{
    /**
     * Mark all unpaid accepted bookings for a trip service as paid.
     *
     * @param  Request  $request
     * @param  string  $id Trip service ID
     * @return JsonResponse
     */
    public function __invoke(Request $request, int $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $tripService = $user->tripServices()->findOrFail($id);

        $tripService->bookings()
            ->where('status', BookingStatusEnum::ACCEPTED())
            ->whereNull('paid_at')
            ->update(['paid_at' => Carbon::now()]);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.update_data'),
        );
    }
}
