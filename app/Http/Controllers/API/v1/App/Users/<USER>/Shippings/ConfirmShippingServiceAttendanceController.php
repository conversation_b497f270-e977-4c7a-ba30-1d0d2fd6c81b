<?php

namespace App\Http\Controllers\API\v1\App\Users\Services\Shippings;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Services\Shipping\ShippingServiceResource;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ConfirmShippingServiceAttendanceController extends Controller
{
    /**
     * Confirm shipping service attendance by the owner.
     *
     * @param int $id
     * @return JsonResponse
     *
     * @response ShippingServiceResource
     *
     * @tags User Shipping Services
     */
    public function __invoke(Request $request, int $id)
    {
        /** @var User $user */
        $user = Auth::user();
        $shippingService = $user->shippingServices()->findOrFail($id);

        if ($shippingService->attendance_confirmed_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.shipping_service.attendance_already_confirmed'),
                null,
                422
            );
        }

        if ($shippingService->cancelled_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.shipping_service.cannot_confirm_cancelled_shipping'),
                null,
                422
            );
        }

        $now = Carbon::now();
        $departureTime = Carbon::parse($shippingService->departure_datetime);
        $minutesUntilDeparture = $now->diffInMinutes($departureTime, false);

        if ($minutesUntilDeparture > 30 || $minutesUntilDeparture < 0) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.shipping_service.attendance_confirmation_window'),
                null,
                422
            );
        }

        $shippingService->update([
            'attendance_confirmed_at' => $now
        ]);

        //ShippingServiceAttendanceConfirmedEvent::dispatch($shippingService->id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.shipping_service.attendance_confirmed'),
            ShippingServiceResource::make($shippingService->fresh())
        );
    }
}
