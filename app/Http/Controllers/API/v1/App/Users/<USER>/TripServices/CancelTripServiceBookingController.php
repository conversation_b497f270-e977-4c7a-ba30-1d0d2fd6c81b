<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\TripServices;

use App\Enums\Bookings\BookingStatusEnum;
use App\Enums\Travel\SeatStatusEnum;
use App\Events\TripServiceBookingCancelledByOwnerEvent;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Bookings\TripServices\CancelTripServiceBookingRequest;
use App\Http\Resources\Users\Bookings\TripServices\TripServiceBookingResource;
use App\Models\Booking\TripServiceBooking;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class CancelTripServiceBookingController extends Controller
{
    /**
     * Trip service owner cancels a booking.
     *
     * @param CancelTripServiceBookingByOwnerRequest $request
     * @param int $bookingId
     * @return JsonResponse
     *
     * @response TripServiceBookingResource
     *
     * @tags User Trip Services Bookings
     */
    public function __invoke(CancelTripServiceBookingRequest $request, int $bookingId)
    {
        /** @var User $user */
        $user = Auth::user();

        $booking = TripServiceBooking::query()
            ->whereHas('tripService', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->where('id', $bookingId)
            ->firstOrFail();

        if ($booking->status !== BookingStatusEnum::PENDING()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_service.booking.booking_is_not_pending'),
                null,
                422
            );
        }

        DB::beginTransaction();

        try {
            $booking->update([
                'status' => BookingStatusEnum::CANCELLED_BY_OWNER(),
                'cancellation_reason' => $request->validated('cancellation_reason'),
                'cancellation_note' => $request->validated('note'),
                'cancelled_at' => now(),
            ]);

            foreach ($booking->seats as $bookedSeat) {
                $bookedSeat->seat->update(['status' => SeatStatusEnum::AVAILABLE()]);
            }

            $booking->tripService->increment('number_of_available_seats', $booking->seats->count());

            DB::commit();

            TripServiceBookingCancelledByOwnerEvent::dispatch($booking->id);

            return ResponseHelper::sendSuccessResponse(
                __('messages.trip_service.bookings.cancelled'),
                TripServiceBookingResource::make($booking->fresh())
            );
        } catch (\Exception $e) {
            DB::rollBack();
            return ResponseHelper::sendFailedResponse(__('exceptions.internal_server_error'), ['error' => $e->getMessage()], 500);
        }
    }
}
