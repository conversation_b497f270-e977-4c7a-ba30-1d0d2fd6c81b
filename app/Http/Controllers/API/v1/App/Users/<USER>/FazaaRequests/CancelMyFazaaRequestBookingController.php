<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\FazaaRequests;

use App\Enums\Bookings\BookingStatusEnum;
use App\Events\FazaaRequestBookingCancelled;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Bookings\FazaaRequests\CancelMyFazaaRequestBookingRequest;
use App\Http\Resources\Users\Bookings\FazaaRequests\FazaaRequestBookingResource;
use App\Models\Booking\FazaaRequestBooking;
use App\Models\User;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;

class CancelMyFazaaRequestBookingController extends Controller
{
    /**
     * Cancel my Fazaa request booking.
     *
     * @param CancelMyFazaaRequestBookingRequest $request
     * @param int $bookingId Booking ID
     * @return JsonResponse
     *
     * @response FazaaRequestBookingResource
     *
     * @tags User Bookings, Fazaa Requests
     */
    public function __invoke(CancelMyFazaaRequestBookingRequest $request, int $bookingId)
    {
        /** @var User $user */
        $user = Auth::user();

        $booking = $user->fazaaRequestBookings()->with('fazaaRequest')
            ->findOrFail($bookingId);

        $booking->update([
            'status' => BookingStatusEnum::CANCELLED(),
            'cancellation_reason' => $request->validated('cancellation_reason'),
            'cancellation_note' => $request->validated('note'),
            'cancelled_at' => now(),
        ]);

        FazaaRequestBookingCancelled::dispatch($booking->id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.update_data'),
            FazaaRequestBookingResource::make($booking)
        );
    }
}
