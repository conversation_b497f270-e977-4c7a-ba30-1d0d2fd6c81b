<?php

namespace App\Http\Controllers\API\v1\App\Users\Accounts\Authentication\Otp;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Accounts\Authentication\Otp\SendOtpRequest;
use App\Models\User;
use App\Notifications\SendOtpNotification;
use Illuminate\Http\JsonResponse;

class SendOtpController extends Controller
{
    public function __invoke(SendOtpRequest $request): JsonResponse
    {
        $data = $request->validated();

        $user = User::query()
            ->where(function ($query) use ($data) {
                $query->when(isset($data['email']), fn($query) => $query->where('email', $data['email']))
                    ->when(isset($data['mobile']), fn($query) => $query->where('mobile', $data['mobile']));
            })->first();

        if (is_null($user)) {
            return ResponseHelper::sendFailedResponse(__('auth.failed'));
        }

        $otp = app()->environment('production', 'testing') ? rand(1000, 9999) : defaultOtp();

        $verificationCode = $user->verificationCode()->updateOrCreate(
            ['user_id' => $user->id],
            [
                'otp' => $otp,
                'expire_at' => now()->addMinutes(20),
            ]
        );

        if (app()->environment('production', 'testing')) {
            $user->notify(new SendOtpNotification($verificationCode->otp));
        }

        return ResponseHelper::sendSuccessResponse(
            __('auth.otp.verification_code_sent')
        );
    }
}
