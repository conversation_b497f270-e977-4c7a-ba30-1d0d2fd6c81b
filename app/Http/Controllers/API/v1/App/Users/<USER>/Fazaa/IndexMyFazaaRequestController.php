<?php

namespace App\Http\Controllers\API\v1\App\Users\ServiceRequests\Fazaa;

use App\Enums\Travel\TripStatusEnum;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\ServiceRequests\FazaaRequests\MyFazaaRequestsResource;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class IndexMyFazaaRequestController extends Controller
{
    /**
     * Get the user's Fazaa requests.
     *
     *
     * @param Request $request
     * @return JsonResponse
     *
     * @response Illuminate\Http\Resources\Json\AnonymousResourceCollection<Illuminate\Pagination\LengthAwarePaginator<MyFazaaRequestsResource>>
     *
     * @tags User Fazaa Requests
     */
    public function __invoke(Request $request): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();

        $perPage = $request->input('per_page', 10);

        $status = $request->input('status');

        $status = $request->input('status');

        $orderBy = $request->input('order_by', 'created_at');

        $sort = $request->input('sort', 'desc');

        $query = $user->fazaaRequests()->with(
            ['fromCity', 'toCity']
        )->when(
        !is_null($status),
            function (Builder $query) use ($status) {
                return match ($status) {
                    TripStatusEnum::ACTIVE() => $query->active(),
                    TripStatusEnum::COMPLETED() => $query->completed(),
                    TripStatusEnum::CANCELLED() => $query->cancelled(),
                };
            }
        )->orderBy($orderBy, $sort)->paginate($perPage);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            MyFazaaRequestsResource::collection($query)->appends($request->query())->toArray()
        );
    }
}
