<?php

namespace App\Http\Controllers\API\v1\App\Users\Accounts\Authentication\Otp;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Accounts\Authentication\Otp\VerifyOtpRequest;
use App\Http\Resources\Users\Accounts\Authentication\AuthenticationResource;
use App\Models\User;
use App\Notifications\SendOtpNotification;
use Illuminate\Http\JsonResponse;

class VerifyOtpController extends Controller
{
    public function __invoke(VerifyOtpRequest $request): JsonResponse
    {
        $data = $request->validated();

        $user = User::query()
            ->where(function ($query) use ($data) {
                $query->when(isset($data['email']), fn($query) => $query->where('email', $data['email']))
                    ->when(isset($data['mobile']), fn($query) => $query->where('mobile', $data['mobile']));
            })->first();


        if (is_null($user) || $user->verificationCode?->otp !== $data['otp']) {
            return ResponseHelper::sendFailedResponse(__('auth.otp.verification_code_failed'));
        }

        if ($user->verificationCode?->expire_at < now()) {
            return ResponseHelper::sendFailedResponse(__('auth.otp.verification_code_expired'));
        }

        /** @var User $user*/
        $accessToken =  $user->createToken('Personal Access Token');
        $user['token'] = $accessToken->plainTextToken;

        $user->verificationCode->delete();

        return ResponseHelper::sendSuccessResponse(
            __('auth.otp.verification_code_verified'),
            AuthenticationResource::make($user)
        );
    }
}
