<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\TripRequests;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Bookings\TripRequests\TripRequestsBookingResource;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ShowMyTripRequestBookingController extends Controller
{
    /**
     * Show details of a specific trip request booking.
     *
     * @param Request $request
     * @param int $id Booking ID
     * @return JsonResponse
     *
     * @response TripRequestsBookingResource
     *
     * @tags User Trip Requests Bookings
     */
    public function __invoke(Request $request, int $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $booking = $user->tripRequestBookings()->findOrFail($id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            TripRequestsBookingResource::make($booking->load('tripRequest'))
        );
    }
}
