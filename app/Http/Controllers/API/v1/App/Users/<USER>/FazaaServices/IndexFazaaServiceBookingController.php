<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\FazaaServices;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Bookings\FazaaServices\FazaaServiceBookingsResource;
use App\Models\Service\FazaaService;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class IndexFazaaServiceBookingController extends Controller
{
    /**
     * Get list of Fazaa service bookings.
     *
     * @param Request $request
     * @param int $id Fazaa service ID
     * @return JsonResponse
     *
     * @response Illuminate\Http\Resources\Json\AnonymousResourceCollection<Illuminate\Pagination\LengthAwarePaginator<FazaaServiceBookingResource>>
     *
     * @tags User Fazaa Services Bookings
     */
    public function __invoke(Request $request, int $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $search = $request->input('search');

        $fazaaService = FazaaService::where('user_id', $user->id)
            ->findOrFail($id);

        $perPage = $request->input('per_page', 10);

        $bookings = $fazaaService->bookings()
            ->with('user')
            ->when($search, function ($query) use ($search) {
                $query->where('note', 'ilike', '%' . $search . '%')
                ->orWhereHas('fazaaRequest', function ($query) use ($search) {
                    $query->where('note', 'ilike', '%' . $search . '%')
                    ->orWhereHas('fromCity', function ($query) use ($search) {
                        $query->where('name_en', 'ilike', '%' . $search . '%')
                            ->orWhere('name_ar', 'ilike', '%' . $search . '%');
                    })
                    ->orWhereHas('toCity', function ($query) use ($search) {
                        $query->where('name_en', 'ilike', '%' . $search . '%')
                            ->orWhere('name_ar', 'ilike', '%' . $search . '%');
                    });
                });
            })->paginate($perPage);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            FazaaServiceBookingsResource::collection($bookings)->appends($request->query())->toArray()
        );
    }
}
