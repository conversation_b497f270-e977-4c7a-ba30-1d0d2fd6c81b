<?php

namespace App\Http\Controllers\API\v1\App\Users\ServiceRequests\Fazaa;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\ServiceRequests\FazaaRequests\ShowFazaaRequestResource;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ShowMyFazaaRequestController extends Controller
{
    /**
     * Show details of a specific Fazaa request.
     *
     * @param Request $request
     * @param int $id Fazaa request ID
     * @return JsonResponse
     *
     * @response ShowFazaaRequestResource
     *
     * @tags User Fazaa Requests
     */
    public function __invoke(Request $request, int $id): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();

        $fazaaRequest = $user->fazaaRequests()
            ->with(['fromCity', 'toCity', 'fazaaServiceType', 'specificType'])
            ->findOrFail($id);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            ShowFazaaRequestResource::make($fazaaRequest)
        );
    }
}
