<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\TripServices;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\Bookings\TripServices\RateDriverRequest;
use App\Http\Requests\App\Users\Bookings\TripServices\StoreTripServiceBookingRatingRequest;
use App\Http\Resources\Users\Ratings\RatingResource;
use App\Models\Rating;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class StoreTripServiceBookingRatingController extends Controller
{
    /**
     * Rate a driver after a trip service has completed.
     *
     * @param RateDriverRequest $request
     * @param int $id Trip service booking ID
     * @return JsonResponse
     *
     * @response RatingResource
     *
     * @tags User Trip Services Bookings, Ratings
     */
    public function __invoke(StoreTripServiceBookingRatingRequest $request, int $id): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();

        $booking = $user->tripServiceBookings()->findOrFail($id);

        $tripService = $booking->tripService;

        if (!$tripService->arrival_datetime || $tripService->arrival_datetime > now()) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_service.cannot_rate_driver_before_arrival'),
                null,
                422
            );
        }

        if ($booking->arrival_confirmed_at === null) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_service.cannot_rate_driver_before_arrival_confirmed'),
                null,
                422
            );
        }

        $existingRating = Rating::where('trip_service_booking_id', $booking->id)
            ->where('user_id', $user->id)
            ->exists();

        if ($existingRating) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.trip_service.booking.already_rated'),
                null,
                422
            );
        }

        $rating = $booking->rating()->create([
            'user_id' => $user->id,
            'driver_id' => $tripService->user->id,
            'rating' => $request->rating,
            'comment' => $request->comment,
        ]);

        $averageRating = Rating::whereHas('tripServiceBooking', function ($query) use ($tripService) {
            $query->where('trip_service_id', $tripService->id);
        })->avg('rating');

        $tripService->update(['rating' => $averageRating ?? 0]);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.create_data'),
            RatingResource::make($rating)
        );
    }
}
