<?php

namespace App\Http\Controllers\API\v1\App\Users\ServiceRequests\Shippings;

use App\Events\ShippingRequestCancelledEvent;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\App\Users\ServiceRequests\Shippings\CancelShippingRequestRequest;
use App\Http\Resources\Users\ServiceRequests\ShippingRequests\ShippingRequestResource;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class CancelShippingRequestController extends Controller
{
    /**
     * Cancel an existing shipping request.
     *
     * @param CancelShippingRequestRequest $request
     * @param int $id
     * @return JsonResponse
     *
     * @response ShippingRequestResource
     *
     * @tags User Shipping Requests
     */
    public function __invoke(CancelShippingRequestRequest $request, $id)
    {
        /** @var User $user */
        $user = Auth::user();

        $shippingRequest = $user->shippingRequests()->findOrFail($id);

        if ($shippingRequest->cancelled_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.shipping_request.already_cancelled'),
                null,
                422
            );
        }

        if (Carbon::now()->gte($shippingRequest->departure_datetime)) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.shipping_request.cannot_cancel_after_departure'),
                null,
                422
            );
        }

        if ($shippingRequest->attendance_confirmed_at) {
            return ResponseHelper::sendFailedResponse(
                __('exceptions.shipping_request.cannot_cancel_after_attendance_confirmed'),
                null,
                422
            );
        }

        DB::beginTransaction();
        try {
            $shippingRequest->update([
                'cancelled_at' => Carbon::now(),
                'cancellation_reason' => $request->cancellation_reason,
                'cancellation_note' => $request->note,
            ]);

            ShippingRequestCancelledEvent::dispatch($shippingRequest->id);

            DB::commit();

            return ResponseHelper::sendSuccessResponse(
                __('messages.response.cancel_success'),
                ShippingRequestResource::make($shippingRequest->fresh())
            );
        } catch (\Exception $e) {
            DB::rollBack();
            return ResponseHelper::sendFailedResponse(
                __('exceptions.internal_server_error'),
                ['error' => $e->getMessage()],
                500
            );
        }
    }
}
