<?php

namespace App\Http\Controllers\API\v1\App\Users\Bookings\FazaaServices;

use App\Enums\Bookings\BookingStatusEnum;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Bookings\FazaaServices\FazaaServiceBookingResource;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class IndexMyFazaaServiceBookingController extends Controller
{
    /**
     * Get list of user's Fazaa service bookings.
     *
     * @param Request $request
     * @return JsonResponse
     *
     * @response Illuminate\Http\Resources\Json\AnonymousResourceCollection<Illuminate\Pagination\LengthAwarePaginator<FazaaServiceBookingResource>>
     *
     * @tags User Fazaa Services Bookings
     */
    public function __invoke(Request $request)
    {
        /** @var User $user */
        $user = Auth::user();

        $perPage = $request->input('per_page', 10);

        $status = $request->input('status');

        $search = $request->input('search');

        $orderBy = $request->input('order_by', 'created_at');

        $sort = $request->input('sort', 'desc');

        $bookings = $user->fazaaServiceBookings()
            ->with(['fazaaService.fromCity', 'fazaaService.toCity'])
            ->when(
                $status,
                function (Builder $query) use ($status) {
                    return match ($status) {
                        BookingStatusEnum::PENDING() => $query->where('status', BookingStatusEnum::PENDING()),
                        BookingStatusEnum::ACCEPTED() => $query->where('status', BookingStatusEnum::ACCEPTED()),
                        BookingStatusEnum::REJECTED() => $query->where('status', BookingStatusEnum::REJECTED()),
                        BookingStatusEnum::CANCELLED() => $query->where('status', BookingStatusEnum::CANCELLED())
                            ->orWhere('status', BookingStatusEnum::CANCELLED_BY_OWNER()),
                        default => $query
                    };
                }
            )->when($search, function ($query) use ($search) {
                $query->where('note', 'ilike', '%' . $search . '%')
                    ->orWhereHas('fazaaService', function ($query) use ($search) {
                        $query->where('note', 'ilike', '%' . $search . '%')
                            ->orWhereHas('fromCity', function ($query) use ($search) {
                                $query->where('name_en', 'ilike', '%' . $search . '%')
                                    ->orWhere('name_ar', 'ilike', '%' . $search . '%');
                            })
                            ->orWhereHas('toCity', function ($query) use ($search) {
                                $query->where('name_en', 'ilike', '%' . $search . '%')
                                    ->orWhere('name_ar', 'ilike', '%' . $search . '%');
                            });
                    });
            })->orderBy($orderBy, $sort)->paginate($perPage);


        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            FazaaServiceBookingResource::collection($bookings)->appends($request->all())->toArray()
        );
    }
}
