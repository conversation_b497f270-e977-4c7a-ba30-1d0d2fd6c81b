<?php

namespace App\Http\Controllers\Common\Countries;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Common\CountryResource;
use App\Models\Common\Country;
use Illuminate\Http\JsonResponse;

class IndexCountryController extends Controller
{
    public function __invoke(): JsonResponse
    {
        $countries = Country::all();

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            CountryResource::collection($countries)
        );
    }
}
