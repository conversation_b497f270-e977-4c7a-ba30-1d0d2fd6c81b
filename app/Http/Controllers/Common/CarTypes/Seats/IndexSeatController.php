<?php

namespace App\Http\Controllers\Common\CarTypes\Seats;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Cars\Seats\SeatsResource;
use App\Models\Common\CarType;
use App\Models\Common\Seat;
use Illuminate\Http\Request;

class IndexSeatController extends Controller
{
    /**
     * Get seats for a specific car type.
     *
     * @param Request $request
     * @param int $id Car type ID
     * @return JsonResponse
     *
     * @response Illuminate\Http\Resources\Json\AnonymousResourceCollection<SeatsResource>
     *
     * @tags Car Types, Seats
     */
    public function __invoke(Request $request, int $id)
    {
        $carType = CarType::findOrFail($id);

        $seats = Seat::query()->take($carType->number_of_seats + 2)
            ->orderBy('number', 'asc')->get();

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            SeatsResource::collection($seats)
        );
    }
}
