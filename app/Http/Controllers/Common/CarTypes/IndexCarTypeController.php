<?php

namespace App\Http\Controllers\Common\CarTypes;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Users\Cars\CarTypesResource;
use App\Models\Common\CarType;
use Illuminate\Http\Request;

class IndexCarTypeController extends Controller
{
    /**
     * Get all car types.
     *
     * @param Request $request
     * @return JsonResponse
     *
     * @response Illuminate\Http\Resources\Json\AnonymousResourceCollection<Illuminate\Pagination\LengthAwarePaginator<CarTypesResource>>
     *
     * @tags Car Types
     */
    public function __invoke(Request $request)
    {
        $sort = $request->input('sort') ?? 'desc';

        $orderBy = $request->input('order_by') ?? 'created_at';

        $perPage = $request->input('per_page') ?? 10;

        $query = CarType::query()->orderBy($orderBy, $sort)->paginate($perPage);

        $carTypes = CarTypesResource::collection($query);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            $carTypes
        );
    }
}
