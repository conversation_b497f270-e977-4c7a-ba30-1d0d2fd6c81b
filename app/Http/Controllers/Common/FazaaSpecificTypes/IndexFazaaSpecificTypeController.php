<?php

namespace App\Http\Controllers\Common\FazaaSpecificTypes;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\FazaaSpecificTypes\FazaaSpecificTypesResource;
use App\Models\Common\FazaaSpecificType;
use Illuminate\Http\Request;

class IndexFazaaSpecificTypeController extends Controller
{
    /**
     * Get the list of fazaa specific types.
     *
     * @param Request $request
     * @return JsonResponse
     *
     * @response Illuminate\Http\Resources\Json\AnonymousResourceCollection<Illuminate\Pagination\LengthAwarePaginator<FazaaSpecificTypesResource>>
     *
     * @tags Fazaa Specific Types
     */
    public function __invoke(Request $request)
    {
        $sort = $request->input('sort', 'desc');
        $orderBy = $request->input('order_by', 'created_at');

        $query = FazaaSpecificType::query()
            ->when(
                $request->exists('fazaa_service_type_id'),
                fn($query) => $query->where('fazaa_service_type_id', $request->input('fazaa_service_type_id'))
            )
            ->orderBy($orderBy, $sort)
            ->get();

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            FazaaSpecificTypesResource::collection($query)
        );
    }
}
