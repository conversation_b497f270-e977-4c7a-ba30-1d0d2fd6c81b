<?php

namespace App\Http\Controllers\Common\AdditionalServices;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\AdditionalServices\AdditionalServicesResource;
use App\Models\Service\AdditionalService;
use Illuminate\Http\Request;

class IndexAdditionalServiceController extends Controller
{
    /**
     * Get additional services.
     *
     * @param Request $request
     * @return JsonResponse
     *
     * @response Illuminate\Http\Resources\Json\AnonymousResourceCollection<Illuminate\Pagination\LengthAwarePaginator<AdditionalServicesResource>>
     *
     * @tags Additional Services
     */
    public function __invoke(Request $request)
    {
        $sort = $request->input('sort') ?? 'desc';

        $orderBy = $request->input('order_by') ?? 'created_at';

        $perPage = $request->input('per_page') ?? 10;

        $query = AdditionalService::query()->orderBy($orderBy, $sort)->paginate($perPage);

        $additionalServices = AdditionalServicesResource::collection($query);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            $additionalServices
        );
    }
}
