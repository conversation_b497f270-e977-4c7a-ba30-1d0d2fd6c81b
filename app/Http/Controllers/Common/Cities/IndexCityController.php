<?php

namespace App\Http\Controllers\Common\Cities;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Cities\CitiesResource;
use App\Models\Common\City;
use Illuminate\Http\Request;

class IndexCityController extends Controller
{
    /**
     * Get the list of cities.
     *
     * @param Request $request
     * @return JsonResponse
     *
     * @response Illuminate\Http\Resources\Json\AnonymousResourceCollection<Illuminate\Pagination\LengthAwarePaginator<CitiesResource>
     *
     * @tags Cities
     */
    public function __invoke(Request $request)
    {
        $sort = $request->input('sort') ?? 'desc';

        $orderBy = $request->input('order_by') ?? 'created_at';

        $perPage = $request->input('per_page') ?? 10;

        $query = City::query()->orderBy($orderBy, $sort)->paginate($perPage);

        $cities = CitiesResource::collection($query);

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            $cities
        );
    }
}
