<?php

namespace App\Http\Controllers\Common\FazaaServiceTypes;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\FazaaServiceTypes\FazaaServiceTypesResource;
use App\Models\Common\FazaaServiceType;
use Illuminate\Http\Request;

class IndexFazaaServiceTypeController extends Controller
{
    /**
     * Get the list of fazaa service types.
     *
     * @param Request $request
     * @return JsonResponse
     *
     * @response Illuminate\Http\Resources\Json\AnonymousResourceCollection<Illuminate\Pagination\LengthAwarePaginator<FazaaServiceTypesResource>>
     *
     * @tags Fazaa Service Types
     */
    public function __invoke(Request $request)
    {
        $fazaaServiceTypes = FazaaServiceType::all();

        return ResponseHelper::sendSuccessResponse(
            __('messages.response.get_data'),
            FazaaServiceTypesResource::collection($fazaaServiceTypes)
        );
    }
}
