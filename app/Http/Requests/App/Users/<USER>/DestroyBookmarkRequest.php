<?php

namespace App\Http\Requests\App\Users\Bookmarks;

use App\Http\Requests\AbstractFormRequest;

class DestroyBookmarkRequest extends AbstractFormRequest
{

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'bookmarkable_id' => [
                'required',
                'integer',
            ],
        ];
    }
}
