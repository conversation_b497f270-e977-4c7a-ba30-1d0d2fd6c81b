<?php

namespace App\Http\Requests\App\Users\Devices;

use App\Http\Requests\AbstractFormRequest;

class StoreDeviceRequest extends AbstractFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'fcm_token' => [
                'required',
                'string',
            ],
        ];
    }
}
