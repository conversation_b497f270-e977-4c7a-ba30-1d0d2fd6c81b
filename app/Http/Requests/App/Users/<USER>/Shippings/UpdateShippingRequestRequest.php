<?php

namespace App\Http\Requests\App\Users\ServiceRequests\Shippings;

use App\Enums\Travel\TransportationTypeEnum;
use App\Http\Requests\AbstractFormRequest;
use Illuminate\Validation\Rule;

class UpdateShippingRequestRequest extends AbstractFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'transportation_type' => [
                'sometimes',
                'string',
                Rule::in(TransportationTypeEnum::values()),
            ],
            'departure_datetime' => [
                'sometimes',
                'date',
                'after:now',
                'before_or_equal:' . now()->addDays(5)->format('Y-m-d H:i:s'),
            ],
            'arrival_datetime' => [
                'sometimes',
                'date',
                'after:departure_datetime',
            ],
            'from_city_en' => [
                'sometimes',
                'string',
                'min:2',
                'max:255',
            ],
            'from_city_ar' => [
                'sometimes',
                'string',
                'min:2',
                'max:255',
            ],
            'to_city_en' => [
                'sometimes',
                'string',
                'min:2',
                'max:255',
            ],
            'to_city_ar' => [
                'sometimes',
                'string',
                'min:2',
                'max:255',
            ],
            'from_location' => [
                'sometimes',
                'array'
            ],
            'from_location.lat' => [
                'required_with:from_location',
                'numeric',
                'min:-90',
                'max:90'
            ],
            'from_location.lng' => [
                'required_with:from_location',
                'numeric',
                'min:-180',
                'max:180'
            ],
            'to_location' => [
                'sometimes',
                'array'
            ],
            'to_location.lat' => [
                'required_with:to_location',
                'numeric',
                'min:-90',
                'max:90'
            ],
            'to_location.lng' => [
                'required_with:to_location',
                'numeric',
                'min:-180',
                'max:180'
            ],
            'can_ship_packages' => [
                'sometimes',
                'boolean'
            ],
            'can_ship_documents' => [
                'sometimes',
                'boolean'
            ],
            'can_ship_furniture' => [
                'sometimes',
                'boolean'
            ],
            'packages_volume' => [
                Rule::requiredIf(
                    fn() => $this->input('can_ship_packages') === true
                ),
                'integer',
                'min:1',
                'max:100000',
            ],
            'document_volume' => [
                Rule::requiredIf(
                    fn() => $this->input('can_ship_documents') === true
                ),
                'integer',
                'min:1',
                'max:100000',
            ],
            'furniture_volume' => [
                Rule::requiredIf(
                    fn() => $this->input('can_ship_furniture') === true
                ),
                'integer',
                'min:1',
                'max:100000',
            ],
            'delivery_location' => [
                'sometimes',
                'string',
                'max:255',
            ],
            'note' => [
                'sometimes',
                'nullable',
                'string',
                'max:512',
            ],
            'is_fragile' => [
                'sometimes',
                'boolean'
            ],
        ];
    }
}
