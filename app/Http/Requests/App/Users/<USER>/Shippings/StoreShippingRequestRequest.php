<?php

namespace App\Http\Requests\App\Users\ServiceRequests\Shippings;

use App\Enums\Travel\TransportationTypeEnum;
use App\Http\Requests\AbstractFormRequest;
use Illuminate\Support\Carbon;
use Illuminate\Validation\Rule;

class StoreShippingRequestRequest extends AbstractFormRequest
{
    public function rules(): array
    {
        return [
            'transportation_type' => [
                'required',
                'string',
                Rule::in(TransportationTypeEnum::values()),
            ],
            'from_city_en' => [
                'required',
                'string',
                'min:2',
                'max:255',
            ],
            'from_city_ar' => [
                'required',
                'string',
                'min:2',
                'max:255',
            ],
            'from_location.lat' => [
                'required',
                'numeric',
                'min:-90',
                'max:90',
            ],
            'from_location.lng' => [
                'required',
                'numeric',
                'min:-180',
                'max:180',
            ],
            'to_city_en' => [
                'required',
                'string',
                'min:2',
                'max:255',
            ],
            'to_city_ar' => [
                'required',
                'string',
                'min:2',
                'max:255',
            ],
            'to_location.lat' => [
                'required',
                'numeric',
                'min:-90',
                'max:90',
            ],
            'to_location.lng' => [
                'required',
                'numeric',
                'min:-180',
                'max:180',
            ],
            'departure_datetime' => [
                'required',
                'date',
                'after_or_equal:now',
                'before_or_equal:' . now()->addDays(5)->format('Y-m-d H:i:s'),
            ],
            'arrival_datetime' => [
                'required',
                'date',
                'after:departure_datetime',
            ],
            'can_ship_packages' => [
                'required',
                'boolean'
            ],
            'can_ship_documents' => [
                'required',
                'boolean'
            ],
            'can_ship_furniture' => [
                'required',
                'boolean'
            ],
            'packages_volume' => [
                Rule::requiredIf(
                    fn() => $this->input('can_ship_packages') === true
                ),
                'integer',
                'min:1',
                'max:100000',
            ],
            'document_volume' => [
                Rule::requiredIf(
                    fn() => $this->input('can_ship_documents') === true
                ),
                'integer',
                'min:1',
                'max:100000',
            ],
            'furniture_volume' => [
                Rule::requiredIf(
                    fn() => $this->input('can_ship_furniture') === true
                ),
                'integer',
                'min:1',
                'max:100000',
            ],
            'delivery_location' => [
                'nullable',
                'string',
                'max:255',
            ],
            'note' => [
                'nullable',
                'string',
                'max:512',
            ],
            'is_fragile' => [
                'required',
                'boolean'
            ],
        ];
    }
}
