<?php

namespace App\Http\Requests\App\Users\ServiceRequests\Fazaa;

use App\Http\Requests\AbstractFormRequest;

class DelayFazaaRequestRequest extends AbstractFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'departure_datetime' => [
                'required',
                'date',
                'after:now',
                'before_or_equal:' . now()->addDays(5)->format('Y-m-d H:i:s'),
            ],
            'arrival_datetime' => [
                'required',
                'date',
                'after:departure_datetime'
            ],
            'delay_reason' => [
                'required',
                'string',
                'in:medical_reason,other_reason'
            ],
            'note' => [
                'nullable',
                'string',
                'max:500'
            ]
        ];
    }
}
