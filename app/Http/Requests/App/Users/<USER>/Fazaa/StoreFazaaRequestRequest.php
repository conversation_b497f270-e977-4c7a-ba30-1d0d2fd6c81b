<?php

namespace App\Http\Requests\App\Users\ServiceRequests\Fazaa;

use App\Enums\Travel\TransportationTypeEnum;
use App\Http\Requests\AbstractFormRequest;
use Illuminate\Validation\Rule;

class StoreFazaaRequestRequest extends AbstractFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'fazaa_service_type_id' => [
                'required',
                'integer',
                Rule::exists('fazaa_service_types', 'id'),
            ],
            'specific_type_id' => [
                'required',
                'integer',
                Rule::exists('fazaa_specific_types', 'id')->where(function ($query) {
                    $query->where('fazaa_service_type_id', $this->input('fazaa_service_type_id'));
                }),
            ],
            'transportation_type' => [
                'required',
                'string',
                Rule::in(TransportationTypeEnum::values()),

            ],
            'from_city_en' => [
                'required',
                'string',
                'min:2',
                'max:255',
            ],
            'from_city_ar' => [
                'required',
                'string',
                'min:2',
                'max:255',
            ],
            'from_location.lat' => [
                'required',
                'numeric',
                'min:-90',
                'max:90',
            ],
            'from_location.lng' => [
                'required',
                'numeric',
                'min:-180',
                'max:180',
            ],
            'to_city_en' => [
                'required',
                'string',
                'min:2',
                'max:255',
            ],
            'to_city_ar' => [
                'required',
                'string',
                'min:2',
                'max:255',
            ],
            'to_location.lat' => [
                'required',
                'numeric',
                'min:-90',
                'max:90',
            ],
            'to_location.lng' => [
                'required',
                'numeric',
                'min:-180',
                'max:180',
            ],
            'departure_datetime' => [
                'required',
                'date',
                'after_or_equal:now',
                'before_or_equal:' . now()->addDays(5)->format('Y-m-d H:i:s'),
            ],
            'arrival_datetime' => [
                'required',
                'date',
                'after:departure_datetime',
            ],
            'service_location' => [
                'required',
                'string',
                'min:2',
                'max:255',
            ],
            'note' => [
                'nullable',
                'string',
                'max:512',
            ],
        ];
    }
}
