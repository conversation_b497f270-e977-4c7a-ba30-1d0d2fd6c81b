<?php

namespace App\Http\Requests\App\Users\Services\Fazaa;

use App\Http\Requests\AbstractFormRequest;
use Carbon\Carbon;

class DelayFazaaServiceRequest extends AbstractFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'departure_datetime' => [
                'required',
                'date',
                'after:now',
                'before_or_equal:' . now()->addDays(5)->format('Y-m-d H:i:s'),
            ],
            'arrival_datetime' => [
                'required',
                'date',
                'after:departure_datetime',
            ],
            'delay_reason' => [
                'required',
                'string',
                'min:3',
                'max:255'
            ],
            'note' => [
                'nullable',
                'string',
                'max:500'
            ]
        ];
    }
}
