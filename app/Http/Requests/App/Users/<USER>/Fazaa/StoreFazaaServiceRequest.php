<?php

namespace App\Http\Requests\App\Users\Services\Fazaa;

use App\Enums\Travel\FazaaServiceTypeEnum;
use App\Enums\Travel\TransportationTypeEnum;
use App\Http\Requests\AbstractFormRequest;
use App\Models\Common\FazaaServiceType;
use Illuminate\Validation\Rule;

class StoreFazaaServiceRequest extends AbstractFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'fazaa_service_type_id' => [
                'required',
                'integer',
                Rule::exists('fazaa_service_types', 'id'),
            ],
            'specific_type_id' => [
                'required',
                'integer',
                Rule::exists('fazaa_specific_types', 'id')->where(function ($query) {
                    $query->where('fazaa_service_type_id', $this->input('fazaa_service_type_id'));
                }),
            ],
            'transportation_type' => [
                'required',
                'string',
                Rule::in(TransportationTypeEnum::values()),
            ],
            'from_city_en' => [
                'required',
                'string',
                'min:2',
                'max:255',
            ],
            'from_city_ar' => [
                'required',
                'string',
                'min:2',
                'max:255',
            ],
            'from_location.lat' => [
                'required',
                'numeric',
                'min:-90',
                'max:90',
            ],
            'from_location.lng' => [
                'required',
                'numeric',
                'min:-180',
                'max:180',
            ],
            'to_city_en' => [
                'required',
                'string',
                'min:2',
                'max:255',
            ],
            'to_city_ar' => [
                'required',
                'string',
                'min:2',
                'max:255',
            ],
            'to_location.lat' => [
                'required',
                'numeric',
                'min:-90',
                'max:90',
            ],
            'to_location.lng' => [
                'required',
                'numeric',
                'min:-180',
                'max:180',
            ],
            'departure_datetime' => [
                'required',
                'date',
                'after_or_equal:now',
                'before_or_equal:' . now()->addDays(5)->format('Y-m-d H:i:s'),
            ],
            'arrival_datetime' => [
                'required',
                'date',
                'after:departure_datetime',
            ],
            'service_location' => [
                'required',
                'string',
                'min:2',
                'max:255',
            ],
            'price' => [
                'required',
                'numeric',
                'min:1',
                'max:100000',
            ],
            'shipment_volume' => [
                Rule::requiredIf(function () {
                    $serviceType = FazaaServiceType::find($this->input('fazaa_service_type_id'));
                    return $serviceType && $serviceType->key === 'receive_shipment';
                }),
                'nullable',
                'integer',
                'min:1',
                'max:100000',
            ],
            'note' => [
                'nullable',
                'string',
                'max:512',
            ],
        ];
    }
}
