<?php

namespace App\Http\Requests\App\Users\Bookings\FazaaRequests;

use App\Http\Requests\AbstractFormRequest;

class StoreFazaaRequestBookingRequest extends AbstractFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'price' => [
                'required',
                'numeric',
                'min:0',
                'max:10000',
            ],
            'note' => [
                'nullable',
                'string',
                'max:512',
            ],
        ];
    }
}
