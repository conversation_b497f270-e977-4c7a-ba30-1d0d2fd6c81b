<?php

namespace App\Http\Requests\App\Users\Bookings\FazaaServices;

use App\Http\Requests\AbstractFormRequest;

class StoreFazaaServiceBookingRequest extends AbstractFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'note' => [
                'nullable',
                'string',
                'max:512',
            ],
        ];
    }
}
