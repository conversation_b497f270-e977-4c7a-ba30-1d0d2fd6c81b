<?php

namespace App\Http\Requests\App\Users\Cars;

use App\Http\Requests\AbstractFormRequest;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class StoreCarRequest extends AbstractFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'type_id' => [
                'required',
                'integer',
                'exists:car_types,id',
            ],
            'model' => [
                'required',
                'integer',
                'min:1980',
                'max:' . Carbon::now()->year
            ],
            'plate_number' => [
                'required',
                'string',
                'min:5',
                'max:10',
                Rule::unique('cars', 'plate_number')
                    ->where('type_id', $this->type_id)
                    ->where('model', $this->model)
                    ->where('user_id', Auth::id()),
            ],
        ];
    }
}
