<?php

namespace App\Http\Requests\App\Users\Cars;

use App\Http\Requests\AbstractFormRequest;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class UpdateCarRequest extends AbstractFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'type_id' => [
                'sometimes',
                'integer',
                'exists:car_types,id',
            ],
            'model' => [
                'sometimes',
                'integer',
                'min:1980',
                'max:' . Carbon::now()->year
            ],
            'plate_number' => [
                'sometimes',
                'string',
                'min:5',
                'max:10',
                Rule::unique('cars', 'plate_number')
                    ->where('type_id', $this->type_id)
                    ->where('model', $this->model)
                    ->where('user_id', Auth::id())
                    ->ignore($this->route('car')),
            ],
        ];
    }
}
