<?php

namespace App\Http\Requests\App\Users\Bookings\TripRequests;

use App\Http\Requests\AbstractFormRequest;

class StoreTripRequestBookingRequest extends AbstractFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'price' => [
                'required',
                'numeric',
                'min:0',
                'max:100000'
            ],
            'note' => [
                'nullable',
                'string',
                'max:512',
            ],
        ];
    }
}
