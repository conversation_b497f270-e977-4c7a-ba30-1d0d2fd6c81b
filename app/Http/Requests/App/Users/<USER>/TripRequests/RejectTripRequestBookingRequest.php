<?php

namespace App\Http\Requests\App\Users\Bookings\TripRequests;

use App\Http\Requests\AbstractFormRequest;

class RejectTripRequestBookingRequest extends AbstractFormRequest
{
    public function rules(): array
    {
        return [
            'rejection_reason' => [
                'required',
                'string',
                'max:255'
            ],
            'note' => [
                'nullable',
                'string',
                'max:500'
            ]
        ];
    }
}
