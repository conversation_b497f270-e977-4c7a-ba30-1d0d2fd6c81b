<?php

namespace App\Http\Requests\App\Users\Accounts\Authentication\Otp;

use App\Http\Requests\AbstractFormRequest;

class VerifyOtpRequest extends AbstractFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'email' => [
                'required_without:mobile',
                'string',
                'email',
            ],
            'mobile' => [
                'required_without:email',
                'string',
            ],
            'otp' => [
                'required',
                'string',
            ],
        ];
    }
}
