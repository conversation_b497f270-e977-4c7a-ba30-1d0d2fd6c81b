<?php

namespace App\Http\Requests\App\Users\Accounts\Authentication;

use App\Http\Requests\AbstractFormRequest;

class RegisterRequest extends AbstractFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'name' => [
                'required',
                'string',
                'max:255'
            ],
            'email' => [
                'required',
                'string',
                'email:rfc,dns,filter',
                'max:255',
                'unique:users'
            ],
            'mobile' => [
                'sometimes',
                'string',
                'unique:users'
            ],
            'newsletter_subscription' => [
                'sometimes',
                'boolean'
            ],
            'whatsapp_number' => [
                'sometimes',
                'string',
                'unique:users'
            ],
        ];
    }
}
