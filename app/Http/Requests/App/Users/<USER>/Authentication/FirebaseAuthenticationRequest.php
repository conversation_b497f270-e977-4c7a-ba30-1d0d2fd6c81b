<?php

namespace App\Http\Requests\App\Users\Accounts\Authentication;

use App\Http\Requests\AbstractFormRequest;

class FirebaseAuthenticationRequest extends AbstractFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'id_token' => [
                'required',
                'string'
            ],
        ];
    }
}
