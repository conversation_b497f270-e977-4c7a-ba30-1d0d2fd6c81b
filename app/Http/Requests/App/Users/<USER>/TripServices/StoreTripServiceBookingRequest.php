<?php

namespace App\Http\Requests\App\Users\Bookings\TripServices;

use App\Enums\Travel\SeatStatusEnum;
use App\Http\Requests\AbstractFormRequest;
use App\Models\Service\TripService;

class StoreTripServiceBookingRequest extends AbstractFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $tripService = TripService::findOrFail($this->route('id'));

        return [
            'note' => [
                'nullable',
                'string',
                'max:512',
            ],
            'seats' => [
                'required',
                'array',
                'min:1',
                'max:' . $tripService->number_of_available_seats,
            ],
            'seats.*' => [
                'required',
                'string',
                'regex:/^[A-C][1-3]$/',
                function ($attribute, $value, $fail) use ($tripService) {
                    $seat = $tripService->seats()->where('number', $value)->first();
                    if (!$seat) {
                        $fail(__('exceptions.trip_service.booking.seat_does_not_exist'));
                    } elseif ($seat->status !== SeatStatusEnum::AVAILABLE()) {
                        $fail(__('exceptions.trip_service.booking.seat_not_available'));
                    }
                },
            ],
            'has_fragile_items' => [
                'required',
                'boolean',
            ],
        ];
    }
}
