<?php

namespace App\Http\Requests\App\Users\Bookings\TripServices;

use App\Http\Requests\AbstractFormRequest;

class StoreTripServiceBookingRatingRequest extends AbstractFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {

        return [
            'rating' => [
                'required',
                'integer',
                'min:1',
                'max:5',
            ],
            'comment' => [
                'nullable',
                'string',
                'max:500',
            ]
        ];
    }
}
