<?php

namespace App\Http\Requests\App\Users\Bookings\TripServices;

use App\Http\Requests\AbstractFormRequest;

class MarkTripServiceBookingsAsPaidRequest extends AbstractFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'booking_ids' => [
                'required',
                'array',
            ],
            'booking_ids.*' => [
                'required',
                'integer',
            ],
        ];
    }
}
