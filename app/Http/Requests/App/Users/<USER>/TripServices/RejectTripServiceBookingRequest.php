<?php

namespace App\Http\Requests\App\Users\Bookings\TripServices;

use App\Http\Requests\AbstractFormRequest;

class RejectTripServiceBookingRequest extends AbstractFormRequest
{
    public function rules(): array
    {
        return [
            'rejection_reason' => [
                'required',
                'string',
                'max:255'
            ],
            'note' => [
                'nullable',
                'string',
                'max:500'
            ]
        ];
    }
}
