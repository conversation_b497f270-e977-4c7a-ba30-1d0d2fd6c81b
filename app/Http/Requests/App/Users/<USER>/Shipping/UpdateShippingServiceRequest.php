<?php

namespace App\Http\Requests\App\Users\Services\Shipping;

use App\Enums\Travel\TransportationTypeEnum;
use App\Http\Requests\AbstractFormRequest;
use App\Models\Common\Car;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class UpdateShippingServiceRequest extends AbstractFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'transportation_type' => [
                'sometimes',
                'string',
                Rule::in(TransportationTypeEnum::values()),
            ],
            'car_id' => [
                'sometimes',
                'integer',
                Rule::exists(Car::class, 'id')->where('user_id', Auth::user()?->id),
            ],
            'from_city_en' => [
                'sometimes',
                'string',
                'min:2',
                'max:255',
            ],
            'from_city_ar' => [
                'sometimes',
                'string',
                'min:2',
                'max:255',
            ],
            'from_location.lat' => [
                'sometimes',
                'numeric',
                'min:-90',
                'max:90',
            ],
            'from_location.lng' => [
                'sometimes',
                'numeric',
                'min:-180',
                'max:180',
            ],
            'to_city_en' => [
                'sometimes',
                'string',
                'min:2',
                'max:255',
            ],
            'to_city_ar' => [
                'sometimes',
                'string',
                'min:2',
                'max:255',
            ],
            'to_location.lat' => [
                'sometimes',
                'numeric',
                'min:-90',
                'max:90',
            ],
            'to_location.lng' => [
                'sometimes',
                'numeric',
                'min:-180',
                'max:180',
            ],
            'departure_datetime' => [
                'sometimes',
                'date',
                'after:now',
                'before_or_equal:' . now()->addDays(5)->format('Y-m-d H:i:s'),
            ],
            'arrival_datetime' => [
                'sometimes',
                'date',
                'after:departure_datetime',
            ],
            'can_ship_packages' => [
                'sometimes',
                'boolean',
            ],
            'can_ship_documents' => [
                'sometimes',
                'boolean',
            ],
            'can_ship_furniture' => [
                'sometimes',
                'boolean',
                function ($attribute, $value, $fail) {
                    if ($this->input('transportation_type') === TransportationTypeEnum::FLIGHT() && $value === true) {
                        $fail('Furniture shipping is not allowed for flight transportation.');
                    }
                },
            ],
            'packages_volume' => [
                'sometimes',
                'nullable',
                'numeric',
                'min:1',
                'max:100000',
                Rule::requiredIf(fn() => $this->input('can_ship_packages') === true),
            ],
            'document_volume' => [
                'sometimes',
                'nullable',
                'numeric',
                'min:1',
                'max:100000',
                Rule::requiredIf(fn() => $this->input('can_ship_documents') === true),
            ],
            'furniture_volume' => [
                'sometimes',
                'nullable',
                'numeric',
                'min:1',
                'max:100000',
                Rule::requiredIf(fn() => $this->input('can_ship_furniture') === true),
            ],
            'package_price' => [
                'sometimes',
                'nullable',
                'numeric',
                'min:1',
                'max:10000',
                Rule::requiredIf(fn() => $this->input('can_ship_packages') === true),
            ],
            'document_price' => [
                'sometimes',
                'nullable',
                'numeric',
                'min:1',
                'max:10000',
                Rule::requiredIf(fn() => $this->input('can_ship_documents') === true),
            ],
            'furniture_price' => [
                'sometimes',
                'nullable',
                'numeric',
                'min:1',
                'max:10000',
                Rule::requiredIf(fn() => $this->input('can_ship_furniture') === true),
            ],
            'note' => [
                'sometimes',
                'string',
                'max:512',
            ],
        ];
    }

    protected function prepareForValidation()
    {
        if ($this->has('can_ship_packages') && $this->input('can_ship_packages') === false) {
            $this->merge([
                'packages_volume' => null,
                'package_price' => null,
                'available_packages_volume' => null,
            ]);
        }
        if ($this->has('can_ship_documents') && $this->input('can_ship_documents') === false) {
            $this->merge([
                'document_volume' => null,
                'document_price' => null,
                'available_document_volume' => null,
            ]);
        }
        if ($this->has('can_ship_furniture') && $this->input('can_ship_furniture') === false) {
            $this->merge([
                'furniture_volume' => null,
                'furniture_price' => null,
                'available_furniture_volume' => null,
            ]);
        }
    }
}
