<?php

namespace App\Http\Requests\App\Users\Services\Shipping;

use App\Enums\Travel\TransportationTypeEnum;
use App\Http\Requests\AbstractFormRequest;
use App\Models\Common\Car;
use App\Models\User;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class StoreShippingServiceRequest extends AbstractFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'transportation_type' => [
                'required',
                'string',
                Rule::in(TransportationTypeEnum::values()),
            ],
            'car_id' => [
                Rule::requiredIf(
                    fn() => $this->input('transportation_type') === TransportationTypeEnum::CAR()
                ),
                'integer',
                Rule::exists(Car::class, 'id')->where('user_id', Auth::user()?->id),
            ],
            'from_city_en' => [
                'required',
                'string',
                'min:2',
                'max:255',
            ],
            'from_city_ar' => [
                'required',
                'string',
                'min:2',
                'max:255',
            ],
            'from_location.lat' => [
                'required',
                'numeric',
                'min:-90',
                'max:90',
            ],
            'from_location.lng' => [
                'required',
                'numeric',
                'min:-180',
                'max:180',
            ],
            'to_city_en' => [
                'required',
                'string',
                'min:2',
                'max:255',
            ],
            'to_city_ar' => [
                'required',
                'string',
                'min:2',
                'max:255',
            ],
            'to_location.lat' => [
                'required',
                'numeric',
                'min:-90',
                'max:90',
            ],
            'to_location.lng' => [
                'required',
                'numeric',
                'min:-180',
                'max:180',
            ],
            'departure_datetime' => [
                'required',
                'date',
                'after_or_equal:now',
                'before_or_equal:' . now()->addDays(5)->format('Y-m-d H:i:s'),
            ],
            'arrival_datetime' => [
                'required',
                'date',
                'after:departure_datetime',
            ],
            'can_ship_packages' => [
                'required_without_all:can_ship_documents,can_ship_furniture',
                'boolean'
            ],
            'can_ship_documents' => [
                'required_without_all:can_ship_packages,can_ship_furniture',
                'boolean'
            ],
            'can_ship_furniture' => [
                'required_without_all:can_ship_packages,can_ship_documents',
                function ($attribute, $value, $fail) {
                    if ($this->input('transportation_type') === TransportationTypeEnum::FLIGHT() && $value === true) {
                        $fail('Furniture shipping is not allowed for flight transportation.');
                    }
                },
                'boolean'
            ],
            'packages_volume' => [
                Rule::requiredIf(
                    fn() => $this->input('can_ship_packages') === true
                ),
                'integer',
                'nullable',
                'min:1',
                'max:100000',
            ],
            'document_volume' => [
                Rule::requiredIf(
                    fn() => $this->input('can_ship_documents') === true
                ),
                'integer',
                'nullable',
                'min:1',
                'max:100000',
            ],
            'furniture_volume' => [
                Rule::requiredIf(
                    fn() => $this->input('can_ship_furniture') === true
                ),
                'integer',
                'nullable',
                'min:1',
                'max:100000',
            ],
            'delivery_location' => [
                'nullable',
                'string',
                'max:255',
            ],
            'package_price' => [
                Rule::requiredIf(
                    fn() => $this->input('can_ship_packages') === true
                ),
                'nullable',
                'numeric',
                'min:10',
                'max:10000',
            ],
            'document_price' => [
                Rule::requiredIf(
                    fn() => $this->input('can_ship_documents') === true
                ),
                'nullable',
                'numeric',
                'min:10',
                'max:10000',
            ],
            'furniture_price' => [
                Rule::requiredIf(
                    fn() => $this->input('can_ship_furniture') === true
                ),
                'nullable',
                'numeric',
                'min:10',
                'max:10000',
            ],
            'note' => [
                'nullable',
                'string',
                'max:512',
            ],
        ];
    }
}
