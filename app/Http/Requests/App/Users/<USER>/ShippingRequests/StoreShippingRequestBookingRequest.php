<?php

namespace App\Http\Requests\App\Users\Bookings\ShippingRequests;

use App\Http\Requests\AbstractFormRequest;
use App\Models\Request\ShippingRequest;
use Illuminate\Validation\Rule;

class StoreShippingRequestBookingRequest extends AbstractFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $shippingRequest = ShippingRequest::find($this->route('id'));

        return [
            'package_price' => [
                Rule::requiredIf(
                    fn() => $shippingRequest->can_ship_packages === true
                ),
                'numeric',
                'min:10',
                'max:100000',
            ],
            'document_price' => [
                Rule::requiredIf(
                    fn() => $shippingRequest->can_ship_documents === true
                ),
                'numeric',
                'min:10',
                'max:100000',
            ],
            'furniture_price' => [
                Rule::requiredIf(
                    fn() => $shippingRequest->can_ship_furniture === true
                ),
                'numeric',
                'min:10',
                'max:100000',
            ],
            'note' => [
                'nullable',
                'string',
                'max:512',
            ],
        ];
    }
}
