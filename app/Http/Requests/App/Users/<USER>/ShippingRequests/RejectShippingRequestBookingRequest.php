<?php

namespace App\Http\Requests\App\Users\Bookings\ShippingRequests;

use App\Http\Requests\AbstractFormRequest;

class RejectShippingRequestBookingRequest extends AbstractFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'rejection_reason' => [
                'required',
                'string',
                'max:255'
            ],
            'note' => [
                'nullable',
                'string',
                'max:500'
            ]
        ];
    }
}
