<?php

namespace App\Http\Requests\App\Users\Accounts\Profile;

use App\Http\Requests\AbstractFormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\File;

class UpdateProfileRequest extends AbstractFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'name' => [
                'sometimes',
                'string',
                'max:255'
            ],
            'birth_date' => [
                'sometimes',
                'date',
                'before:today'
            ],
            'email' => [
                'sometimes',
                'string',
                'email',
                'max:255',
                Rule::unique('users', 'email')->ignore($this->user()->id)
            ],
            'mobile' => [
                'sometimes',
                'string',
                Rule::unique('users', 'mobile')->ignore($this->user()->id)
            ],
            'city_en' => [
                'sometimes',
                'string',
                'min:2',
                'max:255',
            ],
            'city_ar' => [
                'sometimes',
                'string',
                'min:2',
                'max:255',
            ],
            'location.lat' => [
                'sometimes',
                'numeric',
                'min:-90',
                'max:90',
            ],
            'location.lng' => [
                'sometimes',
                'numeric',
                'min:-180',
                'max:180',
            ],
            'gender' => [
                'sometimes',
                'string',
                Rule::in(['male', 'female'])
            ],
            'country_id' => [
                'sometimes',
                'exists:countries,id'
            ],
            'picture' => [
                'sometimes',
                File::image()->max(5 * 1024)
            ],
            'whatsapp_number' => [
                'sometimes',
                'string',
                Rule::unique('users', 'whatsapp_number')->ignore($this->user()->id)
            ],
        ];
    }
}
