<?php

namespace App\Http\Requests\App\Users\Bookmarks;

use App\Enums\Travel\TransportServiceTypeEnum;
use App\Http\Requests\AbstractFormRequest;
use Illuminate\Validation\Rule;

class StoreBookmarkRequest extends AbstractFormRequest
{

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'bookmarkable_id' => [
                'required',
                'integer',
            ],
            'bookmarkable_type' => [
                'required',
                'string',
                Rule::in(TransportServiceTypeEnum::values())
            ],
        ];
    }
}
