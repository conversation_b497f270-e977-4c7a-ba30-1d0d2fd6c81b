<?php

namespace App\Http\Requests\App\Users\Bookings\ShippingServices;

use App\Http\Requests\AbstractFormRequest;
use App\Models\Service\ShippingService;
use Illuminate\Validation\Rule;

class StoreShippingServiceBookingRequest extends AbstractFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $shippingService = ShippingService::find($this->route('id'));

        return [
            'note' => [
                'nullable',
                'string',
                'max:512',
            ],
            'packages_volume' => [
                'nullable',
                'required_without_all:document_volume,furniture_volume',
                Rule::prohibitedIf(!$shippingService->can_ship_packages),
                'integer',
                'min:1',
                'max:' . (int) $shippingService->available_packages_volume,
            ],
            'document_volume' => [
                'nullable',
                'required_without_all:packages_volume,furniture_volume',
                Rule::prohibitedIf(!$shippingService->can_ship_documents),
                'integer',
                'min:1',
                'max:' . (int) $shippingService->available_document_volume,
            ],
            'furniture_volume' => [
                'nullable',
                'required_without_all:packages_volume,document_volume',
                Rule::prohibitedIf(!$shippingService->can_ship_furniture),
                'integer',
                'min:1',
                'max:' . (int) $shippingService->available_furniture_volume,
            ],
            'is_fragile' => [
                'required',
                'boolean',
            ],
        ];
    }
}
