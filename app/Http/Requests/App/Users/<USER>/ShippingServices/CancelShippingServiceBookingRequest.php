<?php

namespace App\Http\Requests\App\Users\Bookings\ShippingServices;

use App\Http\Requests\AbstractFormRequest;

class CancelShippingServiceBookingRequest extends AbstractFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'cancellation_reason' => [
                'required',
                'string',
                'max:255'
            ],
            'note' => [
                'nullable',
                'string',
                'max:500'
            ],
        ];
    }
}
