<?php

namespace App\Http\Requests\App\Users\Complaints;

use App\Enums\Bookings\BookingTypeEnum;
use App\Http\Requests\AbstractFormRequest;
use Illuminate\Validation\Rule;

class StoreComplaintRequest extends AbstractFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'booking_id' => [
                'required',
            ],
            'booking_type' => [
                'required',
                Rule::in(BookingTypeEnum::values()),
            ],
            'description' => [
                'required',
                'string',
                'min:10',
                'max:1000',
            ],
        ];
    }
}
