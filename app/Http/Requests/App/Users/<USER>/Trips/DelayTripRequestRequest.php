<?php

namespace App\Http\Requests\App\Users\ServiceRequests\Trips;

use App\Http\Requests\AbstractFormRequest;

class DelayTripRequestRequest extends AbstractFormRequest
{
    public function rules(): array
    {
        return [
            'departure_datetime' => [
                'required',
                'date',
                'after:now',
                'before_or_equal:' . now()->addDays(5)->format('Y-m-d H:i:s'),
            ],
            'arrival_datetime' => [
                'required',
                'date',
                'after:departure_datetime',
            ],
            'delay_reason' => [
                'required',
                'string',
                'min:3',
                'max:255'
            ],
            'note' => [
                'nullable',
                'string',
                'max:500'
            ]
        ];
    }
}
