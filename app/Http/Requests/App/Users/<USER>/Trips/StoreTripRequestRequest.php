<?php

namespace App\Http\Requests\App\Users\ServiceRequests\Trips;

use App\Enums\Travel\TripTypeEnum;
use App\Http\Requests\AbstractFormRequest;
use App\Models\Common\CarType;
use Illuminate\Validation\Rule;

class StoreTripRequestRequest extends AbstractFormRequest
{
    public function rules(): array
    {
        $numberOfSeats = CarType::find($this->post('car_type_id'))?->number_of_seats ?? 10;

        return [
            'trip_type' => [
                'required',
                'string',
                Rule::in(TripTypeEnum::values()),
            ],
            'from_city_en' => [
                'required',
                'string',
                'min:2',
                'max:255',
            ],
            'from_city_ar' => [
                'required',
                'string',
                'min:2',
                'max:255',
            ],
            'from_location.lat' => [
                'required',
                'numeric',
                'min:-90',
                'max:90',
            ],
            'from_location.lng' => [
                'required',
                'numeric',
                'min:-180',
                'max:180',
            ],
            'to_city_en' => [
                'required',
                'string',
                'min:2',
                'max:255',
            ],
            'to_city_ar' => [
                'required',
                'string',
                'min:2',
                'max:255',
            ],
            'to_location.lat' => [
                'required',
                'numeric',
                'min:-90',
                'max:90',
            ],
            'to_location.lng' => [
                'required',
                'numeric',
                'min:-180',
                'max:180',
            ],
            'departure_datetime' => [
                'required',
                'date',
                'after_or_equal:now',
                'before_or_equal:' . now()->addDays(5)->format('Y-m-d H:i:s'),
            ],
            'arrival_datetime' => [
                'required',
                'date',
                'after:departure_datetime',
            ],
            'price' => [
                'required',
                'numeric',
                'min:1',
                'max:100000',
            ],
            'allow_smoking' => [
                'required',
                'boolean'
            ],
            'deliver_to_door' => [
                'required',
                'boolean'
            ],
            'car_type_id' => [
                'required',
                'string',
                Rule::exists('car_types', 'id'),
            ],
            'seats' => [
                'required',
                'array',
                'min:1',
                'max:' . $numberOfSeats,
            ],
            'seats.*' => [
                'required',
                'string',
                'regex:/^[A-C][1-3]$/',
            ],
            // 'additional_services' => [
            //     'sometimes',
            //     'array',
            // ],
            // 'additional_services.*' => [
            //     'sometimes',
            //     'integer',
            //     'exists:additional_services,id',
            // ],
            'note' => [
                'nullable',
                'string',
                'max:512',
            ],
            'arrive_destination' => [
                'nullable',
                'string',
                'min:5',
                'max:512',
            ],
        ];
    }
}
