<?php

namespace App\Http\Requests\App\Users\Services\Trips;

use App\Enums\Travel\SeatStatusEnum;
use App\Enums\Travel\TripTypeEnum;
use App\Http\Requests\AbstractFormRequest;
use App\Models\Common\Car;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class StoreTripServiceRequest extends AbstractFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $numberOfSeats = Car::find($this->car_id)?->type?->number_of_seats  ?? 10;

        return [
            'trip_type' => [
                'required',
                'string',
                Rule::in(TripTypeEnum::values()),
            ],
            'from_city_en' => [
                'required',
                'string',
                'min:2',
                'max:255',
            ],
            'from_city_ar' => [
                'required',
                'string',
                'min:2',
                'max:255',
            ],
            'from_location.lat' => [
                'required',
                'numeric',
                'min:-90',
                'max:90',
            ],
            'from_location.lng' => [
                'required',
                'numeric',
                'min:-180',
                'max:180',
            ],
            'to_city_en' => [
                'required',
                'string',
                'min:2',
                'max:255',
            ],
            'to_city_ar' => [
                'required',
                'string',
                'min:2',
                'max:255',
            ],
            'to_location.lat' => [
                'required',
                'numeric',
                'min:-90',
                'max:90',
            ],
            'to_location.lng' => [
                'required',
                'numeric',
                'min:-180',
                'max:180',
            ],
            'departure_datetime' => [
                'required',
                'date',
                'after_or_equal:now',
                'before_or_equal:' . now()->addDays(5)->format('Y-m-d H:i:s'),
            ],
            'arrival_datetime' => [
                'required',
                'date',
                'after:departure_datetime',
            ],
            'price' => [
                'required',
                'numeric',
                'min:1',
                'max:100000',
            ],
            'number_of_free_cartons' => [
                'required',
                'integer',
                'min:0',
                'max:10',
            ],
            'additional_services' => [
                'sometimes',
                'array',
            ],
            'additional_services.*' => [
                'required',
                'integer',
                'exists:additional_services,id',
            ],
            'deliver_to_door' => [
                'required',
                'boolean'
            ],
            'allow_smoking' => [
                'required',
                'boolean'
            ],
            'car_id' => [
                'required',
                'integer',
                Rule::exists(Car::class, 'id')->where('user_id', Auth::user()?->id),
            ],
            'seats' => [
                'required',
                'array',
                'min:1',
                function ($attribute, $value, $fail) use ($numberOfSeats) {
                    if (count($value) > $numberOfSeats) {
                        $fail(__('messages.validation.trip_service.seats_count_exceeds_maximum', ['max' => $numberOfSeats]));
                    }
                },
            ],
            'seats.*' => [
                'required',
                'string',
                'regex:/^[A-C][1-3]$/',
            ],
            'note' => [
                'sometimes',
                'string',
                'max:255',
            ],
        ];
    }
}
