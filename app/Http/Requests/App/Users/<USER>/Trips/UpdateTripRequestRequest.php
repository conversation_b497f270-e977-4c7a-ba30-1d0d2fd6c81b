<?php

namespace App\Http\Requests\App\Users\ServiceRequests\Trips;

use App\Enums\Travel\TripTypeEnum;
use App\Http\Requests\AbstractFormRequest;
use App\Models\Common\CarType;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class UpdateTripRequestRequest extends AbstractFormRequest
{
    public function rules(): array
    {
        return [
            'trip_type' => [
                'sometimes',
                'string',
                Rule::in(TripTypeEnum::values()),
            ],
            'from_city_en' => [
                'sometimes',
                'string',
                'min:2',
                'max:255',
            ],
            'from_city_ar' => [
                'sometimes',
                'string',
                'min:2',
                'max:255',
            ],
            'from_location.lat' => [
                'sometimes',
                'numeric',
                'min:-90',
                'max:90',
            ],
            'from_location.lng' => [
                'sometimes',
                'numeric',
                'min:-180',
                'max:180',
            ],
            'to_city_en' => [
                'sometimes',
                'string',
                'min:2',
                'max:255',
            ],
            'to_city_ar' => [
                'sometimes',
                'string',
                'min:2',
                'max:255',
            ],
            'to_location.lat' => [
                'sometimes',
                'numeric',
                'min:-90',
                'max:90',
            ],
            'to_location.lng' => [
                'sometimes',
                'numeric',
                'min:-180',
                'max:180',
            ],
            'departure_datetime' => [
                'sometimes',
                'date',
                'after:now',
                'before_or_equal:' . now()->addDays(5)->format('Y-m-d H:i:s'),
            ],
            'arrival_datetime' => [
                'sometimes',
                'date',
                'after:departure_datetime',
            ],
            'price' => [
                'sometimes',
                'numeric',
                'min:0',
            ],
            'additional_services' => [
                'sometimes',
                'array',
            ],
            'additional_services.*' => [
                'integer',
                'exists:additional_services,id',
            ],
            'deliver_to_door' => [
                'sometimes',
                'boolean'
            ],
            'allow_smoking' => [
                'sometimes',
                'boolean'
            ],
            'car_type_id' => [
                'sometimes',
                'integer',
                Rule::exists(CarType::class, 'id'),
            ],
            'seats' => [
                'sometimes',
                'array',
                'min:1',
            ],
            'seats.*' => [
                'required',
                'string',
            ],
            'note' => [
                'sometimes',
                'nullable',
                'string',
                'max:512',
            ],
        ];
    }
}
