<?php

namespace App\Http\Requests\App\Users\Services\Trips;

use App\Enums\Travel\SeatStatusEnum;
use App\Enums\Travel\TripTypeEnum;
use App\Http\Requests\AbstractFormRequest;
use App\Models\Common\Car;
use App\Models\Service\TripService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class UpdateTripServiceRequest extends AbstractFormRequest
{
    public function rules(): array
    {
        $numberOfSeats = Car::find($this->car_id)?->type?->number_of_seats ?? 10;

        return [
            'trip_type' => [
                'sometimes',
                'string',
                Rule::in(TripTypeEnum::values()),
            ],
            'from_city_en' => [
                'sometimes',
                'string',
                'min:2',
                'max:255',
            ],
            'from_city_ar' => [
                'sometimes',
                'string',
                'min:2',
                'max:255',
            ],
            'from_location.lat' => [
                'sometimes',
                'numeric',
                'min:-90',
                'max:90',
            ],
            'from_location.lng' => [
                'sometimes',
                'numeric',
                'min:-180',
                'max:180',
            ],
            'to_city_en' => [
                'sometimes',
                'string',
                'min:2',
                'max:255',
            ],
            'to_city_ar' => [
                'sometimes',
                'string',
                'min:2',
                'max:255',
            ],
            'to_location.lat' => [
                'sometimes',
                'numeric',
                'min:-90',
                'max:90',
            ],
            'to_location.lng' => [
                'sometimes',
                'numeric',
                'min:-180',
                'max:180',
            ],
            'departure_datetime' => [
                'sometimes',
                'date',
                'after_or_equal:now',
                'before_or_equal:' . now()->addDays(5)->format('Y-m-d H:i:s'),
            ],
            'arrival_datetime' => [
                'sometimes',
                'date',
                'after:departure_datetime',
            ],
            'price' => [
                'sometimes',
                'numeric',
                'min:1',
                'max:100000',
            ],
            'number_of_free_cartons' => [
                'sometimes',
                'integer',
                'min:0',
                'max:10',
            ],
            'additional_services' => [
                'sometimes',
                'array',
            ],
            'additional_services.*' => [
                'sometimes',
                'integer',
                'exists:additional_services,id',
            ],
            'deliver_to_door' => [
                'sometimes',
                'boolean'
            ],
            'allow_smoking' => [
                'sometimes',
                'boolean'
            ],
            'car_id' => [
                'sometimes',
                'integer',
                Rule::exists(Car::class, 'id')->where('user_id', Auth::user()?->id),
            ],
            'seats' => [
                'sometimes',
                'array',
                'min:1',
                function ($attribute, $value, $fail) use ($numberOfSeats) {
                    if (count($value) > $numberOfSeats) {
                        $fail(__('validation.max.numeric', ['max' => $numberOfSeats]));
                    }
                }
            ],
            'seats.*.number' => [
                'required',
                'string',
            ],
            'seats.*.status' => [
                'required',
                'string',
                Rule::in(SeatStatusEnum::values()),
            ],
            'note' => [
                'sometimes',
                'nullable',
                'string',
                'max:512',
            ],
        ];
    }
}
