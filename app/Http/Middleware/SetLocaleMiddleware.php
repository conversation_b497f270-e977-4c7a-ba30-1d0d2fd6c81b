<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Symfony\Component\HttpFoundation\Response;

class SetLocaleMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        if ($request->user() && $request->user()->preferredLocale()) {
            App::setLocale($request->user()->preferredLocale());
        } else {
            $locale = $request->header('locale', config('app.locale'));
            App::setLocale($locale);
        }

        return $next($request);
    }
}
