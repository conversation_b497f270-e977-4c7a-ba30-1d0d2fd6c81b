<?php

namespace App\Http\Middleware;

use App\Enums\TransportStatus;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckTransportOfficeStatus
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::guard('transport')->user();

        if ($user && $user->transportationOffice) {
            $office = $user->transportationOffice;

            // If office is not active, logout and redirect
            if (!$office->canLogin()) {
                Auth::guard('transport')->logout();

                $message = match ($office->status) {
                    TransportStatus::INACTIVE->value => __('messages.transport.auth.account_inactive'),
                    TransportStatus::SUSPENDED->value => __('messages.transport.auth.account_suspended'),
                    default => __('messages.transport.auth.account_not_accessible'),
                };

                return redirect()->route('filament.transport.auth.login')
                    ->with('error', $message);
            }
        }

        return $next($request);
    }
}
