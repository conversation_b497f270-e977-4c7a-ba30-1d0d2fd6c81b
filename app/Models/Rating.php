<?php

namespace App\Models;

use App\Models\Booking\TripServiceBooking;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

class Rating extends Model
{
    use Snowflakes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'trip_service_booking_id',
        'user_id',
        'driver_id',
        'rating',
        'comment',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'trip_service_booking_id' => SnowflakeCast::class,
            'user_id' => SnowflakeCast::class,
            'driver_id' => SnowflakeCast::class,
            'rating' => 'float',
        ];
    }

    /**
     * Get the user who provided the rating.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the driver who was rated.
     */
    public function driver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'driver_id');
    }

    /**
     * Get the trip service booking associated with the rating.
     */
    public function tripServiceBooking(): BelongsTo
    {
        return $this->belongsTo(TripServiceBooking::class);
    }
}
