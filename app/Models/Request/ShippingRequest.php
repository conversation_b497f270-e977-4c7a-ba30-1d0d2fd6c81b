<?php

namespace App\Models\Request;

use App\Enums\Travel\TripStatusEnum;
use App\Models\Booking\ShippingRequestBooking;
use App\Models\Common\City;
use App\Models\User;
use App\Models\Dashboard\Admin;
use App\Traits\HasReadTracking;
use Database\Factories\Request\ShippingRequestFactory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

class ShippingRequest extends Model
{
    use HasFactory, Snowflakes, HasReadTracking;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'transportation_type',
        'can_ship_packages',
        'can_ship_documents',
        'can_ship_furniture',
        'departure_datetime',
        'arrival_datetime',
        'packages_volume',
        'document_volume',
        'furniture_volume',
        'from_city_id',
        'to_city_id',
        'from_location_lat',
        'from_location_lng',
        'to_location_lat',
        'to_location_lng',
        'delivery_location',
        'user_id',
        'is_fragile',
        'note',
        'cancelled_at',
        'cancellation_reason',
        'cancellation_note',
        'attendance_confirmed_at',
        'delayed_at',
        'delay_reason',
        'delay_note',
        'read_at',
        'read_by_id',
        'weather_status',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'from_city_id' => SnowflakeCast::class,
            'to_city_id' => SnowflakeCast::class,
            'departure_datetime' => 'datetime',
            'arrival_datetime' => 'datetime',
            'user_id' => SnowflakeCast::class,
            'to_location_lat' => 'float',
            'to_location_lng' => 'float',
            'from_location_lat' => 'float',
            'from_location_lng' => 'float',
            'attendance_confirmed_at' => 'datetime',
            'delayed_at' => 'datetime',
            'cancelled_at' => 'datetime',
            'read_at' => 'datetime',
            'read_by_id' => SnowflakeCast::class,
        ];
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function fromCity()
    {
        return $this->belongsTo(City::class);
    }

    public function toCity()
    {
        return $this->belongsTo(City::class);
    }

    /**
     * Get the bookings for the shipping request.
     */
    public function bookings()
    {
        return $this->hasMany(ShippingRequestBooking::class);
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): ShippingRequestFactory
    {
        return ShippingRequestFactory::new();
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function scopeUpcoming(Builder $query)
    {
        return $query->where('departure_datetime', '>', Carbon::now());
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function scopeAvailable(Builder $query)
    {
        return $query->whereDoesntHave('bookings');
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function scopeActive(Builder $query)
    {
        return $query->where('arrival_datetime', '>=', Carbon::now())->whereNull('cancelled_at');
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function scopeCompleted(Builder $query)
    {
        return $query->where('arrival_datetime', '<', Carbon::now());
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function scopeCancelled(Builder $query)
    {
        return $query->whereNotNull('cancelled_at');
    }

    protected function transportationTypeFormatted(): Attribute
    {
        return Attribute::make(
            get: fn($value, array $attributes) => __('enums.transportation_type.' . $attributes['transportation_type']),
        );
    }

    /**
     * @return Attribute
     */
    protected function status(): Attribute
    {
        return Attribute::make(
            get: function (mixed $value, array $attributes) {
                return match (true) {
                    $attributes['cancelled_at'] !== null => TripStatusEnum::CANCELLED(),
                    $attributes['arrival_datetime'] < Carbon::now() => TripStatusEnum::COMPLETED(),
                    $attributes['arrival_datetime'] > Carbon::now() => TripStatusEnum::ACTIVE(),
                    default => TripStatusEnum::ACTIVE(),
                };
            }
        );
    }
}
