<?php

namespace App\Models\Request;

use Database\Factories\Request\TripRequestSeatFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

class TripRequestSeat extends Model
{
    use HasFactory, Snowflakes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'trip_request_id',
        'number',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class
        ];
    }


    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): TripRequestSeatFactory
    {
        return TripRequestSeatFactory::new();
    }
}
