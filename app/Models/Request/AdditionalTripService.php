<?php

namespace App\Models\Request;

use Illuminate\Database\Eloquent\Relations\Pivot;
use Snowflake\SnowflakeCast;

class AdditionalTripService extends Pivot
{
    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'trip_service_id' => SnowflakeCast::class,
            'additional_service_id' => SnowflakeCast::class,
        ];
    }
}
