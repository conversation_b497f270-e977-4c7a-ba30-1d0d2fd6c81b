<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;

use App\Concerns\InteractsWithFcmTokens;
use App\Models\Booking\FazaaRequestBooking;
use App\Models\Booking\FazaaServiceBooking;
use App\Models\Booking\ShippingRequestBooking;
use App\Models\Booking\ShippingServiceBooking;
use App\Models\Booking\TripRequestBooking;
use App\Models\Booking\TripServiceBooking;
use App\Models\Common\Car;
use App\Models\Common\City;
use App\Models\Common\Country;
use App\Models\Request\FazaaRequest;
use App\Models\Request\ShippingRequest;
use App\Models\Request\TripRequest;
use App\Models\Service\FazaaService;
use App\Models\Service\ShippingService;
use App\Models\Service\TripService;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, Snowflakes, InteractsWithFcmTokens;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'picture',
        'email',
        'password',
        'otp_code',
        'country_id',
        'firebase_uid',
        'mobile',
        'newsletter_subscription',
        'city_id',
        'location_lat',
        'location_lng',
        'gender',
        'birth_date',
        'locale',
        'whatsapp_number',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'location_lat' => 'float',
            'location_lng' => 'float',
        ];
    }

    protected function name(): Attribute
    {
        return new Attribute(
            get: fn(mixed $value, array $attributes) => $attributes['name'] ?? 'user' . substr($attributes['id'], -5),
        );
    }

    protected function picture(): Attribute
    {
        return new Attribute(
            get: function ($value) {
                if (!$value) {
                    return null;
                }

                // If picture already starts with http:// or https://, it's an external URL (e.g., from Google)
                if (str_starts_with($value, 'http://') || str_starts_with($value, 'https://')) {
                    return $value;
                }

                // Otherwise, prepend the storage URL
                return asset('storage/' . $value);
            }
        );
    }

    public function preferredLocale(): string
    {
        return $this->locale ?? 'ar';
    }

    public function verificationCode(): HasOne
    {
        return $this->hasOne(VerificationCode::class);
    }

    public function cars()
    {
        return $this->hasMany(Car::class);
    }

    public function tripServices()
    {
        return $this->hasMany(TripService::class);
    }

    public function fazaaServices()
    {
        return $this->hasMany(FazaaService::class);
    }

    public function shippingServices()
    {
        return $this->hasMany(ShippingService::class);
    }

    public function tripRequests()
    {
        return $this->hasMany(TripRequest::class);
    }

    public function shippingRequests()
    {
        return $this->hasMany(ShippingRequest::class);
    }

    public function fazaaRequests()
    {
        return $this->hasMany(FazaaRequest::class);
    }

    public function fazaaServiceBookings()
    {
        return $this->hasMany(FazaaServiceBooking::class);
    }

    public function tripServiceBookings()
    {
        return $this->hasMany(TripServiceBooking::class);
    }

    public function shippingServiceBookings()
    {
        return $this->hasMany(ShippingServiceBooking::class);
    }

    public function country()
    {
        return $this->belongsTo(Country::class);
    }

    public function city()
    {
        return $this->belongsTo(City::class);
    }

    public function bookmarks()
    {
        return $this->hasMany(Bookmark::class);
    }

    public function devices()
    {
        return $this->hasMany(Device::class, 'notifiable_id');
    }

    public function tripRequestBookings()
    {
        return $this->hasMany(TripRequestBooking::class);
    }

    /**
     * Get the transportation office drivers for this user.
     */
    public function transportationOfficeDrivers()
    {
        return $this->hasMany(TransportationOfficeDriver::class);
    }

    public function shippingRequestBookings()
    {
        return $this->hasMany(ShippingRequestBooking::class);
    }

    public function fazaaRequestBookings()
    {
        return $this->hasMany(FazaaRequestBooking::class);
    }

    /**
     * Get the complaints for the user.
     */
    public function complaints(): HasMany
    {
        return $this->hasMany(Complaint::class);
    }

    /**
     * Get the ratings given by the user.
     */
    public function givenRatings(): HasMany
    {
        return $this->hasMany(Rating::class, 'user_id');
    }

    /**
     * Get the ratings received as a driver.
     */
    public function receivedRatings(): HasMany
    {
        return $this->hasMany(Rating::class, 'driver_id');
    }

    public function isProfileCompleted(): Attribute
    {
        return new Attribute(
            get: fn($value, array $attributes) => $attributes['name'] && $attributes['email'] && $attributes['mobile'] && $attributes['whatsapp_number'],
        );
    }
}
