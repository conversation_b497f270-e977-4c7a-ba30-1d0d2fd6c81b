<?php

namespace App\Models\Service;

use App\Enums\Bookings\BookingStatusEnum;
use App\Enums\Travel\TripStatusEnum;
use App\Models\Booking\ShippingServiceBooking;
use App\Models\Common\Car;
use App\Models\Common\City;
use App\Models\User;
use App\Models\Dashboard\Admin;
use App\Models\Common\ServiceType;
use Database\Factories\Service\ShippingServiceFactory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;
use App\Traits\HasReadTracking;

class ShippingService extends Model
{
    use HasFactory, Snowflakes, HasReadTracking;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'transportation_type',
        'can_ship_packages',
        'can_ship_documents',
        'can_ship_furniture',
        'departure_datetime',
        'arrival_datetime',
        'packages_volume',
        'available_packages_volume',

        'document_volume',
        'available_document_volume',

        'furniture_volume',
        'available_furniture_volume',

        'from_city_id',
        'to_city_id',
        'from_location_lat',
        'from_location_lng',
        'to_location_lat',
        'to_location_lng',
        'delivery_location',
        'package_price',
        'document_price',
        'furniture_price',
        'user_id',
        'car_id',
        'note',
        'cancelled_at',
        'cancellation_reason',
        'cancellation_note',
        'attendance_confirmed_at',
        'delayed_at',
        'delay_reason',
        'delay_note',
        'read_at',
        'read_by_id',
        'weather_status',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'from_city_id' => SnowflakeCast::class,
            'to_city_id' => SnowflakeCast::class,
            'departure_datetime' => 'datetime',
            'arrival_datetime' => 'datetime',
            'document_price',
            'furniture_price',
            'package_price',
            'user_id' => SnowflakeCast::class,
            'to_location_lat' => 'float',
            'to_location_lng' => 'float',
            'from_location_lat' => 'float',
            'from_location_lng' => 'float',
            'package_price' => 'float',
            'document_price' => 'float',
            'furniture_price' => 'float',
            'attendance_confirmed_at' => 'datetime',
            'delayed_at' => 'datetime',
            'read_at' => 'datetime',
            'read_by_id' => SnowflakeCast::class,
        ];
    }

    protected function transportationTypeFormatted(): Attribute
    {
        return Attribute::make(
            get: fn($value, array $attributes) => __('enums.transportation_type.' . $attributes['transportation_type']),
        );
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function fromCity()
    {
        return $this->belongsTo(City::class);
    }

    public function toCity()
    {
        return $this->belongsTo(City::class);
    }

    public function car()
    {
        return $this->belongsTo(Car::class);
    }

    public function bookings()
    {
        return $this->hasMany(ShippingServiceBooking::class);
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): ShippingServiceFactory
    {
        return ShippingServiceFactory::new();
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function scopeUpcoming(Builder $query)
    {
        return $query->where('departure_datetime', '>', Carbon::now());
    }

    public function isFullyBooked()
    {
        return $this->available_packages_volume <= 0 &&
            $this->available_document_volume <= 0 &&
            $this->available_furniture_volume <= 0;
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function scopeAvailableVolumes(Builder $query)
    {
        return $query->where('available_packages_volume', '>', 0)
            ->orWhere('available_document_volume', '>', 0)
            ->orWhere('available_furniture_volume', '>', 0);
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function scopeActive(Builder $query)
    {
        return $query->where('arrival_datetime', '>=', Carbon::now())->whereNull('cancelled_at');
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function scopeCompleted(Builder $query)
    {
        return $query->where('arrival_datetime', '<', Carbon::now());
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function scopeCancelled(Builder $query)
    {
        return $query->whereNotNull('cancelled_at');
    }

    /**
     * @return Attribute
     */
    protected function status(): Attribute
    {
        return Attribute::make(
            get: function (mixed $value, array $attributes) {
                return match (true) {
                    $attributes['cancelled_at'] !== null => TripStatusEnum::CANCELLED(),
                    $attributes['arrival_datetime'] < Carbon::now() => TripStatusEnum::COMPLETED(),
                    $attributes['arrival_datetime'] >= Carbon::now() => TripStatusEnum::ACTIVE(),
                    default => TripStatusEnum::ACTIVE(),
                };
            }
        );
    }
}
