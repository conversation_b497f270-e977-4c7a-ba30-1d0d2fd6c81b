<?php

namespace App\Models\Service;

use App\Enums\Bookings\BookingStatusEnum;
use App\Enums\Travel\SeatStatusEnum;
use App\Enums\Travel\TripStatusEnum;
use App\Models\Booking\TripServiceBooking;
use App\Models\Common\Car;
use App\Models\Common\City;
use App\Models\Common\TripSeat;
use App\Models\User;
use App\Models\Dashboard\Admin;
use App\Traits\HasReadTracking;
use Database\Factories\Service\TripServiceFactory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Carbon;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

class TripService extends Model
{
    use HasFactory, Snowflakes, HasReadTracking;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'trip_type',
        'transportation_type',
        'departure_datetime',
        'arrival_datetime',
        'number_of_seats',
        'number_of_available_seats',
        'from_city_id',
        'to_city_id',
        'from_location_lat',
        'from_location_lng',
        'to_location_lat',
        'to_location_lng',
        'price',
        'number_of_free_cartons',
        'user_id',
        'transportation_office_id',
        'allow_smoking',
        'deliver_to_door',
        'car_id',
        'note',
        'cancelled_at',
        'cancellation_reason',
        'cancellation_note',
        'refunds_processed',
        'attendance_confirmed_at',
        'arrival_confirmed_at',
        'delayed_at',
        'delay_reason',
        'delay_note',
        'read_at',
        'read_by_id',
        'rating',
        'weather_status',
        'from_location',
        'to_location',
    ];

    /**
     * The attributes that should be appended to the model's array form.
     *
     * @var array<int, string>
     */
    protected $appends = [
        'from_location',
        'to_location',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'departure_datetime' => 'datetime',
            'arrival_datetime' => 'datetime',
            'price' => 'float',
            'allow_smoking' => 'boolean',
            'deliver_to_door' => 'boolean',
            'user_id' => SnowflakeCast::class,
            'to_location_lat' => 'float',
            'to_location_lng' => 'float',
            'from_location_lat' => 'float',
            'from_location_lng' => 'float',
            'refunds_processed' => 'boolean',
            'attendance_confirmed_at' => 'datetime',
            'arrival_confirmed_at' => 'datetime',
            'delayed_at' => 'datetime',
            'cancelled_at' => 'datetime',
            'read_at' => 'datetime',
            'read_by_id' => SnowflakeCast::class,
            'rating' => 'float',
        ];
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function transportationOffice()
    {
        return $this->belongsTo(\App\Models\TransportationOffice::class);
    }

    public function fromCity()
    {
        return $this->belongsTo(City::class);
    }

    public function toCity()
    {
        return $this->belongsTo(City::class);
    }

    /**
     * @return BelongsToMany
     */
    public function additionalServices(): BelongsToMany
    {
        return $this->belongsToMany(AdditionalService::class)->using(AdditionalTripService::class);
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): TripServiceFactory
    {
        return TripServiceFactory::new();
    }

    public function scopeUpcoming(Builder $query)
    {
        return $query->where('departure_datetime', '>', Carbon::now());
    }

    public function scopeNotCancelled(Builder $query)
    {
        return $query->whereNull('cancelled_at');
    }

    public function scopeAvailable(Builder $query)
    {
        return $query->upcoming()->notCancelled()->hasAvailableSeats();
    }

    public function car()
    {
        return $this->belongsTo(Car::class);
    }

    public function bookings()
    {
        return $this->hasMany(TripServiceBooking::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasManyThrough
     */
    public function travelers()
    {
        return $this->hasManyThrough(User::class, TripServiceBooking::class, 'trip_service_id', 'id', 'id', 'user_id');
    }

    /**
     * Get only travelers with accepted bookings
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasManyThrough
     */
    public function acceptedTravelers()
    {
        return $this->hasManyThrough(
            User::class,
            TripServiceBooking::class,
            'trip_service_id',
            'id',
            'id',
            'user_id'
        )->where('trip_service_bookings.status', BookingStatusEnum::ACCEPTED());
    }

    /**
     * @return HasMany
     */
    public function seats()
    {
        return $this->hasMany(TripSeat::class, 'trip_id');
    }

    /**
     * @return HasMany
     */
    public function availableSeats()
    {
        return $this->hasMany(TripSeat::class, 'trip_id')->where('status', SeatStatusEnum::AVAILABLE());
    }

    /**
     * @return HasMany
     */
    public function unavailableSeats()
    {
        return $this->hasMany(TripSeat::class, 'trip_id')->whereNot('status', SeatStatusEnum::AVAILABLE());
    }

    protected function tripTypeFormatted(): Attribute
    {
        return Attribute::make(
            get: fn($value, array $attributes) => __('enums.trip_type.' . $attributes['trip_type']),
        );
    }

    /**
     * @return Attribute
     */
    protected function status(): Attribute
    {
        return Attribute::make(
            get: function (mixed $value, array $attributes) {
                return match (true) {
                    $attributes['cancelled_at'] !== null => TripStatusEnum::CANCELLED(),
                    $attributes['arrival_datetime'] < Carbon::now() => TripStatusEnum::COMPLETED(),
                    $attributes['arrival_datetime'] >= Carbon::now() => TripStatusEnum::ACTIVE(),
                    default => TripStatusEnum::ACTIVE(),
                };
            }
        );
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function scopeHasAvailableSeats(Builder $query): Builder
    {
        return $query->whereHas('seats', fn($query) => $query->where('status', SeatStatusEnum::AVAILABLE()));
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function scopeActive(Builder $query)
    {
        return $query->where('arrival_datetime', '>=', Carbon::now())->whereNull('cancelled_at');
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function scopeCompleted(Builder $query)
    {
        return $query->where('arrival_datetime', '<', Carbon::now());
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function scopeCancelled(Builder $query)
    {
        return $query->whereNotNull('cancelled_at');
    }

    /**
     * @return bool
     */
    public function isFullyBooked(): bool
    {
        return $this->number_of_available_seats < 1;
    }

    /**
     * Returns the 'from_location_lat' and 'from_location_lng' attributes as the computed 'from_location' attribute,
     * as a standard Google Maps style Point array with 'lat' and 'lng' attributes.
     *
     * Used by the Filament Google Maps package.
     *
     * @return array
     */
    public function getFromLocationAttribute(): array
    {
        return [
            "lat" => (float)$this->from_location_lat,
            "lng" => (float)$this->from_location_lng,
        ];
    }

    /**
     * Takes a Google style Point array of 'lat' and 'lng' values and assigns them to the
     * 'from_location_lat' and 'from_location_lng' attributes on this model.
     *
     * Used by the Filament Google Maps package.
     *
     * @param ?array $location
     * @return void
     */
    public function setFromLocationAttribute(?array $location): void
    {
        if (is_array($location)) {
            $this->attributes['from_location_lat'] = $location['lat'];
            $this->attributes['from_location_lng'] = $location['lng'];
            unset($this->attributes['from_location']);
        }
    }

    /**
     * Returns the 'to_location_lat' and 'to_location_lng' attributes as the computed 'to_location' attribute,
     * as a standard Google Maps style Point array with 'lat' and 'lng' attributes.
     *
     * Used by the Filament Google Maps package.
     *
     * @return array
     */
    public function getToLocationAttribute(): array
    {
        return [
            "lat" => (float)$this->to_location_lat,
            "lng" => (float)$this->to_location_lng,
        ];
    }

    /**
     * Takes a Google style Point array of 'lat' and 'lng' values and assigns them to the
     * 'to_location_lat' and 'to_location_lng' attributes on this model.
     *
     * Used by the Filament Google Maps package.
     *
     * @param ?array $location
     * @return void
     */
    public function setToLocationAttribute(?array $location): void
    {
        if (is_array($location)) {
            $this->attributes['to_location_lat'] = $location['lat'];
            $this->attributes['to_location_lng'] = $location['lng'];
            unset($this->attributes['to_location']);
        }
    }
}
