<?php

namespace App\Models\Service;

use Database\Factories\Service\AdditionalServiceFactory;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Facades\App;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

class AdditionalService extends Model
{
    use HasFactory, Snowflakes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name_ar',
        'name_en',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class
        ];
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): AdditionalServiceFactory
    {
        return AdditionalServiceFactory::new();
    }

    /**
     * @return BelongsToMany
     */
    public function AdditionalServices(): BelongsToMany
    {
        return $this->belongsToMany(TripService::class)->using(AdditionalTripService::class);
    }

    public function name(): Attribute
    {
        return new Attribute(
            get: fn () => $this->{'name_' . App::getLocale()}
        );
    }
}
