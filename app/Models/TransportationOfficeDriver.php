<?php

namespace App\Models;

use App\Enums\DriverType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

class TransportationOfficeDriver extends Model
{
    use HasFactory, SoftDeletes, Snowflakes;

    protected $fillable = [
        'transportation_office_id',
        'user_id',
        'type',
        'status',
        // Temporary fields for employee creation
        'name',
        'email',
        'mobile',
        'country_id',
        'city_id',
    ];

    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'transportation_office_id' => SnowflakeCast::class,
            'user_id' => SnowflakeCast::class,
        ];
    }


    /**
     * Get the transportation office that owns the driver.
     */
    public function transportationOffice()
    {
        return $this->belongsTo(TransportationOffice::class);
    }

    /**
     * Get the user (for collaborator type).
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the country (for employee creation form).
     */
    public function country()
    {
        return $this->belongsTo(\App\Models\Common\Country::class);
    }

    /**
     * Get the city (for employee creation form).
     */
    public function city()
    {
        return $this->belongsTo(\App\Models\Common\City::class);
    }

    /**
     * Check if driver is a collaborator.
     */
    public function isCollaborator(): bool
    {
        return $this->type === DriverType::COLLABORATOR->value;
    }

    /**
     * Check if driver is an employee.
     */
    public function isEmployee(): bool
    {
        return $this->type === DriverType::EMPLOYEE->value;
    }

    /**
     * Get driver display name.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->user?->name ?? 'Unknown Driver';
    }

    /**
     * Get driver email.
     */
    public function getDisplayEmailAttribute(): string
    {
        return $this->user?->email ?? '';
    }

    /**
     * Get driver phone.
     */
    public function getDisplayPhoneAttribute(): string
    {
        return $this->user?->mobile ?? '';
    }
}
