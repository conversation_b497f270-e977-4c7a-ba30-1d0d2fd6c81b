<?php

namespace App\Models\Common;

use Database\Factories\Common\CityFactory;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\App;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

class City extends Model
{
    use HasFactory, Snowflakes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name_ar',
        'name_en',
        'current_weather',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
        ];
    }

    protected function name(): Attribute
    {
        return new Attribute(
            get: fn () => $this->{'name_' . App::getLocale()}
        );
    }

    public function country()
    {
        return $this->belongsTo(Country::class);
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): CityFactory
    {
        return CityFactory::new();
    }
}
