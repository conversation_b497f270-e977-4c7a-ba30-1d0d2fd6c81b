<?php

namespace App\Models\Common;

use Database\Factories\Common\FazaaSpecificTypeFactory;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\App;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

class FazaaSpecificType extends Model
{
    use HasFactory, Snowflakes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name_en',
        'name_ar',
        'fazaa_service_type_id',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'fazaa_service_type_id' => SnowflakeCast::class,
        ];
    }

    protected function name(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->{'name_' . App::getLocale()}
        );
    }

    public function fazaaServiceType(): BelongsTo
    {
        return $this->belongsTo(FazaaServiceType::class);
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): FazaaSpecificTypeFactory
    {
        return FazaaSpecificTypeFactory::new();
    }
}
