<?php

namespace App\Models\Common;

use Database\Factories\Common\CountryFactory;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

class Country extends Model
{
    use HasFactory, Snowflakes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name_ar',
        'name_en',
        'code',
        'mobile_code',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
        ];
    }

    public function cities()
    {
        return $this->hasMany(City::class);
    }

    protected function name(): Attribute
    {
        return Attribute::make(
            get: fn($value, $attributes) => $attributes['name_' . app()->getLocale()] ?? $attributes['name_ar'],
        );
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): CountryFactory
    {
        return CountryFactory::new();
    }
}
