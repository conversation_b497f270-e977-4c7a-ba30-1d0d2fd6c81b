<?php

namespace App\Models\Common;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\App;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

class FazaaServiceType extends Model
{
    use Snowflakes;

    protected $fillable = [
        'name_en',
        'name_ar',
        'key',
    ];

    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class
        ];
    }

    public function specificTypes()
    {
        return $this->hasMany(FazaaSpecificType::class);
    }


    protected function name(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->{'name_' . App::getLocale()}
        );
    }
}
