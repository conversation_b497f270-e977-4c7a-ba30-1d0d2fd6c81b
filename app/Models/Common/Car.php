<?php

namespace App\Models\Common;

use App\Models\User;
use Database\Factories\Common\CarFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

class Car extends Model
{
    use HasFactory, Snowflakes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'type_id',
        'user_id',
        'model',
        'plate_number',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'type_id' => SnowflakeCast::class,
            'user_id' => SnowflakeCast::class,
        ];
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): CarFactory
    {
        return CarFactory::new();
    }

    /**
     *
     * @return BelongsTo
     */
    public function type(): BelongsTo
    {
        return $this->belongsTo(CarType::class);
    }

    /**
     *
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
