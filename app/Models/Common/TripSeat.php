<?php

namespace App\Models\Common;

use Database\Factories\Common\TripSeatFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

class TripSeat extends Model
{
    use HasFactory, Snowflakes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'trip_id',
        'number',
        'status',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class
        ];
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): TripSeatFactory
    {
        return TripSeatFactory::new();
    }
}
