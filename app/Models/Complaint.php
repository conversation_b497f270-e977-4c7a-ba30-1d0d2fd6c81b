<?php

namespace App\Models;

use App\Enums\Complaints\ComplaintStatusEnum;
use App\Models\Booking\TripServiceBooking;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

class Complaint extends Model
{
    use Snowflakes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'booking_id',
        'booking_type',
        'description',
        'status',
        'admin_response',
        'admin_id',
        'resolved_at',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'user_id' => SnowflakeCast::class,
            'booking_id' => SnowflakeCast::class,
            'admin_id' => SnowflakeCast::class,
            'resolved_at' => 'datetime',
            'status' => ComplaintStatusEnum::class,
        ];
    }

    /**
     * Get the user who filed the complaint.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the admin who processed the complaint.
     */
    public function admin(): BelongsTo
    {
        return $this->belongsTo(User::class, 'admin_id');
    }

    /**
     * Get the booking associated with the complaint.
     */
    public function booking(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scope a query to only include pending complaints.
     */
    public function scopePending($query)
    {
        return $query->where('status', ComplaintStatusEnum::PENDING());
    }

    /**
     * Scope a query to only include resolved complaints.
     */
    public function scopeResolved($query)
    {
        return $query->where('status', ComplaintStatusEnum::RESOLVED());
    }

    /**
     * Scope a query to only include rejected complaints.
     */
    public function scopeRejected($query)
    {
        return $query->where('status', ComplaintStatusEnum::REJECTED());
    }
}
