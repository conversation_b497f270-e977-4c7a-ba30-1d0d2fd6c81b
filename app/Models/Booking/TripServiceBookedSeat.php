<?php

namespace App\Models\Booking;

use App\Models\Common\TripSeat;
use Database\Factories\Booking\TripServiceBookedSeatFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

class TripServiceBookedSeat extends Model
{
    use HasFactory, Snowflakes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'trip_service_booking_id',
        'seat_id',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class
        ];
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): TripServiceBookedSeatFactory
    {
        return TripServiceBookedSeatFactory::new();
    }

    public function tripServiceBooking(): BelongsTo
    {
        return $this->belongsTo(TripServiceBooking::class);
    }

    public function seat(): BelongsTo
    {
        return $this->belongsTo(TripSeat::class, 'seat_id');
    }
}
