<?php

namespace App\Models\Booking;

use App\Enums\Bookings\BookingStatusEnum;
use App\Models\Request\FazaaRequest;
use App\Models\User;
use App\Models\Complaint;
use Database\Factories\Booking\FazaaRequestBookingFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use App\Models\Dashboard\Admin;
use App\Traits\HasReadTracking;

class FazaaRequestBooking extends Model
{
    use HasFactory, Snowflakes, HasReadTracking;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'fazaa_request_id',
        'user_id',
        'price',
        'note',
        'status',
        'rejection_reason',
        'rejection_note',
        'accepted_at',
        'rejected_at',
        'cancellation_reason',
        'cancellation_note',
        'cancelled_at',
        'attendance_confirmed_at',
        'read_at',
        'read_by_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'fazaa_request_id' => SnowflakeCast::class,
            'user_id' => SnowflakeCast::class,
            'price' => 'float',
            'accepted_at' => 'datetime',
            'cancelled_at' => 'datetime',
            'rejected_at' => 'datetime',
            'attendance_confirmed_at' => 'datetime',
            'read_at' => 'datetime',
            'read_by_id' => SnowflakeCast::class,
        ];
    }

    /**
     * Get the FazaaRequest that owns the booking.
     */
    public function fazaaRequest()
    {
        return $this->belongsTo(FazaaRequest::class);
    }

    /**
     * Get the User who made the booking.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory()
    {
        return FazaaRequestBookingFactory::new();
    }

    /**
     * Get the complaints for the booking.
     */
    public function complaints(): MorphMany
    {
        return $this->morphMany(Complaint::class, 'booking');
    }

    /**
     * Scope a query to only include pending bookings.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePending($query)
    {
        return $query->where('status', BookingStatusEnum::PENDING());
    }

    /**
     * Scope a query to only include accepted bookings.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeUpcoming($query)
    {
        return $query->whereNot('status', BookingStatusEnum::REJECTED())
            ->whereHas('fazaaRequest', function ($q) {
                $q->where('departure_datetime', '>', now());
            });
    }

    /**
     * Scope a query to only include active bookings.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->whereNot('status', BookingStatusEnum::REJECTED())
            ->whereHas('fazaaRequest', function ($q) {
                $q->where('arrival_datetime', '>=', now());
            });
    }
}
