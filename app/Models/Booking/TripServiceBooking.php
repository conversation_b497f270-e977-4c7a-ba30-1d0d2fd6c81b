<?php

namespace App\Models\Booking;

use App\Enums\Bookings\BookingStatusEnum;
use App\Models\Complaint;
use App\Models\Service\TripService;
use App\Models\User;
use Database\Factories\Booking\TripServiceBookingFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;
use App\Traits\HasReadTracking;
use Illuminate\Database\Eloquent\Casts\Attribute;

class TripServiceBooking extends Model
{
    use HasFactory, Snowflakes, HasReadTracking;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'trip_service_id',
        'user_id',
        'note',
        'number_of_seats',
        'has_fragile_items',
        'status',
        'rejection_reason',
        'rejection_note',
        'accepted_at',
        'rejected_at',
        'cancellation_reason',
        'cancellation_note',
        'cancelled_at',
        'attendance_confirmed_at',
        'late_arrival_notified_at',
        'arrival_confirmed_at',
        'read_at',
        'read_by_id',
        'paid_at',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'accepted_at' => 'datetime',
            'cancelled_at' => 'datetime',
            'rejected_at' => 'datetime',
            'attendance_confirmed_at' => 'datetime',
            'late_arrival_notified_at' => 'datetime',
            'arrival_confirmed_at' => 'datetime',
            'read_at' => 'datetime',
            'read_by_id' => SnowflakeCast::class,
            'paid_at' => 'datetime',
        ];
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): TripServiceBookingFactory
    {
        return TripServiceBookingFactory::new();
    }

    public function tripService()
    {
        return $this->belongsTo(TripService::class);
    }

    public function seats()
    {
        return $this->hasMany(TripServiceBookedSeat::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the complaints for the booking.
     */
    public function complaints(): MorphMany
    {
        return $this->morphMany(Complaint::class, 'booking');
    }

    /**
     * Get the rating for the trip service booking.
     */
    public function rating()
    {
        return $this->hasOne(\App\Models\Rating::class);
    }

    /**
     * Scope a query to only include accepted bookings.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeAccepted($query)
    {
        return $query->where('status', BookingStatusEnum::ACCEPTED());
    }

    /**
     * Scope a query to only include pending bookings.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePending($query)
    {
        return $query->where('status', BookingStatusEnum::PENDING());
    }

    /**
     * Scope a query to only include rejected bookings.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRejected($query)
    {
        return $query->where('status', BookingStatusEnum::REJECTED());
    }

    public function price(): Attribute
    {
        return new Attribute(
            get: fn(mixed $value, array $attributes) => $this->tripService->price * $attributes['number_of_seats'],
        );
    }

    /**
     * Scope a query to only include accepted bookings.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeUpcoming($query)
    {
        return $query->whereNot('status', BookingStatusEnum::REJECTED())
            ->whereHas('tripService', function ($q) {
                $q->where('departure_datetime', '>', now());
            });
    }

    /**
     * Scope a query to only include active bookings.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->whereNot('status', BookingStatusEnum::REJECTED())
            ->whereHas('tripService', function ($q) {
                $q->where('arrival_datetime', '>=', now());
            });
    }
}
