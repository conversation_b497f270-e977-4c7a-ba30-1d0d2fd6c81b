<?php

namespace App\Models\Booking;

use App\Enums\Bookings\BookingStatusEnum;
use App\Models\Service\ShippingService;
use App\Models\User;
use App\Models\Dashboard\Admin;
use Database\Factories\Booking\ShippingServiceBookingFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;
use App\Models\Complaint;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use App\Traits\HasReadTracking;

class ShippingServiceBooking extends Model
{
    use HasFactory, Snowflakes, HasReadTracking;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'shipping_service_id',
        'user_id',
        'packages_volume',
        'document_volume',
        'furniture_volume',
        'is_fragile',
        'note',
        'status',
        'accepted_at',
        'rejection_reason',
        'rejection_note',
        'rejected_at',
        'cancellation_reason',
        'cancellation_note',
        'cancelled_at',
        'attendance_confirmed_at',
        'late_arrival_notified_at',
        'read_at',
        'read_by_id',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'packages_volume' => 'float',
            'document_volume' => 'float',
            'furniture_volume' => 'float',
            'accepted_at' => 'datetime',
            'cancelled_at' => 'datetime',
            'rejected_at' => 'datetime',
            'attendance_confirmed_at' => 'datetime',
            'late_arrival_notified_at' => 'datetime',
            'read_at' => 'datetime',
            'read_by_id' => SnowflakeCast::class,
        ];
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): ShippingServiceBookingFactory
    {
        return ShippingServiceBookingFactory::new();
    }

    public function shippingService()
    {
        return $this->belongsTo(ShippingService::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the complaints for the booking.
     */
    public function complaints(): MorphMany
    {
        return $this->morphMany(Complaint::class, 'booking');
    }

    /**
     * Scope a query to only include pending bookings.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePending($query)
    {
        return $query->where('status', BookingStatusEnum::PENDING());
    }

    /**
     * Scope a query to only include accepted bookings.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeUpcoming($query)
    {
        return $query->whereNot('status', BookingStatusEnum::REJECTED())
            ->whereHas('shippingService', function ($q) {
                $q->where('departure_datetime', '>', now());
            });
    }

    /**
     * Scope a query to only include active bookings.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->whereNot('status', BookingStatusEnum::REJECTED())
            ->whereHas('shippingService', function ($q) {
                $q->where('arrival_datetime', '>=', now());
            });
    }
}
