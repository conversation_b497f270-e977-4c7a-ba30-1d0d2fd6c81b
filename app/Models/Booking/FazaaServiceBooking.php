<?php

namespace App\Models\Booking;

use App\Enums\Bookings\BookingStatusEnum;
use App\Models\Service\FazaaService;
use App\Models\User;
use Database\Factories\Booking\FazaaServiceBookingFactory;
use Illuminate\Database\Eloquent\Model;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;
use App\Models\Complaint;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use App\Models\Dashboard\Admin;
use App\Traits\HasReadTracking;

class FazaaServiceBooking extends Model
{
    use Snowflakes, HasReadTracking;

    protected $fillable = [
        'fazaa_service_id',
        'user_id',
        'note',
        'status',
        'accepted_at',
        'rejection_reason',
        'rejection_note',
        'rejected_at',
        'cancellation_reason',
        'cancellation_note',
        'cancelled_at',
        'attendance_confirmed_at',
        'late_arrival_notified_at',
        'read_at',
        'read_by_id',
    ];

    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'fazaa_service_id' => SnowflakeCast::class,
            'user_id' => SnowflakeCast::class,
            'accepted_at' => 'datetime',
            'cancelled_at' => 'datetime',
            'rejected_at' => 'datetime',
            'attendance_confirmed_at' => 'datetime',
            'late_arrival_notified_at' => 'datetime',
            'read_at' => 'datetime',
            'read_by_id' => SnowflakeCast::class,
        ];
    }

    public function fazaaService()
    {
        return $this->belongsTo(FazaaService::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): FazaaServiceBookingFactory
    {
        return FazaaServiceBookingFactory::new();
    }

    /**
     * Get the complaints for the booking.
     */
    public function complaints(): MorphMany
    {
        return $this->morphMany(Complaint::class, 'booking');
    }
    /**
     * Scope a query to only include pending bookings.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePending($query)
    {
        return $query->where('status', BookingStatusEnum::PENDING());
    }

    /**
     * Scope a query to only include accepted bookings.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeAccepted($query)
    {
        return $query->where('status', BookingStatusEnum::ACCEPTED());
    }

    /**
     * Scope a query to only include accepted bookings.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeUpcoming($query)
    {
        return $query->whereNot('status', BookingStatusEnum::REJECTED())
            ->whereHas('fazaaService', function ($q) {
                $q->where('departure_datetime', '>', now());
            });
    }

    /**
     * Scope a query to only include active bookings.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->whereNot('status', BookingStatusEnum::REJECTED())
            ->whereHas('fazaaService', function ($q) {
                $q->where('arrival_datetime', '>=', now());
            });
    }
}
