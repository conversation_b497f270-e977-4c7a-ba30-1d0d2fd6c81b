<?php

namespace App\Models\Booking;

use App\Enums\Bookings\BookingStatusEnum;
use App\Models\Request\ShippingRequest;
use App\Models\User;
use App\Models\Complaint;
use App\Models\Dashboard\Admin;
use Database\Factories\Booking\ShippingRequestBookingFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;
use App\Traits\HasReadTracking;

class ShippingRequestBooking extends Model
{
    use HasFactory, Snowflakes, HasReadTracking;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'shipping_request_id',
        'user_id',
        'status',
        'package_price',
        'document_price',
        'furniture_price',
        'note',
        'accepted_at',
        'rejection_reason',
        'rejection_note',
        'rejected_at',
        'cancellation_reason',
        'cancellation_note',
        'cancelled_at',
        'attendance_confirmed_at',
        'read_at',
        'read_by_id',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'shipping_request_id' => SnowflakeCast::class,
            'user_id' => SnowflakeCast::class,
            'package_price' => 'float',
            'document_price' => 'float',
            'furniture_price' => 'float',
            'accepted_at' => 'datetime',
            'cancelled_at' => 'datetime',
            'rejected_at' => 'datetime',
            'attendance_confirmed_at' => 'datetime',
            'read_at' => 'datetime',
            'read_by_id' => SnowflakeCast::class,
        ];
    }

    /**
     * Get the shipping request that owns the booking.
     */
    public function shippingRequest(): BelongsTo
    {
        return $this->belongsTo(ShippingRequest::class);
    }

    /**
     * Get the user that owns the booking.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): ShippingRequestBookingFactory
    {
        return ShippingRequestBookingFactory::new();
    }

    /**
     * Get the complaints for the booking.
     */
    public function complaints(): MorphMany
    {
        return $this->morphMany(Complaint::class, 'booking');
    }

    /**
     * Scope a query to only include pending bookings.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePending($query)
    {
        return $query->where('status', BookingStatusEnum::PENDING());
    }

    /**
     * Scope a query to only include accepted bookings.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeUpcoming($query)
    {
        return $query->whereNot('status', BookingStatusEnum::REJECTED())
            ->whereHas('shippingRequest', function ($q) {
                $q->where('departure_datetime', '>', now());
            });
    }

    /**
     * Scope a query to only include active bookings.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->whereNot('status', BookingStatusEnum::REJECTED())
            ->whereHas('shippingRequest', function ($q) {
                $q->where('arrival_datetime', '>=', now());
            });
    }
}
