<?php

namespace App\Models;

use App\Enums\Travel\TransportServiceTypeEnum;
use App\Models\Request\FazaaRequest;
use App\Models\Request\ShippingRequest;
use App\Models\Request\TripRequest;
use App\Models\Service\FazaaService;
use App\Models\Service\ShippingService;
use App\Models\Service\TripService;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

class Bookmark extends Model
{
    use HasFactory, Snowflakes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'bookmarkable_type',
        'bookmarkable_id'
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'user_id' => SnowflakeCast::class,
            'bookmarkable_id' => SnowflakeCast::class
        ];
    }

    /**
     * Get the user that owns the bookmark.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the bookmarkable model.
     */
    public function bookmarkable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the bookmarkable type formatted.
     */
    public function bookmarkableTypeFormatted(): Attribute
    {
        return new Attribute(
            get: fn(mixed $value, array $attributes) => match ($attributes['bookmarkable_type']) {
                TripService::class => TransportServiceTypeEnum::TRIP_SERVICE(),
                ShippingService::class => TransportServiceTypeEnum::SHIPPING_SERVICE(),
                FazaaService::class => TransportServiceTypeEnum::FAZAA_SERVICE(),
                TripRequest::class => TransportServiceTypeEnum::TRIP_REQUEST(),
                ShippingRequest::class => TransportServiceTypeEnum::SHIPPING_REQUEST(),
                FazaaRequest::class => TransportServiceTypeEnum::FAZAA_REQUEST(),

                default => $attributes['bookmarkable_type'],
            },
        );
    }
}
