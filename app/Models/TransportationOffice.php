<?php

namespace App\Models;

use App\Enums\TransportStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

class TransportationOffice extends Model
{
    use HasFactory, Snowflakes, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'phone',
        'address',
        'city',
        'country',
        'license_number',
        'status',
        'description',
        'office_logo',
        'license_document',
        'registration_documents',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
        ];
    }

    /**
     * Get the user for the transportation office.
     */
    public function user()
    {
        return $this->hasOne(TransportationOfficeUser::class);
    }

    /**
     * Get the drivers for the transportation office.
     */
    public function drivers()
    {
        return $this->hasMany(TransportationOfficeDriver::class);
    }

    /**
     * Check if the office can login.
     */
    public function canLogin(): bool
    {
        return $this->status === TransportStatus::ACTIVE->value;
    }

    /**
     * Check if the office is active.
     */
    public function isActive(): bool
    {
        return $this->status === TransportStatus::ACTIVE->value;
    }

    /**
     * Check if the office is suspended.
     */
    public function isSuspended(): bool
    {
        return $this->status === TransportStatus::SUSPENDED->value;
    }
}
