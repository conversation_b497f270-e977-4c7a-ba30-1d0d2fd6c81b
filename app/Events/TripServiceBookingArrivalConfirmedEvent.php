<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class TripServiceBookingArrivalConfirmedEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The booking ID.
     *
     * @var int
     */
    public $bookingId;

    /**
     * Create a new event instance.
     *
     * @param int $bookingId
     * @return void
     */
    public function __construct(int $bookingId)
    {
        $this->bookingId = $bookingId;
    }
}
