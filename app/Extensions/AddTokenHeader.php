<?php

declare(strict_types=1);

namespace App\Extensions;

use Dedoc\Scramble\Support\RouteInfo;
use Dedoc\Scramble\Support\Generator\Schema;
use Dedoc\Scramble\Support\Generator\Operation;
use Dedoc\Scramble\Support\Generator\Parameter;
use Dedoc\Scramble\Extensions\OperationExtension;
use Dedoc\Scramble\Support\Generator\Types\StringType;

class AddTokenHeader extends OperationExtension
{
    public function handle(Operation $operation, RouteInfo $routeInfo)
    {
        $operation->addParameters([
            Parameter::make('Authorization', 'header')
                ->setSchema(
                    Schema::fromType(new StringType())
                )
                ->required(false)
                ->example("Bearer 23|IElW45fftC8zAfdoR6vvTDYGJGXVpDN94ZoD8G5c8f7efc")
        ]);
    }
}
