<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ __('mails.registration_otp.title') }}</title>
</head>
<body style="font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f0e6fe; color: #333; direction: {{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}; text-align: {{ app()->getLocale() === 'ar' ? 'right' : 'left' }};">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px; background-color: #fff; border-radius: 20px; text-align: center; box-shadow: 0 4px 6px rgba(0,0,0,0.05);">
        <div style="width: 120px; height: 120px; margin: 20px auto;">
            <!-- App logo -->
            <img src="{{ asset('images/app_logo.png') }}" alt="{{ __('mails.registration_otp.logo_alt') }}" style="max-width: 100%;">
        </div>

        <h1 style="font-size: 36px; margin-bottom: 20px; color: #222;">{{ __('mails.registration_otp.heading') }}</h1>

        <p style="font-size: 16px; line-height: 1.5; margin-bottom: 30px; color: #444;">
            {{ __('mails.registration_otp.description') }}
            <br>{{ __('mails.registration_otp.expiry_notice') }}
        </p>

        <table style="margin: 0 auto 40px auto; border-collapse: separate; border-spacing: 15px;">
            <tr>
                @if(app()->getLocale() === 'ar')
                    @foreach(array_reverse(str_split($otp)) as $digit)
                        <td style="width: 60px; height: 60px; background-color: #f8f5ff; border: 2px solid #e6d4fe; border-radius: 12px; text-align: center; vertical-align: middle; font-size: 28px; font-weight: bold; color: #6b46c1; box-shadow: 0 2px 4px rgba(107, 70, 193, 0.1); line-height: 1;">{{ $digit }}</td>
                    @endforeach
                @else
                    @foreach(str_split($otp) as $digit)
                        <td style="width: 60px; height: 60px; background-color: #f8f5ff; border: 2px solid #e6d4fe; border-radius: 12px; text-align: center; vertical-align: middle; font-size: 28px; font-weight: bold; color: #6b46c1; box-shadow: 0 2px 4px rgba(107, 70, 193, 0.1); line-height: 1;">{{ $digit }}</td>
                    @endforeach
                @endif
            </tr>
        </table>

        <div style="margin-top: 50px; color: #666; font-size: 14px;">
            {{ __('mails.common.disclaimer') }}
        </div>
    </div>
</body>
</html>
