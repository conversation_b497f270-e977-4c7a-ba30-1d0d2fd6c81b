@php
    use Carbon\Carbon;

    $now = Carbon::now();
    $departureTime = Carbon::parse($getRecord()->departure_datetime);
    $arrivalTime = Carbon::parse($getRecord()->arrival_datetime);

    // Calculate the actual duration in hours (rounded)
    $durationHours = ceil($departureTime->diffInMinutes($arrivalTime) / 60);

    // Calculate progress percentage
    $progress = 0;
    $totalDuration = $departureTime->diffInSeconds($arrivalTime);

    if ($totalDuration > 0) {
        if ($now->lt($departureTime)) {
            // Trip hasn't started yet
        $progress = 0;
    } elseif ($now->gt($arrivalTime)) {
        // Trip has ended
        $progress = 100;
    } else {
        // Trip is in progress
        $elapsedDuration = $departureTime->diffInSeconds($now);
        $progress = min(100, max(0, ($elapsedDuration / $totalDuration) * 100));
    }
}

// Determine status
$status = 'normal';
$statusText = '';
$statusBadge = '';

if ($getRecord()->cancelled_at) {
    $status = 'cancelled';
    $statusText = __('fields.not_started');
    $statusBadge = __('fields.status_cancelled');
} elseif ($now->lt($departureTime)) {
    $status = 'not-started';
    $statusText = __('fields.not_started');
    $statusBadge = __('fields.status_active');
} elseif ($now->gt($arrivalTime)) {
    if ($now->diffInHours($arrivalTime) > 3) {
        // More than 3 hours late
        $status = 'delayed';
        $statusText = __('fields.delayed_more_than_3_hours');
        $statusBadge = __('fields.status_cancelled');
    } else {
        $status = 'completed';
        $statusText = __('fields.completed');
        $statusBadge = __('fields.status_completed');
    }
} else {
    $status = 'active';
    $statusText = __('fields.active');
    $statusBadge = __('fields.status_active');
    }
@endphp

<div class="flex flex-col w-full px-1">
    <!-- Top row with duration and status badge -->
    <div class="flex items-center justify-end w-full mb-1">
        <div class="text-sm font-medium">{{ $durationHours }} {{ __('fields.hours') }}</div>
        <div class="ml-2">
            <span
                class="px-2 py-0.5 text-xs font-medium rounded-md {{ $status === 'cancelled' || $status === 'delayed' ? 'bg-red-100 text-red-800' : 'bg-indigo-100 text-indigo-800' }}">
                {{ $statusBadge }}
            </span>
        </div>
    </div>

    <!-- Middle row with view buttons and progress curve -->
    <div class="flex items-center w-full">
        <!-- Left view button -->
        <div class="flex-none mr-2">
            <button type="button"
                class="text-gray-400 hover:text-gray-600 focus:outline-none text-xs font-medium flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-4 h-4 mr-1">
                    <path d="M12 15a3 3 0 100-6 3 3 0 000 6z" />
                    <path fill-rule="evenodd"
                        d="M1.323 11.447C2.811 6.976 7.028 3.75 12.001 3.75c4.97 0 9.185 3.223 10.675 7.69.12.362.12.752 0 1.113-1.487 4.471-5.705 7.697-10.677 7.697-4.97 0-9.186-3.223-10.675-7.69a1.762 1.762 0 010-1.113zM17.25 12a5.25 5.25 0 11-10.5 0 5.25 5.25 0 0110.5 0z"
                        clip-rule="evenodd" />
                </svg>
                {{ __('fields.view') }}
            </button>
        </div>

        <!-- Progress curve -->
        <div class="flex-grow relative h-6">
            <svg class="w-full h-full" viewBox="0 0 100 20" preserveAspectRatio="none">
                <!-- Background path -->
                <path d="M0,10 Q50,0 100,10" stroke="#e5e7eb" stroke-width="2" fill="none" />

                <!-- Colored path based on status -->
                @if ($status === 'delayed' || $status === 'cancelled')
                    <path d="M0,10 Q50,0 100,10" stroke="#f87171" stroke-width="2" fill="none" />
                @else
                    <linearGradient id="gradient-{{ $getRecord()->id }}" x1="0%" y1="0%" x2="100%"
                        y2="0%">
                        <stop offset="0%" stop-color="#4ade80" />
                        <stop offset="100%" stop-color="#6366f1" />
                    </linearGradient>
                    <path d="M0,10 Q50,0 100,10" stroke="url(#gradient-{{ $getRecord()->id }})" stroke-width="2"
                        fill="none" />
                @endif

                <!-- Start point (green dot) -->
                <circle cx="0" cy="10" r="3" fill="#4ade80" />

                <!-- End point (purple dot) -->
                <circle cx="100" cy="10" r="3" fill="#6366f1" />

                <!-- Progress indicator (moving dot) -->
                @if ($status !== 'cancelled')
                    @php
                        // Calculate the position on the curve
                        $x = $progress;
                        // For a quadratic bezier curve, we need to calculate the y position
                        // The formula for our curve is: y = 10 - 10 * sin(x * π / 100)
                        $y = 10 - 10 * sin(($x * pi()) / 100);
                    @endphp
                    <circle cx="{{ $x }}" cy="{{ $y }}" r="4" fill="white"
                        stroke="{{ $status === 'delayed' ? '#f87171' : '#4ade80' }}" stroke-width="2" />
                @endif
            </svg>
        </div>

        <!-- Right view button -->
        <div class="flex-none ml-2">
            <button type="button"
                class="text-gray-400 hover:text-gray-600 focus:outline-none text-xs font-medium flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-4 h-4 mr-1">
                    <path d="M12 15a3 3 0 100-6 3 3 0 000 6z" />
                    <path fill-rule="evenodd"
                        d="M1.323 11.447C2.811 6.976 7.028 3.75 12.001 3.75c4.97 0 9.185 3.223 10.675 7.69.12.362.12.752 0 1.113-1.487 4.471-5.705 7.697-10.677 7.697-4.97 0-9.186-3.223-10.675-7.69a1.762 1.762 0 010-1.113zM17.25 12a5.25 5.25 0 11-10.5 0 5.25 5.25 0 0110.5 0z"
                        clip-rule="evenodd" />
                </svg>
                {{ __('fields.view') }}
            </button>
        </div>
    </div>

    <!-- Bottom row with status text -->
    <div
        class="text-xs {{ $status === 'delayed' || $status === 'cancelled' ? 'text-red-500' : 'text-gray-500' }} text-center mt-1">
        {{ $statusText }}
    </div>
</div>
