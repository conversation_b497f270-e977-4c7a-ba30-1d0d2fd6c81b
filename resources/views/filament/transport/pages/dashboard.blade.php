<x-filament-panels::page>
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Office Information Card -->
        <div class="lg:col-span-2">
            <x-filament::section>
                <x-slot name="heading">
                    {{ __('sections.transport.office_info_card') }}
                </x-slot>

                <x-slot name="description">
                    {{ __('sections.transport.office_information_description') }}
                </x-slot>

                @if (auth('transport')->user()?->transportationOffice)
                    @php
                        $office = auth('transport')->user()->transportationOffice;
                    @endphp

                    <div class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">
                                    {{ __('fields.transport.name') }}</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ $office->name }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">
                                    {{ __('fields.transport.email') }}</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ $office->email }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">
                                    {{ __('fields.transport.phone') }}</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                                    {{ $office->phone ?? __('transport.messages.not_provided') }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">
                                    {{ __('fields.transport.status') }}</dt>
                                <dd class="mt-1">
                                    <x-filament::badge :color="match ($office->status) {
                                        'active' => 'success',
                                        'inactive' => 'warning',
                                        'suspended' => 'danger',
                                        default => 'gray',
                                    }">
                                        {{ match ($office->status) {
                                            'active' => __('fields.transport.active'),
                                            'inactive' => __('fields.transport.inactive'),
                                            'suspended' => __('fields.transport.suspended'),
                                            default => $office->status,
                                        } }}
                                    </x-filament::badge>
                                </dd>
                            </div>
                        </div>

                        @if ($office->address)
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">
                                    {{ __('fields.transport.address') }}</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ $office->address }}</dd>
                            </div>
                        @endif

                        @if ($office->city || $office->country)
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                @if ($office->city)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">
                                            {{ __('fields.transport.city') }}</dt>
                                        <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ $office->city }}
                                        </dd>
                                    </div>
                                @endif
                                @if ($office->country)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">
                                            {{ __('fields.transport.country') }}</dt>
                                        <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                                            {{ $office->country }}
                                        </dd>
                                    </div>
                                @endif
                            </div>
                        @endif

                        @if ($office->license_number)
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">
                                    {{ __('fields.transport.license_number') }}</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ $office->license_number }}
                                </dd>
                            </div>
                        @endif
                    </div>
                @else
                    <p class="text-gray-500 dark:text-gray-400">{{ __('transport.messages.no_office_info') }}</p>
                @endif
            </x-filament::section>
        </div>

        <!-- Quick Actions Card -->
        <div>
            <x-filament::section>
                <x-slot name="heading">
                    {{ __('sections.transport.quick_actions') }}
                </x-slot>

                <div class="space-y-3">
                    <x-filament::button href="{{ \App\Filament\Transport\Pages\ManageOfficeSettings::getUrl() }}"
                        icon="heroicon-o-cog-6-tooth" class="w-full justify-start">
                        {{ __('actions.transport.manage_settings') }}
                    </x-filament::button>
                </div>
            </x-filament::section>
        </div>
    </div>

    <!-- Statistics Row -->
    <div class="mt-6">
        <x-filament::section>
            <x-slot name="heading">
                {{ __('sections.transport.statistics') }}
            </x-slot>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {{ match (auth('transport')->user()?->transportationOffice?->status) {
                            'active' => __('fields.transport.active'),
                            'inactive' => __('fields.transport.inactive'),
                            'suspended' => __('fields.transport.suspended'),
                            default => __('fields.transport.inactive'),
                        } }}
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        {{ __('messages.transport.office_status') }}
                    </div>
                </div>

                <div class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                        {{ auth('transport')->user()?->transportationOffice?->license_number ? __('messages.transport.licensed') : __('messages.transport.unlicensed') }}
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        {{ __('messages.transport.license_status') }}
                    </div>
                </div>

                <div class="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                    <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">
                        {{ auth('transport')->user()?->transportationOffice?->created_at?->format('Y') ?? 'N/A' }}
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">{{ __('messages.transport.established') }}
                    </div>
                </div>
            </div>
        </x-filament::section>
    </div>
</x-filament-panels::page>
