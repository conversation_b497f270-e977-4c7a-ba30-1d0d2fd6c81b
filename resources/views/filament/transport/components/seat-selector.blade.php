@php
    $seatLayout = $field->getSeatLayout();
    $selectedSeats = $getState() ?? [];
    $reservedSeats = $field->getReservedSeats();
    $statePath = $getStatePath();
@endphp

<x-dynamic-component :component="$getFieldWrapperView()" :field="$field">
    <div class="space-y-6" wire:ignore x-data="{
        selectedSeats: @js($selectedSeats),
        reservedSeats: @js($reservedSeats),

        toggleSeat(seat) {
            console.log('🪑 Seat clicked:', seat);

            if (this.reservedSeats.includes(seat)) {
                console.log('❌ Seat is reserved, cannot select:', seat);
                return;
            }

            // Create a completely new array to ensure Alpine detects the change
            let newSelectedSeats = [...this.selectedSeats];

            const seatIndex = newSelectedSeats.indexOf(seat);

            if (seatIndex > -1) {
                // Remove seat
                newSelectedSeats.splice(seatIndex, 1);
                console.log('➖ Removing seat:', seat);
            } else {
                // Add seat
                newSelectedSeats.push(seat);
                console.log('➕ Adding seat:', seat);
            }

            // Update Alpine state immediately
            this.selectedSeats = newSelectedSeats;

            // Update Livewire state immediately
            $wire.set('{{ $statePath }}', newSelectedSeats);

            console.log('✅ Updated states. Selected:', JSON.stringify(newSelectedSeats));
        },
        getSeatClass(seat) {
            const baseClasses = 'flex items-center justify-center transition-all duration-200';

            if (this.reservedSeats.includes(seat)) {
                return `${baseClasses} cursor-not-allowed`;
            } else {
                return `${baseClasses} cursor-pointer`;
            }
        },

        getSeatStyle(seat) {
            const baseStyle = 'width: 39.78px; height: 39.78px; border-radius: 6px; border-width: 1px;';

            // Use a more reliable way to check seat state
            const isReserved = this.reservedSeats.includes(seat);
            const isSelected = this.selectedSeats.includes(seat);

            if (isReserved) {
                return `${baseStyle} background: #D9D9D9; border-color: #D9D9D9;`;
            } else if (isSelected) {
                return `${baseStyle} background: #8B5CF6; border-color: #7C3AED;`;
            } else {
                return `${baseStyle} background: #FEFEFE; border-color: #D9D9D9;`;
            }
        },

        isSeatSelected(seat) {
            return this.selectedSeats.includes(seat);
        },

        isSeatReserved(seat) {
            return this.reservedSeats.includes(seat);
        },

        getSortedSelectedSeats() {
            // Sort seats in logical order: A1, A2, A3, B1, B2, B3, C1, C2, C3
            const seatOrder = ['A1', 'A2', 'A3', 'B1', 'B2', 'B3', 'C1', 'C2', 'C3'];
            return this.selectedSeats.sort((a, b) => {
                return seatOrder.indexOf(a) - seatOrder.indexOf(b);
            });
        },

        getSelectedSeatsDisplay() {
            return this.getSortedSelectedSeats().join(', ');
        }
    }">

        <!-- Legend -->
        <div class="flex flex-wrap items-center justify-center gap-4 p-4 bg-gray-50 rounded-lg">
            <div class="flex items-center gap-2">
                <div
                    style="width: 39.78px; height: 39.78px; background: #FEFEFE; border: 1px solid #D9D9D9; border-radius: 6px;">
                </div>
                <span class="text-sm text-gray-700">{{ __('messages.transport.seat_available') }}</span>
            </div>
            <div class="flex items-center gap-2">
                <div style="width: 39.78px; height: 39.78px; background: #D9D9D9; border: 1px solid #D9D9D9; border-radius: 6px;"
                    class="flex items-center justify-center">
                    <svg width="18" height="18" viewBox="0 0 18 18" fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M9 0C7.21997 0 5.47991 0.527841 3.99987 1.51677C2.51983 2.50571 1.36628 3.91131 0.685088 5.55585C0.00389959 7.20038 -0.17433 9.00998 0.172936 10.7558C0.520203 12.5016 1.37737 14.1053 2.63604 15.364C3.89472 16.6226 5.49836 17.4798 7.24419 17.8271C8.99002 18.1743 10.7996 17.9961 12.4442 17.3149C14.0887 16.6337 15.4943 15.4802 16.4832 14.0001C17.4722 12.5201 18 10.78 18 9C17.9972 6.61391 17.0481 4.32636 15.3609 2.63915C13.6736 0.95193 11.3861 0.00281493 9 0ZM9 1.63636C10.2102 1.63708 11.4017 1.93608 12.4688 2.50691C13.536 3.07774 14.446 3.9028 15.1184 4.90909H2.88164C3.554 3.9028 4.46402 3.07774 5.53118 2.50691C6.59835 1.93608 7.78976 1.63708 9 1.63636ZM2.06919 11.4619C2.65211 11.4804 3.2244 11.623 3.74784 11.8802C4.27129 12.1374 4.73384 12.5033 5.10464 12.9535C5.74932 13.7274 6.0739 14.7184 6.012 15.7238C5.1011 15.3164 4.28214 14.7288 3.60451 13.9963C2.92687 13.2638 2.4046 12.4017 2.06919 11.4619ZM11.988 15.7238C11.9257 14.7184 12.2503 13.7272 12.8954 12.9535C13.2661 12.5032 13.7287 12.1373 14.2521 11.8801C14.7756 11.6229 15.3479 11.4803 15.9308 11.4619C15.5954 12.4017 15.0731 13.2638 14.3955 13.9963C13.7179 14.7288 12.8989 15.3164 11.988 15.7238ZM16.3636 9C16.3629 9.27341 16.3467 9.54657 16.3154 9.81818C15.4259 9.77956 14.5396 9.94867 13.7269 10.3121C12.9141 10.6755 12.1972 11.2233 11.6329 11.9119C11.1394 12.5062 10.7751 13.1966 10.5631 13.9394C10.3511 14.6822 10.2961 15.461 10.4015 16.2262C9.47617 16.4095 8.52383 16.4095 7.59846 16.2262C7.70394 15.461 7.64893 14.6822 7.43693 13.9394C7.22493 13.1966 6.86059 12.5062 6.36709 11.9119C5.79983 11.2267 5.08235 10.6815 4.27031 10.3184C3.45827 9.95527 2.57351 9.78411 1.68464 9.81818C1.55558 8.71247 1.68613 7.59189 2.06591 6.54545H15.9341C16.2167 7.33303 16.362 8.16327 16.3636 9ZM7.77273 9.40909C7.77273 9.16636 7.84471 8.92908 7.97956 8.72725C8.11442 8.52543 8.30609 8.36813 8.53034 8.27524C8.7546 8.18235 9.00136 8.15804 9.23943 8.2054C9.4775 8.25275 9.69618 8.36964 9.86782 8.54128C10.0395 8.71291 10.1563 8.93159 10.2037 9.16966C10.251 9.40773 10.2267 9.65449 10.1339 9.87875C10.041 10.103 9.88366 10.2947 9.68184 10.4295C9.48001 10.5644 9.24273 10.6364 9 10.6364C8.67451 10.6364 8.36235 10.5071 8.13219 10.2769C7.90203 10.0467 7.77273 9.73458 7.77273 9.40909Z"
                            fill="#767676" />
                    </svg>
                </div>
                <span class="text-sm text-gray-700">{{ __('messages.transport.seat_reserved') }}</span>
            </div>
            <div class="flex items-center gap-2">
                <div
                    style="width: 39.78px; height: 39.78px; background: #8B5CF6; border: 1px solid #7C3AED; border-radius: 6px;">
                </div>
                <span class="text-sm text-gray-700">{{ __('messages.transport.seat_selected') }}</span>
            </div>
        </div>

        <!-- Seat Layout -->
        <div class="flex flex-col items-center" style="gap: 10px; direction: ltr;">
            @foreach ($seatLayout as $rowLetter => $seats)
                <div class="flex items-center" style="gap: 10px; direction: ltr;">
                    @foreach ($seats as $seat)
                        <div :class="getSeatClass('{{ $seat }}')" :style="getSeatStyle('{{ $seat }}')"
                            @click="toggleSeat('{{ $seat }}')" title="{{ $seat }}"
                            :data-seat="'{{ $seat }}'" :data-selected="isSeatSelected('{{ $seat }}')"
                            :data-reserved="isSeatReserved('{{ $seat }}')">
                            @if ($seat === 'A1')
                                <svg width="18" height="18" viewBox="0 0 18 18" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M9 0C7.21997 0 5.47991 0.527841 3.99987 1.51677C2.51983 2.50571 1.36628 3.91131 0.685088 5.55585C0.00389959 7.20038 -0.17433 9.00998 0.172936 10.7558C0.520203 12.5016 1.37737 14.1053 2.63604 15.364C3.89472 16.6226 5.49836 17.4798 7.24419 17.8271C8.99002 18.1743 10.7996 17.9961 12.4442 17.3149C14.0887 16.6337 15.4943 15.4802 16.4832 14.0001C17.4722 12.5201 18 10.78 18 9C17.9972 6.61391 17.0481 4.32636 15.3609 2.63915C13.6736 0.95193 11.3861 0.00281493 9 0ZM9 1.63636C10.2102 1.63708 11.4017 1.93608 12.4688 2.50691C13.536 3.07774 14.446 3.9028 15.1184 4.90909H2.88164C3.554 3.9028 4.46402 3.07774 5.53118 2.50691C6.59835 1.93608 7.78976 1.63708 9 1.63636ZM2.06919 11.4619C2.65211 11.4804 3.2244 11.623 3.74784 11.8802C4.27129 12.1374 4.73384 12.5033 5.10464 12.9535C5.74932 13.7274 6.0739 14.7184 6.012 15.7238C5.1011 15.3164 4.28214 14.7288 3.60451 13.9963C2.92687 13.2638 2.4046 12.4017 2.06919 11.4619ZM11.988 15.7238C11.9257 14.7184 12.2503 13.7272 12.8954 12.9535C13.2661 12.5032 13.7287 12.1373 14.2521 11.8801C14.7756 11.6229 15.3479 11.4803 15.9308 11.4619C15.5954 12.4017 15.0731 13.2638 14.3955 13.9963C13.7179 14.7288 12.8989 15.3164 11.988 15.7238ZM16.3636 9C16.3629 9.27341 16.3467 9.54657 16.3154 9.81818C15.4259 9.77956 14.5396 9.94867 13.7269 10.3121C12.9141 10.6755 12.1972 11.2233 11.6329 11.9119C11.1394 12.5062 10.7751 13.1966 10.5631 13.9394C10.3511 14.6822 10.2961 15.461 10.4015 16.2262C9.47617 16.4095 8.52383 16.4095 7.59846 16.2262C7.70394 15.461 7.64893 14.6822 7.43693 13.9394C7.22493 13.1966 6.86059 12.5062 6.36709 11.9119C5.79983 11.2267 5.08235 10.6815 4.27031 10.3184C3.45827 9.95527 2.57351 9.78411 1.68464 9.81818C1.55558 8.71247 1.68613 7.59189 2.06591 6.54545H15.9341C16.2167 7.33303 16.362 8.16327 16.3636 9ZM7.77273 9.40909C7.77273 9.16636 7.84471 8.92908 7.97956 8.72725C8.11442 8.52543 8.30609 8.36813 8.53034 8.27524C8.7546 8.18235 9.00136 8.15804 9.23943 8.2054C9.4775 8.25275 9.69618 8.36964 9.86782 8.54128C10.0395 8.71291 10.1563 8.93159 10.2037 9.16966C10.251 9.40773 10.2267 9.65449 10.1339 9.87875C10.041 10.103 9.88366 10.2947 9.68184 10.4295C9.48001 10.5644 9.24273 10.6364 9 10.6364C8.67451 10.6364 8.36235 10.5071 8.13219 10.2769C7.90203 10.0467 7.77273 9.73458 7.77273 9.40909Z"
                                        fill="#767676" />
                                </svg>
                            @endif
                        </div>
                    @endforeach
                </div>
            @endforeach
        </div>

        <!-- Selected Seats Info -->
        <div x-show="selectedSeats.length > 0" class="p-4 bg-purple-50 border border-purple-200 rounded-lg">
            <h4 class="font-medium text-purple-900 mb-2">{{ __('messages.transport.selected_seats') }}:</h4>
            <div class="flex flex-wrap gap-2">
                <span class="px-3 py-1 bg-purple-500 text-white text-sm rounded-full"
                    x-text="getSelectedSeatsDisplay()"></span>
            </div>
            <p class="text-sm text-purple-700 mt-2">
                {{ __('messages.transport.total_selected_seats') }}<span x-text="selectedSeats.length"></span>
            </p>
        </div>
    </div>
</x-dynamic-component>
