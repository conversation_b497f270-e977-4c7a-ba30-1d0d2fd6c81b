/* User Profile Styling */
.user-profile-section {
    background-color: #1a1f36;
    border-radius: 0.5rem;
    padding: 1.5rem;
    color: white;
}

.user-profile-section .fi-section-header-heading {
    color: white;
    font-size: 1.25rem;
    font-weight: bold;
    text-align: right;
}

.user-profile-section .fi-in-image {
    width: 100px;
    height: 100px;
    margin: 0 auto;
    border: 3px solid #4f46e5;
}

.user-profile-section .fi-in-text {
    color: white;
}

.user-profile-section .fi-in-text-size-xl {
    color: #4f46e5;
    font-size: 1.5rem;
}

.recent-transactions-section {
    background-color: #1a1f36;
    border-radius: 0.5rem;
    padding: 1.5rem;
    color: white;
}

.recent-transactions-section .fi-section-header-heading {
    color: white;
    font-size: 1.25rem;
    font-weight: bold;
    text-align: right;
}

.recent-transactions-section .fi-in-repeatable {
    background-color: #2a2f46;
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
    padding: 0.75rem;
}

.recent-transactions-section .fi-in-text {
    color: white;
}

.recent-transactions-section .fi-in-icon {
    display: flex;
    justify-content: center;
}

.contact-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1rem;
}

.contact-actions button {
    border-radius: 9999px;
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* RTL Support */
html[dir="rtl"] .fi-section-header-heading,
html[dir="rtl"] .fi-in-text {
    text-align: right;
}