FROM --platform=linux/amd64 php:8.2-fpm

# Install system dependencies
RUN apt-get update && apt-get install -y \
    libpng-dev \
    libjpeg-dev \
    libfreetype6-dev \
    libzip-dev \
    libpq-dev \
    zip \
    unzip \
    git \
    curl \
    nginx \
    vim \
    supervisor

# Install PHP extensions
RUN docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install gd pgsql pdo_pgsql zip bcmath pcntl exif opcache

# Install Redis PHP extension
RUN pecl install redis && docker-php-ext-enable redis

# Copy your Nginx configuration
COPY container-files/nginx.conf /etc/nginx/nginx.conf

# Configure supervisord
COPY container-files/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

COPY --chown=www-data:www-data . ./

# Install Composer
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

# Install project dependencies
RUN composer install --no-interaction --ignore-platform-reqs

# Change permissions
RUN chmod -R 777 storage/logs
RUN chmod -R 777 bootstrap/cache/

RUN chown -R www-data:www-data .

# Expose port 80
EXPOSE 80

# start nginx with fpm process
# CMD ./container-files/setup.sh
