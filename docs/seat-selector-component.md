# Visual Seat Selector Component

## Overview

The `SeatSelector` component provides a beautiful visual interface for selecting seats in transportation vehicles. It replaces the previous checkbox-based seat selection with an intuitive grid layout that shows seat availability and selection status.

## Features

- **Visual Seat Layout**: Displays seats in a grid format similar to actual vehicle layouts
- **Color-coded Status**: 
  - White seats: Available for selection
  - Gray seats: Reserved (A1 for driver, A2 for front passenger)
  - Purple seats: Selected by user
- **Interactive Selection**: Click seats to toggle selection
- **Dynamic Layout**: Adapts to different car types and seat counts
- **Real-time Updates**: Shows selected seats count and list
- **Responsive Design**: Works on different screen sizes

## Usage

### Basic Implementation

```php
use App\Filament\Transport\Components\SeatSelector;

SeatSelector::make('available_seats')
    ->label('Select Available Seats')
    ->required()
    ->helperText('Choose which seats customers can book');
```

### With Custom Reserved Seats

```php
SeatSelector::make('available_seats')
    ->label('Select Available Seats')
    ->reservedSeats(['A1', 'A2', 'B1']) // Custom reserved seats
    ->required();
```

## Component Structure

### Files Created

1. **Component Class**: `app/Filament/Transport/Components/SeatSelector.php`
2. **Blade View**: `resources/views/filament/transport/components/seat-selector.blade.php`
3. **Language Files**: Updated `lang/ar/messages.php` and `lang/en/messages.php`

### Key Methods

- `getSeatLayout()`: Generates seat layout based on car type
- `getReservedSeats()`: Returns list of reserved seats
- `getSeatStatus()`: Determines seat status (available/reserved/selected)
- `reservedSeats()`: Sets custom reserved seats

## Seat Layout Logic

### Default Layout (9 seats)
```
Row A: 🚗 A1 (Driver) | 👤 A2 (Front) | 💺 A3
Row B: 💺 B1         | 💺 B2        | 💺 B3  
Row C: 💺 C1         | 💺 C2        | 💺 C3
```

### Dynamic Layout
The component automatically generates layouts based on the selected car's seat count:
- Seats are arranged in rows of 3
- A1 and A2 are always reserved by default
- Additional rows are created as needed

## Integration

### In TripServiceResource

The component is integrated into the trip service creation form:

```php
Forms\Components\Section::make(__('sections.transport.seat_management'))
    ->schema([
        // Car selection info placeholder
        Forms\Components\Placeholder::make('seat_layout_info')
            ->label(__('fields.transport.seat_layout'))
            ->content(function (Forms\Get $get) {
                // Display car info and seat counts
            }),

        // Visual seat selector
        \App\Filament\Transport\Components\SeatSelector::make('available_seats')
            ->label(__('fields.transport.select_available_seats'))
            ->required()
            ->helperText(__('messages.transport.select_seats_help')),
    ]),
```

## Styling

The component uses Tailwind CSS classes for styling:
- Responsive grid layout
- Hover effects and transitions
- Color-coded seat states
- Clean, modern design

## Language Support

Supports both Arabic and English with the following translation keys:
- `messages.transport.seat_available`
- `messages.transport.seat_reserved` 
- `messages.transport.seat_selected`
- `messages.transport.selected_seats`
- `messages.transport.total_selected_seats`

## Testing

A test page is available at `/transport/test-seat-selector` to preview the component functionality.

## Future Enhancements

Potential improvements:
- Seat numbering customization
- Different vehicle layouts (bus, van, etc.)
- Seat pricing tiers
- Accessibility features
- Mobile-optimized touch interactions
