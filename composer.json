{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "archtechx/enums": "^1.1", "aws/aws-sdk-php": "^3.343", "caneara/snowflake": "^2.0", "cheesegrits/filament-google-maps": "^3.0", "dedoc/scramble": "^0.11.31", "fakerphp/faker": "^1.23", "filament/filament": "^3.2", "filament/spatie-laravel-settings-plugin": "*", "kreait/laravel-firebase": "^5.9", "laravel-notification-channels/fcm": "^4.3", "laravel/framework": "^11.0", "laravel/horizon": "^5.24", "laravel/sanctum": "^4.0", "laravel/telescope": "^5.0", "laravel/tinker": "^2.9", "opcodesio/log-viewer": "^3.11", "sentry/sentry-laravel": "^4.13", "spatie/laravel-data": "^4.6", "spatie/laravel-permission": "^6.17", "spatie/laravel-settings": "^3.4", "spatie/laravel-web-tinker": "^1.10"}, "require-dev": {"barryvdh/laravel-ide-helper": "^3.0", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "pestphp/pest": "^2.34", "pestphp/pest-plugin-laravel": "^2.4", "spatie/laravel-ignition": "^2.4"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/Methods.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/base.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}