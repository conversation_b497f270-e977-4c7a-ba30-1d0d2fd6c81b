!function(e){var t={};function r(n){if(t[n])return t[n].exports;var i=t[n]={i:n,l:!1,exports:{}};return e[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{configurable:!1,enumerable:!0,get:n})},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="/",r(r.s=0)}({0:function(e,t,r){r("F1kH"),e.exports=r("iTlA")},"162o":function(e,t,r){(function(e){var n=void 0!==e&&e||"undefined"!=typeof self&&self||window,i=Function.prototype.apply;function o(e,t){this._id=e,this._clearFn=t}t.setTimeout=function(){return new o(i.call(setTimeout,n,arguments),clearTimeout)},t.setInterval=function(){return new o(i.call(setInterval,n,arguments),clearInterval)},t.clearTimeout=t.clearInterval=function(e){e&&e.close()},o.prototype.unref=o.prototype.ref=function(){},o.prototype.close=function(){this._clearFn.call(n,this._id)},t.enroll=function(e,t){clearTimeout(e._idleTimeoutId),e._idleTimeout=t},t.unenroll=function(e){clearTimeout(e._idleTimeoutId),e._idleTimeout=-1},t._unrefActive=t.active=function(e){clearTimeout(e._idleTimeoutId);var t=e._idleTimeout;t>=0&&(e._idleTimeoutId=setTimeout(function(){e._onTimeout&&e._onTimeout()},t))},r("mypn"),t.setImmediate="undefined"!=typeof self&&self.setImmediate||void 0!==e&&e.setImmediate||this&&this.setImmediate,t.clearImmediate="undefined"!=typeof self&&self.clearImmediate||void 0!==e&&e.clearImmediate||this&&this.clearImmediate}).call(t,r("DuR2"))},"21It":function(e,t,r){"use strict";var n=r("FtD3");e.exports=function(e,t,r){var i=r.config.validateStatus;r.status&&i&&!i(r.status)?t(n("Request failed with status code "+r.status,r.config,null,r.request,r)):e(r)}},"5IAE":function(e,t,r){(function(e){"use strict";e.defineMode("javascript",function(t,r){var n,i,o=t.indentUnit,a=r.statementIndent,s=r.jsonld,l=r.json||s,c=r.typescript,u=r.wordCharacters||/[\w$\xa1-\uffff]/,f=function(){function e(e){return{type:e,style:"keyword"}}var t=e("keyword a"),r=e("keyword b"),n=e("keyword c"),i=e("keyword d"),o=e("operator"),a={type:"atom",style:"atom"};return{if:e("if"),while:t,with:t,else:r,do:r,try:r,finally:r,return:i,break:i,continue:i,new:e("new"),delete:n,void:n,throw:n,debugger:e("debugger"),var:e("var"),const:e("var"),let:e("var"),function:e("function"),catch:e("catch"),for:e("for"),switch:e("switch"),case:e("case"),default:e("default"),in:o,typeof:o,instanceof:o,true:a,false:a,null:a,undefined:a,NaN:a,Infinity:a,this:e("this"),class:e("class"),super:e("atom"),yield:n,export:e("export"),import:e("import"),extends:n,await:n}}(),d=/[+\-*&%=<>!?|~^@]/,p=/^@(context|id|value|language|type|container|list|set|reverse|index|base|vocab|graph)"/;function h(e,t,r){return n=e,i=r,t}function m(e,t){var r,n=e.next();if('"'==n||"'"==n)return t.tokenize=(r=n,function(e,t){var n,i=!1;if(s&&"@"==e.peek()&&e.match(p))return t.tokenize=m,h("jsonld-keyword","meta");for(;null!=(n=e.next())&&(n!=r||i);)i=!i&&"\\"==n;return i||(t.tokenize=m),h("string","string")}),t.tokenize(e,t);if("."==n&&e.match(/^\d[\d_]*(?:[eE][+\-]?[\d_]+)?/))return h("number","number");if("."==n&&e.match(".."))return h("spread","meta");if(/[\[\]{}\(\),;\:\.]/.test(n))return h(n);if("="==n&&e.eat(">"))return h("=>","operator");if("0"==n&&e.match(/^(?:x[\dA-Fa-f_]+|o[0-7_]+|b[01_]+)n?/))return h("number","number");if(/\d/.test(n))return e.match(/^[\d_]*(?:n|(?:\.[\d_]*)?(?:[eE][+\-]?[\d_]+)?)?/),h("number","number");if("/"==n)return e.eat("*")?(t.tokenize=g,g(e,t)):e.eat("/")?(e.skipToEnd(),h("comment","comment")):Ke(e,t,1)?(function(e){for(var t,r=!1,n=!1;null!=(t=e.next());){if(!r){if("/"==t&&!n)return;"["==t?n=!0:n&&"]"==t&&(n=!1)}r=!r&&"\\"==t}}(e),e.match(/^\b(([gimyus])(?![gimyus]*\2))+\b/),h("regexp","string-2")):(e.eat("="),h("operator","operator",e.current()));if("`"==n)return t.tokenize=v,v(e,t);if("#"==n)return e.skipToEnd(),h("error","error");if(d.test(n))return">"==n&&t.lexical&&">"==t.lexical.type||(e.eat("=")?"!"!=n&&"="!=n||e.eat("="):/[<>*+\-]/.test(n)&&(e.eat(n),">"==n&&e.eat(n))),h("operator","operator",e.current());if(u.test(n)){e.eatWhile(u);var i=e.current();if("."!=t.lastType){if(f.propertyIsEnumerable(i)){var o=f[i];return h(o.type,o.style,i)}if("async"==i&&e.match(/^(\s|\/\*.*?\*\/)*[\[\(\w]/,!1))return h("async","keyword",i)}return h("variable","variable",i)}}function g(e,t){for(var r,n=!1;r=e.next();){if("/"==r&&n){t.tokenize=m;break}n="*"==r}return h("comment","comment")}function v(e,t){for(var r,n=!1;null!=(r=e.next());){if(!n&&("`"==r||"$"==r&&e.eat("{"))){t.tokenize=m;break}n=!n&&"\\"==r}return h("quasi","string-2",e.current())}var y="([{}])";function b(e,t){t.fatArrowAt&&(t.fatArrowAt=null);var r=e.string.indexOf("=>",e.start);if(!(r<0)){if(c){var n=/:\s*(?:\w+(?:<[^>]*>|\[\])?|\{[^}]*\})\s*$/.exec(e.string.slice(e.start,r));n&&(r=n.index)}for(var i=0,o=!1,a=r-1;a>=0;--a){var s=e.string.charAt(a),l=y.indexOf(s);if(l>=0&&l<3){if(!i){++a;break}if(0==--i){"("==s&&(o=!0);break}}else if(l>=3&&l<6)++i;else if(u.test(s))o=!0;else if(/["'\/`]/.test(s))for(;;--a){if(0==a)return;if(e.string.charAt(a-1)==s&&"\\"!=e.string.charAt(a-2)){a--;break}}else if(o&&!i){++a;break}}o&&!i&&(t.fatArrowAt=a)}}var w={atom:!0,number:!0,variable:!0,string:!0,regexp:!0,this:!0,"jsonld-keyword":!0};function x(e,t,r,n,i,o){this.indented=e,this.column=t,this.type=r,this.prev=i,this.info=o,null!=n&&(this.align=n)}function _(e,t){for(var r=e.localVars;r;r=r.next)if(r.name==t)return!0;for(var n=e.context;n;n=n.prev)for(r=n.vars;r;r=r.next)if(r.name==t)return!0}var k={state:null,column:null,marked:null,cc:null};function C(){for(var e=arguments.length-1;e>=0;e--)k.cc.push(arguments[e])}function S(){return C.apply(null,arguments),!0}function T(e,t){for(var r=t;r;r=r.next)if(r.name==e)return!0;return!1}function M(e){var t=k.state;if(k.marked="def",t.context)if("var"==t.lexical.info&&t.context&&t.context.block){var n=function e(t,r){if(r){if(r.block){var n=e(t,r.prev);return n?n==r.prev?r:new A(n,r.vars,!0):null}return T(t,r.vars)?r:new A(r.prev,new O(t,r.vars),!1)}return null}(e,t.context);if(null!=n)return void(t.context=n)}else if(!T(e,t.localVars))return void(t.localVars=new O(e,t.localVars));r.globalVars&&!T(e,t.globalVars)&&(t.globalVars=new O(e,t.globalVars))}function L(e){return"public"==e||"private"==e||"protected"==e||"abstract"==e||"readonly"==e}function A(e,t,r){this.prev=e,this.vars=t,this.block=r}function O(e,t){this.name=e,this.next=t}var N=new O("this",new O("arguments",null));function E(){k.state.context=new A(k.state.context,k.state.localVars,!1),k.state.localVars=N}function D(){k.state.context=new A(k.state.context,k.state.localVars,!0),k.state.localVars=null}function I(){k.state.localVars=k.state.context.vars,k.state.context=k.state.context.prev}function P(e,t){var r=function(){var r=k.state,n=r.indented;if("stat"==r.lexical.type)n=r.lexical.indented;else for(var i=r.lexical;i&&")"==i.type&&i.align;i=i.prev)n=i.indented;r.lexical=new x(n,k.stream.column(),e,null,r.lexical,t)};return r.lex=!0,r}function z(){var e=k.state;e.lexical.prev&&(")"==e.lexical.type&&(e.indented=e.lexical.indented),e.lexical=e.lexical.prev)}function R(e){return function t(r){return r==e?S():";"==e||"}"==r||")"==r||"]"==r?C():S(t)}}function $(e,t){return"var"==e?S(P("vardef",t),be,R(";"),z):"keyword a"==e?S(P("form"),q,$,z):"keyword b"==e?S(P("form"),$,z):"keyword d"==e?k.stream.match(/^\s*$/,!1)?S():S(P("stat"),U,R(";"),z):"debugger"==e?S(R(";")):"{"==e?S(P("}"),D,ae,z,I):";"==e?S():"if"==e?("else"==k.state.lexical.info&&k.state.cc[k.state.cc.length-1]==z&&k.state.cc.pop()(),S(P("form"),q,$,z,Se)):"function"==e?S(Ae):"for"==e?S(P("form"),Te,$,z):"class"==e||c&&"interface"==t?(k.marked="keyword",S(P("form","class"==e?e:t),Ie,z)):"variable"==e?c&&"declare"==t?(k.marked="keyword",S($)):c&&("module"==t||"enum"==t||"type"==t)&&k.stream.match(/^\s*\w/,!1)?(k.marked="keyword","enum"==t?S(Ge):"type"==t?S(Ne,R("operator"),fe,R(";")):S(P("form"),we,R("{"),P("}"),ae,z,z)):c&&"namespace"==t?(k.marked="keyword",S(P("form"),j,$,z)):c&&"abstract"==t?(k.marked="keyword",S($)):S(P("stat"),Q):"switch"==e?S(P("form"),q,R("{"),P("}","switch"),D,ae,z,z,I):"case"==e?S(j,R(":")):"default"==e?S(R(":")):"catch"==e?S(P("form"),E,F,$,z,I):"export"==e?S(P("stat"),$e,z):"import"==e?S(P("stat"),je,z):"async"==e?S($):"@"==t?S(j,$):C(P("stat"),j,R(";"),z)}function F(e){if("("==e)return S(Ee,R(")"))}function j(e,t){return H(e,t,!1)}function W(e,t){return H(e,t,!0)}function q(e){return"("!=e?C():S(P(")"),j,R(")"),z)}function H(e,t,r){if(k.state.fatArrowAt==k.stream.start){var n=r?Y:X;if("("==e)return S(E,P(")"),ie(Ee,")"),z,R("=>"),n,I);if("variable"==e)return C(E,we,R("=>"),n,I)}var i=r?G:B;return w.hasOwnProperty(e)?S(i):"function"==e?S(Ae,i):"class"==e||c&&"interface"==t?(k.marked="keyword",S(P("form"),De,z)):"keyword c"==e||"async"==e?S(r?W:j):"("==e?S(P(")"),U,R(")"),z,i):"operator"==e||"spread"==e?S(r?W:j):"["==e?S(P("]"),Be,z,i):"{"==e?oe(te,"}",null,i):"quasi"==e?C(V,i):"new"==e?S(function(e){return function(t){return"."==t?S(e?J:Z):"variable"==t&&c?S(ge,e?G:B):C(e?W:j)}}(r)):"import"==e?S(j):S()}function U(e){return e.match(/[;\}\)\],]/)?C():C(j)}function B(e,t){return","==e?S(j):G(e,t,!1)}function G(e,t,r){var n=0==r?B:G,i=0==r?j:W;return"=>"==e?S(E,r?Y:X,I):"operator"==e?/\+\+|--/.test(t)||c&&"!"==t?S(n):c&&"<"==t&&k.stream.match(/^([^>]|<.*?>)*>\s*\(/,!1)?S(P(">"),ie(fe,">"),z,n):"?"==t?S(j,R(":"),i):S(i):"quasi"==e?C(V,n):";"!=e?"("==e?oe(W,")","call",n):"."==e?S(ee,n):"["==e?S(P("]"),U,R("]"),z,n):c&&"as"==t?(k.marked="keyword",S(fe,n)):"regexp"==e?(k.state.lastType=k.marked="operator",k.stream.backUp(k.stream.pos-k.stream.start-1),S(i)):void 0:void 0}function V(e,t){return"quasi"!=e?C():"${"!=t.slice(t.length-2)?S(V):S(j,K)}function K(e){if("}"==e)return k.marked="string-2",k.state.tokenize=v,S(V)}function X(e){return b(k.stream,k.state),C("{"==e?$:j)}function Y(e){return b(k.stream,k.state),C("{"==e?$:W)}function Z(e,t){if("target"==t)return k.marked="keyword",S(B)}function J(e,t){if("target"==t)return k.marked="keyword",S(G)}function Q(e){return":"==e?S(z,$):C(B,R(";"),z)}function ee(e){if("variable"==e)return k.marked="property",S()}function te(e,t){if("async"==e)return k.marked="property",S(te);if("variable"==e||"keyword"==k.style){return k.marked="property","get"==t||"set"==t?S(re):(c&&k.state.fatArrowAt==k.stream.start&&(r=k.stream.match(/^\s*:\s*/,!1))&&(k.state.fatArrowAt=k.stream.pos+r[0].length),S(ne));var r}else{if("number"==e||"string"==e)return k.marked=s?"property":k.style+" property",S(ne);if("jsonld-keyword"==e)return S(ne);if(c&&L(t))return k.marked="keyword",S(te);if("["==e)return S(j,se,R("]"),ne);if("spread"==e)return S(W,ne);if("*"==t)return k.marked="keyword",S(te);if(":"==e)return C(ne)}}function re(e){return"variable"!=e?C(ne):(k.marked="property",S(Ae))}function ne(e){return":"==e?S(W):"("==e?C(Ae):void 0}function ie(e,t,r){function n(i,o){if(r?r.indexOf(i)>-1:","==i){var a=k.state.lexical;return"call"==a.info&&(a.pos=(a.pos||0)+1),S(function(r,n){return r==t||n==t?C():C(e)},n)}return i==t||o==t?S():r&&r.indexOf(";")>-1?C(e):S(R(t))}return function(r,i){return r==t||i==t?S():C(e,n)}}function oe(e,t,r){for(var n=3;n<arguments.length;n++)k.cc.push(arguments[n]);return S(P(t,r),ie(e,t),z)}function ae(e){return"}"==e?S():C($,ae)}function se(e,t){if(c){if(":"==e)return S(fe);if("?"==t)return S(se)}}function le(e,t){if(c&&(":"==e||"in"==t))return S(fe)}function ce(e){if(c&&":"==e)return k.stream.match(/^\s*\w+\s+is\b/,!1)?S(j,ue,fe):S(fe)}function ue(e,t){if("is"==t)return k.marked="keyword",S()}function fe(e,t){return"keyof"==t||"typeof"==t||"infer"==t?(k.marked="keyword",S("typeof"==t?W:fe)):"variable"==e||"void"==t?(k.marked="type",S(me)):"|"==t||"&"==t?S(fe):"string"==e||"number"==e||"atom"==e?S(me):"["==e?S(P("]"),ie(fe,"]",","),z,me):"{"==e?S(P("}"),ie(pe,"}",",;"),z,me):"("==e?S(ie(he,")"),de,me):"<"==e?S(ie(fe,">"),fe):void 0}function de(e){if("=>"==e)return S(fe)}function pe(e,t){return"variable"==e||"keyword"==k.style?(k.marked="property",S(pe)):"?"==t||"number"==e||"string"==e?S(pe):":"==e?S(fe):"["==e?S(R("variable"),le,R("]"),pe):"("==e?C(Oe,pe):void 0}function he(e,t){return"variable"==e&&k.stream.match(/^\s*[?:]/,!1)||"?"==t?S(he):":"==e?S(fe):"spread"==e?S(he):C(fe)}function me(e,t){return"<"==t?S(P(">"),ie(fe,">"),z,me):"|"==t||"."==e||"&"==t?S(fe):"["==e?S(fe,R("]"),me):"extends"==t||"implements"==t?(k.marked="keyword",S(fe)):"?"==t?S(fe,R(":"),fe):void 0}function ge(e,t){if("<"==t)return S(P(">"),ie(fe,">"),z,me)}function ve(){return C(fe,ye)}function ye(e,t){if("="==t)return S(fe)}function be(e,t){return"enum"==t?(k.marked="keyword",S(Ge)):C(we,se,ke,Ce)}function we(e,t){return c&&L(t)?(k.marked="keyword",S(we)):"variable"==e?(M(t),S()):"spread"==e?S(we):"["==e?oe(_e,"]"):"{"==e?oe(xe,"}"):void 0}function xe(e,t){return"variable"!=e||k.stream.match(/^\s*:/,!1)?("variable"==e&&(k.marked="property"),"spread"==e?S(we):"}"==e?C():"["==e?S(j,R("]"),R(":"),xe):S(R(":"),we,ke)):(M(t),S(ke))}function _e(){return C(we,ke)}function ke(e,t){if("="==t)return S(W)}function Ce(e){if(","==e)return S(be)}function Se(e,t){if("keyword b"==e&&"else"==t)return S(P("form","else"),$,z)}function Te(e,t){return"await"==t?S(Te):"("==e?S(P(")"),Me,z):void 0}function Me(e){return"var"==e?S(be,Le):"variable"==e?S(Le):C(Le)}function Le(e,t){return")"==e?S():";"==e?S(Le):"in"==t||"of"==t?(k.marked="keyword",S(j,Le)):C(j,Le)}function Ae(e,t){return"*"==t?(k.marked="keyword",S(Ae)):"variable"==e?(M(t),S(Ae)):"("==e?S(E,P(")"),ie(Ee,")"),z,ce,$,I):c&&"<"==t?S(P(">"),ie(ve,">"),z,Ae):void 0}function Oe(e,t){return"*"==t?(k.marked="keyword",S(Oe)):"variable"==e?(M(t),S(Oe)):"("==e?S(E,P(")"),ie(Ee,")"),z,ce,I):c&&"<"==t?S(P(">"),ie(ve,">"),z,Oe):void 0}function Ne(e,t){return"keyword"==e||"variable"==e?(k.marked="type",S(Ne)):"<"==t?S(P(">"),ie(ve,">"),z):void 0}function Ee(e,t){return"@"==t&&S(j,Ee),"spread"==e?S(Ee):c&&L(t)?(k.marked="keyword",S(Ee)):c&&"this"==e?S(se,ke):C(we,se,ke)}function De(e,t){return"variable"==e?Ie(e,t):Pe(e,t)}function Ie(e,t){if("variable"==e)return M(t),S(Pe)}function Pe(e,t){return"<"==t?S(P(">"),ie(ve,">"),z,Pe):"extends"==t||"implements"==t||c&&","==e?("implements"==t&&(k.marked="keyword"),S(c?fe:j,Pe)):"{"==e?S(P("}"),ze,z):void 0}function ze(e,t){return"async"==e||"variable"==e&&("static"==t||"get"==t||"set"==t||c&&L(t))&&k.stream.match(/^\s+[\w$\xa1-\uffff]/,!1)?(k.marked="keyword",S(ze)):"variable"==e||"keyword"==k.style?(k.marked="property",S(c?Re:Ae,ze)):"number"==e||"string"==e?S(c?Re:Ae,ze):"["==e?S(j,se,R("]"),c?Re:Ae,ze):"*"==t?(k.marked="keyword",S(ze)):c&&"("==e?C(Oe,ze):";"==e||","==e?S(ze):"}"==e?S():"@"==t?S(j,ze):void 0}function Re(e,t){if("?"==t)return S(Re);if(":"==e)return S(fe,ke);if("="==t)return S(W);var r=k.state.lexical.prev;return C(r&&"interface"==r.info?Oe:Ae)}function $e(e,t){return"*"==t?(k.marked="keyword",S(Ue,R(";"))):"default"==t?(k.marked="keyword",S(j,R(";"))):"{"==e?S(ie(Fe,"}"),Ue,R(";")):C($)}function Fe(e,t){return"as"==t?(k.marked="keyword",S(R("variable"))):"variable"==e?C(W,Fe):void 0}function je(e){return"string"==e?S():"("==e?C(j):C(We,qe,Ue)}function We(e,t){return"{"==e?oe(We,"}"):("variable"==e&&M(t),"*"==t&&(k.marked="keyword"),S(He))}function qe(e){if(","==e)return S(We,qe)}function He(e,t){if("as"==t)return k.marked="keyword",S(We)}function Ue(e,t){if("from"==t)return k.marked="keyword",S(j)}function Be(e){return"]"==e?S():C(ie(W,"]"))}function Ge(){return C(P("form"),we,R("{"),P("}"),ie(Ve,"}"),z,z)}function Ve(){return C(we,ke)}function Ke(e,t,r){return t.tokenize==m&&/^(?:operator|sof|keyword [bcd]|case|new|export|default|spread|[\[{}\(,;:]|=>)$/.test(t.lastType)||"quasi"==t.lastType&&/\{\s*$/.test(e.string.slice(0,e.pos-(r||0)))}return I.lex=!0,z.lex=!0,{startState:function(e){var t={tokenize:m,lastType:"sof",cc:[],lexical:new x((e||0)-o,0,"block",!1),localVars:r.localVars,context:r.localVars&&new A(null,null,!1),indented:e||0};return r.globalVars&&"object"==typeof r.globalVars&&(t.globalVars=r.globalVars),t},token:function(e,t){if(e.sol()&&(t.lexical.hasOwnProperty("align")||(t.lexical.align=!1),t.indented=e.indentation(),b(e,t)),t.tokenize!=g&&e.eatSpace())return null;var r=t.tokenize(e,t);return"comment"==n?r:(t.lastType="operator"!=n||"++"!=i&&"--"!=i?n:"incdec",function(e,t,r,n,i){var o=e.cc;for(k.state=e,k.stream=i,k.marked=null,k.cc=o,k.style=t,e.lexical.hasOwnProperty("align")||(e.lexical.align=!0);;)if((o.length?o.pop():l?j:$)(r,n)){for(;o.length&&o[o.length-1].lex;)o.pop()();return k.marked?k.marked:"variable"==r&&_(e,n)?"variable-2":t}}(t,r,n,i,e))},indent:function(t,n){if(t.tokenize==g)return e.Pass;if(t.tokenize!=m)return 0;var i,s=n&&n.charAt(0),l=t.lexical;if(!/^\s*else\b/.test(n))for(var c=t.cc.length-1;c>=0;--c){var u=t.cc[c];if(u==z)l=l.prev;else if(u!=Se)break}for(;("stat"==l.type||"form"==l.type)&&("}"==s||(i=t.cc[t.cc.length-1])&&(i==B||i==G)&&!/^[,\.=+\-*:?[\(]/.test(n));)l=l.prev;a&&")"==l.type&&"stat"==l.prev.type&&(l=l.prev);var f=l.type,p=s==f;return"vardef"==f?l.indented+("operator"==t.lastType||","==t.lastType?l.info.length+1:0):"form"==f&&"{"==s?l.indented:"form"==f?l.indented+o:"stat"==f?l.indented+(function(e,t){return"operator"==e.lastType||","==e.lastType||d.test(t.charAt(0))||/[,.]/.test(t.charAt(0))}(t,n)?a||o:0):"switch"!=l.info||p||0==r.doubleIndentSwitch?l.align?l.column+(p?0:1):l.indented+(p?0:o):l.indented+(/^(?:case|default)\b/.test(n)?o:2*o)},electricInput:/^\s*(?:case .*?:|default:|\{|\})$/,blockCommentStart:l?null:"/*",blockCommentEnd:l?null:"*/",blockCommentContinue:l?null:" * ",lineComment:l?null:"//",fold:"brace",closeBrackets:"()[]{}''\"\"``",helperType:l?"json":"javascript",jsonldMode:s,jsonMode:l,expressionAllowed:Ke,skipExpression:function(e){var t=e.cc[e.cc.length-1];t!=j&&t!=W||e.cc.pop()}}}),e.registerHelper("wordChars","javascript",/[\w$]/),e.defineMIME("text/javascript","javascript"),e.defineMIME("text/ecmascript","javascript"),e.defineMIME("application/javascript","javascript"),e.defineMIME("application/x-javascript","javascript"),e.defineMIME("application/ecmascript","javascript"),e.defineMIME("application/json",{name:"javascript",json:!0}),e.defineMIME("application/x-json",{name:"javascript",json:!0}),e.defineMIME("application/ld+json",{name:"javascript",jsonld:!0}),e.defineMIME("text/typescript",{name:"javascript",typescript:!0}),e.defineMIME("application/typescript",{name:"javascript",typescript:!0})})(r("8U58"))},"5VQ+":function(e,t,r){"use strict";var n=r("cGG2");e.exports=function(e,t){n.forEach(e,function(r,n){n!==t&&n.toUpperCase()===t.toUpperCase()&&(e[t]=r,delete e[n])})}},"5kR5":function(e,t,r){var n=r("VU/8")(r("hwvL"),r("e1YO"),!1,function(e){r("xAuk"),r("noON")},null,null);e.exports=n.exports},"6S2o":function(e,t,r){(function(e){"use strict";function t(e,t,r,n,i,o){this.indented=e,this.column=t,this.type=r,this.info=n,this.align=i,this.prev=o}function r(e,r,n,i){var o=e.indented;return e.context&&"statement"==e.context.type&&"statement"!=n&&(o=e.context.indented),e.context=new t(o,r,n,i,null,e.context)}function n(e){var t=e.context.type;return")"!=t&&"]"!=t&&"}"!=t||(e.indented=e.context.indented),e.context=e.context.prev}function i(e,t,r){return"variable"==t.prevToken||"type"==t.prevToken||(!!/\S(?:[^- ]>|[*\]])\s*$|\*$/.test(e.string.slice(0,r))||(!(!t.typeAtEndOfLine||e.column()!=e.indentation())||void 0))}function o(e){for(;;){if(!e||"top"==e.type)return!0;if("}"==e.type&&"namespace"!=e.prev.info)return!1;e=e.prev}}function a(e){for(var t={},r=e.split(" "),n=0;n<r.length;++n)t[r[n]]=!0;return t}function s(e,t){return"function"==typeof e?e(t):e.propertyIsEnumerable(t)}e.defineMode("clike",function(a,l){var c,u,f=a.indentUnit,d=l.statementIndentUnit||f,p=l.dontAlignCalls,h=l.keywords||{},m=l.types||{},g=l.builtin||{},v=l.blockKeywords||{},y=l.defKeywords||{},b=l.atoms||{},w=l.hooks||{},x=l.multiLineStrings,_=!1!==l.indentStatements,k=!1!==l.indentSwitch,C=l.namespaceSeparator,S=l.isPunctuationChar||/[\[\]{}\(\),;\:\.]/,T=l.numberStart||/[\d\.]/,M=l.number||/^(?:0x[a-f\d]+|0b[01]+|(?:\d+\.?\d*|\.\d+)(?:e[-+]?\d+)?)(u|ll?|l|f)?/i,L=l.isOperatorChar||/[+\-*&%=<>!?|\/]/,A=l.isIdentifierChar||/[\w\$_\xa1-\uffff]/,O=l.isReservedIdentifier||!1;function N(e,t){var r,n=e.next();if(w[n]){var i=w[n](e,t);if(!1!==i)return i}if('"'==n||"'"==n)return t.tokenize=(r=n,function(e,t){for(var n,i=!1,o=!1;null!=(n=e.next());){if(n==r&&!i){o=!0;break}i=!i&&"\\"==n}return(o||!i&&!x)&&(t.tokenize=null),"string"}),t.tokenize(e,t);if(S.test(n))return c=n,null;if(T.test(n)){if(e.backUp(1),e.match(M))return"number";e.next()}if("/"==n){if(e.eat("*"))return t.tokenize=E,E(e,t);if(e.eat("/"))return e.skipToEnd(),"comment"}if(L.test(n)){for(;!e.match(/^\/[\/*]/,!1)&&e.eat(L););return"operator"}if(e.eatWhile(A),C)for(;e.match(C);)e.eatWhile(A);var o=e.current();return s(h,o)?(s(v,o)&&(c="newstatement"),s(y,o)&&(u=!0),"keyword"):s(m,o)?"type":s(g,o)||O&&O(o)?(s(v,o)&&(c="newstatement"),"builtin"):s(b,o)?"atom":"variable"}function E(e,t){for(var r,n=!1;r=e.next();){if("/"==r&&n){t.tokenize=null;break}n="*"==r}return"comment"}function D(e,t){l.typeFirstDefinitions&&e.eol()&&o(t.context)&&(t.typeAtEndOfLine=i(e,t,e.pos))}return{startState:function(e){return{tokenize:null,context:new t((e||0)-f,0,"top",null,!1),indented:0,startOfLine:!0,prevToken:null}},token:function(e,t){var a=t.context;if(e.sol()&&(null==a.align&&(a.align=!1),t.indented=e.indentation(),t.startOfLine=!0),e.eatSpace())return D(e,t),null;c=u=null;var s=(t.tokenize||N)(e,t);if("comment"==s||"meta"==s)return s;if(null==a.align&&(a.align=!0),";"==c||":"==c||","==c&&e.match(/^\s*(?:\/\/.*)?$/,!1))for(;"statement"==t.context.type;)n(t);else if("{"==c)r(t,e.column(),"}");else if("["==c)r(t,e.column(),"]");else if("("==c)r(t,e.column(),")");else if("}"==c){for(;"statement"==a.type;)a=n(t);for("}"==a.type&&(a=n(t));"statement"==a.type;)a=n(t)}else c==a.type?n(t):_&&(("}"==a.type||"top"==a.type)&&";"!=c||"statement"==a.type&&"newstatement"==c)&&r(t,e.column(),"statement",e.current());if("variable"==s&&("def"==t.prevToken||l.typeFirstDefinitions&&i(e,t,e.start)&&o(t.context)&&e.match(/^\s*\(/,!1))&&(s="def"),w.token){var f=w.token(e,t,s);void 0!==f&&(s=f)}return"def"==s&&!1===l.styleDefs&&(s="variable"),t.startOfLine=!1,t.prevToken=u?"def":s||c,D(e,t),s},indent:function(t,r){if(t.tokenize!=N&&null!=t.tokenize||t.typeAtEndOfLine)return e.Pass;var n=t.context,i=r&&r.charAt(0),o=i==n.type;if("statement"==n.type&&"}"==i&&(n=n.prev),l.dontIndentStatements)for(;"statement"==n.type&&l.dontIndentStatements.test(n.info);)n=n.prev;if(w.indent){var a=w.indent(t,n,r,f);if("number"==typeof a)return a}var s=n.prev&&"switch"==n.prev.info;if(l.allmanIndentation&&/[{(]/.test(i)){for(;"top"!=n.type&&"}"!=n.type;)n=n.prev;return n.indented}return"statement"==n.type?n.indented+("{"==i?0:d):!n.align||p&&")"==n.type?")"!=n.type||o?n.indented+(o?0:f)+(o||!s||/^(?:case|default)\b/.test(r)?0:f):n.indented+d:n.column+(o?0:1)},electricInput:k?/^\s*(?:case .*?:|default:|\{\}?|\})$/:/^\s*[{}]$/,blockCommentStart:"/*",blockCommentEnd:"*/",blockCommentContinue:" * ",lineComment:"//",fold:"brace"}});var l="auto if break case register continue return default do sizeof static else struct switch extern typedef union for goto while enum const volatile inline restrict asm fortran",c=a("int long char short double float unsigned signed void bool"),u=a("SEL instancetype id Class Protocol BOOL");function f(e){return s(c,e)||/.+_t$/.test(e)}var d="case do else for if switch while struct enum union";function p(e,t){if(!t.startOfLine)return!1;for(var r,n=null;r=e.peek();){if("\\"==r&&e.match(/^.$/)){n=p;break}if("/"==r&&e.match(/^\/[\/\*]/,!1))break;e.next()}return t.tokenize=n,"meta"}function h(e,t){return"type"==t.prevToken&&"type"}function m(e){return!(!e||e.length<2)&&("_"==e[0]&&("_"==e[1]||e[1]!==e[1].toLowerCase()))}function g(e){return e.eatWhile(/[\w\.']/),"number"}function v(e,t){if(e.backUp(1),e.match(/(R|u8R|uR|UR|LR)/)){var r=e.match(/"([^\s\\()]{0,16})\(/);return!!r&&(t.cpp11RawStringDelim=r[1],t.tokenize=b,b(e,t))}return e.match(/(u8|u|U|L)/)?!!e.match(/["']/,!1)&&"string":(e.next(),!1)}function y(e,t){for(var r;null!=(r=e.next());)if('"'==r&&!e.eat('"')){t.tokenize=null;break}return"string"}function b(e,t){var r=t.cpp11RawStringDelim.replace(/[^\w\s]/g,"\\$&");return e.match(new RegExp(".*?\\)"+r+'"'))?t.tokenize=null:e.skipToEnd(),"string"}function w(t,r){"string"==typeof t&&(t=[t]);var n=[];function i(e){if(e)for(var t in e)e.hasOwnProperty(t)&&n.push(t)}i(r.keywords),i(r.types),i(r.builtin),i(r.atoms),n.length&&(r.helperType=t[0],e.registerHelper("hintWords",t[0],n));for(var o=0;o<t.length;++o)e.defineMIME(t[o],r)}function x(e,t){for(var r=!1;!e.eol();){if(!r&&e.match('"""')){t.tokenize=null;break}r="\\"==e.next()&&!r}return"string"}w(["text/x-csrc","text/x-c","text/x-chdr"],{name:"clike",keywords:a(l),types:f,blockKeywords:a(d),defKeywords:a("struct enum union"),typeFirstDefinitions:!0,atoms:a("NULL true false"),isReservedIdentifier:m,hooks:{"#":p,"*":h},modeProps:{fold:["brace","include"]}}),w(["text/x-c++src","text/x-c++hdr"],{name:"clike",keywords:a(l+"alignas alignof and and_eq audit axiom bitand bitor catch class compl concept constexpr const_cast decltype delete dynamic_cast explicit export final friend import module mutable namespace new noexcept not not_eq operator or or_eq override private protected public reinterpret_cast requires static_assert static_cast template this thread_local throw try typeid typename using virtual xor xor_eq"),types:f,blockKeywords:a(d+" class try catch"),defKeywords:a("struct enum union class namespace"),typeFirstDefinitions:!0,atoms:a("true false NULL nullptr"),dontIndentStatements:/^template$/,isIdentifierChar:/[\w\$_~\xa1-\uffff]/,isReservedIdentifier:m,hooks:{"#":p,"*":h,u:v,U:v,L:v,R:v,0:g,1:g,2:g,3:g,4:g,5:g,6:g,7:g,8:g,9:g,token:function(e,t,r){if("variable"==r&&"("==e.peek()&&(";"==t.prevToken||null==t.prevToken||"}"==t.prevToken)&&(n=e.current(),(i=/(\w+)::~?(\w+)$/.exec(n))&&i[1]==i[2]))return"def";var n,i}},namespaceSeparator:"::",modeProps:{fold:["brace","include"]}}),w("text/x-java",{name:"clike",keywords:a("abstract assert break case catch class const continue default do else enum extends final finally for goto if implements import instanceof interface native new package private protected public return static strictfp super switch synchronized this throw throws transient try volatile while @interface"),types:a("byte short int long float double boolean char void Boolean Byte Character Double Float Integer Long Number Object Short String StringBuffer StringBuilder Void"),blockKeywords:a("catch class do else finally for if switch try while"),defKeywords:a("class interface enum @interface"),typeFirstDefinitions:!0,atoms:a("true false null"),number:/^(?:0x[a-f\d_]+|0b[01_]+|(?:[\d_]+\.?\d*|\.\d+)(?:e[-+]?[\d_]+)?)(u|ll?|l|f)?/i,hooks:{"@":function(e){return!e.match("interface",!1)&&(e.eatWhile(/[\w\$_]/),"meta")}},modeProps:{fold:["brace","import"]}}),w("text/x-csharp",{name:"clike",keywords:a("abstract as async await base break case catch checked class const continue default delegate do else enum event explicit extern finally fixed for foreach goto if implicit in interface internal is lock namespace new operator out override params private protected public readonly ref return sealed sizeof stackalloc static struct switch this throw try typeof unchecked unsafe using virtual void volatile while add alias ascending descending dynamic from get global group into join let orderby partial remove select set value var yield"),types:a("Action Boolean Byte Char DateTime DateTimeOffset Decimal Double Func Guid Int16 Int32 Int64 Object SByte Single String Task TimeSpan UInt16 UInt32 UInt64 bool byte char decimal double short int long object sbyte float string ushort uint ulong"),blockKeywords:a("catch class do else finally for foreach if struct switch try while"),defKeywords:a("class interface namespace struct var"),typeFirstDefinitions:!0,atoms:a("true false null"),hooks:{"@":function(e,t){return e.eat('"')?(t.tokenize=y,y(e,t)):(e.eatWhile(/[\w\$_]/),"meta")}}}),w("text/x-scala",{name:"clike",keywords:a("abstract case catch class def do else extends final finally for forSome if implicit import lazy match new null object override package private protected return sealed super this throw trait try type val var while with yield _ assert assume require print println printf readLine readBoolean readByte readShort readChar readInt readLong readFloat readDouble"),types:a("AnyVal App Application Array BufferedIterator BigDecimal BigInt Char Console Either Enumeration Equiv Error Exception Fractional Function IndexedSeq Int Integral Iterable Iterator List Map Numeric Nil NotNull Option Ordered Ordering PartialFunction PartialOrdering Product Proxy Range Responder Seq Serializable Set Specializable Stream StringBuilder StringContext Symbol Throwable Traversable TraversableOnce Tuple Unit Vector Boolean Byte Character CharSequence Class ClassLoader Cloneable Comparable Compiler Double Exception Float Integer Long Math Number Object Package Pair Process Runtime Runnable SecurityManager Short StackTraceElement StrictMath String StringBuffer System Thread ThreadGroup ThreadLocal Throwable Triple Void"),multiLineStrings:!0,blockKeywords:a("catch class enum do else finally for forSome if match switch try while"),defKeywords:a("class enum def object package trait type val var"),atoms:a("true false null"),indentStatements:!1,indentSwitch:!1,isOperatorChar:/[+\-*&%=<>!?|\/#:@]/,hooks:{"@":function(e){return e.eatWhile(/[\w\$_]/),"meta"},'"':function(e,t){return!!e.match('""')&&(t.tokenize=x,t.tokenize(e,t))},"'":function(e){return e.eatWhile(/[\w\$_\xa1-\uffff]/),"atom"},"=":function(e,r){var n=r.context;return!("}"!=n.type||!n.align||!e.eat(">"))&&(r.context=new t(n.indented,n.column,n.type,n.info,null,n.prev),"operator")},"/":function(e,t){return!!e.eat("*")&&(t.tokenize=function e(t){return function(r,n){for(var i;i=r.next();){if("*"==i&&r.eat("/")){if(1==t){n.tokenize=null;break}return n.tokenize=e(t-1),n.tokenize(r,n)}if("/"==i&&r.eat("*"))return n.tokenize=e(t+1),n.tokenize(r,n)}return"comment"}}(1),t.tokenize(e,t))}},modeProps:{closeBrackets:{pairs:'()[]{}""',triples:'"'}}}),w("text/x-kotlin",{name:"clike",keywords:a("package as typealias class interface this super val operator var fun for is in This throw return annotation break continue object if else while do try when !in !is as? file import where by get set abstract enum open inner override private public internal protected catch finally out final vararg reified dynamic companion constructor init sealed field property receiver param sparam lateinit data inline noinline tailrec external annotation crossinline const operator infix suspend actual expect setparam"),types:a("Boolean Byte Character CharSequence Class ClassLoader Cloneable Comparable Compiler Double Exception Float Integer Long Math Number Object Package Pair Process Runtime Runnable SecurityManager Short StackTraceElement StrictMath String StringBuffer System Thread ThreadGroup ThreadLocal Throwable Triple Void Annotation Any BooleanArray ByteArray Char CharArray DeprecationLevel DoubleArray Enum FloatArray Function Int IntArray Lazy LazyThreadSafetyMode LongArray Nothing ShortArray Unit"),intendSwitch:!1,indentStatements:!1,multiLineStrings:!0,number:/^(?:0x[a-f\d_]+|0b[01_]+|(?:[\d_]+(\.\d+)?|\.\d+)(?:e[-+]?[\d_]+)?)(u|ll?|l|f)?/i,blockKeywords:a("catch class do else finally for if where try while enum"),defKeywords:a("class val var object interface fun"),atoms:a("true false null this"),hooks:{"@":function(e){return e.eatWhile(/[\w\$_]/),"meta"},"*":function(e,t){return"."==t.prevToken?"variable":"operator"},'"':function(e,t){var r;return t.tokenize=(r=e.match('""'),function(e,t){for(var n,i=!1,o=!1;!e.eol();){if(!r&&!i&&e.match('"')){o=!0;break}if(r&&e.match('"""')){o=!0;break}n=e.next(),!i&&"$"==n&&e.match("{")&&e.skipTo("}"),i=!i&&"\\"==n&&!r}return!o&&r||(t.tokenize=null),"string"}),t.tokenize(e,t)},indent:function(e,t,r,n){var i=r&&r.charAt(0);return"}"!=e.prevToken&&")"!=e.prevToken||""!=r?"operator"==e.prevToken&&"}"!=r||"variable"==e.prevToken&&"."==i||("}"==e.prevToken||")"==e.prevToken)&&"."==i?2*n+t.indented:t.align&&"}"==t.type?t.indented+(e.context.type==(r||"").charAt(0)?0:n):void 0:e.indented}},modeProps:{closeBrackets:{triples:'"'}}}),w(["x-shader/x-vertex","x-shader/x-fragment"],{name:"clike",keywords:a("sampler1D sampler2D sampler3D samplerCube sampler1DShadow sampler2DShadow const attribute uniform varying break continue discard return for while do if else struct in out inout"),types:a("float int bool void vec2 vec3 vec4 ivec2 ivec3 ivec4 bvec2 bvec3 bvec4 mat2 mat3 mat4"),blockKeywords:a("for while do if else struct"),builtin:a("radians degrees sin cos tan asin acos atan pow exp log exp2 sqrt inversesqrt abs sign floor ceil fract mod min max clamp mix step smoothstep length distance dot cross normalize ftransform faceforward reflect refract matrixCompMult lessThan lessThanEqual greaterThan greaterThanEqual equal notEqual any all not texture1D texture1DProj texture1DLod texture1DProjLod texture2D texture2DProj texture2DLod texture2DProjLod texture3D texture3DProj texture3DLod texture3DProjLod textureCube textureCubeLod shadow1D shadow2D shadow1DProj shadow2DProj shadow1DLod shadow2DLod shadow1DProjLod shadow2DProjLod dFdx dFdy fwidth noise1 noise2 noise3 noise4"),atoms:a("true false gl_FragColor gl_SecondaryColor gl_Normal gl_Vertex gl_MultiTexCoord0 gl_MultiTexCoord1 gl_MultiTexCoord2 gl_MultiTexCoord3 gl_MultiTexCoord4 gl_MultiTexCoord5 gl_MultiTexCoord6 gl_MultiTexCoord7 gl_FogCoord gl_PointCoord gl_Position gl_PointSize gl_ClipVertex gl_FrontColor gl_BackColor gl_FrontSecondaryColor gl_BackSecondaryColor gl_TexCoord gl_FogFragCoord gl_FragCoord gl_FrontFacing gl_FragData gl_FragDepth gl_ModelViewMatrix gl_ProjectionMatrix gl_ModelViewProjectionMatrix gl_TextureMatrix gl_NormalMatrix gl_ModelViewMatrixInverse gl_ProjectionMatrixInverse gl_ModelViewProjectionMatrixInverse gl_TexureMatrixTranspose gl_ModelViewMatrixInverseTranspose gl_ProjectionMatrixInverseTranspose gl_ModelViewProjectionMatrixInverseTranspose gl_TextureMatrixInverseTranspose gl_NormalScale gl_DepthRange gl_ClipPlane gl_Point gl_FrontMaterial gl_BackMaterial gl_LightSource gl_LightModel gl_FrontLightModelProduct gl_BackLightModelProduct gl_TextureColor gl_EyePlaneS gl_EyePlaneT gl_EyePlaneR gl_EyePlaneQ gl_FogParameters gl_MaxLights gl_MaxClipPlanes gl_MaxTextureUnits gl_MaxTextureCoords gl_MaxVertexAttribs gl_MaxVertexUniformComponents gl_MaxVaryingFloats gl_MaxVertexTextureImageUnits gl_MaxTextureImageUnits gl_MaxFragmentUniformComponents gl_MaxCombineTextureImageUnits gl_MaxDrawBuffers"),indentSwitch:!1,hooks:{"#":p},modeProps:{fold:["brace","include"]}}),w("text/x-nesc",{name:"clike",keywords:a(l+" as atomic async call command component components configuration event generic implementation includes interface module new norace nx_struct nx_union post provides signal task uses abstract extends"),types:f,blockKeywords:a(d),atoms:a("null true false"),hooks:{"#":p},modeProps:{fold:["brace","include"]}}),w("text/x-objectivec",{name:"clike",keywords:a(l+" bycopy byref in inout oneway out self super atomic nonatomic retain copy readwrite readonly strong weak assign typeof nullable nonnull null_resettable _cmd @interface @implementation @end @protocol @encode @property @synthesize @dynamic @class @public @package @private @protected @required @optional @try @catch @finally @import @selector @encode @defs @synchronized @autoreleasepool @compatibility_alias @available"),types:function(e){return f(e)||s(u,e)},builtin:a("FOUNDATION_EXPORT FOUNDATION_EXTERN NS_INLINE NS_FORMAT_FUNCTION NS_RETURNS_RETAINED NS_ERROR_ENUM NS_RETURNS_NOT_RETAINED NS_RETURNS_INNER_POINTER NS_DESIGNATED_INITIALIZER NS_ENUM NS_OPTIONS NS_REQUIRES_NIL_TERMINATION NS_ASSUME_NONNULL_BEGIN NS_ASSUME_NONNULL_END NS_SWIFT_NAME NS_REFINED_FOR_SWIFT"),blockKeywords:a(d+" @synthesize @try @catch @finally @autoreleasepool @synchronized"),defKeywords:a("struct enum union @interface @implementation @protocol @class"),dontIndentStatements:/^@.*$/,typeFirstDefinitions:!0,atoms:a("YES NO NULL Nil nil true false nullptr"),isReservedIdentifier:m,hooks:{"#":p,"*":h},modeProps:{fold:["brace","include"]}}),w("text/x-squirrel",{name:"clike",keywords:a("base break clone continue const default delete enum extends function in class foreach local resume return this throw typeof yield constructor instanceof static"),types:f,blockKeywords:a("case catch class else for foreach if switch try while"),defKeywords:a("function local class"),typeFirstDefinitions:!0,atoms:a("true false null"),hooks:{"#":p},modeProps:{fold:["brace","include"]}});var _=null;w("text/x-ceylon",{name:"clike",keywords:a("abstracts alias assembly assert assign break case catch class continue dynamic else exists extends finally for function given if import in interface is let module new nonempty object of out outer package return satisfies super switch then this throw try value void while"),types:function(e){var t=e.charAt(0);return t===t.toUpperCase()&&t!==t.toLowerCase()},blockKeywords:a("case catch class dynamic else finally for function if interface module new object switch try while"),defKeywords:a("class dynamic function interface module object package value"),builtin:a("abstract actual aliased annotation by default deprecated doc final formal late license native optional sealed see serializable shared suppressWarnings tagged throws variable"),isPunctuationChar:/[\[\]{}\(\),;\:\.`]/,isOperatorChar:/[+\-*&%=<>!?|^~:\/]/,numberStart:/[\d#$]/,number:/^(?:#[\da-fA-F_]+|\$[01_]+|[\d_]+[kMGTPmunpf]?|[\d_]+\.[\d_]+(?:[eE][-+]?\d+|[kMGTPmunpf]|)|)/i,multiLineStrings:!0,typeFirstDefinitions:!0,atoms:a("true false null larger smaller equal empty finished"),indentSwitch:!1,styleDefs:!1,hooks:{"@":function(e){return e.eatWhile(/[\w\$_]/),"meta"},'"':function(e,t){return t.tokenize=function e(t){return function(r,n){for(var i,o=!1,a=!1;!r.eol();){if(!o&&r.match('"')&&("single"==t||r.match('""'))){a=!0;break}if(!o&&r.match("``")){_=e(t),a=!0;break}i=r.next(),o="single"==t&&!o&&"\\"==i}return a&&(n.tokenize=null),"string"}}(e.match('""')?"triple":"single"),t.tokenize(e,t)},"`":function(e,t){return!(!_||!e.match("`"))&&(t.tokenize=_,_=null,t.tokenize(e,t))},"'":function(e){return e.eatWhile(/[\w\$_\xa1-\uffff]/),"atom"},token:function(e,t,r){if(("variable"==r||"type"==r)&&"."==t.prevToken)return"variable-2"}},modeProps:{fold:["brace","import"],closeBrackets:{triples:'"'}}})})(r("8U58"))},"7GwW":function(e,t,r){"use strict";var n=r("cGG2"),i=r("21It"),o=r("p1b6"),a=r("DQCr"),s=r("Oi+a"),l=r("oJlt"),c=r("GHBc"),u=r("FtD3");e.exports=function(e){return new Promise(function(t,r){var f=e.data,d=e.headers;n.isFormData(f)&&delete d["Content-Type"];var p=new XMLHttpRequest;if(e.auth){var h=e.auth.username||"",m=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";d.Authorization="Basic "+btoa(h+":"+m)}var g=s(e.baseURL,e.url);if(p.open(e.method.toUpperCase(),a(g,e.params,e.paramsSerializer),!0),p.timeout=e.timeout,p.onreadystatechange=function(){if(p&&4===p.readyState&&(0!==p.status||p.responseURL&&0===p.responseURL.indexOf("file:"))){var n="getAllResponseHeaders"in p?l(p.getAllResponseHeaders()):null,o={data:e.responseType&&"text"!==e.responseType?p.response:p.responseText,status:p.status,statusText:p.statusText,headers:n,config:e,request:p};i(t,r,o),p=null}},p.onabort=function(){p&&(r(u("Request aborted",e,"ECONNABORTED",p)),p=null)},p.onerror=function(){r(u("Network Error",e,null,p)),p=null},p.ontimeout=function(){var t="timeout of "+e.timeout+"ms exceeded";e.timeoutErrorMessage&&(t=e.timeoutErrorMessage),r(u(t,e,"ECONNABORTED",p)),p=null},n.isStandardBrowserEnv()){var v=(e.withCredentials||c(g))&&e.xsrfCookieName?o.read(e.xsrfCookieName):void 0;v&&(d[e.xsrfHeaderName]=v)}if("setRequestHeader"in p&&n.forEach(d,function(e,t){void 0===f&&"content-type"===t.toLowerCase()?delete d[t]:p.setRequestHeader(t,e)}),n.isUndefined(e.withCredentials)||(p.withCredentials=!!e.withCredentials),e.responseType)try{p.responseType=e.responseType}catch(t){if("json"!==e.responseType)throw t}"function"==typeof e.onDownloadProgress&&p.addEventListener("progress",e.onDownloadProgress),"function"==typeof e.onUploadProgress&&p.upload&&p.upload.addEventListener("progress",e.onUploadProgress),e.cancelToken&&e.cancelToken.promise.then(function(e){p&&(p.abort(),r(e),p=null)}),f||(f=null),p.send(f)})}},"8Nur":function(e,t,r){(function(e){"use strict";var t={script:[["lang",/(javascript|babel)/i,"javascript"],["type",/^(?:text|application)\/(?:x-)?(?:java|ecma)script$|^module$|^$/i,"javascript"],["type",/./,"text/plain"],[null,null,"javascript"]],style:[["lang",/^css$/i,"css"],["type",/^(text\/)?(x-)?(stylesheet|css)$/i,"css"],["type",/./,"text/plain"],[null,null,"css"]]};var r={};function n(e,t){var n=e.match(function(e){var t=r[e];return t||(r[e]=new RegExp("\\s+"+e+"\\s*=\\s*('|\")?([^'\"]+)('|\")?\\s*"))}(t));return n?/^\s*(.*?)\s*$/.exec(n[2])[1]:""}function i(e,t){return new RegExp((t?"^":"")+"</s*"+e+"s*>","i")}function o(e,t){for(var r in e)for(var n=t[r]||(t[r]=[]),i=e[r],o=i.length-1;o>=0;o--)n.unshift(i[o])}e.defineMode("htmlmixed",function(r,a){var s=e.getMode(r,{name:"xml",htmlMode:!0,multilineTagIndentFactor:a.multilineTagIndentFactor,multilineTagIndentPastTag:a.multilineTagIndentPastTag}),l={},c=a&&a.tags,u=a&&a.scriptTypes;if(o(t,l),c&&o(c,l),u)for(var f=u.length-1;f>=0;f--)l.script.unshift(["type",u[f].matches,u[f].mode]);function d(t,o){var a,c=s.token(t,o.htmlState),u=/\btag\b/.test(c);if(u&&!/[<>\s\/]/.test(t.current())&&(a=o.htmlState.tagName&&o.htmlState.tagName.toLowerCase())&&l.hasOwnProperty(a))o.inTag=a+" ";else if(o.inTag&&u&&/>$/.test(t.current())){var f=/^([\S]+) (.*)/.exec(o.inTag);o.inTag=null;var p=">"==t.current()&&function(e,t){for(var r=0;r<e.length;r++){var i=e[r];if(!i[0]||i[1].test(n(t,i[0])))return i[2]}}(l[f[1]],f[2]),h=e.getMode(r,p),m=i(f[1],!0),g=i(f[1],!1);o.token=function(e,t){return e.match(m,!1)?(t.token=d,t.localState=t.localMode=null,null):function(e,t,r){var n=e.current(),i=n.search(t);return i>-1?e.backUp(n.length-i):n.match(/<\/?$/)&&(e.backUp(n.length),e.match(t,!1)||e.match(n)),r}(e,g,t.localMode.token(e,t.localState))},o.localMode=h,o.localState=e.startState(h,s.indent(o.htmlState,"",""))}else o.inTag&&(o.inTag+=t.current(),t.eol()&&(o.inTag+=" "));return c}return{startState:function(){return{token:d,inTag:null,localMode:null,localState:null,htmlState:e.startState(s)}},copyState:function(t){var r;return t.localState&&(r=e.copyState(t.localMode,t.localState)),{token:t.token,inTag:t.inTag,localMode:t.localMode,localState:r,htmlState:e.copyState(s,t.htmlState)}},token:function(e,t){return t.token(e,t)},indent:function(t,r,n){return!t.localMode||/^\s*<\//.test(r)?s.indent(t.htmlState,r,n):t.localMode.indent?t.localMode.indent(t.localState,r,n):e.Pass},innerMode:function(e){return{state:e.localState||e.htmlState,mode:e.localMode||s}}}},"xml","javascript","css"),e.defineMIME("text/html","htmlmixed")})(r("8U58"),r("ezqs"),r("5IAE"),r("puAj"))},"8U58":function(e,t,r){var n;n=function(){"use strict";var e=navigator.userAgent,t=navigator.platform,r=/gecko\/\d/i.test(e),n=/MSIE \d/.test(e),i=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(e),o=/Edge\/(\d+)/.exec(e),a=n||i||o,s=a&&(n?document.documentMode||6:+(o||i)[1]),l=!o&&/WebKit\//.test(e),c=l&&/Qt\/\d+\.\d+/.test(e),u=!o&&/Chrome\//.test(e),f=/Opera\//.test(e),d=/Apple Computer/.test(navigator.vendor),p=/Mac OS X 1\d\D([8-9]|\d\d)\D/.test(e),h=/PhantomJS/.test(e),m=!o&&/AppleWebKit/.test(e)&&/Mobile\/\w+/.test(e),g=/Android/.test(e),v=m||g||/webOS|BlackBerry|Opera Mini|Opera Mobi|IEMobile/i.test(e),y=m||/Mac/.test(t),b=/\bCrOS\b/.test(e),w=/win/i.test(t),x=f&&e.match(/Version\/(\d*\.\d*)/);x&&(x=Number(x[1])),x&&x>=15&&(f=!1,l=!0);var _=y&&(c||f&&(null==x||x<12.11)),k=r||a&&s>=9;function C(e){return new RegExp("(^|\\s)"+e+"(?:$|\\s)\\s*")}var S,T=function(e,t){var r=e.className,n=C(t).exec(r);if(n){var i=r.slice(n.index+n[0].length);e.className=r.slice(0,n.index)+(i?n[1]+i:"")}};function M(e){for(var t=e.childNodes.length;t>0;--t)e.removeChild(e.firstChild);return e}function L(e,t){return M(e).appendChild(t)}function A(e,t,r,n){var i=document.createElement(e);if(r&&(i.className=r),n&&(i.style.cssText=n),"string"==typeof t)i.appendChild(document.createTextNode(t));else if(t)for(var o=0;o<t.length;++o)i.appendChild(t[o]);return i}function O(e,t,r,n){var i=A(e,t,r,n);return i.setAttribute("role","presentation"),i}function N(e,t){if(3==t.nodeType&&(t=t.parentNode),e.contains)return e.contains(t);do{if(11==t.nodeType&&(t=t.host),t==e)return!0}while(t=t.parentNode)}function E(){var e;try{e=document.activeElement}catch(t){e=document.body||null}for(;e&&e.shadowRoot&&e.shadowRoot.activeElement;)e=e.shadowRoot.activeElement;return e}function D(e,t){var r=e.className;C(t).test(r)||(e.className+=(r?" ":"")+t)}function I(e,t){for(var r=e.split(" "),n=0;n<r.length;n++)r[n]&&!C(r[n]).test(t)&&(t+=" "+r[n]);return t}S=document.createRange?function(e,t,r,n){var i=document.createRange();return i.setEnd(n||e,r),i.setStart(e,t),i}:function(e,t,r){var n=document.body.createTextRange();try{n.moveToElementText(e.parentNode)}catch(e){return n}return n.collapse(!0),n.moveEnd("character",r),n.moveStart("character",t),n};var P=function(e){e.select()};function z(e){var t=Array.prototype.slice.call(arguments,1);return function(){return e.apply(null,t)}}function R(e,t,r){for(var n in t||(t={}),e)!e.hasOwnProperty(n)||!1===r&&t.hasOwnProperty(n)||(t[n]=e[n]);return t}function $(e,t,r,n,i){null==t&&-1==(t=e.search(/[^\s\u00a0]/))&&(t=e.length);for(var o=n||0,a=i||0;;){var s=e.indexOf("\t",o);if(s<0||s>=t)return a+(t-o);a+=s-o,a+=r-a%r,o=s+1}}m?P=function(e){e.selectionStart=0,e.selectionEnd=e.value.length}:a&&(P=function(e){try{e.select()}catch(e){}});var F=function(){this.id=null};function j(e,t){for(var r=0;r<e.length;++r)if(e[r]==t)return r;return-1}F.prototype.set=function(e,t){clearTimeout(this.id),this.id=setTimeout(t,e)};var W=30,q={toString:function(){return"CodeMirror.Pass"}},H={scroll:!1},U={origin:"*mouse"},B={origin:"+move"};function G(e,t,r){for(var n=0,i=0;;){var o=e.indexOf("\t",n);-1==o&&(o=e.length);var a=o-n;if(o==e.length||i+a>=t)return n+Math.min(a,t-i);if(i+=o-n,n=o+1,(i+=r-i%r)>=t)return n}}var V=[""];function K(e){for(;V.length<=e;)V.push(X(V)+" ");return V[e]}function X(e){return e[e.length-1]}function Y(e,t){for(var r=[],n=0;n<e.length;n++)r[n]=t(e[n],n);return r}function Z(){}function J(e,t){var r;return Object.create?r=Object.create(e):(Z.prototype=e,r=new Z),t&&R(t,r),r}var Q=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;function ee(e){return/\w/.test(e)||e>""&&(e.toUpperCase()!=e.toLowerCase()||Q.test(e))}function te(e,t){return t?!!(t.source.indexOf("\\w")>-1&&ee(e))||t.test(e):ee(e)}function re(e){for(var t in e)if(e.hasOwnProperty(t)&&e[t])return!1;return!0}var ne=/[\u0300-\u036f\u0483-\u0489\u0591-\u05bd\u05bf\u05c1\u05c2\u05c4\u05c5\u05c7\u0610-\u061a\u064b-\u065e\u0670\u06d6-\u06dc\u06de-\u06e4\u06e7\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0900-\u0902\u093c\u0941-\u0948\u094d\u0951-\u0955\u0962\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2\u09e3\u0a01\u0a02\u0a3c\u0a41\u0a42\u0a47\u0a48\u0a4b-\u0a4d\u0a51\u0a70\u0a71\u0a75\u0a81\u0a82\u0abc\u0ac1-\u0ac5\u0ac7\u0ac8\u0acd\u0ae2\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55\u0c56\u0c62\u0c63\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc\u0ccd\u0cd5\u0cd6\u0ce2\u0ce3\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb\u0ebc\u0ec8-\u0ecd\u0f18\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86\u0f87\u0f90-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039\u103a\u103d\u103e\u1058\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085\u1086\u108d\u109d\u135f\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193b\u1a17\u1a18\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80\u1b81\u1ba2-\u1ba5\u1ba8\u1ba9\u1c2c-\u1c33\u1c36\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1dc0-\u1de6\u1dfd-\u1dff\u200c\u200d\u20d0-\u20f0\u2cef-\u2cf1\u2de0-\u2dff\u302a-\u302f\u3099\u309a\ua66f-\ua672\ua67c\ua67d\ua6f0\ua6f1\ua802\ua806\ua80b\ua825\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\uaa29-\uaa2e\uaa31\uaa32\uaa35\uaa36\uaa43\uaa4c\uaab0\uaab2-\uaab4\uaab7\uaab8\uaabe\uaabf\uaac1\uabe5\uabe8\uabed\udc00-\udfff\ufb1e\ufe00-\ufe0f\ufe20-\ufe26\uff9e\uff9f]/;function ie(e){return e.charCodeAt(0)>=768&&ne.test(e)}function oe(e,t,r){for(;(r<0?t>0:t<e.length)&&ie(e.charAt(t));)t+=r;return t}function ae(e,t,r){for(var n=t>r?-1:1;;){if(t==r)return t;var i=(t+r)/2,o=n<0?Math.ceil(i):Math.floor(i);if(o==t)return e(o)?t:r;e(o)?r=o:t=o+n}}var se=null;function le(e,t,r){var n;se=null;for(var i=0;i<e.length;++i){var o=e[i];if(o.from<t&&o.to>t)return i;o.to==t&&(o.from!=o.to&&"before"==r?n=i:se=i),o.from==t&&(o.from!=o.to&&"before"!=r?n=i:se=i)}return null!=n?n:se}var ce=function(){var e="bbbbbbbbbtstwsbbbbbbbbbbbbbbssstwNN%%%NNNNNN,N,N1111111111NNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNbbbbbbsbbbbbbbbbbbbbbbbbbbbbbbbbb,N%%%%NNNNLNNNNN%%11NLNNN1LNNNNNLLLLLLLLLLLLLLLLLLLLLLLNLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLN",t="nnnnnnNNr%%r,rNNmmmmmmmmmmmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmmmmmmmmmmmmmmmnnnnnnnnnn%nnrrrmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmnNmmmmmmrrmmNmmmmrr1111111111";var r=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/,n=/[stwN]/,i=/[LRr]/,o=/[Lb1n]/,a=/[1n]/;function s(e,t,r){this.level=e,this.from=t,this.to=r}return function(l,c){var u="ltr"==c?"L":"R";if(0==l.length||"ltr"==c&&!r.test(l))return!1;for(var f,d=l.length,p=[],h=0;h<d;++h)p.push((f=l.charCodeAt(h))<=247?e.charAt(f):1424<=f&&f<=1524?"R":1536<=f&&f<=1785?t.charAt(f-1536):1774<=f&&f<=2220?"r":8192<=f&&f<=8203?"w":8204==f?"b":"L");for(var m=0,g=u;m<d;++m){var v=p[m];"m"==v?p[m]=g:g=v}for(var y=0,b=u;y<d;++y){var w=p[y];"1"==w&&"r"==b?p[y]="n":i.test(w)&&(b=w,"r"==w&&(p[y]="R"))}for(var x=1,_=p[0];x<d-1;++x){var k=p[x];"+"==k&&"1"==_&&"1"==p[x+1]?p[x]="1":","!=k||_!=p[x+1]||"1"!=_&&"n"!=_||(p[x]=_),_=k}for(var C=0;C<d;++C){var S=p[C];if(","==S)p[C]="N";else if("%"==S){var T=void 0;for(T=C+1;T<d&&"%"==p[T];++T);for(var M=C&&"!"==p[C-1]||T<d&&"1"==p[T]?"1":"N",L=C;L<T;++L)p[L]=M;C=T-1}}for(var A=0,O=u;A<d;++A){var N=p[A];"L"==O&&"1"==N?p[A]="L":i.test(N)&&(O=N)}for(var E=0;E<d;++E)if(n.test(p[E])){var D=void 0;for(D=E+1;D<d&&n.test(p[D]);++D);for(var I="L"==(E?p[E-1]:u),P=I==("L"==(D<d?p[D]:u))?I?"L":"R":u,z=E;z<D;++z)p[z]=P;E=D-1}for(var R,$=[],F=0;F<d;)if(o.test(p[F])){var j=F;for(++F;F<d&&o.test(p[F]);++F);$.push(new s(0,j,F))}else{var W=F,q=$.length;for(++F;F<d&&"L"!=p[F];++F);for(var H=W;H<F;)if(a.test(p[H])){W<H&&$.splice(q,0,new s(1,W,H));var U=H;for(++H;H<F&&a.test(p[H]);++H);$.splice(q,0,new s(2,U,H)),W=H}else++H;W<F&&$.splice(q,0,new s(1,W,F))}return"ltr"==c&&(1==$[0].level&&(R=l.match(/^\s+/))&&($[0].from=R[0].length,$.unshift(new s(0,0,R[0].length))),1==X($).level&&(R=l.match(/\s+$/))&&(X($).to-=R[0].length,$.push(new s(0,d-R[0].length,d)))),"rtl"==c?$.reverse():$}}();function ue(e,t){var r=e.order;return null==r&&(r=e.order=ce(e.text,t)),r}var fe=[],de=function(e,t,r){if(e.addEventListener)e.addEventListener(t,r,!1);else if(e.attachEvent)e.attachEvent("on"+t,r);else{var n=e._handlers||(e._handlers={});n[t]=(n[t]||fe).concat(r)}};function pe(e,t){return e._handlers&&e._handlers[t]||fe}function he(e,t,r){if(e.removeEventListener)e.removeEventListener(t,r,!1);else if(e.detachEvent)e.detachEvent("on"+t,r);else{var n=e._handlers,i=n&&n[t];if(i){var o=j(i,r);o>-1&&(n[t]=i.slice(0,o).concat(i.slice(o+1)))}}}function me(e,t){var r=pe(e,t);if(r.length)for(var n=Array.prototype.slice.call(arguments,2),i=0;i<r.length;++i)r[i].apply(null,n)}function ge(e,t,r){return"string"==typeof t&&(t={type:t,preventDefault:function(){this.defaultPrevented=!0}}),me(e,r||t.type,e,t),_e(t)||t.codemirrorIgnore}function ve(e){var t=e._handlers&&e._handlers.cursorActivity;if(t)for(var r=e.curOp.cursorActivityHandlers||(e.curOp.cursorActivityHandlers=[]),n=0;n<t.length;++n)-1==j(r,t[n])&&r.push(t[n])}function ye(e,t){return pe(e,t).length>0}function be(e){e.prototype.on=function(e,t){de(this,e,t)},e.prototype.off=function(e,t){he(this,e,t)}}function we(e){e.preventDefault?e.preventDefault():e.returnValue=!1}function xe(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}function _e(e){return null!=e.defaultPrevented?e.defaultPrevented:0==e.returnValue}function ke(e){we(e),xe(e)}function Ce(e){return e.target||e.srcElement}function Se(e){var t=e.which;return null==t&&(1&e.button?t=1:2&e.button?t=3:4&e.button&&(t=2)),y&&e.ctrlKey&&1==t&&(t=3),t}var Te,Me,Le=function(){if(a&&s<9)return!1;var e=A("div");return"draggable"in e||"dragDrop"in e}();function Ae(e){if(null==Te){var t=A("span","​");L(e,A("span",[t,document.createTextNode("x")])),0!=e.firstChild.offsetHeight&&(Te=t.offsetWidth<=1&&t.offsetHeight>2&&!(a&&s<8))}var r=Te?A("span","​"):A("span"," ",null,"display: inline-block; width: 1px; margin-right: -1px");return r.setAttribute("cm-text",""),r}function Oe(e){if(null!=Me)return Me;var t=L(e,document.createTextNode("AخA")),r=S(t,0,1).getBoundingClientRect(),n=S(t,1,2).getBoundingClientRect();return M(e),!(!r||r.left==r.right)&&(Me=n.right-r.right<3)}var Ne,Ee=3!="\n\nb".split(/\n/).length?function(e){for(var t=0,r=[],n=e.length;t<=n;){var i=e.indexOf("\n",t);-1==i&&(i=e.length);var o=e.slice(t,"\r"==e.charAt(i-1)?i-1:i),a=o.indexOf("\r");-1!=a?(r.push(o.slice(0,a)),t+=a+1):(r.push(o),t=i+1)}return r}:function(e){return e.split(/\r\n?|\n/)},De=window.getSelection?function(e){try{return e.selectionStart!=e.selectionEnd}catch(e){return!1}}:function(e){var t;try{t=e.ownerDocument.selection.createRange()}catch(e){}return!(!t||t.parentElement()!=e)&&0!=t.compareEndPoints("StartToEnd",t)},Ie="oncopy"in(Ne=A("div"))||(Ne.setAttribute("oncopy","return;"),"function"==typeof Ne.oncopy),Pe=null;var ze={},Re={};function $e(e){if("string"==typeof e&&Re.hasOwnProperty(e))e=Re[e];else if(e&&"string"==typeof e.name&&Re.hasOwnProperty(e.name)){var t=Re[e.name];"string"==typeof t&&(t={name:t}),(e=J(t,e)).name=t.name}else{if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+xml$/.test(e))return $e("application/xml");if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+json$/.test(e))return $e("application/json")}return"string"==typeof e?{name:e}:e||{name:"null"}}function Fe(e,t){t=$e(t);var r=ze[t.name];if(!r)return Fe(e,"text/plain");var n=r(e,t);if(je.hasOwnProperty(t.name)){var i=je[t.name];for(var o in i)i.hasOwnProperty(o)&&(n.hasOwnProperty(o)&&(n["_"+o]=n[o]),n[o]=i[o])}if(n.name=t.name,t.helperType&&(n.helperType=t.helperType),t.modeProps)for(var a in t.modeProps)n[a]=t.modeProps[a];return n}var je={};function We(e,t){R(t,je.hasOwnProperty(e)?je[e]:je[e]={})}function qe(e,t){if(!0===t)return t;if(e.copyState)return e.copyState(t);var r={};for(var n in t){var i=t[n];i instanceof Array&&(i=i.concat([])),r[n]=i}return r}function He(e,t){for(var r;e.innerMode&&(r=e.innerMode(t))&&r.mode!=e;)t=r.state,e=r.mode;return r||{mode:e,state:t}}function Ue(e,t,r){return!e.startState||e.startState(t,r)}var Be=function(e,t,r){this.pos=this.start=0,this.string=e,this.tabSize=t||8,this.lastColumnPos=this.lastColumnValue=0,this.lineStart=0,this.lineOracle=r};function Ge(e,t){if((t-=e.first)<0||t>=e.size)throw new Error("There is no line "+(t+e.first)+" in the document.");for(var r=e;!r.lines;)for(var n=0;;++n){var i=r.children[n],o=i.chunkSize();if(t<o){r=i;break}t-=o}return r.lines[t]}function Ve(e,t,r){var n=[],i=t.line;return e.iter(t.line,r.line+1,function(e){var o=e.text;i==r.line&&(o=o.slice(0,r.ch)),i==t.line&&(o=o.slice(t.ch)),n.push(o),++i}),n}function Ke(e,t,r){var n=[];return e.iter(t,r,function(e){n.push(e.text)}),n}function Xe(e,t){var r=t-e.height;if(r)for(var n=e;n;n=n.parent)n.height+=r}function Ye(e){if(null==e.parent)return null;for(var t=e.parent,r=j(t.lines,e),n=t.parent;n;t=n,n=n.parent)for(var i=0;n.children[i]!=t;++i)r+=n.children[i].chunkSize();return r+t.first}function Ze(e,t){var r=e.first;e:do{for(var n=0;n<e.children.length;++n){var i=e.children[n],o=i.height;if(t<o){e=i;continue e}t-=o,r+=i.chunkSize()}return r}while(!e.lines);for(var a=0;a<e.lines.length;++a){var s=e.lines[a].height;if(t<s)break;t-=s}return r+a}function Je(e,t){return t>=e.first&&t<e.first+e.size}function Qe(e,t){return String(e.lineNumberFormatter(t+e.firstLineNumber))}function et(e,t,r){if(void 0===r&&(r=null),!(this instanceof et))return new et(e,t,r);this.line=e,this.ch=t,this.sticky=r}function tt(e,t){return e.line-t.line||e.ch-t.ch}function rt(e,t){return e.sticky==t.sticky&&0==tt(e,t)}function nt(e){return et(e.line,e.ch)}function it(e,t){return tt(e,t)<0?t:e}function ot(e,t){return tt(e,t)<0?e:t}function at(e,t){return Math.max(e.first,Math.min(t,e.first+e.size-1))}function st(e,t){if(t.line<e.first)return et(e.first,0);var r=e.first+e.size-1;return t.line>r?et(r,Ge(e,r).text.length):function(e,t){var r=e.ch;return null==r||r>t?et(e.line,t):r<0?et(e.line,0):e}(t,Ge(e,t.line).text.length)}function lt(e,t){for(var r=[],n=0;n<t.length;n++)r[n]=st(e,t[n]);return r}Be.prototype.eol=function(){return this.pos>=this.string.length},Be.prototype.sol=function(){return this.pos==this.lineStart},Be.prototype.peek=function(){return this.string.charAt(this.pos)||void 0},Be.prototype.next=function(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)},Be.prototype.eat=function(e){var t=this.string.charAt(this.pos);if("string"==typeof e?t==e:t&&(e.test?e.test(t):e(t)))return++this.pos,t},Be.prototype.eatWhile=function(e){for(var t=this.pos;this.eat(e););return this.pos>t},Be.prototype.eatSpace=function(){for(var e=this.pos;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>e},Be.prototype.skipToEnd=function(){this.pos=this.string.length},Be.prototype.skipTo=function(e){var t=this.string.indexOf(e,this.pos);if(t>-1)return this.pos=t,!0},Be.prototype.backUp=function(e){this.pos-=e},Be.prototype.column=function(){return this.lastColumnPos<this.start&&(this.lastColumnValue=$(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue-(this.lineStart?$(this.string,this.lineStart,this.tabSize):0)},Be.prototype.indentation=function(){return $(this.string,null,this.tabSize)-(this.lineStart?$(this.string,this.lineStart,this.tabSize):0)},Be.prototype.match=function(e,t,r){if("string"!=typeof e){var n=this.string.slice(this.pos).match(e);return n&&n.index>0?null:(n&&!1!==t&&(this.pos+=n[0].length),n)}var i=function(e){return r?e.toLowerCase():e};if(i(this.string.substr(this.pos,e.length))==i(e))return!1!==t&&(this.pos+=e.length),!0},Be.prototype.current=function(){return this.string.slice(this.start,this.pos)},Be.prototype.hideFirstChars=function(e,t){this.lineStart+=e;try{return t()}finally{this.lineStart-=e}},Be.prototype.lookAhead=function(e){var t=this.lineOracle;return t&&t.lookAhead(e)},Be.prototype.baseToken=function(){var e=this.lineOracle;return e&&e.baseToken(this.pos)};var ct=function(e,t){this.state=e,this.lookAhead=t},ut=function(e,t,r,n){this.state=t,this.doc=e,this.line=r,this.maxLookAhead=n||0,this.baseTokens=null,this.baseTokenPos=1};function ft(e,t,r,n){var i=[e.state.modeGen],o={};wt(e,t.text,e.doc.mode,r,function(e,t){return i.push(e,t)},o,n);for(var a=r.state,s=function(n){r.baseTokens=i;var s=e.state.overlays[n],l=1,c=0;r.state=!0,wt(e,t.text,s.mode,r,function(e,t){for(var r=l;c<e;){var n=i[l];n>e&&i.splice(l,1,e,i[l+1],n),l+=2,c=Math.min(e,n)}if(t)if(s.opaque)i.splice(r,l-r,e,"overlay "+t),l=r+2;else for(;r<l;r+=2){var o=i[r+1];i[r+1]=(o?o+" ":"")+"overlay "+t}},o),r.state=a,r.baseTokens=null,r.baseTokenPos=1},l=0;l<e.state.overlays.length;++l)s(l);return{styles:i,classes:o.bgClass||o.textClass?o:null}}function dt(e,t,r){if(!t.styles||t.styles[0]!=e.state.modeGen){var n=pt(e,Ye(t)),i=t.text.length>e.options.maxHighlightLength&&qe(e.doc.mode,n.state),o=ft(e,t,n);i&&(n.state=i),t.stateAfter=n.save(!i),t.styles=o.styles,o.classes?t.styleClasses=o.classes:t.styleClasses&&(t.styleClasses=null),r===e.doc.highlightFrontier&&(e.doc.modeFrontier=Math.max(e.doc.modeFrontier,++e.doc.highlightFrontier))}return t.styles}function pt(e,t,r){var n=e.doc,i=e.display;if(!n.mode.startState)return new ut(n,!0,t);var o=function(e,t,r){for(var n,i,o=e.doc,a=r?-1:t-(e.doc.mode.innerMode?1e3:100),s=t;s>a;--s){if(s<=o.first)return o.first;var l=Ge(o,s-1),c=l.stateAfter;if(c&&(!r||s+(c instanceof ct?c.lookAhead:0)<=o.modeFrontier))return s;var u=$(l.text,null,e.options.tabSize);(null==i||n>u)&&(i=s-1,n=u)}return i}(e,t,r),a=o>n.first&&Ge(n,o-1).stateAfter,s=a?ut.fromSaved(n,a,o):new ut(n,Ue(n.mode),o);return n.iter(o,t,function(r){ht(e,r.text,s);var n=s.line;r.stateAfter=n==t-1||n%5==0||n>=i.viewFrom&&n<i.viewTo?s.save():null,s.nextLine()}),r&&(n.modeFrontier=s.line),s}function ht(e,t,r,n){var i=e.doc.mode,o=new Be(t,e.options.tabSize,r);for(o.start=o.pos=n||0,""==t&&mt(i,r.state);!o.eol();)gt(i,o,r.state),o.start=o.pos}function mt(e,t){if(e.blankLine)return e.blankLine(t);if(e.innerMode){var r=He(e,t);return r.mode.blankLine?r.mode.blankLine(r.state):void 0}}function gt(e,t,r,n){for(var i=0;i<10;i++){n&&(n[0]=He(e,r).mode);var o=e.token(t,r);if(t.pos>t.start)return o}throw new Error("Mode "+e.name+" failed to advance stream.")}ut.prototype.lookAhead=function(e){var t=this.doc.getLine(this.line+e);return null!=t&&e>this.maxLookAhead&&(this.maxLookAhead=e),t},ut.prototype.baseToken=function(e){if(!this.baseTokens)return null;for(;this.baseTokens[this.baseTokenPos]<=e;)this.baseTokenPos+=2;var t=this.baseTokens[this.baseTokenPos+1];return{type:t&&t.replace(/( |^)overlay .*/,""),size:this.baseTokens[this.baseTokenPos]-e}},ut.prototype.nextLine=function(){this.line++,this.maxLookAhead>0&&this.maxLookAhead--},ut.fromSaved=function(e,t,r){return t instanceof ct?new ut(e,qe(e.mode,t.state),r,t.lookAhead):new ut(e,qe(e.mode,t),r)},ut.prototype.save=function(e){var t=!1!==e?qe(this.doc.mode,this.state):this.state;return this.maxLookAhead>0?new ct(t,this.maxLookAhead):t};var vt=function(e,t,r){this.start=e.start,this.end=e.pos,this.string=e.current(),this.type=t||null,this.state=r};function yt(e,t,r,n){var i,o,a=e.doc,s=a.mode,l=Ge(a,(t=st(a,t)).line),c=pt(e,t.line,r),u=new Be(l.text,e.options.tabSize,c);for(n&&(o=[]);(n||u.pos<t.ch)&&!u.eol();)u.start=u.pos,i=gt(s,u,c.state),n&&o.push(new vt(u,i,qe(a.mode,c.state)));return n?o:new vt(u,i,c.state)}function bt(e,t){if(e)for(;;){var r=e.match(/(?:^|\s+)line-(background-)?(\S+)/);if(!r)break;e=e.slice(0,r.index)+e.slice(r.index+r[0].length);var n=r[1]?"bgClass":"textClass";null==t[n]?t[n]=r[2]:new RegExp("(?:^|s)"+r[2]+"(?:$|s)").test(t[n])||(t[n]+=" "+r[2])}return e}function wt(e,t,r,n,i,o,a){var s=r.flattenSpans;null==s&&(s=e.options.flattenSpans);var l,c=0,u=null,f=new Be(t,e.options.tabSize,n),d=e.options.addModeClass&&[null];for(""==t&&bt(mt(r,n.state),o);!f.eol();){if(f.pos>e.options.maxHighlightLength?(s=!1,a&&ht(e,t,n,f.pos),f.pos=t.length,l=null):l=bt(gt(r,f,n.state,d),o),d){var p=d[0].name;p&&(l="m-"+(l?p+" "+l:p))}if(!s||u!=l){for(;c<f.start;)i(c=Math.min(f.start,c+5e3),u);u=l}f.start=f.pos}for(;c<f.pos;){var h=Math.min(f.pos,c+5e3);i(h,u),c=h}}var xt=!1,_t=!1;function kt(e,t,r){this.marker=e,this.from=t,this.to=r}function Ct(e,t){if(e)for(var r=0;r<e.length;++r){var n=e[r];if(n.marker==t)return n}}function St(e,t){for(var r,n=0;n<e.length;++n)e[n]!=t&&(r||(r=[])).push(e[n]);return r}function Tt(e,t){if(t.full)return null;var r=Je(e,t.from.line)&&Ge(e,t.from.line).markedSpans,n=Je(e,t.to.line)&&Ge(e,t.to.line).markedSpans;if(!r&&!n)return null;var i=t.from.ch,o=t.to.ch,a=0==tt(t.from,t.to),s=function(e,t,r){var n;if(e)for(var i=0;i<e.length;++i){var o=e[i],a=o.marker;if(null==o.from||(a.inclusiveLeft?o.from<=t:o.from<t)||o.from==t&&"bookmark"==a.type&&(!r||!o.marker.insertLeft)){var s=null==o.to||(a.inclusiveRight?o.to>=t:o.to>t);(n||(n=[])).push(new kt(a,o.from,s?null:o.to))}}return n}(r,i,a),l=function(e,t,r){var n;if(e)for(var i=0;i<e.length;++i){var o=e[i],a=o.marker;if(null==o.to||(a.inclusiveRight?o.to>=t:o.to>t)||o.from==t&&"bookmark"==a.type&&(!r||o.marker.insertLeft)){var s=null==o.from||(a.inclusiveLeft?o.from<=t:o.from<t);(n||(n=[])).push(new kt(a,s?null:o.from-t,null==o.to?null:o.to-t))}}return n}(n,o,a),c=1==t.text.length,u=X(t.text).length+(c?i:0);if(s)for(var f=0;f<s.length;++f){var d=s[f];if(null==d.to){var p=Ct(l,d.marker);p?c&&(d.to=null==p.to?null:p.to+u):d.to=i}}if(l)for(var h=0;h<l.length;++h){var m=l[h];if(null!=m.to&&(m.to+=u),null==m.from)Ct(s,m.marker)||(m.from=u,c&&(s||(s=[])).push(m));else m.from+=u,c&&(s||(s=[])).push(m)}s&&(s=Mt(s)),l&&l!=s&&(l=Mt(l));var g=[s];if(!c){var v,y=t.text.length-2;if(y>0&&s)for(var b=0;b<s.length;++b)null==s[b].to&&(v||(v=[])).push(new kt(s[b].marker,null,null));for(var w=0;w<y;++w)g.push(v);g.push(l)}return g}function Mt(e){for(var t=0;t<e.length;++t){var r=e[t];null!=r.from&&r.from==r.to&&!1!==r.marker.clearWhenEmpty&&e.splice(t--,1)}return e.length?e:null}function Lt(e){var t=e.markedSpans;if(t){for(var r=0;r<t.length;++r)t[r].marker.detachLine(e);e.markedSpans=null}}function At(e,t){if(t){for(var r=0;r<t.length;++r)t[r].marker.attachLine(e);e.markedSpans=t}}function Ot(e){return e.inclusiveLeft?-1:0}function Nt(e){return e.inclusiveRight?1:0}function Et(e,t){var r=e.lines.length-t.lines.length;if(0!=r)return r;var n=e.find(),i=t.find(),o=tt(n.from,i.from)||Ot(e)-Ot(t);if(o)return-o;var a=tt(n.to,i.to)||Nt(e)-Nt(t);return a||t.id-e.id}function Dt(e,t){var r,n=_t&&e.markedSpans;if(n)for(var i=void 0,o=0;o<n.length;++o)(i=n[o]).marker.collapsed&&null==(t?i.from:i.to)&&(!r||Et(r,i.marker)<0)&&(r=i.marker);return r}function It(e){return Dt(e,!0)}function Pt(e){return Dt(e,!1)}function zt(e,t){var r,n=_t&&e.markedSpans;if(n)for(var i=0;i<n.length;++i){var o=n[i];o.marker.collapsed&&(null==o.from||o.from<t)&&(null==o.to||o.to>t)&&(!r||Et(r,o.marker)<0)&&(r=o.marker)}return r}function Rt(e,t,r,n,i){var o=Ge(e,t),a=_t&&o.markedSpans;if(a)for(var s=0;s<a.length;++s){var l=a[s];if(l.marker.collapsed){var c=l.marker.find(0),u=tt(c.from,r)||Ot(l.marker)-Ot(i),f=tt(c.to,n)||Nt(l.marker)-Nt(i);if(!(u>=0&&f<=0||u<=0&&f>=0)&&(u<=0&&(l.marker.inclusiveRight&&i.inclusiveLeft?tt(c.to,r)>=0:tt(c.to,r)>0)||u>=0&&(l.marker.inclusiveRight&&i.inclusiveLeft?tt(c.from,n)<=0:tt(c.from,n)<0)))return!0}}}function $t(e){for(var t;t=It(e);)e=t.find(-1,!0).line;return e}function Ft(e,t){var r=Ge(e,t),n=$t(r);return r==n?t:Ye(n)}function jt(e,t){if(t>e.lastLine())return t;var r,n=Ge(e,t);if(!Wt(e,n))return t;for(;r=Pt(n);)n=r.find(1,!0).line;return Ye(n)+1}function Wt(e,t){var r=_t&&t.markedSpans;if(r)for(var n=void 0,i=0;i<r.length;++i)if((n=r[i]).marker.collapsed){if(null==n.from)return!0;if(!n.marker.widgetNode&&0==n.from&&n.marker.inclusiveLeft&&qt(e,t,n))return!0}}function qt(e,t,r){if(null==r.to){var n=r.marker.find(1,!0);return qt(e,n.line,Ct(n.line.markedSpans,r.marker))}if(r.marker.inclusiveRight&&r.to==t.text.length)return!0;for(var i=void 0,o=0;o<t.markedSpans.length;++o)if((i=t.markedSpans[o]).marker.collapsed&&!i.marker.widgetNode&&i.from==r.to&&(null==i.to||i.to!=r.from)&&(i.marker.inclusiveLeft||r.marker.inclusiveRight)&&qt(e,t,i))return!0}function Ht(e){for(var t=0,r=(e=$t(e)).parent,n=0;n<r.lines.length;++n){var i=r.lines[n];if(i==e)break;t+=i.height}for(var o=r.parent;o;o=(r=o).parent)for(var a=0;a<o.children.length;++a){var s=o.children[a];if(s==r)break;t+=s.height}return t}function Ut(e){if(0==e.height)return 0;for(var t,r=e.text.length,n=e;t=It(n);){var i=t.find(0,!0);n=i.from.line,r+=i.from.ch-i.to.ch}for(n=e;t=Pt(n);){var o=t.find(0,!0);r-=n.text.length-o.from.ch,r+=(n=o.to.line).text.length-o.to.ch}return r}function Bt(e){var t=e.display,r=e.doc;t.maxLine=Ge(r,r.first),t.maxLineLength=Ut(t.maxLine),t.maxLineChanged=!0,r.iter(function(e){var r=Ut(e);r>t.maxLineLength&&(t.maxLineLength=r,t.maxLine=e)})}var Gt=function(e,t,r){this.text=e,At(this,t),this.height=r?r(this):1};function Vt(e){e.parent=null,Lt(e)}Gt.prototype.lineNo=function(){return Ye(this)},be(Gt);var Kt={},Xt={};function Yt(e,t){if(!e||/^\s*$/.test(e))return null;var r=t.addModeClass?Xt:Kt;return r[e]||(r[e]=e.replace(/\S+/g,"cm-$&"))}function Zt(e,t){var r=O("span",null,null,l?"padding-right: .1px":null),n={pre:O("pre",[r],"CodeMirror-line"),content:r,col:0,pos:0,cm:e,trailingSpace:!1,splitSpaces:e.getOption("lineWrapping")};t.measure={};for(var i=0;i<=(t.rest?t.rest.length:0);i++){var o=i?t.rest[i-1]:t.line,a=void 0;n.pos=0,n.addToken=Qt,Oe(e.display.measure)&&(a=ue(o,e.doc.direction))&&(n.addToken=er(n.addToken,a)),n.map=[],rr(o,n,dt(e,o,t!=e.display.externalMeasured&&Ye(o))),o.styleClasses&&(o.styleClasses.bgClass&&(n.bgClass=I(o.styleClasses.bgClass,n.bgClass||"")),o.styleClasses.textClass&&(n.textClass=I(o.styleClasses.textClass,n.textClass||""))),0==n.map.length&&n.map.push(0,0,n.content.appendChild(Ae(e.display.measure))),0==i?(t.measure.map=n.map,t.measure.cache={}):((t.measure.maps||(t.measure.maps=[])).push(n.map),(t.measure.caches||(t.measure.caches=[])).push({}))}if(l){var s=n.content.lastChild;(/\bcm-tab\b/.test(s.className)||s.querySelector&&s.querySelector(".cm-tab"))&&(n.content.className="cm-tab-wrap-hack")}return me(e,"renderLine",e,t.line,n.pre),n.pre.className&&(n.textClass=I(n.pre.className,n.textClass||"")),n}function Jt(e){var t=A("span","•","cm-invalidchar");return t.title="\\u"+e.charCodeAt(0).toString(16),t.setAttribute("aria-label",t.title),t}function Qt(e,t,r,n,i,o,l){if(t){var c,u=e.splitSpaces?function(e,t){if(e.length>1&&!/  /.test(e))return e;for(var r=t,n="",i=0;i<e.length;i++){var o=e.charAt(i);" "!=o||!r||i!=e.length-1&&32!=e.charCodeAt(i+1)||(o=" "),n+=o,r=" "==o}return n}(t,e.trailingSpace):t,f=e.cm.state.specialChars,d=!1;if(f.test(t)){c=document.createDocumentFragment();for(var p=0;;){f.lastIndex=p;var h=f.exec(t),m=h?h.index-p:t.length-p;if(m){var g=document.createTextNode(u.slice(p,p+m));a&&s<9?c.appendChild(A("span",[g])):c.appendChild(g),e.map.push(e.pos,e.pos+m,g),e.col+=m,e.pos+=m}if(!h)break;p+=m+1;var v=void 0;if("\t"==h[0]){var y=e.cm.options.tabSize,b=y-e.col%y;(v=c.appendChild(A("span",K(b),"cm-tab"))).setAttribute("role","presentation"),v.setAttribute("cm-text","\t"),e.col+=b}else"\r"==h[0]||"\n"==h[0]?((v=c.appendChild(A("span","\r"==h[0]?"␍":"␤","cm-invalidchar"))).setAttribute("cm-text",h[0]),e.col+=1):((v=e.cm.options.specialCharPlaceholder(h[0])).setAttribute("cm-text",h[0]),a&&s<9?c.appendChild(A("span",[v])):c.appendChild(v),e.col+=1);e.map.push(e.pos,e.pos+1,v),e.pos++}}else e.col+=t.length,c=document.createTextNode(u),e.map.push(e.pos,e.pos+t.length,c),a&&s<9&&(d=!0),e.pos+=t.length;if(e.trailingSpace=32==u.charCodeAt(t.length-1),r||n||i||d||o){var w=r||"";n&&(w+=n),i&&(w+=i);var x=A("span",[c],w,o);if(l)for(var _ in l)l.hasOwnProperty(_)&&"style"!=_&&"class"!=_&&x.setAttribute(_,l[_]);return e.content.appendChild(x)}e.content.appendChild(c)}}function er(e,t){return function(r,n,i,o,a,s,l){i=i?i+" cm-force-border":"cm-force-border";for(var c=r.pos,u=c+n.length;;){for(var f=void 0,d=0;d<t.length&&!((f=t[d]).to>c&&f.from<=c);d++);if(f.to>=u)return e(r,n,i,o,a,s,l);e(r,n.slice(0,f.to-c),i,o,null,s,l),o=null,n=n.slice(f.to-c),c=f.to}}}function tr(e,t,r,n){var i=!n&&r.widgetNode;i&&e.map.push(e.pos,e.pos+t,i),!n&&e.cm.display.input.needsContentAttribute&&(i||(i=e.content.appendChild(document.createElement("span"))),i.setAttribute("cm-marker",r.id)),i&&(e.cm.display.input.setUneditable(i),e.content.appendChild(i)),e.pos+=t,e.trailingSpace=!1}function rr(e,t,r){var n=e.markedSpans,i=e.text,o=0;if(n)for(var a,s,l,c,u,f,d,p=i.length,h=0,m=1,g="",v=0;;){if(v==h){l=c=u=s="",d=null,f=null,v=1/0;for(var y=[],b=void 0,w=0;w<n.length;++w){var x=n[w],_=x.marker;if("bookmark"==_.type&&x.from==h&&_.widgetNode)y.push(_);else if(x.from<=h&&(null==x.to||x.to>h||_.collapsed&&x.to==h&&x.from==h)){if(null!=x.to&&x.to!=h&&v>x.to&&(v=x.to,c=""),_.className&&(l+=" "+_.className),_.css&&(s=(s?s+";":"")+_.css),_.startStyle&&x.from==h&&(u+=" "+_.startStyle),_.endStyle&&x.to==v&&(b||(b=[])).push(_.endStyle,x.to),_.title&&((d||(d={})).title=_.title),_.attributes)for(var k in _.attributes)(d||(d={}))[k]=_.attributes[k];_.collapsed&&(!f||Et(f.marker,_)<0)&&(f=x)}else x.from>h&&v>x.from&&(v=x.from)}if(b)for(var C=0;C<b.length;C+=2)b[C+1]==v&&(c+=" "+b[C]);if(!f||f.from==h)for(var S=0;S<y.length;++S)tr(t,0,y[S]);if(f&&(f.from||0)==h){if(tr(t,(null==f.to?p+1:f.to)-h,f.marker,null==f.from),null==f.to)return;f.to==h&&(f=!1)}}if(h>=p)break;for(var T=Math.min(p,v);;){if(g){var M=h+g.length;if(!f){var L=M>T?g.slice(0,T-h):g;t.addToken(t,L,a?a+l:l,u,h+L.length==v?c:"",s,d)}if(M>=T){g=g.slice(T-h),h=T;break}h=M,u=""}g=i.slice(o,o=r[m++]),a=Yt(r[m++],t.cm.options)}}else for(var A=1;A<r.length;A+=2)t.addToken(t,i.slice(o,o=r[A]),Yt(r[A+1],t.cm.options))}function nr(e,t,r){this.line=t,this.rest=function(e){for(var t,r;t=Pt(e);)e=t.find(1,!0).line,(r||(r=[])).push(e);return r}(t),this.size=this.rest?Ye(X(this.rest))-r+1:1,this.node=this.text=null,this.hidden=Wt(e,t)}function ir(e,t,r){for(var n,i=[],o=t;o<r;o=n){var a=new nr(e.doc,Ge(e.doc,o),o);n=o+a.size,i.push(a)}return i}var or=null;var ar=null;function sr(e,t){var r=pe(e,t);if(r.length){var n,i=Array.prototype.slice.call(arguments,2);or?n=or.delayedCallbacks:ar?n=ar:(n=ar=[],setTimeout(lr,0));for(var o=function(e){n.push(function(){return r[e].apply(null,i)})},a=0;a<r.length;++a)o(a)}}function lr(){var e=ar;ar=null;for(var t=0;t<e.length;++t)e[t]()}function cr(e,t,r,n){for(var i=0;i<t.changes.length;i++){var o=t.changes[i];"text"==o?dr(e,t):"gutter"==o?hr(e,t,r,n):"class"==o?pr(e,t):"widget"==o&&mr(e,t,n)}t.changes=null}function ur(e){return e.node==e.text&&(e.node=A("div",null,null,"position: relative"),e.text.parentNode&&e.text.parentNode.replaceChild(e.node,e.text),e.node.appendChild(e.text),a&&s<8&&(e.node.style.zIndex=2)),e.node}function fr(e,t){var r=e.display.externalMeasured;return r&&r.line==t.line?(e.display.externalMeasured=null,t.measure=r.measure,r.built):Zt(e,t)}function dr(e,t){var r=t.text.className,n=fr(e,t);t.text==t.node&&(t.node=n.pre),t.text.parentNode.replaceChild(n.pre,t.text),t.text=n.pre,n.bgClass!=t.bgClass||n.textClass!=t.textClass?(t.bgClass=n.bgClass,t.textClass=n.textClass,pr(e,t)):r&&(t.text.className=r)}function pr(e,t){!function(e,t){var r=t.bgClass?t.bgClass+" "+(t.line.bgClass||""):t.line.bgClass;if(r&&(r+=" CodeMirror-linebackground"),t.background)r?t.background.className=r:(t.background.parentNode.removeChild(t.background),t.background=null);else if(r){var n=ur(t);t.background=n.insertBefore(A("div",null,r),n.firstChild),e.display.input.setUneditable(t.background)}}(e,t),t.line.wrapClass?ur(t).className=t.line.wrapClass:t.node!=t.text&&(t.node.className="");var r=t.textClass?t.textClass+" "+(t.line.textClass||""):t.line.textClass;t.text.className=r||""}function hr(e,t,r,n){if(t.gutter&&(t.node.removeChild(t.gutter),t.gutter=null),t.gutterBackground&&(t.node.removeChild(t.gutterBackground),t.gutterBackground=null),t.line.gutterClass){var i=ur(t);t.gutterBackground=A("div",null,"CodeMirror-gutter-background "+t.line.gutterClass,"left: "+(e.options.fixedGutter?n.fixedPos:-n.gutterTotalWidth)+"px; width: "+n.gutterTotalWidth+"px"),e.display.input.setUneditable(t.gutterBackground),i.insertBefore(t.gutterBackground,t.text)}var o=t.line.gutterMarkers;if(e.options.lineNumbers||o){var a=ur(t),s=t.gutter=A("div",null,"CodeMirror-gutter-wrapper","left: "+(e.options.fixedGutter?n.fixedPos:-n.gutterTotalWidth)+"px");if(e.display.input.setUneditable(s),a.insertBefore(s,t.text),t.line.gutterClass&&(s.className+=" "+t.line.gutterClass),!e.options.lineNumbers||o&&o["CodeMirror-linenumbers"]||(t.lineNumber=s.appendChild(A("div",Qe(e.options,r),"CodeMirror-linenumber CodeMirror-gutter-elt","left: "+n.gutterLeft["CodeMirror-linenumbers"]+"px; width: "+e.display.lineNumInnerWidth+"px"))),o)for(var l=0;l<e.display.gutterSpecs.length;++l){var c=e.display.gutterSpecs[l].className,u=o.hasOwnProperty(c)&&o[c];u&&s.appendChild(A("div",[u],"CodeMirror-gutter-elt","left: "+n.gutterLeft[c]+"px; width: "+n.gutterWidth[c]+"px"))}}}function mr(e,t,r){t.alignable&&(t.alignable=null);for(var n=t.node.firstChild,i=void 0;n;n=i)i=n.nextSibling,"CodeMirror-linewidget"==n.className&&t.node.removeChild(n);vr(e,t,r)}function gr(e,t,r,n){var i=fr(e,t);return t.text=t.node=i.pre,i.bgClass&&(t.bgClass=i.bgClass),i.textClass&&(t.textClass=i.textClass),pr(e,t),hr(e,t,r,n),vr(e,t,n),t.node}function vr(e,t,r){if(yr(e,t.line,t,r,!0),t.rest)for(var n=0;n<t.rest.length;n++)yr(e,t.rest[n],t,r,!1)}function yr(e,t,r,n,i){if(t.widgets)for(var o=ur(r),a=0,s=t.widgets;a<s.length;++a){var l=s[a],c=A("div",[l.node],"CodeMirror-linewidget");l.handleMouseEvents||c.setAttribute("cm-ignore-events","true"),br(l,c,r,n),e.display.input.setUneditable(c),i&&l.above?o.insertBefore(c,r.gutter||r.text):o.appendChild(c),sr(l,"redraw")}}function br(e,t,r,n){if(e.noHScroll){(r.alignable||(r.alignable=[])).push(t);var i=n.wrapperWidth;t.style.left=n.fixedPos+"px",e.coverGutter||(i-=n.gutterTotalWidth,t.style.paddingLeft=n.gutterTotalWidth+"px"),t.style.width=i+"px"}e.coverGutter&&(t.style.zIndex=5,t.style.position="relative",e.noHScroll||(t.style.marginLeft=-n.gutterTotalWidth+"px"))}function wr(e){if(null!=e.height)return e.height;var t=e.doc.cm;if(!t)return 0;if(!N(document.body,e.node)){var r="position: relative;";e.coverGutter&&(r+="margin-left: -"+t.display.gutters.offsetWidth+"px;"),e.noHScroll&&(r+="width: "+t.display.wrapper.clientWidth+"px;"),L(t.display.measure,A("div",[e.node],null,r))}return e.height=e.node.parentNode.offsetHeight}function xr(e,t){for(var r=Ce(t);r!=e.wrapper;r=r.parentNode)if(!r||1==r.nodeType&&"true"==r.getAttribute("cm-ignore-events")||r.parentNode==e.sizer&&r!=e.mover)return!0}function _r(e){return e.lineSpace.offsetTop}function kr(e){return e.mover.offsetHeight-e.lineSpace.offsetHeight}function Cr(e){if(e.cachedPaddingH)return e.cachedPaddingH;var t=L(e.measure,A("pre","x")),r=window.getComputedStyle?window.getComputedStyle(t):t.currentStyle,n={left:parseInt(r.paddingLeft),right:parseInt(r.paddingRight)};return isNaN(n.left)||isNaN(n.right)||(e.cachedPaddingH=n),n}function Sr(e){return W-e.display.nativeBarWidth}function Tr(e){return e.display.scroller.clientWidth-Sr(e)-e.display.barWidth}function Mr(e){return e.display.scroller.clientHeight-Sr(e)-e.display.barHeight}function Lr(e,t,r){if(e.line==t)return{map:e.measure.map,cache:e.measure.cache};for(var n=0;n<e.rest.length;n++)if(e.rest[n]==t)return{map:e.measure.maps[n],cache:e.measure.caches[n]};for(var i=0;i<e.rest.length;i++)if(Ye(e.rest[i])>r)return{map:e.measure.maps[i],cache:e.measure.caches[i],before:!0}}function Ar(e,t,r,n){return Er(e,Nr(e,t),r,n)}function Or(e,t){if(t>=e.display.viewFrom&&t<e.display.viewTo)return e.display.view[ln(e,t)];var r=e.display.externalMeasured;return r&&t>=r.lineN&&t<r.lineN+r.size?r:void 0}function Nr(e,t){var r=Ye(t),n=Or(e,r);n&&!n.text?n=null:n&&n.changes&&(cr(e,n,r,rn(e)),e.curOp.forceUpdate=!0),n||(n=function(e,t){var r=Ye(t=$t(t)),n=e.display.externalMeasured=new nr(e.doc,t,r);n.lineN=r;var i=n.built=Zt(e,n);return n.text=i.pre,L(e.display.lineMeasure,i.pre),n}(e,t));var i=Lr(n,t,r);return{line:t,view:n,rect:null,map:i.map,cache:i.cache,before:i.before,hasHeights:!1}}function Er(e,t,r,n,i){t.before&&(r=-1);var o,l=r+(n||"");return t.cache.hasOwnProperty(l)?o=t.cache[l]:(t.rect||(t.rect=t.view.text.getBoundingClientRect()),t.hasHeights||(!function(e,t,r){var n=e.options.lineWrapping,i=n&&Tr(e);if(!t.measure.heights||n&&t.measure.width!=i){var o=t.measure.heights=[];if(n){t.measure.width=i;for(var a=t.text.firstChild.getClientRects(),s=0;s<a.length-1;s++){var l=a[s],c=a[s+1];Math.abs(l.bottom-c.bottom)>2&&o.push((l.bottom+c.top)/2-r.top)}}o.push(r.bottom-r.top)}}(e,t.view,t.rect),t.hasHeights=!0),(o=function(e,t,r,n){var i,o=Pr(t.map,r,n),l=o.node,c=o.start,u=o.end,f=o.collapse;if(3==l.nodeType){for(var d=0;d<4;d++){for(;c&&ie(t.line.text.charAt(o.coverStart+c));)--c;for(;o.coverStart+u<o.coverEnd&&ie(t.line.text.charAt(o.coverStart+u));)++u;if((i=a&&s<9&&0==c&&u==o.coverEnd-o.coverStart?l.parentNode.getBoundingClientRect():zr(S(l,c,u).getClientRects(),n)).left||i.right||0==c)break;u=c,c-=1,f="right"}a&&s<11&&(i=function(e,t){if(!window.screen||null==screen.logicalXDPI||screen.logicalXDPI==screen.deviceXDPI||!function(e){if(null!=Pe)return Pe;var t=L(e,A("span","x")),r=t.getBoundingClientRect(),n=S(t,0,1).getBoundingClientRect();return Pe=Math.abs(r.left-n.left)>1}(e))return t;var r=screen.logicalXDPI/screen.deviceXDPI,n=screen.logicalYDPI/screen.deviceYDPI;return{left:t.left*r,right:t.right*r,top:t.top*n,bottom:t.bottom*n}}(e.display.measure,i))}else{var p;c>0&&(f=n="right"),i=e.options.lineWrapping&&(p=l.getClientRects()).length>1?p["right"==n?p.length-1:0]:l.getBoundingClientRect()}if(a&&s<9&&!c&&(!i||!i.left&&!i.right)){var h=l.parentNode.getClientRects()[0];i=h?{left:h.left,right:h.left+tn(e.display),top:h.top,bottom:h.bottom}:Ir}for(var m=i.top-t.rect.top,g=i.bottom-t.rect.top,v=(m+g)/2,y=t.view.measure.heights,b=0;b<y.length-1&&!(v<y[b]);b++);var w=b?y[b-1]:0,x=y[b],_={left:("right"==f?i.right:i.left)-t.rect.left,right:("left"==f?i.left:i.right)-t.rect.left,top:w,bottom:x};i.left||i.right||(_.bogus=!0);e.options.singleCursorHeightPerLine||(_.rtop=m,_.rbottom=g);return _}(e,t,r,n)).bogus||(t.cache[l]=o)),{left:o.left,right:o.right,top:i?o.rtop:o.top,bottom:i?o.rbottom:o.bottom}}var Dr,Ir={left:0,right:0,top:0,bottom:0};function Pr(e,t,r){for(var n,i,o,a,s,l,c=0;c<e.length;c+=3)if(s=e[c],l=e[c+1],t<s?(i=0,o=1,a="left"):t<l?o=(i=t-s)+1:(c==e.length-3||t==l&&e[c+3]>t)&&(i=(o=l-s)-1,t>=l&&(a="right")),null!=i){if(n=e[c+2],s==l&&r==(n.insertLeft?"left":"right")&&(a=r),"left"==r&&0==i)for(;c&&e[c-2]==e[c-3]&&e[c-1].insertLeft;)n=e[2+(c-=3)],a="left";if("right"==r&&i==l-s)for(;c<e.length-3&&e[c+3]==e[c+4]&&!e[c+5].insertLeft;)n=e[(c+=3)+2],a="right";break}return{node:n,start:i,end:o,collapse:a,coverStart:s,coverEnd:l}}function zr(e,t){var r=Ir;if("left"==t)for(var n=0;n<e.length&&(r=e[n]).left==r.right;n++);else for(var i=e.length-1;i>=0&&(r=e[i]).left==r.right;i--);return r}function Rr(e){if(e.measure&&(e.measure.cache={},e.measure.heights=null,e.rest))for(var t=0;t<e.rest.length;t++)e.measure.caches[t]={}}function $r(e){e.display.externalMeasure=null,M(e.display.lineMeasure);for(var t=0;t<e.display.view.length;t++)Rr(e.display.view[t])}function Fr(e){$r(e),e.display.cachedCharWidth=e.display.cachedTextHeight=e.display.cachedPaddingH=null,e.options.lineWrapping||(e.display.maxLineChanged=!0),e.display.lineNumChars=null}function jr(){return u&&g?-(document.body.getBoundingClientRect().left-parseInt(getComputedStyle(document.body).marginLeft)):window.pageXOffset||(document.documentElement||document.body).scrollLeft}function Wr(){return u&&g?-(document.body.getBoundingClientRect().top-parseInt(getComputedStyle(document.body).marginTop)):window.pageYOffset||(document.documentElement||document.body).scrollTop}function qr(e){var t=0;if(e.widgets)for(var r=0;r<e.widgets.length;++r)e.widgets[r].above&&(t+=wr(e.widgets[r]));return t}function Hr(e,t,r,n,i){if(!i){var o=qr(t);r.top+=o,r.bottom+=o}if("line"==n)return r;n||(n="local");var a=Ht(t);if("local"==n?a+=_r(e.display):a-=e.display.viewOffset,"page"==n||"window"==n){var s=e.display.lineSpace.getBoundingClientRect();a+=s.top+("window"==n?0:Wr());var l=s.left+("window"==n?0:jr());r.left+=l,r.right+=l}return r.top+=a,r.bottom+=a,r}function Ur(e,t,r){if("div"==r)return t;var n=t.left,i=t.top;if("page"==r)n-=jr(),i-=Wr();else if("local"==r||!r){var o=e.display.sizer.getBoundingClientRect();n+=o.left,i+=o.top}var a=e.display.lineSpace.getBoundingClientRect();return{left:n-a.left,top:i-a.top}}function Br(e,t,r,n,i){return n||(n=Ge(e.doc,t.line)),Hr(e,n,Ar(e,n,t.ch,i),r)}function Gr(e,t,r,n,i,o){function a(t,a){var s=Er(e,i,t,a?"right":"left",o);return a?s.left=s.right:s.right=s.left,Hr(e,n,s,r)}n=n||Ge(e.doc,t.line),i||(i=Nr(e,n));var s=ue(n,e.doc.direction),l=t.ch,c=t.sticky;if(l>=n.text.length?(l=n.text.length,c="before"):l<=0&&(l=0,c="after"),!s)return a("before"==c?l-1:l,"before"==c);function u(e,t,r){var n=1==s[t].level;return a(r?e-1:e,n!=r)}var f=le(s,l,c),d=se,p=u(l,f,"before"==c);return null!=d&&(p.other=u(l,d,"before"!=c)),p}function Vr(e,t){var r=0;t=st(e.doc,t),e.options.lineWrapping||(r=tn(e.display)*t.ch);var n=Ge(e.doc,t.line),i=Ht(n)+_r(e.display);return{left:r,right:r,top:i,bottom:i+n.height}}function Kr(e,t,r,n,i){var o=et(e,t,r);return o.xRel=i,n&&(o.outside=!0),o}function Xr(e,t,r){var n=e.doc;if((r+=e.display.viewOffset)<0)return Kr(n.first,0,null,!0,-1);var i=Ze(n,r),o=n.first+n.size-1;if(i>o)return Kr(n.first+n.size-1,Ge(n,o).text.length,null,!0,1);t<0&&(t=0);for(var a=Ge(n,i);;){var s=Qr(e,a,i,t,r),l=zt(a,s.ch+(s.xRel>0?1:0));if(!l)return s;var c=l.find(1);if(c.line==i)return c;a=Ge(n,i=c.line)}}function Yr(e,t,r,n){n-=qr(t);var i=t.text.length,o=ae(function(t){return Er(e,r,t-1).bottom<=n},i,0);return{begin:o,end:i=ae(function(t){return Er(e,r,t).top>n},o,i)}}function Zr(e,t,r,n){return r||(r=Nr(e,t)),Yr(e,t,r,Hr(e,t,Er(e,r,n),"line").top)}function Jr(e,t,r,n){return!(e.bottom<=r)&&(e.top>r||(n?e.left:e.right)>t)}function Qr(e,t,r,n,i){i-=Ht(t);var o=Nr(e,t),a=qr(t),s=0,l=t.text.length,c=!0,u=ue(t,e.doc.direction);if(u){var f=(e.options.lineWrapping?function(e,t,r,n,i,o,a){var s=Yr(e,t,n,a),l=s.begin,c=s.end;/\s/.test(t.text.charAt(c-1))&&c--;for(var u=null,f=null,d=0;d<i.length;d++){var p=i[d];if(!(p.from>=c||p.to<=l)){var h=1!=p.level,m=Er(e,n,h?Math.min(c,p.to)-1:Math.max(l,p.from)).right,g=m<o?o-m+1e9:m-o;(!u||f>g)&&(u=p,f=g)}}u||(u=i[i.length-1]);u.from<l&&(u={from:l,to:u.to,level:u.level});u.to>c&&(u={from:u.from,to:c,level:u.level});return u}:function(e,t,r,n,i,o,a){var s=ae(function(s){var l=i[s],c=1!=l.level;return Jr(Gr(e,et(r,c?l.to:l.from,c?"before":"after"),"line",t,n),o,a,!0)},0,i.length-1),l=i[s];if(s>0){var c=1!=l.level,u=Gr(e,et(r,c?l.from:l.to,c?"after":"before"),"line",t,n);Jr(u,o,a,!0)&&u.top>a&&(l=i[s-1])}return l})(e,t,r,o,u,n,i);s=(c=1!=f.level)?f.from:f.to-1,l=c?f.to:f.from-1}var d,p,h=null,m=null,g=ae(function(t){var r=Er(e,o,t);return r.top+=a,r.bottom+=a,!!Jr(r,n,i,!1)&&(r.top<=i&&r.left<=n&&(h=t,m=r),!0)},s,l),v=!1;if(m){var y=n-m.left<m.right-n,b=y==c;g=h+(b?0:1),p=b?"after":"before",d=y?m.left:m.right}else{c||g!=l&&g!=s||g++,p=0==g?"after":g==t.text.length?"before":Er(e,o,g-(c?1:0)).bottom+a<=i==c?"after":"before";var w=Gr(e,et(r,g,p),"line",t,o);d=w.left,v=i<w.top||i>=w.bottom}return Kr(r,g=oe(t.text,g,1),p,v,n-d)}function en(e){if(null!=e.cachedTextHeight)return e.cachedTextHeight;if(null==Dr){Dr=A("pre");for(var t=0;t<49;++t)Dr.appendChild(document.createTextNode("x")),Dr.appendChild(A("br"));Dr.appendChild(document.createTextNode("x"))}L(e.measure,Dr);var r=Dr.offsetHeight/50;return r>3&&(e.cachedTextHeight=r),M(e.measure),r||1}function tn(e){if(null!=e.cachedCharWidth)return e.cachedCharWidth;var t=A("span","xxxxxxxxxx"),r=A("pre",[t]);L(e.measure,r);var n=t.getBoundingClientRect(),i=(n.right-n.left)/10;return i>2&&(e.cachedCharWidth=i),i||10}function rn(e){for(var t=e.display,r={},n={},i=t.gutters.clientLeft,o=t.gutters.firstChild,a=0;o;o=o.nextSibling,++a){var s=e.display.gutterSpecs[a].className;r[s]=o.offsetLeft+o.clientLeft+i,n[s]=o.clientWidth}return{fixedPos:nn(t),gutterTotalWidth:t.gutters.offsetWidth,gutterLeft:r,gutterWidth:n,wrapperWidth:t.wrapper.clientWidth}}function nn(e){return e.scroller.getBoundingClientRect().left-e.sizer.getBoundingClientRect().left}function on(e){var t=en(e.display),r=e.options.lineWrapping,n=r&&Math.max(5,e.display.scroller.clientWidth/tn(e.display)-3);return function(i){if(Wt(e.doc,i))return 0;var o=0;if(i.widgets)for(var a=0;a<i.widgets.length;a++)i.widgets[a].height&&(o+=i.widgets[a].height);return r?o+(Math.ceil(i.text.length/n)||1)*t:o+t}}function an(e){var t=e.doc,r=on(e);t.iter(function(e){var t=r(e);t!=e.height&&Xe(e,t)})}function sn(e,t,r,n){var i=e.display;if(!r&&"true"==Ce(t).getAttribute("cm-not-content"))return null;var o,a,s=i.lineSpace.getBoundingClientRect();try{o=t.clientX-s.left,a=t.clientY-s.top}catch(t){return null}var l,c=Xr(e,o,a);if(n&&1==c.xRel&&(l=Ge(e.doc,c.line).text).length==c.ch){var u=$(l,l.length,e.options.tabSize)-l.length;c=et(c.line,Math.max(0,Math.round((o-Cr(e.display).left)/tn(e.display))-u))}return c}function ln(e,t){if(t>=e.display.viewTo)return null;if((t-=e.display.viewFrom)<0)return null;for(var r=e.display.view,n=0;n<r.length;n++)if((t-=r[n].size)<0)return n}function cn(e,t,r,n){null==t&&(t=e.doc.first),null==r&&(r=e.doc.first+e.doc.size),n||(n=0);var i=e.display;if(n&&r<i.viewTo&&(null==i.updateLineNumbers||i.updateLineNumbers>t)&&(i.updateLineNumbers=t),e.curOp.viewChanged=!0,t>=i.viewTo)_t&&Ft(e.doc,t)<i.viewTo&&fn(e);else if(r<=i.viewFrom)_t&&jt(e.doc,r+n)>i.viewFrom?fn(e):(i.viewFrom+=n,i.viewTo+=n);else if(t<=i.viewFrom&&r>=i.viewTo)fn(e);else if(t<=i.viewFrom){var o=dn(e,r,r+n,1);o?(i.view=i.view.slice(o.index),i.viewFrom=o.lineN,i.viewTo+=n):fn(e)}else if(r>=i.viewTo){var a=dn(e,t,t,-1);a?(i.view=i.view.slice(0,a.index),i.viewTo=a.lineN):fn(e)}else{var s=dn(e,t,t,-1),l=dn(e,r,r+n,1);s&&l?(i.view=i.view.slice(0,s.index).concat(ir(e,s.lineN,l.lineN)).concat(i.view.slice(l.index)),i.viewTo+=n):fn(e)}var c=i.externalMeasured;c&&(r<c.lineN?c.lineN+=n:t<c.lineN+c.size&&(i.externalMeasured=null))}function un(e,t,r){e.curOp.viewChanged=!0;var n=e.display,i=e.display.externalMeasured;if(i&&t>=i.lineN&&t<i.lineN+i.size&&(n.externalMeasured=null),!(t<n.viewFrom||t>=n.viewTo)){var o=n.view[ln(e,t)];if(null!=o.node){var a=o.changes||(o.changes=[]);-1==j(a,r)&&a.push(r)}}}function fn(e){e.display.viewFrom=e.display.viewTo=e.doc.first,e.display.view=[],e.display.viewOffset=0}function dn(e,t,r,n){var i,o=ln(e,t),a=e.display.view;if(!_t||r==e.doc.first+e.doc.size)return{index:o,lineN:r};for(var s=e.display.viewFrom,l=0;l<o;l++)s+=a[l].size;if(s!=t){if(n>0){if(o==a.length-1)return null;i=s+a[o].size-t,o++}else i=s-t;t+=i,r+=i}for(;Ft(e.doc,r)!=r;){if(o==(n<0?0:a.length-1))return null;r+=n*a[o-(n<0?1:0)].size,o+=n}return{index:o,lineN:r}}function pn(e){for(var t=e.display.view,r=0,n=0;n<t.length;n++){var i=t[n];i.hidden||i.node&&!i.changes||++r}return r}function hn(e){e.display.input.showSelection(e.display.input.prepareSelection())}function mn(e,t){void 0===t&&(t=!0);for(var r=e.doc,n={},i=n.cursors=document.createDocumentFragment(),o=n.selection=document.createDocumentFragment(),a=0;a<r.sel.ranges.length;a++)if(t||a!=r.sel.primIndex){var s=r.sel.ranges[a];if(!(s.from().line>=e.display.viewTo||s.to().line<e.display.viewFrom)){var l=s.empty();(l||e.options.showCursorWhenSelecting)&&gn(e,s.head,i),l||yn(e,s,o)}}return n}function gn(e,t,r){var n=Gr(e,t,"div",null,null,!e.options.singleCursorHeightPerLine),i=r.appendChild(A("div"," ","CodeMirror-cursor"));if(i.style.left=n.left+"px",i.style.top=n.top+"px",i.style.height=Math.max(0,n.bottom-n.top)*e.options.cursorHeight+"px",n.other){var o=r.appendChild(A("div"," ","CodeMirror-cursor CodeMirror-secondarycursor"));o.style.display="",o.style.left=n.other.left+"px",o.style.top=n.other.top+"px",o.style.height=.85*(n.other.bottom-n.other.top)+"px"}}function vn(e,t){return e.top-t.top||e.left-t.left}function yn(e,t,r){var n=e.display,i=e.doc,o=document.createDocumentFragment(),a=Cr(e.display),s=a.left,l=Math.max(n.sizerWidth,Tr(e)-n.sizer.offsetLeft)-a.right,c="ltr"==i.direction;function u(e,t,r,n){t<0&&(t=0),t=Math.round(t),n=Math.round(n),o.appendChild(A("div",null,"CodeMirror-selected","position: absolute; left: "+e+"px;\n                             top: "+t+"px; width: "+(null==r?l-e:r)+"px;\n                             height: "+(n-t)+"px"))}function f(t,r,n){var o,a,f=Ge(i,t),d=f.text.length;function p(r,n){return Br(e,et(t,r),"div",f,n)}function h(t,r,n){var i=Zr(e,f,null,t),o="ltr"==r==("after"==n)?"left":"right";return p("after"==n?i.begin:i.end-(/\s/.test(f.text.charAt(i.end-1))?2:1),o)[o]}var m=ue(f,i.direction);return function(e,t,r,n){if(!e)return n(t,r,"ltr",0);for(var i=!1,o=0;o<e.length;++o){var a=e[o];(a.from<r&&a.to>t||t==r&&a.to==t)&&(n(Math.max(a.from,t),Math.min(a.to,r),1==a.level?"rtl":"ltr",o),i=!0)}i||n(t,r,"ltr")}(m,r||0,null==n?d:n,function(e,t,i,f){var g="ltr"==i,v=p(e,g?"left":"right"),y=p(t-1,g?"right":"left"),b=null==r&&0==e,w=null==n&&t==d,x=0==f,_=!m||f==m.length-1;if(y.top-v.top<=3){var k=(c?w:b)&&_,C=(c?b:w)&&x?s:(g?v:y).left,S=k?l:(g?y:v).right;u(C,v.top,S-C,v.bottom)}else{var T,M,L,A;g?(T=c&&b&&x?s:v.left,M=c?l:h(e,i,"before"),L=c?s:h(t,i,"after"),A=c&&w&&_?l:y.right):(T=c?h(e,i,"before"):s,M=!c&&b&&x?l:v.right,L=!c&&w&&_?s:y.left,A=c?h(t,i,"after"):l),u(T,v.top,M-T,v.bottom),v.bottom<y.top&&u(s,v.bottom,null,y.top),u(L,y.top,A-L,y.bottom)}(!o||vn(v,o)<0)&&(o=v),vn(y,o)<0&&(o=y),(!a||vn(v,a)<0)&&(a=v),vn(y,a)<0&&(a=y)}),{start:o,end:a}}var d=t.from(),p=t.to();if(d.line==p.line)f(d.line,d.ch,p.ch);else{var h=Ge(i,d.line),m=Ge(i,p.line),g=$t(h)==$t(m),v=f(d.line,d.ch,g?h.text.length+1:null).end,y=f(p.line,g?0:null,p.ch).start;g&&(v.top<y.top-2?(u(v.right,v.top,null,v.bottom),u(s,y.top,y.left,y.bottom)):u(v.right,v.top,y.left-v.right,v.bottom)),v.bottom<y.top&&u(s,v.bottom,null,y.top)}r.appendChild(o)}function bn(e){if(e.state.focused){var t=e.display;clearInterval(t.blinker);var r=!0;t.cursorDiv.style.visibility="",e.options.cursorBlinkRate>0?t.blinker=setInterval(function(){return t.cursorDiv.style.visibility=(r=!r)?"":"hidden"},e.options.cursorBlinkRate):e.options.cursorBlinkRate<0&&(t.cursorDiv.style.visibility="hidden")}}function wn(e){e.state.focused||(e.display.input.focus(),_n(e))}function xn(e){e.state.delayingBlurEvent=!0,setTimeout(function(){e.state.delayingBlurEvent&&(e.state.delayingBlurEvent=!1,kn(e))},100)}function _n(e,t){e.state.delayingBlurEvent&&(e.state.delayingBlurEvent=!1),"nocursor"!=e.options.readOnly&&(e.state.focused||(me(e,"focus",e,t),e.state.focused=!0,D(e.display.wrapper,"CodeMirror-focused"),e.curOp||e.display.selForContextMenu==e.doc.sel||(e.display.input.reset(),l&&setTimeout(function(){return e.display.input.reset(!0)},20)),e.display.input.receivedFocus()),bn(e))}function kn(e,t){e.state.delayingBlurEvent||(e.state.focused&&(me(e,"blur",e,t),e.state.focused=!1,T(e.display.wrapper,"CodeMirror-focused")),clearInterval(e.display.blinker),setTimeout(function(){e.state.focused||(e.display.shift=!1)},150))}function Cn(e){for(var t=e.display,r=t.lineDiv.offsetTop,n=0;n<t.view.length;n++){var i=t.view[n],o=e.options.lineWrapping,l=void 0,c=0;if(!i.hidden){if(a&&s<8){var u=i.node.offsetTop+i.node.offsetHeight;l=u-r,r=u}else{var f=i.node.getBoundingClientRect();l=f.bottom-f.top,!o&&i.text.firstChild&&(c=i.text.firstChild.getBoundingClientRect().right-f.left-1)}var d=i.line.height-l;if((d>.005||d<-.005)&&(Xe(i.line,l),Sn(i.line),i.rest))for(var p=0;p<i.rest.length;p++)Sn(i.rest[p]);if(c>e.display.sizerWidth){var h=Math.ceil(c/tn(e.display));h>e.display.maxLineLength&&(e.display.maxLineLength=h,e.display.maxLine=i.line,e.display.maxLineChanged=!0)}}}}function Sn(e){if(e.widgets)for(var t=0;t<e.widgets.length;++t){var r=e.widgets[t],n=r.node.parentNode;n&&(r.height=n.offsetHeight)}}function Tn(e,t,r){var n=r&&null!=r.top?Math.max(0,r.top):e.scroller.scrollTop;n=Math.floor(n-_r(e));var i=r&&null!=r.bottom?r.bottom:n+e.wrapper.clientHeight,o=Ze(t,n),a=Ze(t,i);if(r&&r.ensure){var s=r.ensure.from.line,l=r.ensure.to.line;s<o?(o=s,a=Ze(t,Ht(Ge(t,s))+e.wrapper.clientHeight)):Math.min(l,t.lastLine())>=a&&(o=Ze(t,Ht(Ge(t,l))-e.wrapper.clientHeight),a=l)}return{from:o,to:Math.max(a,o+1)}}function Mn(e,t){var r=e.display,n=en(e.display);t.top<0&&(t.top=0);var i=e.curOp&&null!=e.curOp.scrollTop?e.curOp.scrollTop:r.scroller.scrollTop,o=Mr(e),a={};t.bottom-t.top>o&&(t.bottom=t.top+o);var s=e.doc.height+kr(r),l=t.top<n,c=t.bottom>s-n;if(t.top<i)a.scrollTop=l?0:t.top;else if(t.bottom>i+o){var u=Math.min(t.top,(c?s:t.bottom)-o);u!=i&&(a.scrollTop=u)}var f=e.curOp&&null!=e.curOp.scrollLeft?e.curOp.scrollLeft:r.scroller.scrollLeft,d=Tr(e)-(e.options.fixedGutter?r.gutters.offsetWidth:0),p=t.right-t.left>d;return p&&(t.right=t.left+d),t.left<10?a.scrollLeft=0:t.left<f?a.scrollLeft=Math.max(0,t.left-(p?0:10)):t.right>d+f-3&&(a.scrollLeft=t.right+(p?0:10)-d),a}function Ln(e,t){null!=t&&(Nn(e),e.curOp.scrollTop=(null==e.curOp.scrollTop?e.doc.scrollTop:e.curOp.scrollTop)+t)}function An(e){Nn(e);var t=e.getCursor();e.curOp.scrollToPos={from:t,to:t,margin:e.options.cursorScrollMargin}}function On(e,t,r){null==t&&null==r||Nn(e),null!=t&&(e.curOp.scrollLeft=t),null!=r&&(e.curOp.scrollTop=r)}function Nn(e){var t=e.curOp.scrollToPos;t&&(e.curOp.scrollToPos=null,En(e,Vr(e,t.from),Vr(e,t.to),t.margin))}function En(e,t,r,n){var i=Mn(e,{left:Math.min(t.left,r.left),top:Math.min(t.top,r.top)-n,right:Math.max(t.right,r.right),bottom:Math.max(t.bottom,r.bottom)+n});On(e,i.scrollLeft,i.scrollTop)}function Dn(e,t){Math.abs(e.doc.scrollTop-t)<2||(r||oi(e,{top:t}),In(e,t,!0),r&&oi(e),ei(e,100))}function In(e,t,r){t=Math.min(e.display.scroller.scrollHeight-e.display.scroller.clientHeight,t),(e.display.scroller.scrollTop!=t||r)&&(e.doc.scrollTop=t,e.display.scrollbars.setScrollTop(t),e.display.scroller.scrollTop!=t&&(e.display.scroller.scrollTop=t))}function Pn(e,t,r,n){t=Math.min(t,e.display.scroller.scrollWidth-e.display.scroller.clientWidth),(r?t==e.doc.scrollLeft:Math.abs(e.doc.scrollLeft-t)<2)&&!n||(e.doc.scrollLeft=t,li(e),e.display.scroller.scrollLeft!=t&&(e.display.scroller.scrollLeft=t),e.display.scrollbars.setScrollLeft(t))}function zn(e){var t=e.display,r=t.gutters.offsetWidth,n=Math.round(e.doc.height+kr(e.display));return{clientHeight:t.scroller.clientHeight,viewHeight:t.wrapper.clientHeight,scrollWidth:t.scroller.scrollWidth,clientWidth:t.scroller.clientWidth,viewWidth:t.wrapper.clientWidth,barLeft:e.options.fixedGutter?r:0,docHeight:n,scrollHeight:n+Sr(e)+t.barHeight,nativeBarWidth:t.nativeBarWidth,gutterWidth:r}}var Rn=function(e,t,r){this.cm=r;var n=this.vert=A("div",[A("div",null,null,"min-width: 1px")],"CodeMirror-vscrollbar"),i=this.horiz=A("div",[A("div",null,null,"height: 100%; min-height: 1px")],"CodeMirror-hscrollbar");n.tabIndex=i.tabIndex=-1,e(n),e(i),de(n,"scroll",function(){n.clientHeight&&t(n.scrollTop,"vertical")}),de(i,"scroll",function(){i.clientWidth&&t(i.scrollLeft,"horizontal")}),this.checkedZeroWidth=!1,a&&s<8&&(this.horiz.style.minHeight=this.vert.style.minWidth="18px")};Rn.prototype.update=function(e){var t=e.scrollWidth>e.clientWidth+1,r=e.scrollHeight>e.clientHeight+1,n=e.nativeBarWidth;if(r){this.vert.style.display="block",this.vert.style.bottom=t?n+"px":"0";var i=e.viewHeight-(t?n:0);this.vert.firstChild.style.height=Math.max(0,e.scrollHeight-e.clientHeight+i)+"px"}else this.vert.style.display="",this.vert.firstChild.style.height="0";if(t){this.horiz.style.display="block",this.horiz.style.right=r?n+"px":"0",this.horiz.style.left=e.barLeft+"px";var o=e.viewWidth-e.barLeft-(r?n:0);this.horiz.firstChild.style.width=Math.max(0,e.scrollWidth-e.clientWidth+o)+"px"}else this.horiz.style.display="",this.horiz.firstChild.style.width="0";return!this.checkedZeroWidth&&e.clientHeight>0&&(0==n&&this.zeroWidthHack(),this.checkedZeroWidth=!0),{right:r?n:0,bottom:t?n:0}},Rn.prototype.setScrollLeft=function(e){this.horiz.scrollLeft!=e&&(this.horiz.scrollLeft=e),this.disableHoriz&&this.enableZeroWidthBar(this.horiz,this.disableHoriz,"horiz")},Rn.prototype.setScrollTop=function(e){this.vert.scrollTop!=e&&(this.vert.scrollTop=e),this.disableVert&&this.enableZeroWidthBar(this.vert,this.disableVert,"vert")},Rn.prototype.zeroWidthHack=function(){var e=y&&!p?"12px":"18px";this.horiz.style.height=this.vert.style.width=e,this.horiz.style.pointerEvents=this.vert.style.pointerEvents="none",this.disableHoriz=new F,this.disableVert=new F},Rn.prototype.enableZeroWidthBar=function(e,t,r){e.style.pointerEvents="auto",t.set(1e3,function n(){var i=e.getBoundingClientRect();("vert"==r?document.elementFromPoint(i.right-1,(i.top+i.bottom)/2):document.elementFromPoint((i.right+i.left)/2,i.bottom-1))!=e?e.style.pointerEvents="none":t.set(1e3,n)})},Rn.prototype.clear=function(){var e=this.horiz.parentNode;e.removeChild(this.horiz),e.removeChild(this.vert)};var $n=function(){};function Fn(e,t){t||(t=zn(e));var r=e.display.barWidth,n=e.display.barHeight;jn(e,t);for(var i=0;i<4&&r!=e.display.barWidth||n!=e.display.barHeight;i++)r!=e.display.barWidth&&e.options.lineWrapping&&Cn(e),jn(e,zn(e)),r=e.display.barWidth,n=e.display.barHeight}function jn(e,t){var r=e.display,n=r.scrollbars.update(t);r.sizer.style.paddingRight=(r.barWidth=n.right)+"px",r.sizer.style.paddingBottom=(r.barHeight=n.bottom)+"px",r.heightForcer.style.borderBottom=n.bottom+"px solid transparent",n.right&&n.bottom?(r.scrollbarFiller.style.display="block",r.scrollbarFiller.style.height=n.bottom+"px",r.scrollbarFiller.style.width=n.right+"px"):r.scrollbarFiller.style.display="",n.bottom&&e.options.coverGutterNextToScrollbar&&e.options.fixedGutter?(r.gutterFiller.style.display="block",r.gutterFiller.style.height=n.bottom+"px",r.gutterFiller.style.width=t.gutterWidth+"px"):r.gutterFiller.style.display=""}$n.prototype.update=function(){return{bottom:0,right:0}},$n.prototype.setScrollLeft=function(){},$n.prototype.setScrollTop=function(){},$n.prototype.clear=function(){};var Wn={native:Rn,null:$n};function qn(e){e.display.scrollbars&&(e.display.scrollbars.clear(),e.display.scrollbars.addClass&&T(e.display.wrapper,e.display.scrollbars.addClass)),e.display.scrollbars=new Wn[e.options.scrollbarStyle](function(t){e.display.wrapper.insertBefore(t,e.display.scrollbarFiller),de(t,"mousedown",function(){e.state.focused&&setTimeout(function(){return e.display.input.focus()},0)}),t.setAttribute("cm-not-content","true")},function(t,r){"horizontal"==r?Pn(e,t):Dn(e,t)},e),e.display.scrollbars.addClass&&D(e.display.wrapper,e.display.scrollbars.addClass)}var Hn=0;function Un(e){var t;e.curOp={cm:e,viewChanged:!1,startHeight:e.doc.height,forceUpdate:!1,updateInput:0,typing:!1,changeObjs:null,cursorActivityHandlers:null,cursorActivityCalled:0,selectionChanged:!1,updateMaxLine:!1,scrollLeft:null,scrollTop:null,scrollToPos:null,focus:!1,id:++Hn},t=e.curOp,or?or.ops.push(t):t.ownsGroup=or={ops:[t],delayedCallbacks:[]}}function Bn(e){var t=e.curOp;t&&function(e,t){var r=e.ownsGroup;if(r)try{!function(e){var t=e.delayedCallbacks,r=0;do{for(;r<t.length;r++)t[r].call(null);for(var n=0;n<e.ops.length;n++){var i=e.ops[n];if(i.cursorActivityHandlers)for(;i.cursorActivityCalled<i.cursorActivityHandlers.length;)i.cursorActivityHandlers[i.cursorActivityCalled++].call(null,i.cm)}}while(r<t.length)}(r)}finally{or=null,t(r)}}(t,function(e){for(var t=0;t<e.ops.length;t++)e.ops[t].cm.curOp=null;!function(e){for(var t=e.ops,r=0;r<t.length;r++)Gn(t[r]);for(var n=0;n<t.length;n++)(i=t[n]).updatedDisplay=i.mustUpdate&&ni(i.cm,i.update);var i;for(var o=0;o<t.length;o++)Vn(t[o]);for(var a=0;a<t.length;a++)Kn(t[a]);for(var s=0;s<t.length;s++)Xn(t[s])}(e)})}function Gn(e){var t=e.cm,r=t.display;!function(e){var t=e.display;!t.scrollbarsClipped&&t.scroller.offsetWidth&&(t.nativeBarWidth=t.scroller.offsetWidth-t.scroller.clientWidth,t.heightForcer.style.height=Sr(e)+"px",t.sizer.style.marginBottom=-t.nativeBarWidth+"px",t.sizer.style.borderRightWidth=Sr(e)+"px",t.scrollbarsClipped=!0)}(t),e.updateMaxLine&&Bt(t),e.mustUpdate=e.viewChanged||e.forceUpdate||null!=e.scrollTop||e.scrollToPos&&(e.scrollToPos.from.line<r.viewFrom||e.scrollToPos.to.line>=r.viewTo)||r.maxLineChanged&&t.options.lineWrapping,e.update=e.mustUpdate&&new ri(t,e.mustUpdate&&{top:e.scrollTop,ensure:e.scrollToPos},e.forceUpdate)}function Vn(e){var t=e.cm,r=t.display;e.updatedDisplay&&Cn(t),e.barMeasure=zn(t),r.maxLineChanged&&!t.options.lineWrapping&&(e.adjustWidthTo=Ar(t,r.maxLine,r.maxLine.text.length).left+3,t.display.sizerWidth=e.adjustWidthTo,e.barMeasure.scrollWidth=Math.max(r.scroller.clientWidth,r.sizer.offsetLeft+e.adjustWidthTo+Sr(t)+t.display.barWidth),e.maxScrollLeft=Math.max(0,r.sizer.offsetLeft+e.adjustWidthTo-Tr(t))),(e.updatedDisplay||e.selectionChanged)&&(e.preparedSelection=r.input.prepareSelection())}function Kn(e){var t=e.cm;null!=e.adjustWidthTo&&(t.display.sizer.style.minWidth=e.adjustWidthTo+"px",e.maxScrollLeft<t.doc.scrollLeft&&Pn(t,Math.min(t.display.scroller.scrollLeft,e.maxScrollLeft),!0),t.display.maxLineChanged=!1);var r=e.focus&&e.focus==E();e.preparedSelection&&t.display.input.showSelection(e.preparedSelection,r),(e.updatedDisplay||e.startHeight!=t.doc.height)&&Fn(t,e.barMeasure),e.updatedDisplay&&si(t,e.barMeasure),e.selectionChanged&&bn(t),t.state.focused&&e.updateInput&&t.display.input.reset(e.typing),r&&wn(e.cm)}function Xn(e){var t=e.cm,r=t.display,n=t.doc;(e.updatedDisplay&&ii(t,e.update),null==r.wheelStartX||null==e.scrollTop&&null==e.scrollLeft&&!e.scrollToPos||(r.wheelStartX=r.wheelStartY=null),null!=e.scrollTop&&In(t,e.scrollTop,e.forceScroll),null!=e.scrollLeft&&Pn(t,e.scrollLeft,!0,!0),e.scrollToPos)&&function(e,t){if(!ge(e,"scrollCursorIntoView")){var r=e.display,n=r.sizer.getBoundingClientRect(),i=null;if(t.top+n.top<0?i=!0:t.bottom+n.top>(window.innerHeight||document.documentElement.clientHeight)&&(i=!1),null!=i&&!h){var o=A("div","​",null,"position: absolute;\n                         top: "+(t.top-r.viewOffset-_r(e.display))+"px;\n                         height: "+(t.bottom-t.top+Sr(e)+r.barHeight)+"px;\n                         left: "+t.left+"px; width: "+Math.max(2,t.right-t.left)+"px;");e.display.lineSpace.appendChild(o),o.scrollIntoView(i),e.display.lineSpace.removeChild(o)}}}(t,function(e,t,r,n){var i;null==n&&(n=0),e.options.lineWrapping||t!=r||(r="before"==(t=t.ch?et(t.line,"before"==t.sticky?t.ch-1:t.ch,"after"):t).sticky?et(t.line,t.ch+1,"before"):t);for(var o=0;o<5;o++){var a=!1,s=Gr(e,t),l=r&&r!=t?Gr(e,r):s,c=Mn(e,i={left:Math.min(s.left,l.left),top:Math.min(s.top,l.top)-n,right:Math.max(s.left,l.left),bottom:Math.max(s.bottom,l.bottom)+n}),u=e.doc.scrollTop,f=e.doc.scrollLeft;if(null!=c.scrollTop&&(Dn(e,c.scrollTop),Math.abs(e.doc.scrollTop-u)>1&&(a=!0)),null!=c.scrollLeft&&(Pn(e,c.scrollLeft),Math.abs(e.doc.scrollLeft-f)>1&&(a=!0)),!a)break}return i}(t,st(n,e.scrollToPos.from),st(n,e.scrollToPos.to),e.scrollToPos.margin));var i=e.maybeHiddenMarkers,o=e.maybeUnhiddenMarkers;if(i)for(var a=0;a<i.length;++a)i[a].lines.length||me(i[a],"hide");if(o)for(var s=0;s<o.length;++s)o[s].lines.length&&me(o[s],"unhide");r.wrapper.offsetHeight&&(n.scrollTop=t.display.scroller.scrollTop),e.changeObjs&&me(t,"changes",t,e.changeObjs),e.update&&e.update.finish()}function Yn(e,t){if(e.curOp)return t();Un(e);try{return t()}finally{Bn(e)}}function Zn(e,t){return function(){if(e.curOp)return t.apply(e,arguments);Un(e);try{return t.apply(e,arguments)}finally{Bn(e)}}}function Jn(e){return function(){if(this.curOp)return e.apply(this,arguments);Un(this);try{return e.apply(this,arguments)}finally{Bn(this)}}}function Qn(e){return function(){var t=this.cm;if(!t||t.curOp)return e.apply(this,arguments);Un(t);try{return e.apply(this,arguments)}finally{Bn(t)}}}function ei(e,t){e.doc.highlightFrontier<e.display.viewTo&&e.state.highlight.set(t,z(ti,e))}function ti(e){var t=e.doc;if(!(t.highlightFrontier>=e.display.viewTo)){var r=+new Date+e.options.workTime,n=pt(e,t.highlightFrontier),i=[];t.iter(n.line,Math.min(t.first+t.size,e.display.viewTo+500),function(o){if(n.line>=e.display.viewFrom){var a=o.styles,s=o.text.length>e.options.maxHighlightLength?qe(t.mode,n.state):null,l=ft(e,o,n,!0);s&&(n.state=s),o.styles=l.styles;var c=o.styleClasses,u=l.classes;u?o.styleClasses=u:c&&(o.styleClasses=null);for(var f=!a||a.length!=o.styles.length||c!=u&&(!c||!u||c.bgClass!=u.bgClass||c.textClass!=u.textClass),d=0;!f&&d<a.length;++d)f=a[d]!=o.styles[d];f&&i.push(n.line),o.stateAfter=n.save(),n.nextLine()}else o.text.length<=e.options.maxHighlightLength&&ht(e,o.text,n),o.stateAfter=n.line%5==0?n.save():null,n.nextLine();if(+new Date>r)return ei(e,e.options.workDelay),!0}),t.highlightFrontier=n.line,t.modeFrontier=Math.max(t.modeFrontier,n.line),i.length&&Yn(e,function(){for(var t=0;t<i.length;t++)un(e,i[t],"text")})}}var ri=function(e,t,r){var n=e.display;this.viewport=t,this.visible=Tn(n,e.doc,t),this.editorIsHidden=!n.wrapper.offsetWidth,this.wrapperHeight=n.wrapper.clientHeight,this.wrapperWidth=n.wrapper.clientWidth,this.oldDisplayWidth=Tr(e),this.force=r,this.dims=rn(e),this.events=[]};function ni(e,t){var r=e.display,n=e.doc;if(t.editorIsHidden)return fn(e),!1;if(!t.force&&t.visible.from>=r.viewFrom&&t.visible.to<=r.viewTo&&(null==r.updateLineNumbers||r.updateLineNumbers>=r.viewTo)&&r.renderedView==r.view&&0==pn(e))return!1;ci(e)&&(fn(e),t.dims=rn(e));var i=n.first+n.size,o=Math.max(t.visible.from-e.options.viewportMargin,n.first),a=Math.min(i,t.visible.to+e.options.viewportMargin);r.viewFrom<o&&o-r.viewFrom<20&&(o=Math.max(n.first,r.viewFrom)),r.viewTo>a&&r.viewTo-a<20&&(a=Math.min(i,r.viewTo)),_t&&(o=Ft(e.doc,o),a=jt(e.doc,a));var s=o!=r.viewFrom||a!=r.viewTo||r.lastWrapHeight!=t.wrapperHeight||r.lastWrapWidth!=t.wrapperWidth;!function(e,t,r){var n=e.display;0==n.view.length||t>=n.viewTo||r<=n.viewFrom?(n.view=ir(e,t,r),n.viewFrom=t):(n.viewFrom>t?n.view=ir(e,t,n.viewFrom).concat(n.view):n.viewFrom<t&&(n.view=n.view.slice(ln(e,t))),n.viewFrom=t,n.viewTo<r?n.view=n.view.concat(ir(e,n.viewTo,r)):n.viewTo>r&&(n.view=n.view.slice(0,ln(e,r)))),n.viewTo=r}(e,o,a),r.viewOffset=Ht(Ge(e.doc,r.viewFrom)),e.display.mover.style.top=r.viewOffset+"px";var c=pn(e);if(!s&&0==c&&!t.force&&r.renderedView==r.view&&(null==r.updateLineNumbers||r.updateLineNumbers>=r.viewTo))return!1;var u=function(e){if(e.hasFocus())return null;var t=E();if(!t||!N(e.display.lineDiv,t))return null;var r={activeElt:t};if(window.getSelection){var n=window.getSelection();n.anchorNode&&n.extend&&N(e.display.lineDiv,n.anchorNode)&&(r.anchorNode=n.anchorNode,r.anchorOffset=n.anchorOffset,r.focusNode=n.focusNode,r.focusOffset=n.focusOffset)}return r}(e);return c>4&&(r.lineDiv.style.display="none"),function(e,t,r){var n=e.display,i=e.options.lineNumbers,o=n.lineDiv,a=o.firstChild;function s(t){var r=t.nextSibling;return l&&y&&e.display.currentWheelTarget==t?t.style.display="none":t.parentNode.removeChild(t),r}for(var c=n.view,u=n.viewFrom,f=0;f<c.length;f++){var d=c[f];if(d.hidden);else if(d.node&&d.node.parentNode==o){for(;a!=d.node;)a=s(a);var p=i&&null!=t&&t<=u&&d.lineNumber;d.changes&&(j(d.changes,"gutter")>-1&&(p=!1),cr(e,d,u,r)),p&&(M(d.lineNumber),d.lineNumber.appendChild(document.createTextNode(Qe(e.options,u)))),a=d.node.nextSibling}else{var h=gr(e,d,u,r);o.insertBefore(h,a)}u+=d.size}for(;a;)a=s(a)}(e,r.updateLineNumbers,t.dims),c>4&&(r.lineDiv.style.display=""),r.renderedView=r.view,function(e){if(e&&e.activeElt&&e.activeElt!=E()&&(e.activeElt.focus(),e.anchorNode&&N(document.body,e.anchorNode)&&N(document.body,e.focusNode))){var t=window.getSelection(),r=document.createRange();r.setEnd(e.anchorNode,e.anchorOffset),r.collapse(!1),t.removeAllRanges(),t.addRange(r),t.extend(e.focusNode,e.focusOffset)}}(u),M(r.cursorDiv),M(r.selectionDiv),r.gutters.style.height=r.sizer.style.minHeight=0,s&&(r.lastWrapHeight=t.wrapperHeight,r.lastWrapWidth=t.wrapperWidth,ei(e,400)),r.updateLineNumbers=null,!0}function ii(e,t){for(var r=t.viewport,n=!0;(n&&e.options.lineWrapping&&t.oldDisplayWidth!=Tr(e)||(r&&null!=r.top&&(r={top:Math.min(e.doc.height+kr(e.display)-Mr(e),r.top)}),t.visible=Tn(e.display,e.doc,r),!(t.visible.from>=e.display.viewFrom&&t.visible.to<=e.display.viewTo)))&&ni(e,t);n=!1){Cn(e);var i=zn(e);hn(e),Fn(e,i),si(e,i),t.force=!1}t.signal(e,"update",e),e.display.viewFrom==e.display.reportedViewFrom&&e.display.viewTo==e.display.reportedViewTo||(t.signal(e,"viewportChange",e,e.display.viewFrom,e.display.viewTo),e.display.reportedViewFrom=e.display.viewFrom,e.display.reportedViewTo=e.display.viewTo)}function oi(e,t){var r=new ri(e,t);if(ni(e,r)){Cn(e),ii(e,r);var n=zn(e);hn(e),Fn(e,n),si(e,n),r.finish()}}function ai(e){var t=e.gutters.offsetWidth;e.sizer.style.marginLeft=t+"px"}function si(e,t){e.display.sizer.style.minHeight=t.docHeight+"px",e.display.heightForcer.style.top=t.docHeight+"px",e.display.gutters.style.height=t.docHeight+e.display.barHeight+Sr(e)+"px"}function li(e){var t=e.display,r=t.view;if(t.alignWidgets||t.gutters.firstChild&&e.options.fixedGutter){for(var n=nn(t)-t.scroller.scrollLeft+e.doc.scrollLeft,i=t.gutters.offsetWidth,o=n+"px",a=0;a<r.length;a++)if(!r[a].hidden){e.options.fixedGutter&&(r[a].gutter&&(r[a].gutter.style.left=o),r[a].gutterBackground&&(r[a].gutterBackground.style.left=o));var s=r[a].alignable;if(s)for(var l=0;l<s.length;l++)s[l].style.left=o}e.options.fixedGutter&&(t.gutters.style.left=n+i+"px")}}function ci(e){if(!e.options.lineNumbers)return!1;var t=e.doc,r=Qe(e.options,t.first+t.size-1),n=e.display;if(r.length!=n.lineNumChars){var i=n.measure.appendChild(A("div",[A("div",r)],"CodeMirror-linenumber CodeMirror-gutter-elt")),o=i.firstChild.offsetWidth,a=i.offsetWidth-o;return n.lineGutter.style.width="",n.lineNumInnerWidth=Math.max(o,n.lineGutter.offsetWidth-a)+1,n.lineNumWidth=n.lineNumInnerWidth+a,n.lineNumChars=n.lineNumInnerWidth?r.length:-1,n.lineGutter.style.width=n.lineNumWidth+"px",ai(e.display),!0}return!1}function ui(e,t){for(var r=[],n=!1,i=0;i<e.length;i++){var o=e[i],a=null;if("string"!=typeof o&&(a=o.style,o=o.className),"CodeMirror-linenumbers"==o){if(!t)continue;n=!0}r.push({className:o,style:a})}return t&&!n&&r.push({className:"CodeMirror-linenumbers",style:null}),r}function fi(e){var t=e.gutters,r=e.gutterSpecs;M(t),e.lineGutter=null;for(var n=0;n<r.length;++n){var i=r[n],o=i.className,a=i.style,s=t.appendChild(A("div",null,"CodeMirror-gutter "+o));a&&(s.style.cssText=a),"CodeMirror-linenumbers"==o&&(e.lineGutter=s,s.style.width=(e.lineNumWidth||1)+"px")}t.style.display=r.length?"":"none",ai(e)}function di(e){fi(e.display),cn(e),li(e)}ri.prototype.signal=function(e,t){ye(e,t)&&this.events.push(arguments)},ri.prototype.finish=function(){for(var e=0;e<this.events.length;e++)me.apply(null,this.events[e])};var pi=0,hi=null;function mi(e){var t=e.wheelDeltaX,r=e.wheelDeltaY;return null==t&&e.detail&&e.axis==e.HORIZONTAL_AXIS&&(t=e.detail),null==r&&e.detail&&e.axis==e.VERTICAL_AXIS?r=e.detail:null==r&&(r=e.wheelDelta),{x:t,y:r}}function gi(e){var t=mi(e);return t.x*=hi,t.y*=hi,t}function vi(e,t){var n=mi(t),i=n.x,o=n.y,a=e.display,s=a.scroller,c=s.scrollWidth>s.clientWidth,u=s.scrollHeight>s.clientHeight;if(i&&c||o&&u){if(o&&y&&l)e:for(var d=t.target,p=a.view;d!=s;d=d.parentNode)for(var h=0;h<p.length;h++)if(p[h].node==d){e.display.currentWheelTarget=d;break e}if(i&&!r&&!f&&null!=hi)return o&&u&&Dn(e,Math.max(0,s.scrollTop+o*hi)),Pn(e,Math.max(0,s.scrollLeft+i*hi)),(!o||o&&u)&&we(t),void(a.wheelStartX=null);if(o&&null!=hi){var m=o*hi,g=e.doc.scrollTop,v=g+a.wrapper.clientHeight;m<0?g=Math.max(0,g+m-50):v=Math.min(e.doc.height,v+m+50),oi(e,{top:g,bottom:v})}pi<20&&(null==a.wheelStartX?(a.wheelStartX=s.scrollLeft,a.wheelStartY=s.scrollTop,a.wheelDX=i,a.wheelDY=o,setTimeout(function(){if(null!=a.wheelStartX){var e=s.scrollLeft-a.wheelStartX,t=s.scrollTop-a.wheelStartY,r=t&&a.wheelDY&&t/a.wheelDY||e&&a.wheelDX&&e/a.wheelDX;a.wheelStartX=a.wheelStartY=null,r&&(hi=(hi*pi+r)/(pi+1),++pi)}},200)):(a.wheelDX+=i,a.wheelDY+=o))}}a?hi=-.53:r?hi=15:u?hi=-.7:d&&(hi=-1/3);var yi=function(e,t){this.ranges=e,this.primIndex=t};yi.prototype.primary=function(){return this.ranges[this.primIndex]},yi.prototype.equals=function(e){if(e==this)return!0;if(e.primIndex!=this.primIndex||e.ranges.length!=this.ranges.length)return!1;for(var t=0;t<this.ranges.length;t++){var r=this.ranges[t],n=e.ranges[t];if(!rt(r.anchor,n.anchor)||!rt(r.head,n.head))return!1}return!0},yi.prototype.deepCopy=function(){for(var e=[],t=0;t<this.ranges.length;t++)e[t]=new bi(nt(this.ranges[t].anchor),nt(this.ranges[t].head));return new yi(e,this.primIndex)},yi.prototype.somethingSelected=function(){for(var e=0;e<this.ranges.length;e++)if(!this.ranges[e].empty())return!0;return!1},yi.prototype.contains=function(e,t){t||(t=e);for(var r=0;r<this.ranges.length;r++){var n=this.ranges[r];if(tt(t,n.from())>=0&&tt(e,n.to())<=0)return r}return-1};var bi=function(e,t){this.anchor=e,this.head=t};function wi(e,t,r){var n=e&&e.options.selectionsMayTouch,i=t[r];t.sort(function(e,t){return tt(e.from(),t.from())}),r=j(t,i);for(var o=1;o<t.length;o++){var a=t[o],s=t[o-1],l=tt(s.to(),a.from());if(n&&!a.empty()?l>0:l>=0){var c=ot(s.from(),a.from()),u=it(s.to(),a.to()),f=s.empty()?a.from()==a.head:s.from()==s.head;o<=r&&--r,t.splice(--o,2,new bi(f?u:c,f?c:u))}}return new yi(t,r)}function xi(e,t){return new yi([new bi(e,t||e)],0)}function _i(e){return e.text?et(e.from.line+e.text.length-1,X(e.text).length+(1==e.text.length?e.from.ch:0)):e.to}function ki(e,t){if(tt(e,t.from)<0)return e;if(tt(e,t.to)<=0)return _i(t);var r=e.line+t.text.length-(t.to.line-t.from.line)-1,n=e.ch;return e.line==t.to.line&&(n+=_i(t).ch-t.to.ch),et(r,n)}function Ci(e,t){for(var r=[],n=0;n<e.sel.ranges.length;n++){var i=e.sel.ranges[n];r.push(new bi(ki(i.anchor,t),ki(i.head,t)))}return wi(e.cm,r,e.sel.primIndex)}function Si(e,t,r){return e.line==t.line?et(r.line,e.ch-t.ch+r.ch):et(r.line+(e.line-t.line),e.ch)}function Ti(e){e.doc.mode=Fe(e.options,e.doc.modeOption),Mi(e)}function Mi(e){e.doc.iter(function(e){e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null)}),e.doc.modeFrontier=e.doc.highlightFrontier=e.doc.first,ei(e,100),e.state.modeGen++,e.curOp&&cn(e)}function Li(e,t){return 0==t.from.ch&&0==t.to.ch&&""==X(t.text)&&(!e.cm||e.cm.options.wholeLineUpdateBefore)}function Ai(e,t,r,n){function i(e){return r?r[e]:null}function o(e,r,i){!function(e,t,r,n){e.text=t,e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null),null!=e.order&&(e.order=null),Lt(e),At(e,r);var i=n?n(e):1;i!=e.height&&Xe(e,i)}(e,r,i,n),sr(e,"change",e,t)}function a(e,t){for(var r=[],o=e;o<t;++o)r.push(new Gt(c[o],i(o),n));return r}var s=t.from,l=t.to,c=t.text,u=Ge(e,s.line),f=Ge(e,l.line),d=X(c),p=i(c.length-1),h=l.line-s.line;if(t.full)e.insert(0,a(0,c.length)),e.remove(c.length,e.size-c.length);else if(Li(e,t)){var m=a(0,c.length-1);o(f,f.text,p),h&&e.remove(s.line,h),m.length&&e.insert(s.line,m)}else if(u==f)if(1==c.length)o(u,u.text.slice(0,s.ch)+d+u.text.slice(l.ch),p);else{var g=a(1,c.length-1);g.push(new Gt(d+u.text.slice(l.ch),p,n)),o(u,u.text.slice(0,s.ch)+c[0],i(0)),e.insert(s.line+1,g)}else if(1==c.length)o(u,u.text.slice(0,s.ch)+c[0]+f.text.slice(l.ch),i(0)),e.remove(s.line+1,h);else{o(u,u.text.slice(0,s.ch)+c[0],i(0)),o(f,d+f.text.slice(l.ch),p);var v=a(1,c.length-1);h>1&&e.remove(s.line+1,h-1),e.insert(s.line+1,v)}sr(e,"change",e,t)}function Oi(e,t,r){!function e(n,i,o){if(n.linked)for(var a=0;a<n.linked.length;++a){var s=n.linked[a];if(s.doc!=i){var l=o&&s.sharedHist;r&&!l||(t(s.doc,l),e(s.doc,n,l))}}}(e,null,!0)}function Ni(e,t){if(t.cm)throw new Error("This document is already in use.");e.doc=t,t.cm=e,an(e),Ti(e),Ei(e),e.options.lineWrapping||Bt(e),e.options.mode=t.modeOption,cn(e)}function Ei(e){("rtl"==e.doc.direction?D:T)(e.display.lineDiv,"CodeMirror-rtl")}function Di(e){this.done=[],this.undone=[],this.undoDepth=1/0,this.lastModTime=this.lastSelTime=0,this.lastOp=this.lastSelOp=null,this.lastOrigin=this.lastSelOrigin=null,this.generation=this.maxGeneration=e||1}function Ii(e,t){var r={from:nt(t.from),to:_i(t),text:Ve(e,t.from,t.to)};return Fi(e,r,t.from.line,t.to.line+1),Oi(e,function(e){return Fi(e,r,t.from.line,t.to.line+1)},!0),r}function Pi(e){for(;e.length;){if(!X(e).ranges)break;e.pop()}}function zi(e,t,r,n){var i=e.history;i.undone.length=0;var o,a,s=+new Date;if((i.lastOp==n||i.lastOrigin==t.origin&&t.origin&&("+"==t.origin.charAt(0)&&i.lastModTime>s-(e.cm?e.cm.options.historyEventDelay:500)||"*"==t.origin.charAt(0)))&&(o=function(e,t){return t?(Pi(e.done),X(e.done)):e.done.length&&!X(e.done).ranges?X(e.done):e.done.length>1&&!e.done[e.done.length-2].ranges?(e.done.pop(),X(e.done)):void 0}(i,i.lastOp==n)))a=X(o.changes),0==tt(t.from,t.to)&&0==tt(t.from,a.to)?a.to=_i(t):o.changes.push(Ii(e,t));else{var l=X(i.done);for(l&&l.ranges||$i(e.sel,i.done),o={changes:[Ii(e,t)],generation:i.generation},i.done.push(o);i.done.length>i.undoDepth;)i.done.shift(),i.done[0].ranges||i.done.shift()}i.done.push(r),i.generation=++i.maxGeneration,i.lastModTime=i.lastSelTime=s,i.lastOp=i.lastSelOp=n,i.lastOrigin=i.lastSelOrigin=t.origin,a||me(e,"historyAdded")}function Ri(e,t,r,n){var i=e.history,o=n&&n.origin;r==i.lastSelOp||o&&i.lastSelOrigin==o&&(i.lastModTime==i.lastSelTime&&i.lastOrigin==o||function(e,t,r,n){var i=t.charAt(0);return"*"==i||"+"==i&&r.ranges.length==n.ranges.length&&r.somethingSelected()==n.somethingSelected()&&new Date-e.history.lastSelTime<=(e.cm?e.cm.options.historyEventDelay:500)}(e,o,X(i.done),t))?i.done[i.done.length-1]=t:$i(t,i.done),i.lastSelTime=+new Date,i.lastSelOrigin=o,i.lastSelOp=r,n&&!1!==n.clearRedo&&Pi(i.undone)}function $i(e,t){var r=X(t);r&&r.ranges&&r.equals(e)||t.push(e)}function Fi(e,t,r,n){var i=t["spans_"+e.id],o=0;e.iter(Math.max(e.first,r),Math.min(e.first+e.size,n),function(r){r.markedSpans&&((i||(i=t["spans_"+e.id]={}))[o]=r.markedSpans),++o})}function ji(e){if(!e)return null;for(var t,r=0;r<e.length;++r)e[r].marker.explicitlyCleared?t||(t=e.slice(0,r)):t&&t.push(e[r]);return t?t.length?t:null:e}function Wi(e,t){var r=function(e,t){var r=t["spans_"+e.id];if(!r)return null;for(var n=[],i=0;i<t.text.length;++i)n.push(ji(r[i]));return n}(e,t),n=Tt(e,t);if(!r)return n;if(!n)return r;for(var i=0;i<r.length;++i){var o=r[i],a=n[i];if(o&&a)e:for(var s=0;s<a.length;++s){for(var l=a[s],c=0;c<o.length;++c)if(o[c].marker==l.marker)continue e;o.push(l)}else a&&(r[i]=a)}return r}function qi(e,t,r){for(var n=[],i=0;i<e.length;++i){var o=e[i];if(o.ranges)n.push(r?yi.prototype.deepCopy.call(o):o);else{var a=o.changes,s=[];n.push({changes:s});for(var l=0;l<a.length;++l){var c=a[l],u=void 0;if(s.push({from:c.from,to:c.to,text:c.text}),t)for(var f in c)(u=f.match(/^spans_(\d+)$/))&&j(t,Number(u[1]))>-1&&(X(s)[f]=c[f],delete c[f])}}}return n}function Hi(e,t,r,n){if(n){var i=e.anchor;if(r){var o=tt(t,i)<0;o!=tt(r,i)<0?(i=t,t=r):o!=tt(t,r)<0&&(t=r)}return new bi(i,t)}return new bi(r||t,t)}function Ui(e,t,r,n,i){null==i&&(i=e.cm&&(e.cm.display.shift||e.extend)),Xi(e,new yi([Hi(e.sel.primary(),t,r,i)],0),n)}function Bi(e,t,r){for(var n=[],i=e.cm&&(e.cm.display.shift||e.extend),o=0;o<e.sel.ranges.length;o++)n[o]=Hi(e.sel.ranges[o],t[o],null,i);Xi(e,wi(e.cm,n,e.sel.primIndex),r)}function Gi(e,t,r,n){var i=e.sel.ranges.slice(0);i[t]=r,Xi(e,wi(e.cm,i,e.sel.primIndex),n)}function Vi(e,t,r,n){Xi(e,xi(t,r),n)}function Ki(e,t,r){var n=e.history.done,i=X(n);i&&i.ranges?(n[n.length-1]=t,Yi(e,t,r)):Xi(e,t,r)}function Xi(e,t,r){Yi(e,t,r),Ri(e,e.sel,e.cm?e.cm.curOp.id:NaN,r)}function Yi(e,t,r){(ye(e,"beforeSelectionChange")||e.cm&&ye(e.cm,"beforeSelectionChange"))&&(t=function(e,t,r){var n={ranges:t.ranges,update:function(t){this.ranges=[];for(var r=0;r<t.length;r++)this.ranges[r]=new bi(st(e,t[r].anchor),st(e,t[r].head))},origin:r&&r.origin};return me(e,"beforeSelectionChange",e,n),e.cm&&me(e.cm,"beforeSelectionChange",e.cm,n),n.ranges!=t.ranges?wi(e.cm,n.ranges,n.ranges.length-1):t}(e,t,r)),Zi(e,Qi(e,t,r&&r.bias||(tt(t.primary().head,e.sel.primary().head)<0?-1:1),!0)),r&&!1===r.scroll||!e.cm||An(e.cm)}function Zi(e,t){t.equals(e.sel)||(e.sel=t,e.cm&&(e.cm.curOp.updateInput=1,e.cm.curOp.selectionChanged=!0,ve(e.cm)),sr(e,"cursorActivity",e))}function Ji(e){Zi(e,Qi(e,e.sel,null,!1))}function Qi(e,t,r,n){for(var i,o=0;o<t.ranges.length;o++){var a=t.ranges[o],s=t.ranges.length==e.sel.ranges.length&&e.sel.ranges[o],l=to(e,a.anchor,s&&s.anchor,r,n),c=to(e,a.head,s&&s.head,r,n);(i||l!=a.anchor||c!=a.head)&&(i||(i=t.ranges.slice(0,o)),i[o]=new bi(l,c))}return i?wi(e.cm,i,t.primIndex):t}function eo(e,t,r,n,i){var o=Ge(e,t.line);if(o.markedSpans)for(var a=0;a<o.markedSpans.length;++a){var s=o.markedSpans[a],l=s.marker,c="selectLeft"in l?!l.selectLeft:l.inclusiveLeft,u="selectRight"in l?!l.selectRight:l.inclusiveRight;if((null==s.from||(c?s.from<=t.ch:s.from<t.ch))&&(null==s.to||(u?s.to>=t.ch:s.to>t.ch))){if(i&&(me(l,"beforeCursorEnter"),l.explicitlyCleared)){if(o.markedSpans){--a;continue}break}if(!l.atomic)continue;if(r){var f=l.find(n<0?1:-1),d=void 0;if((n<0?u:c)&&(f=ro(e,f,-n,f&&f.line==t.line?o:null)),f&&f.line==t.line&&(d=tt(f,r))&&(n<0?d<0:d>0))return eo(e,f,t,n,i)}var p=l.find(n<0?-1:1);return(n<0?c:u)&&(p=ro(e,p,n,p.line==t.line?o:null)),p?eo(e,p,t,n,i):null}}return t}function to(e,t,r,n,i){var o=n||1,a=eo(e,t,r,o,i)||!i&&eo(e,t,r,o,!0)||eo(e,t,r,-o,i)||!i&&eo(e,t,r,-o,!0);return a||(e.cantEdit=!0,et(e.first,0))}function ro(e,t,r,n){return r<0&&0==t.ch?t.line>e.first?st(e,et(t.line-1)):null:r>0&&t.ch==(n||Ge(e,t.line)).text.length?t.line<e.first+e.size-1?et(t.line+1,0):null:new et(t.line,t.ch+r)}function no(e){e.setSelection(et(e.firstLine(),0),et(e.lastLine()),H)}function io(e,t,r){var n={canceled:!1,from:t.from,to:t.to,text:t.text,origin:t.origin,cancel:function(){return n.canceled=!0}};return r&&(n.update=function(t,r,i,o){t&&(n.from=st(e,t)),r&&(n.to=st(e,r)),i&&(n.text=i),void 0!==o&&(n.origin=o)}),me(e,"beforeChange",e,n),e.cm&&me(e.cm,"beforeChange",e.cm,n),n.canceled?(e.cm&&(e.cm.curOp.updateInput=2),null):{from:n.from,to:n.to,text:n.text,origin:n.origin}}function oo(e,t,r){if(e.cm){if(!e.cm.curOp)return Zn(e.cm,oo)(e,t,r);if(e.cm.state.suppressEdits)return}if(!(ye(e,"beforeChange")||e.cm&&ye(e.cm,"beforeChange"))||(t=io(e,t,!0))){var n=xt&&!r&&function(e,t,r){var n=null;if(e.iter(t.line,r.line+1,function(e){if(e.markedSpans)for(var t=0;t<e.markedSpans.length;++t){var r=e.markedSpans[t].marker;!r.readOnly||n&&-1!=j(n,r)||(n||(n=[])).push(r)}}),!n)return null;for(var i=[{from:t,to:r}],o=0;o<n.length;++o)for(var a=n[o],s=a.find(0),l=0;l<i.length;++l){var c=i[l];if(!(tt(c.to,s.from)<0||tt(c.from,s.to)>0)){var u=[l,1],f=tt(c.from,s.from),d=tt(c.to,s.to);(f<0||!a.inclusiveLeft&&!f)&&u.push({from:c.from,to:s.from}),(d>0||!a.inclusiveRight&&!d)&&u.push({from:s.to,to:c.to}),i.splice.apply(i,u),l+=u.length-3}}return i}(e,t.from,t.to);if(n)for(var i=n.length-1;i>=0;--i)ao(e,{from:n[i].from,to:n[i].to,text:i?[""]:t.text,origin:t.origin});else ao(e,t)}}function ao(e,t){if(1!=t.text.length||""!=t.text[0]||0!=tt(t.from,t.to)){var r=Ci(e,t);zi(e,t,r,e.cm?e.cm.curOp.id:NaN),co(e,t,r,Tt(e,t));var n=[];Oi(e,function(e,r){r||-1!=j(n,e.history)||(ho(e.history,t),n.push(e.history)),co(e,t,null,Tt(e,t))})}}function so(e,t,r){var n=e.cm&&e.cm.state.suppressEdits;if(!n||r){for(var i,o=e.history,a=e.sel,s="undo"==t?o.done:o.undone,l="undo"==t?o.undone:o.done,c=0;c<s.length&&(i=s[c],r?!i.ranges||i.equals(e.sel):i.ranges);c++);if(c!=s.length){for(o.lastOrigin=o.lastSelOrigin=null;;){if(!(i=s.pop()).ranges){if(n)return void s.push(i);break}if($i(i,l),r&&!i.equals(e.sel))return void Xi(e,i,{clearRedo:!1});a=i}var u=[];$i(a,l),l.push({changes:u,generation:o.generation}),o.generation=i.generation||++o.maxGeneration;for(var f=ye(e,"beforeChange")||e.cm&&ye(e.cm,"beforeChange"),d=function(r){var n=i.changes[r];if(n.origin=t,f&&!io(e,n,!1))return s.length=0,{};u.push(Ii(e,n));var o=r?Ci(e,n):X(s);co(e,n,o,Wi(e,n)),!r&&e.cm&&e.cm.scrollIntoView({from:n.from,to:_i(n)});var a=[];Oi(e,function(e,t){t||-1!=j(a,e.history)||(ho(e.history,n),a.push(e.history)),co(e,n,null,Wi(e,n))})},p=i.changes.length-1;p>=0;--p){var h=d(p);if(h)return h.v}}}}function lo(e,t){if(0!=t&&(e.first+=t,e.sel=new yi(Y(e.sel.ranges,function(e){return new bi(et(e.anchor.line+t,e.anchor.ch),et(e.head.line+t,e.head.ch))}),e.sel.primIndex),e.cm)){cn(e.cm,e.first,e.first-t,t);for(var r=e.cm.display,n=r.viewFrom;n<r.viewTo;n++)un(e.cm,n,"gutter")}}function co(e,t,r,n){if(e.cm&&!e.cm.curOp)return Zn(e.cm,co)(e,t,r,n);if(t.to.line<e.first)lo(e,t.text.length-1-(t.to.line-t.from.line));else if(!(t.from.line>e.lastLine())){if(t.from.line<e.first){var i=t.text.length-1-(e.first-t.from.line);lo(e,i),t={from:et(e.first,0),to:et(t.to.line+i,t.to.ch),text:[X(t.text)],origin:t.origin}}var o=e.lastLine();t.to.line>o&&(t={from:t.from,to:et(o,Ge(e,o).text.length),text:[t.text[0]],origin:t.origin}),t.removed=Ve(e,t.from,t.to),r||(r=Ci(e,t)),e.cm?function(e,t,r){var n=e.doc,i=e.display,o=t.from,a=t.to,s=!1,l=o.line;e.options.lineWrapping||(l=Ye($t(Ge(n,o.line))),n.iter(l,a.line+1,function(e){if(e==i.maxLine)return s=!0,!0}));n.sel.contains(t.from,t.to)>-1&&ve(e);Ai(n,t,r,on(e)),e.options.lineWrapping||(n.iter(l,o.line+t.text.length,function(e){var t=Ut(e);t>i.maxLineLength&&(i.maxLine=e,i.maxLineLength=t,i.maxLineChanged=!0,s=!1)}),s&&(e.curOp.updateMaxLine=!0));(function(e,t){if(e.modeFrontier=Math.min(e.modeFrontier,t),!(e.highlightFrontier<t-10)){for(var r=e.first,n=t-1;n>r;n--){var i=Ge(e,n).stateAfter;if(i&&(!(i instanceof ct)||n+i.lookAhead<t)){r=n+1;break}}e.highlightFrontier=Math.min(e.highlightFrontier,r)}})(n,o.line),ei(e,400);var c=t.text.length-(a.line-o.line)-1;t.full?cn(e):o.line!=a.line||1!=t.text.length||Li(e.doc,t)?cn(e,o.line,a.line+1,c):un(e,o.line,"text");var u=ye(e,"changes"),f=ye(e,"change");if(f||u){var d={from:o,to:a,text:t.text,removed:t.removed,origin:t.origin};f&&sr(e,"change",e,d),u&&(e.curOp.changeObjs||(e.curOp.changeObjs=[])).push(d)}e.display.selForContextMenu=null}(e.cm,t,n):Ai(e,t,n),Yi(e,r,H)}}function uo(e,t,r,n,i){var o;n||(n=r),tt(n,r)<0&&(r=(o=[n,r])[0],n=o[1]),"string"==typeof t&&(t=e.splitLines(t)),oo(e,{from:r,to:n,text:t,origin:i})}function fo(e,t,r,n){r<e.line?e.line+=n:t<e.line&&(e.line=t,e.ch=0)}function po(e,t,r,n){for(var i=0;i<e.length;++i){var o=e[i],a=!0;if(o.ranges){o.copied||((o=e[i]=o.deepCopy()).copied=!0);for(var s=0;s<o.ranges.length;s++)fo(o.ranges[s].anchor,t,r,n),fo(o.ranges[s].head,t,r,n)}else{for(var l=0;l<o.changes.length;++l){var c=o.changes[l];if(r<c.from.line)c.from=et(c.from.line+n,c.from.ch),c.to=et(c.to.line+n,c.to.ch);else if(t<=c.to.line){a=!1;break}}a||(e.splice(0,i+1),i=0)}}}function ho(e,t){var r=t.from.line,n=t.to.line,i=t.text.length-(n-r)-1;po(e.done,r,n,i),po(e.undone,r,n,i)}function mo(e,t,r,n){var i=t,o=t;return"number"==typeof t?o=Ge(e,at(e,t)):i=Ye(t),null==i?null:(n(o,i)&&e.cm&&un(e.cm,i,r),o)}function go(e){this.lines=e,this.parent=null;for(var t=0,r=0;r<e.length;++r)e[r].parent=this,t+=e[r].height;this.height=t}function vo(e){this.children=e;for(var t=0,r=0,n=0;n<e.length;++n){var i=e[n];t+=i.chunkSize(),r+=i.height,i.parent=this}this.size=t,this.height=r,this.parent=null}bi.prototype.from=function(){return ot(this.anchor,this.head)},bi.prototype.to=function(){return it(this.anchor,this.head)},bi.prototype.empty=function(){return this.head.line==this.anchor.line&&this.head.ch==this.anchor.ch},go.prototype={chunkSize:function(){return this.lines.length},removeInner:function(e,t){for(var r=e,n=e+t;r<n;++r){var i=this.lines[r];this.height-=i.height,Vt(i),sr(i,"delete")}this.lines.splice(e,t)},collapse:function(e){e.push.apply(e,this.lines)},insertInner:function(e,t,r){this.height+=r,this.lines=this.lines.slice(0,e).concat(t).concat(this.lines.slice(e));for(var n=0;n<t.length;++n)t[n].parent=this},iterN:function(e,t,r){for(var n=e+t;e<n;++e)if(r(this.lines[e]))return!0}},vo.prototype={chunkSize:function(){return this.size},removeInner:function(e,t){this.size-=t;for(var r=0;r<this.children.length;++r){var n=this.children[r],i=n.chunkSize();if(e<i){var o=Math.min(t,i-e),a=n.height;if(n.removeInner(e,o),this.height-=a-n.height,i==o&&(this.children.splice(r--,1),n.parent=null),0==(t-=o))break;e=0}else e-=i}if(this.size-t<25&&(this.children.length>1||!(this.children[0]instanceof go))){var s=[];this.collapse(s),this.children=[new go(s)],this.children[0].parent=this}},collapse:function(e){for(var t=0;t<this.children.length;++t)this.children[t].collapse(e)},insertInner:function(e,t,r){this.size+=t.length,this.height+=r;for(var n=0;n<this.children.length;++n){var i=this.children[n],o=i.chunkSize();if(e<=o){if(i.insertInner(e,t,r),i.lines&&i.lines.length>50){for(var a=i.lines.length%25+25,s=a;s<i.lines.length;){var l=new go(i.lines.slice(s,s+=25));i.height-=l.height,this.children.splice(++n,0,l),l.parent=this}i.lines=i.lines.slice(0,a),this.maybeSpill()}break}e-=o}},maybeSpill:function(){if(!(this.children.length<=10)){var e=this;do{var t=new vo(e.children.splice(e.children.length-5,5));if(e.parent){e.size-=t.size,e.height-=t.height;var r=j(e.parent.children,e);e.parent.children.splice(r+1,0,t)}else{var n=new vo(e.children);n.parent=e,e.children=[n,t],e=n}t.parent=e.parent}while(e.children.length>10);e.parent.maybeSpill()}},iterN:function(e,t,r){for(var n=0;n<this.children.length;++n){var i=this.children[n],o=i.chunkSize();if(e<o){var a=Math.min(t,o-e);if(i.iterN(e,a,r))return!0;if(0==(t-=a))break;e=0}else e-=o}}};var yo=function(e,t,r){if(r)for(var n in r)r.hasOwnProperty(n)&&(this[n]=r[n]);this.doc=e,this.node=t};function bo(e,t,r){Ht(t)<(e.curOp&&e.curOp.scrollTop||e.doc.scrollTop)&&Ln(e,r)}yo.prototype.clear=function(){var e=this.doc.cm,t=this.line.widgets,r=this.line,n=Ye(r);if(null!=n&&t){for(var i=0;i<t.length;++i)t[i]==this&&t.splice(i--,1);t.length||(r.widgets=null);var o=wr(this);Xe(r,Math.max(0,r.height-o)),e&&(Yn(e,function(){bo(e,r,-o),un(e,n,"widget")}),sr(e,"lineWidgetCleared",e,this,n))}},yo.prototype.changed=function(){var e=this,t=this.height,r=this.doc.cm,n=this.line;this.height=null;var i=wr(this)-t;i&&(Wt(this.doc,n)||Xe(n,n.height+i),r&&Yn(r,function(){r.curOp.forceUpdate=!0,bo(r,n,i),sr(r,"lineWidgetChanged",r,e,Ye(n))}))},be(yo);var wo=0,xo=function(e,t){this.lines=[],this.type=t,this.doc=e,this.id=++wo};function _o(e,t,r,n,i){if(n&&n.shared)return function(e,t,r,n,i){(n=R(n)).shared=!1;var o=[_o(e,t,r,n,i)],a=o[0],s=n.widgetNode;return Oi(e,function(e){s&&(n.widgetNode=s.cloneNode(!0)),o.push(_o(e,st(e,t),st(e,r),n,i));for(var l=0;l<e.linked.length;++l)if(e.linked[l].isParent)return;a=X(o)}),new ko(o,a)}(e,t,r,n,i);if(e.cm&&!e.cm.curOp)return Zn(e.cm,_o)(e,t,r,n,i);var o=new xo(e,i),a=tt(t,r);if(n&&R(n,o,!1),a>0||0==a&&!1!==o.clearWhenEmpty)return o;if(o.replacedWith&&(o.collapsed=!0,o.widgetNode=O("span",[o.replacedWith],"CodeMirror-widget"),n.handleMouseEvents||o.widgetNode.setAttribute("cm-ignore-events","true"),n.insertLeft&&(o.widgetNode.insertLeft=!0)),o.collapsed){if(Rt(e,t.line,t,r,o)||t.line!=r.line&&Rt(e,r.line,t,r,o))throw new Error("Inserting collapsed marker partially overlapping an existing one");_t=!0}o.addToHistory&&zi(e,{from:t,to:r,origin:"markText"},e.sel,NaN);var s,l=t.line,c=e.cm;if(e.iter(l,r.line+1,function(e){c&&o.collapsed&&!c.options.lineWrapping&&$t(e)==c.display.maxLine&&(s=!0),o.collapsed&&l!=t.line&&Xe(e,0),function(e,t){e.markedSpans=e.markedSpans?e.markedSpans.concat([t]):[t],t.marker.attachLine(e)}(e,new kt(o,l==t.line?t.ch:null,l==r.line?r.ch:null)),++l}),o.collapsed&&e.iter(t.line,r.line+1,function(t){Wt(e,t)&&Xe(t,0)}),o.clearOnEnter&&de(o,"beforeCursorEnter",function(){return o.clear()}),o.readOnly&&(xt=!0,(e.history.done.length||e.history.undone.length)&&e.clearHistory()),o.collapsed&&(o.id=++wo,o.atomic=!0),c){if(s&&(c.curOp.updateMaxLine=!0),o.collapsed)cn(c,t.line,r.line+1);else if(o.className||o.startStyle||o.endStyle||o.css||o.attributes||o.title)for(var u=t.line;u<=r.line;u++)un(c,u,"text");o.atomic&&Ji(c.doc),sr(c,"markerAdded",c,o)}return o}xo.prototype.clear=function(){if(!this.explicitlyCleared){var e=this.doc.cm,t=e&&!e.curOp;if(t&&Un(e),ye(this,"clear")){var r=this.find();r&&sr(this,"clear",r.from,r.to)}for(var n=null,i=null,o=0;o<this.lines.length;++o){var a=this.lines[o],s=Ct(a.markedSpans,this);e&&!this.collapsed?un(e,Ye(a),"text"):e&&(null!=s.to&&(i=Ye(a)),null!=s.from&&(n=Ye(a))),a.markedSpans=St(a.markedSpans,s),null==s.from&&this.collapsed&&!Wt(this.doc,a)&&e&&Xe(a,en(e.display))}if(e&&this.collapsed&&!e.options.lineWrapping)for(var l=0;l<this.lines.length;++l){var c=$t(this.lines[l]),u=Ut(c);u>e.display.maxLineLength&&(e.display.maxLine=c,e.display.maxLineLength=u,e.display.maxLineChanged=!0)}null!=n&&e&&this.collapsed&&cn(e,n,i+1),this.lines.length=0,this.explicitlyCleared=!0,this.atomic&&this.doc.cantEdit&&(this.doc.cantEdit=!1,e&&Ji(e.doc)),e&&sr(e,"markerCleared",e,this,n,i),t&&Bn(e),this.parent&&this.parent.clear()}},xo.prototype.find=function(e,t){var r,n;null==e&&"bookmark"==this.type&&(e=1);for(var i=0;i<this.lines.length;++i){var o=this.lines[i],a=Ct(o.markedSpans,this);if(null!=a.from&&(r=et(t?o:Ye(o),a.from),-1==e))return r;if(null!=a.to&&(n=et(t?o:Ye(o),a.to),1==e))return n}return r&&{from:r,to:n}},xo.prototype.changed=function(){var e=this,t=this.find(-1,!0),r=this,n=this.doc.cm;t&&n&&Yn(n,function(){var i=t.line,o=Ye(t.line),a=Or(n,o);if(a&&(Rr(a),n.curOp.selectionChanged=n.curOp.forceUpdate=!0),n.curOp.updateMaxLine=!0,!Wt(r.doc,i)&&null!=r.height){var s=r.height;r.height=null;var l=wr(r)-s;l&&Xe(i,i.height+l)}sr(n,"markerChanged",n,e)})},xo.prototype.attachLine=function(e){if(!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;t.maybeHiddenMarkers&&-1!=j(t.maybeHiddenMarkers,this)||(t.maybeUnhiddenMarkers||(t.maybeUnhiddenMarkers=[])).push(this)}this.lines.push(e)},xo.prototype.detachLine=function(e){if(this.lines.splice(j(this.lines,e),1),!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;(t.maybeHiddenMarkers||(t.maybeHiddenMarkers=[])).push(this)}},be(xo);var ko=function(e,t){this.markers=e,this.primary=t;for(var r=0;r<e.length;++r)e[r].parent=this};function Co(e){return e.findMarks(et(e.first,0),e.clipPos(et(e.lastLine())),function(e){return e.parent})}function So(e){for(var t=function(t){var r=e[t],n=[r.primary.doc];Oi(r.primary.doc,function(e){return n.push(e)});for(var i=0;i<r.markers.length;i++){var o=r.markers[i];-1==j(n,o.doc)&&(o.parent=null,r.markers.splice(i--,1))}},r=0;r<e.length;r++)t(r)}ko.prototype.clear=function(){if(!this.explicitlyCleared){this.explicitlyCleared=!0;for(var e=0;e<this.markers.length;++e)this.markers[e].clear();sr(this,"clear")}},ko.prototype.find=function(e,t){return this.primary.find(e,t)},be(ko);var To=0,Mo=function(e,t,r,n,i){if(!(this instanceof Mo))return new Mo(e,t,r,n,i);null==r&&(r=0),vo.call(this,[new go([new Gt("",null)])]),this.first=r,this.scrollTop=this.scrollLeft=0,this.cantEdit=!1,this.cleanGeneration=1,this.modeFrontier=this.highlightFrontier=r;var o=et(r,0);this.sel=xi(o),this.history=new Di(null),this.id=++To,this.modeOption=t,this.lineSep=n,this.direction="rtl"==i?"rtl":"ltr",this.extend=!1,"string"==typeof e&&(e=this.splitLines(e)),Ai(this,{from:o,to:o,text:e}),Xi(this,xi(o),H)};Mo.prototype=J(vo.prototype,{constructor:Mo,iter:function(e,t,r){r?this.iterN(e-this.first,t-e,r):this.iterN(this.first,this.first+this.size,e)},insert:function(e,t){for(var r=0,n=0;n<t.length;++n)r+=t[n].height;this.insertInner(e-this.first,t,r)},remove:function(e,t){this.removeInner(e-this.first,t)},getValue:function(e){var t=Ke(this,this.first,this.first+this.size);return!1===e?t:t.join(e||this.lineSeparator())},setValue:Qn(function(e){var t=et(this.first,0),r=this.first+this.size-1;oo(this,{from:t,to:et(r,Ge(this,r).text.length),text:this.splitLines(e),origin:"setValue",full:!0},!0),this.cm&&On(this.cm,0,0),Xi(this,xi(t),H)}),replaceRange:function(e,t,r,n){uo(this,e,t=st(this,t),r=r?st(this,r):t,n)},getRange:function(e,t,r){var n=Ve(this,st(this,e),st(this,t));return!1===r?n:n.join(r||this.lineSeparator())},getLine:function(e){var t=this.getLineHandle(e);return t&&t.text},getLineHandle:function(e){if(Je(this,e))return Ge(this,e)},getLineNumber:function(e){return Ye(e)},getLineHandleVisualStart:function(e){return"number"==typeof e&&(e=Ge(this,e)),$t(e)},lineCount:function(){return this.size},firstLine:function(){return this.first},lastLine:function(){return this.first+this.size-1},clipPos:function(e){return st(this,e)},getCursor:function(e){var t=this.sel.primary();return null==e||"head"==e?t.head:"anchor"==e?t.anchor:"end"==e||"to"==e||!1===e?t.to():t.from()},listSelections:function(){return this.sel.ranges},somethingSelected:function(){return this.sel.somethingSelected()},setCursor:Qn(function(e,t,r){Vi(this,st(this,"number"==typeof e?et(e,t||0):e),null,r)}),setSelection:Qn(function(e,t,r){Vi(this,st(this,e),st(this,t||e),r)}),extendSelection:Qn(function(e,t,r){Ui(this,st(this,e),t&&st(this,t),r)}),extendSelections:Qn(function(e,t){Bi(this,lt(this,e),t)}),extendSelectionsBy:Qn(function(e,t){Bi(this,lt(this,Y(this.sel.ranges,e)),t)}),setSelections:Qn(function(e,t,r){if(e.length){for(var n=[],i=0;i<e.length;i++)n[i]=new bi(st(this,e[i].anchor),st(this,e[i].head));null==t&&(t=Math.min(e.length-1,this.sel.primIndex)),Xi(this,wi(this.cm,n,t),r)}}),addSelection:Qn(function(e,t,r){var n=this.sel.ranges.slice(0);n.push(new bi(st(this,e),st(this,t||e))),Xi(this,wi(this.cm,n,n.length-1),r)}),getSelection:function(e){for(var t,r=this.sel.ranges,n=0;n<r.length;n++){var i=Ve(this,r[n].from(),r[n].to());t=t?t.concat(i):i}return!1===e?t:t.join(e||this.lineSeparator())},getSelections:function(e){for(var t=[],r=this.sel.ranges,n=0;n<r.length;n++){var i=Ve(this,r[n].from(),r[n].to());!1!==e&&(i=i.join(e||this.lineSeparator())),t[n]=i}return t},replaceSelection:function(e,t,r){for(var n=[],i=0;i<this.sel.ranges.length;i++)n[i]=e;this.replaceSelections(n,t,r||"+input")},replaceSelections:Qn(function(e,t,r){for(var n=[],i=this.sel,o=0;o<i.ranges.length;o++){var a=i.ranges[o];n[o]={from:a.from(),to:a.to(),text:this.splitLines(e[o]),origin:r}}for(var s=t&&"end"!=t&&function(e,t,r){for(var n=[],i=et(e.first,0),o=i,a=0;a<t.length;a++){var s=t[a],l=Si(s.from,i,o),c=Si(_i(s),i,o);if(i=s.to,o=c,"around"==r){var u=e.sel.ranges[a],f=tt(u.head,u.anchor)<0;n[a]=new bi(f?c:l,f?l:c)}else n[a]=new bi(l,l)}return new yi(n,e.sel.primIndex)}(this,n,t),l=n.length-1;l>=0;l--)oo(this,n[l]);s?Ki(this,s):this.cm&&An(this.cm)}),undo:Qn(function(){so(this,"undo")}),redo:Qn(function(){so(this,"redo")}),undoSelection:Qn(function(){so(this,"undo",!0)}),redoSelection:Qn(function(){so(this,"redo",!0)}),setExtending:function(e){this.extend=e},getExtending:function(){return this.extend},historySize:function(){for(var e=this.history,t=0,r=0,n=0;n<e.done.length;n++)e.done[n].ranges||++t;for(var i=0;i<e.undone.length;i++)e.undone[i].ranges||++r;return{undo:t,redo:r}},clearHistory:function(){this.history=new Di(this.history.maxGeneration)},markClean:function(){this.cleanGeneration=this.changeGeneration(!0)},changeGeneration:function(e){return e&&(this.history.lastOp=this.history.lastSelOp=this.history.lastOrigin=null),this.history.generation},isClean:function(e){return this.history.generation==(e||this.cleanGeneration)},getHistory:function(){return{done:qi(this.history.done),undone:qi(this.history.undone)}},setHistory:function(e){var t=this.history=new Di(this.history.maxGeneration);t.done=qi(e.done.slice(0),null,!0),t.undone=qi(e.undone.slice(0),null,!0)},setGutterMarker:Qn(function(e,t,r){return mo(this,e,"gutter",function(e){var n=e.gutterMarkers||(e.gutterMarkers={});return n[t]=r,!r&&re(n)&&(e.gutterMarkers=null),!0})}),clearGutter:Qn(function(e){var t=this;this.iter(function(r){r.gutterMarkers&&r.gutterMarkers[e]&&mo(t,r,"gutter",function(){return r.gutterMarkers[e]=null,re(r.gutterMarkers)&&(r.gutterMarkers=null),!0})})}),lineInfo:function(e){var t;if("number"==typeof e){if(!Je(this,e))return null;if(t=e,!(e=Ge(this,e)))return null}else if(null==(t=Ye(e)))return null;return{line:t,handle:e,text:e.text,gutterMarkers:e.gutterMarkers,textClass:e.textClass,bgClass:e.bgClass,wrapClass:e.wrapClass,widgets:e.widgets}},addLineClass:Qn(function(e,t,r){return mo(this,e,"gutter"==t?"gutter":"class",function(e){var n="text"==t?"textClass":"background"==t?"bgClass":"gutter"==t?"gutterClass":"wrapClass";if(e[n]){if(C(r).test(e[n]))return!1;e[n]+=" "+r}else e[n]=r;return!0})}),removeLineClass:Qn(function(e,t,r){return mo(this,e,"gutter"==t?"gutter":"class",function(e){var n="text"==t?"textClass":"background"==t?"bgClass":"gutter"==t?"gutterClass":"wrapClass",i=e[n];if(!i)return!1;if(null==r)e[n]=null;else{var o=i.match(C(r));if(!o)return!1;var a=o.index+o[0].length;e[n]=i.slice(0,o.index)+(o.index&&a!=i.length?" ":"")+i.slice(a)||null}return!0})}),addLineWidget:Qn(function(e,t,r){return function(e,t,r,n){var i=new yo(e,r,n),o=e.cm;return o&&i.noHScroll&&(o.display.alignWidgets=!0),mo(e,t,"widget",function(t){var r=t.widgets||(t.widgets=[]);if(null==i.insertAt?r.push(i):r.splice(Math.min(r.length-1,Math.max(0,i.insertAt)),0,i),i.line=t,o&&!Wt(e,t)){var n=Ht(t)<e.scrollTop;Xe(t,t.height+wr(i)),n&&Ln(o,i.height),o.curOp.forceUpdate=!0}return!0}),o&&sr(o,"lineWidgetAdded",o,i,"number"==typeof t?t:Ye(t)),i}(this,e,t,r)}),removeLineWidget:function(e){e.clear()},markText:function(e,t,r){return _o(this,st(this,e),st(this,t),r,r&&r.type||"range")},setBookmark:function(e,t){var r={replacedWith:t&&(null==t.nodeType?t.widget:t),insertLeft:t&&t.insertLeft,clearWhenEmpty:!1,shared:t&&t.shared,handleMouseEvents:t&&t.handleMouseEvents};return _o(this,e=st(this,e),e,r,"bookmark")},findMarksAt:function(e){var t=[],r=Ge(this,(e=st(this,e)).line).markedSpans;if(r)for(var n=0;n<r.length;++n){var i=r[n];(null==i.from||i.from<=e.ch)&&(null==i.to||i.to>=e.ch)&&t.push(i.marker.parent||i.marker)}return t},findMarks:function(e,t,r){e=st(this,e),t=st(this,t);var n=[],i=e.line;return this.iter(e.line,t.line+1,function(o){var a=o.markedSpans;if(a)for(var s=0;s<a.length;s++){var l=a[s];null!=l.to&&i==e.line&&e.ch>=l.to||null==l.from&&i!=e.line||null!=l.from&&i==t.line&&l.from>=t.ch||r&&!r(l.marker)||n.push(l.marker.parent||l.marker)}++i}),n},getAllMarks:function(){var e=[];return this.iter(function(t){var r=t.markedSpans;if(r)for(var n=0;n<r.length;++n)null!=r[n].from&&e.push(r[n].marker)}),e},posFromIndex:function(e){var t,r=this.first,n=this.lineSeparator().length;return this.iter(function(i){var o=i.text.length+n;if(o>e)return t=e,!0;e-=o,++r}),st(this,et(r,t))},indexFromPos:function(e){var t=(e=st(this,e)).ch;if(e.line<this.first||e.ch<0)return 0;var r=this.lineSeparator().length;return this.iter(this.first,e.line,function(e){t+=e.text.length+r}),t},copy:function(e){var t=new Mo(Ke(this,this.first,this.first+this.size),this.modeOption,this.first,this.lineSep,this.direction);return t.scrollTop=this.scrollTop,t.scrollLeft=this.scrollLeft,t.sel=this.sel,t.extend=!1,e&&(t.history.undoDepth=this.history.undoDepth,t.setHistory(this.getHistory())),t},linkedDoc:function(e){e||(e={});var t=this.first,r=this.first+this.size;null!=e.from&&e.from>t&&(t=e.from),null!=e.to&&e.to<r&&(r=e.to);var n=new Mo(Ke(this,t,r),e.mode||this.modeOption,t,this.lineSep,this.direction);return e.sharedHist&&(n.history=this.history),(this.linked||(this.linked=[])).push({doc:n,sharedHist:e.sharedHist}),n.linked=[{doc:this,isParent:!0,sharedHist:e.sharedHist}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r],i=n.find(),o=e.clipPos(i.from),a=e.clipPos(i.to);if(tt(o,a)){var s=_o(e,o,a,n.primary,n.primary.type);n.markers.push(s),s.parent=n}}}(n,Co(this)),n},unlinkDoc:function(e){if(e instanceof ka&&(e=e.doc),this.linked)for(var t=0;t<this.linked.length;++t){if(this.linked[t].doc==e){this.linked.splice(t,1),e.unlinkDoc(this),So(Co(this));break}}if(e.history==this.history){var r=[e.id];Oi(e,function(e){return r.push(e.id)},!0),e.history=new Di(null),e.history.done=qi(this.history.done,r),e.history.undone=qi(this.history.undone,r)}},iterLinkedDocs:function(e){Oi(this,e)},getMode:function(){return this.mode},getEditor:function(){return this.cm},splitLines:function(e){return this.lineSep?e.split(this.lineSep):Ee(e)},lineSeparator:function(){return this.lineSep||"\n"},setDirection:Qn(function(e){var t;("rtl"!=e&&(e="ltr"),e!=this.direction)&&(this.direction=e,this.iter(function(e){return e.order=null}),this.cm&&Yn(t=this.cm,function(){Ei(t),cn(t)}))})}),Mo.prototype.eachLine=Mo.prototype.iter;var Lo=0;function Ao(e){var t=this;if(Oo(t),!ge(t,e)&&!xr(t.display,e)){we(e),a&&(Lo=+new Date);var r=sn(t,e,!0),n=e.dataTransfer.files;if(r&&!t.isReadOnly())if(n&&n.length&&window.FileReader&&window.File)for(var i=n.length,o=Array(i),s=0,l=function(e,n){if(!t.options.allowDropFileTypes||-1!=j(t.options.allowDropFileTypes,e.type)){var a=new FileReader;a.onload=Zn(t,function(){var e=a.result;if(/[\x00-\x08\x0e-\x1f]{2}/.test(e)&&(e=""),o[n]=e,++s==i){var l={from:r=st(t.doc,r),to:r,text:t.doc.splitLines(o.join(t.doc.lineSeparator())),origin:"paste"};oo(t.doc,l),Ki(t.doc,xi(r,_i(l)))}}),a.readAsText(e)}},c=0;c<i;++c)l(n[c],c);else{if(t.state.draggingText&&t.doc.sel.contains(r)>-1)return t.state.draggingText(e),void setTimeout(function(){return t.display.input.focus()},20);try{var u=e.dataTransfer.getData("Text");if(u){var f;if(t.state.draggingText&&!t.state.draggingText.copy&&(f=t.listSelections()),Yi(t.doc,xi(r,r)),f)for(var d=0;d<f.length;++d)uo(t.doc,"",f[d].anchor,f[d].head,"drag");t.replaceSelection(u,"around","paste"),t.display.input.focus()}}catch(e){}}}}function Oo(e){e.display.dragCursor&&(e.display.lineSpace.removeChild(e.display.dragCursor),e.display.dragCursor=null)}function No(e){if(document.getElementsByClassName){for(var t=document.getElementsByClassName("CodeMirror"),r=[],n=0;n<t.length;n++){var i=t[n].CodeMirror;i&&r.push(i)}r.length&&r[0].operation(function(){for(var t=0;t<r.length;t++)e(r[t])})}}var Eo=!1;function Do(){var e;Eo||(de(window,"resize",function(){null==e&&(e=setTimeout(function(){e=null,No(Io)},100))}),de(window,"blur",function(){return No(kn)}),Eo=!0)}function Io(e){var t=e.display;t.cachedCharWidth=t.cachedTextHeight=t.cachedPaddingH=null,t.scrollbarsClipped=!1,e.setSize()}for(var Po={3:"Pause",8:"Backspace",9:"Tab",13:"Enter",16:"Shift",17:"Ctrl",18:"Alt",19:"Pause",20:"CapsLock",27:"Esc",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",44:"PrintScrn",45:"Insert",46:"Delete",59:";",61:"=",91:"Mod",92:"Mod",93:"Mod",106:"*",107:"=",109:"-",110:".",111:"/",145:"ScrollLock",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",63232:"Up",63233:"Down",63234:"Left",63235:"Right",63272:"Delete",63273:"Home",63275:"End",63276:"PageUp",63277:"PageDown",63302:"Insert"},zo=0;zo<10;zo++)Po[zo+48]=Po[zo+96]=String(zo);for(var Ro=65;Ro<=90;Ro++)Po[Ro]=String.fromCharCode(Ro);for(var $o=1;$o<=12;$o++)Po[$o+111]=Po[$o+63235]="F"+$o;var Fo={};function jo(e){var t,r,n,i,o=e.split(/-(?!$)/);e=o[o.length-1];for(var a=0;a<o.length-1;a++){var s=o[a];if(/^(cmd|meta|m)$/i.test(s))i=!0;else if(/^a(lt)?$/i.test(s))t=!0;else if(/^(c|ctrl|control)$/i.test(s))r=!0;else{if(!/^s(hift)?$/i.test(s))throw new Error("Unrecognized modifier name: "+s);n=!0}}return t&&(e="Alt-"+e),r&&(e="Ctrl-"+e),i&&(e="Cmd-"+e),n&&(e="Shift-"+e),e}function Wo(e){var t={};for(var r in e)if(e.hasOwnProperty(r)){var n=e[r];if(/^(name|fallthrough|(de|at)tach)$/.test(r))continue;if("..."==n){delete e[r];continue}for(var i=Y(r.split(" "),jo),o=0;o<i.length;o++){var a=void 0,s=void 0;o==i.length-1?(s=i.join(" "),a=n):(s=i.slice(0,o+1).join(" "),a="...");var l=t[s];if(l){if(l!=a)throw new Error("Inconsistent bindings for "+s)}else t[s]=a}delete e[r]}for(var c in t)e[c]=t[c];return e}function qo(e,t,r,n){var i=(t=Go(t)).call?t.call(e,n):t[e];if(!1===i)return"nothing";if("..."===i)return"multi";if(null!=i&&r(i))return"handled";if(t.fallthrough){if("[object Array]"!=Object.prototype.toString.call(t.fallthrough))return qo(e,t.fallthrough,r,n);for(var o=0;o<t.fallthrough.length;o++){var a=qo(e,t.fallthrough[o],r,n);if(a)return a}}}function Ho(e){var t="string"==typeof e?e:Po[e.keyCode];return"Ctrl"==t||"Alt"==t||"Shift"==t||"Mod"==t}function Uo(e,t,r){var n=e;return t.altKey&&"Alt"!=n&&(e="Alt-"+e),(_?t.metaKey:t.ctrlKey)&&"Ctrl"!=n&&(e="Ctrl-"+e),(_?t.ctrlKey:t.metaKey)&&"Cmd"!=n&&(e="Cmd-"+e),!r&&t.shiftKey&&"Shift"!=n&&(e="Shift-"+e),e}function Bo(e,t){if(f&&34==e.keyCode&&e.char)return!1;var r=Po[e.keyCode];return null!=r&&!e.altGraphKey&&(3==e.keyCode&&e.code&&(r=e.code),Uo(r,e,t))}function Go(e){return"string"==typeof e?Fo[e]:e}function Vo(e,t){for(var r=e.doc.sel.ranges,n=[],i=0;i<r.length;i++){for(var o=t(r[i]);n.length&&tt(o.from,X(n).to)<=0;){var a=n.pop();if(tt(a.from,o.from)<0){o.from=a.from;break}}n.push(o)}Yn(e,function(){for(var t=n.length-1;t>=0;t--)uo(e.doc,"",n[t].from,n[t].to,"+delete");An(e)})}function Ko(e,t,r){var n=oe(e.text,t+r,r);return n<0||n>e.text.length?null:n}function Xo(e,t,r){var n=Ko(e,t.ch,r);return null==n?null:new et(t.line,n,r<0?"after":"before")}function Yo(e,t,r,n,i){if(e){var o=ue(r,t.doc.direction);if(o){var a,s=i<0?X(o):o[0],l=i<0==(1==s.level)?"after":"before";if(s.level>0||"rtl"==t.doc.direction){var c=Nr(t,r);a=i<0?r.text.length-1:0;var u=Er(t,c,a).top;a=ae(function(e){return Er(t,c,e).top==u},i<0==(1==s.level)?s.from:s.to-1,a),"before"==l&&(a=Ko(r,a,1))}else a=i<0?s.to:s.from;return new et(n,a,l)}}return new et(n,i<0?r.text.length:0,i<0?"before":"after")}Fo.basic={Left:"goCharLeft",Right:"goCharRight",Up:"goLineUp",Down:"goLineDown",End:"goLineEnd",Home:"goLineStartSmart",PageUp:"goPageUp",PageDown:"goPageDown",Delete:"delCharAfter",Backspace:"delCharBefore","Shift-Backspace":"delCharBefore",Tab:"defaultTab","Shift-Tab":"indentAuto",Enter:"newlineAndIndent",Insert:"toggleOverwrite",Esc:"singleSelection"},Fo.pcDefault={"Ctrl-A":"selectAll","Ctrl-D":"deleteLine","Ctrl-Z":"undo","Shift-Ctrl-Z":"redo","Ctrl-Y":"redo","Ctrl-Home":"goDocStart","Ctrl-End":"goDocEnd","Ctrl-Up":"goLineUp","Ctrl-Down":"goLineDown","Ctrl-Left":"goGroupLeft","Ctrl-Right":"goGroupRight","Alt-Left":"goLineStart","Alt-Right":"goLineEnd","Ctrl-Backspace":"delGroupBefore","Ctrl-Delete":"delGroupAfter","Ctrl-S":"save","Ctrl-F":"find","Ctrl-G":"findNext","Shift-Ctrl-G":"findPrev","Shift-Ctrl-F":"replace","Shift-Ctrl-R":"replaceAll","Ctrl-[":"indentLess","Ctrl-]":"indentMore","Ctrl-U":"undoSelection","Shift-Ctrl-U":"redoSelection","Alt-U":"redoSelection",fallthrough:"basic"},Fo.emacsy={"Ctrl-F":"goCharRight","Ctrl-B":"goCharLeft","Ctrl-P":"goLineUp","Ctrl-N":"goLineDown","Alt-F":"goWordRight","Alt-B":"goWordLeft","Ctrl-A":"goLineStart","Ctrl-E":"goLineEnd","Ctrl-V":"goPageDown","Shift-Ctrl-V":"goPageUp","Ctrl-D":"delCharAfter","Ctrl-H":"delCharBefore","Alt-D":"delWordAfter","Alt-Backspace":"delWordBefore","Ctrl-K":"killLine","Ctrl-T":"transposeChars","Ctrl-O":"openLine"},Fo.macDefault={"Cmd-A":"selectAll","Cmd-D":"deleteLine","Cmd-Z":"undo","Shift-Cmd-Z":"redo","Cmd-Y":"redo","Cmd-Home":"goDocStart","Cmd-Up":"goDocStart","Cmd-End":"goDocEnd","Cmd-Down":"goDocEnd","Alt-Left":"goGroupLeft","Alt-Right":"goGroupRight","Cmd-Left":"goLineLeft","Cmd-Right":"goLineRight","Alt-Backspace":"delGroupBefore","Ctrl-Alt-Backspace":"delGroupAfter","Alt-Delete":"delGroupAfter","Cmd-S":"save","Cmd-F":"find","Cmd-G":"findNext","Shift-Cmd-G":"findPrev","Cmd-Alt-F":"replace","Shift-Cmd-Alt-F":"replaceAll","Cmd-[":"indentLess","Cmd-]":"indentMore","Cmd-Backspace":"delWrappedLineLeft","Cmd-Delete":"delWrappedLineRight","Cmd-U":"undoSelection","Shift-Cmd-U":"redoSelection","Ctrl-Up":"goDocStart","Ctrl-Down":"goDocEnd",fallthrough:["basic","emacsy"]},Fo.default=y?Fo.macDefault:Fo.pcDefault;var Zo={selectAll:no,singleSelection:function(e){return e.setSelection(e.getCursor("anchor"),e.getCursor("head"),H)},killLine:function(e){return Vo(e,function(t){if(t.empty()){var r=Ge(e.doc,t.head.line).text.length;return t.head.ch==r&&t.head.line<e.lastLine()?{from:t.head,to:et(t.head.line+1,0)}:{from:t.head,to:et(t.head.line,r)}}return{from:t.from(),to:t.to()}})},deleteLine:function(e){return Vo(e,function(t){return{from:et(t.from().line,0),to:st(e.doc,et(t.to().line+1,0))}})},delLineLeft:function(e){return Vo(e,function(e){return{from:et(e.from().line,0),to:e.from()}})},delWrappedLineLeft:function(e){return Vo(e,function(t){var r=e.charCoords(t.head,"div").top+5;return{from:e.coordsChar({left:0,top:r},"div"),to:t.from()}})},delWrappedLineRight:function(e){return Vo(e,function(t){var r=e.charCoords(t.head,"div").top+5,n=e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:r},"div");return{from:t.from(),to:n}})},undo:function(e){return e.undo()},redo:function(e){return e.redo()},undoSelection:function(e){return e.undoSelection()},redoSelection:function(e){return e.redoSelection()},goDocStart:function(e){return e.extendSelection(et(e.firstLine(),0))},goDocEnd:function(e){return e.extendSelection(et(e.lastLine()))},goLineStart:function(e){return e.extendSelectionsBy(function(t){return Jo(e,t.head.line)},{origin:"+move",bias:1})},goLineStartSmart:function(e){return e.extendSelectionsBy(function(t){return Qo(e,t.head)},{origin:"+move",bias:1})},goLineEnd:function(e){return e.extendSelectionsBy(function(t){return function(e,t){var r=Ge(e.doc,t),n=function(e){for(var t;t=Pt(e);)e=t.find(1,!0).line;return e}(r);n!=r&&(t=Ye(n));return Yo(!0,e,r,t,-1)}(e,t.head.line)},{origin:"+move",bias:-1})},goLineRight:function(e){return e.extendSelectionsBy(function(t){var r=e.cursorCoords(t.head,"div").top+5;return e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:r},"div")},B)},goLineLeft:function(e){return e.extendSelectionsBy(function(t){var r=e.cursorCoords(t.head,"div").top+5;return e.coordsChar({left:0,top:r},"div")},B)},goLineLeftSmart:function(e){return e.extendSelectionsBy(function(t){var r=e.cursorCoords(t.head,"div").top+5,n=e.coordsChar({left:0,top:r},"div");return n.ch<e.getLine(n.line).search(/\S/)?Qo(e,t.head):n},B)},goLineUp:function(e){return e.moveV(-1,"line")},goLineDown:function(e){return e.moveV(1,"line")},goPageUp:function(e){return e.moveV(-1,"page")},goPageDown:function(e){return e.moveV(1,"page")},goCharLeft:function(e){return e.moveH(-1,"char")},goCharRight:function(e){return e.moveH(1,"char")},goColumnLeft:function(e){return e.moveH(-1,"column")},goColumnRight:function(e){return e.moveH(1,"column")},goWordLeft:function(e){return e.moveH(-1,"word")},goGroupRight:function(e){return e.moveH(1,"group")},goGroupLeft:function(e){return e.moveH(-1,"group")},goWordRight:function(e){return e.moveH(1,"word")},delCharBefore:function(e){return e.deleteH(-1,"char")},delCharAfter:function(e){return e.deleteH(1,"char")},delWordBefore:function(e){return e.deleteH(-1,"word")},delWordAfter:function(e){return e.deleteH(1,"word")},delGroupBefore:function(e){return e.deleteH(-1,"group")},delGroupAfter:function(e){return e.deleteH(1,"group")},indentAuto:function(e){return e.indentSelection("smart")},indentMore:function(e){return e.indentSelection("add")},indentLess:function(e){return e.indentSelection("subtract")},insertTab:function(e){return e.replaceSelection("\t")},insertSoftTab:function(e){for(var t=[],r=e.listSelections(),n=e.options.tabSize,i=0;i<r.length;i++){var o=r[i].from(),a=$(e.getLine(o.line),o.ch,n);t.push(K(n-a%n))}e.replaceSelections(t)},defaultTab:function(e){e.somethingSelected()?e.indentSelection("add"):e.execCommand("insertTab")},transposeChars:function(e){return Yn(e,function(){for(var t=e.listSelections(),r=[],n=0;n<t.length;n++)if(t[n].empty()){var i=t[n].head,o=Ge(e.doc,i.line).text;if(o)if(i.ch==o.length&&(i=new et(i.line,i.ch-1)),i.ch>0)i=new et(i.line,i.ch+1),e.replaceRange(o.charAt(i.ch-1)+o.charAt(i.ch-2),et(i.line,i.ch-2),i,"+transpose");else if(i.line>e.doc.first){var a=Ge(e.doc,i.line-1).text;a&&(i=new et(i.line,1),e.replaceRange(o.charAt(0)+e.doc.lineSeparator()+a.charAt(a.length-1),et(i.line-1,a.length-1),i,"+transpose"))}r.push(new bi(i,i))}e.setSelections(r)})},newlineAndIndent:function(e){return Yn(e,function(){for(var t=e.listSelections(),r=t.length-1;r>=0;r--)e.replaceRange(e.doc.lineSeparator(),t[r].anchor,t[r].head,"+input");t=e.listSelections();for(var n=0;n<t.length;n++)e.indentLine(t[n].from().line,null,!0);An(e)})},openLine:function(e){return e.replaceSelection("\n","start")},toggleOverwrite:function(e){return e.toggleOverwrite()}};function Jo(e,t){var r=Ge(e.doc,t),n=$t(r);return n!=r&&(t=Ye(n)),Yo(!0,e,n,t,1)}function Qo(e,t){var r=Jo(e,t.line),n=Ge(e.doc,r.line),i=ue(n,e.doc.direction);if(!i||0==i[0].level){var o=Math.max(0,n.text.search(/\S/)),a=t.line==r.line&&t.ch<=o&&t.ch;return et(r.line,a?0:o,r.sticky)}return r}function ea(e,t,r){if("string"==typeof t&&!(t=Zo[t]))return!1;e.display.input.ensurePolled();var n=e.display.shift,i=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),r&&(e.display.shift=!1),i=t(e)!=q}finally{e.display.shift=n,e.state.suppressEdits=!1}return i}var ta=new F;function ra(e,t,r,n){var i=e.state.keySeq;if(i){if(Ho(t))return"handled";if(/\'$/.test(t)?e.state.keySeq=null:ta.set(50,function(){e.state.keySeq==i&&(e.state.keySeq=null,e.display.input.reset())}),na(e,i+" "+t,r,n))return!0}return na(e,t,r,n)}function na(e,t,r,n){var i=function(e,t,r){for(var n=0;n<e.state.keyMaps.length;n++){var i=qo(t,e.state.keyMaps[n],r,e);if(i)return i}return e.options.extraKeys&&qo(t,e.options.extraKeys,r,e)||qo(t,e.options.keyMap,r,e)}(e,t,n);return"multi"==i&&(e.state.keySeq=t),"handled"==i&&sr(e,"keyHandled",e,t,r),"handled"!=i&&"multi"!=i||(we(r),bn(e)),!!i}function ia(e,t){var r=Bo(t,!0);return!!r&&(t.shiftKey&&!e.state.keySeq?ra(e,"Shift-"+r,t,function(t){return ea(e,t,!0)})||ra(e,r,t,function(t){if("string"==typeof t?/^go[A-Z]/.test(t):t.motion)return ea(e,t)}):ra(e,r,t,function(t){return ea(e,t)}))}var oa=null;function aa(e){var t=this;if(t.curOp.focus=E(),!ge(t,e)){a&&s<11&&27==e.keyCode&&(e.returnValue=!1);var r=e.keyCode;t.display.shift=16==r||e.shiftKey;var n=ia(t,e);f&&(oa=n?r:null,!n&&88==r&&!Ie&&(y?e.metaKey:e.ctrlKey)&&t.replaceSelection("",null,"cut")),18!=r||/\bCodeMirror-crosshair\b/.test(t.display.lineDiv.className)||function(e){var t=e.display.lineDiv;function r(e){18!=e.keyCode&&e.altKey||(T(t,"CodeMirror-crosshair"),he(document,"keyup",r),he(document,"mouseover",r))}D(t,"CodeMirror-crosshair"),de(document,"keyup",r),de(document,"mouseover",r)}(t)}}function sa(e){16==e.keyCode&&(this.doc.sel.shift=!1),ge(this,e)}function la(e){var t=this;if(!(xr(t.display,e)||ge(t,e)||e.ctrlKey&&!e.altKey||y&&e.metaKey)){var r=e.keyCode,n=e.charCode;if(f&&r==oa)return oa=null,void we(e);if(!f||e.which&&!(e.which<10)||!ia(t,e)){var i=String.fromCharCode(null==n?r:n);"\b"!=i&&(function(e,t,r){return ra(e,"'"+r+"'",t,function(t){return ea(e,t,!0)})}(t,e,i)||t.display.input.onKeyPress(e))}}}var ca,ua,fa=function(e,t,r){this.time=e,this.pos=t,this.button=r};function da(e){var t=this,r=t.display;if(!(ge(t,e)||r.activeTouch&&r.input.supportsTouch()))if(r.input.ensurePolled(),r.shift=e.shiftKey,xr(r,e))l||(r.scroller.draggable=!1,setTimeout(function(){return r.scroller.draggable=!0},100));else if(!ma(t,e)){var n=sn(t,e),i=Se(e),o=n?function(e,t){var r=+new Date;return ua&&ua.compare(r,e,t)?(ca=ua=null,"triple"):ca&&ca.compare(r,e,t)?(ua=new fa(r,e,t),ca=null,"double"):(ca=new fa(r,e,t),ua=null,"single")}(n,i):"single";window.focus(),1==i&&t.state.selectingText&&t.state.selectingText(e),n&&function(e,t,r,n,i){var o="Click";"double"==n?o="Double"+o:"triple"==n&&(o="Triple"+o);return ra(e,Uo(o=(1==t?"Left":2==t?"Middle":"Right")+o,i),i,function(t){if("string"==typeof t&&(t=Zo[t]),!t)return!1;var n=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),n=t(e,r)!=q}finally{e.state.suppressEdits=!1}return n})}(t,i,n,o,e)||(1==i?n?function(e,t,r,n){a?setTimeout(z(wn,e),0):e.curOp.focus=E();var i,o=function(e,t,r){var n=e.getOption("configureMouse"),i=n?n(e,t,r):{};if(null==i.unit){var o=b?r.shiftKey&&r.metaKey:r.altKey;i.unit=o?"rectangle":"single"==t?"char":"double"==t?"word":"line"}(null==i.extend||e.doc.extend)&&(i.extend=e.doc.extend||r.shiftKey);null==i.addNew&&(i.addNew=y?r.metaKey:r.ctrlKey);null==i.moveOnDrag&&(i.moveOnDrag=!(y?r.altKey:r.ctrlKey));return i}(e,r,n),c=e.doc.sel;e.options.dragDrop&&Le&&!e.isReadOnly()&&"single"==r&&(i=c.contains(t))>-1&&(tt((i=c.ranges[i]).from(),t)<0||t.xRel>0)&&(tt(i.to(),t)>0||t.xRel<0)?function(e,t,r,n){var i=e.display,o=!1,c=Zn(e,function(t){l&&(i.scroller.draggable=!1),e.state.draggingText=!1,he(i.wrapper.ownerDocument,"mouseup",c),he(i.wrapper.ownerDocument,"mousemove",u),he(i.scroller,"dragstart",f),he(i.scroller,"drop",c),o||(we(t),n.addNew||Ui(e.doc,r,null,null,n.extend),l||a&&9==s?setTimeout(function(){i.wrapper.ownerDocument.body.focus(),i.input.focus()},20):i.input.focus())}),u=function(e){o=o||Math.abs(t.clientX-e.clientX)+Math.abs(t.clientY-e.clientY)>=10},f=function(){return o=!0};l&&(i.scroller.draggable=!0);e.state.draggingText=c,c.copy=!n.moveOnDrag,i.scroller.dragDrop&&i.scroller.dragDrop();de(i.wrapper.ownerDocument,"mouseup",c),de(i.wrapper.ownerDocument,"mousemove",u),de(i.scroller,"dragstart",f),de(i.scroller,"drop",c),xn(e),setTimeout(function(){return i.input.focus()},20)}(e,n,t,o):function(e,t,r,n){var i=e.display,o=e.doc;we(t);var a,s,l=o.sel,c=l.ranges;n.addNew&&!n.extend?(s=o.sel.contains(r),a=s>-1?c[s]:new bi(r,r)):(a=o.sel.primary(),s=o.sel.primIndex);if("rectangle"==n.unit)n.addNew||(a=new bi(r,r)),r=sn(e,t,!0,!0),s=-1;else{var u=pa(e,r,n.unit);a=n.extend?Hi(a,u.anchor,u.head,n.extend):u}n.addNew?-1==s?(s=c.length,Xi(o,wi(e,c.concat([a]),s),{scroll:!1,origin:"*mouse"})):c.length>1&&c[s].empty()&&"char"==n.unit&&!n.extend?(Xi(o,wi(e,c.slice(0,s).concat(c.slice(s+1)),0),{scroll:!1,origin:"*mouse"}),l=o.sel):Gi(o,s,a,U):(s=0,Xi(o,new yi([a],0),U),l=o.sel);var f=r;function d(t){if(0!=tt(f,t))if(f=t,"rectangle"==n.unit){for(var i=[],c=e.options.tabSize,u=$(Ge(o,r.line).text,r.ch,c),d=$(Ge(o,t.line).text,t.ch,c),p=Math.min(u,d),h=Math.max(u,d),m=Math.min(r.line,t.line),g=Math.min(e.lastLine(),Math.max(r.line,t.line));m<=g;m++){var v=Ge(o,m).text,y=G(v,p,c);p==h?i.push(new bi(et(m,y),et(m,y))):v.length>y&&i.push(new bi(et(m,y),et(m,G(v,h,c))))}i.length||i.push(new bi(r,r)),Xi(o,wi(e,l.ranges.slice(0,s).concat(i),s),{origin:"*mouse",scroll:!1}),e.scrollIntoView(t)}else{var b,w=a,x=pa(e,t,n.unit),_=w.anchor;tt(x.anchor,_)>0?(b=x.head,_=ot(w.from(),x.anchor)):(b=x.anchor,_=it(w.to(),x.head));var k=l.ranges.slice(0);k[s]=function(e,t){var r=t.anchor,n=t.head,i=Ge(e.doc,r.line);if(0==tt(r,n)&&r.sticky==n.sticky)return t;var o=ue(i);if(!o)return t;var a=le(o,r.ch,r.sticky),s=o[a];if(s.from!=r.ch&&s.to!=r.ch)return t;var l,c=a+(s.from==r.ch==(1!=s.level)?0:1);if(0==c||c==o.length)return t;if(n.line!=r.line)l=(n.line-r.line)*("ltr"==e.doc.direction?1:-1)>0;else{var u=le(o,n.ch,n.sticky),f=u-a||(n.ch-r.ch)*(1==s.level?-1:1);l=u==c-1||u==c?f<0:f>0}var d=o[c+(l?-1:0)],p=l==(1==d.level),h=p?d.from:d.to,m=p?"after":"before";return r.ch==h&&r.sticky==m?t:new bi(new et(r.line,h,m),n)}(e,new bi(st(o,_),b)),Xi(o,wi(e,k,s),U)}}var p=i.wrapper.getBoundingClientRect(),h=0;function m(t){e.state.selectingText=!1,h=1/0,t&&(we(t),i.input.focus()),he(i.wrapper.ownerDocument,"mousemove",g),he(i.wrapper.ownerDocument,"mouseup",v),o.history.lastSelOrigin=null}var g=Zn(e,function(t){0!==t.buttons&&Se(t)?function t(r){var a=++h;var s=sn(e,r,!0,"rectangle"==n.unit);if(!s)return;if(0!=tt(s,f)){e.curOp.focus=E(),d(s);var l=Tn(i,o);(s.line>=l.to||s.line<l.from)&&setTimeout(Zn(e,function(){h==a&&t(r)}),150)}else{var c=r.clientY<p.top?-20:r.clientY>p.bottom?20:0;c&&setTimeout(Zn(e,function(){h==a&&(i.scroller.scrollTop+=c,t(r))}),50)}}(t):m(t)}),v=Zn(e,m);e.state.selectingText=v,de(i.wrapper.ownerDocument,"mousemove",g),de(i.wrapper.ownerDocument,"mouseup",v)}(e,n,t,o)}(t,n,o,e):Ce(e)==r.scroller&&we(e):2==i?(n&&Ui(t.doc,n),setTimeout(function(){return r.input.focus()},20)):3==i&&(k?t.display.input.onContextMenu(e):xn(t)))}}function pa(e,t,r){if("char"==r)return new bi(t,t);if("word"==r)return e.findWordAt(t);if("line"==r)return new bi(et(t.line,0),st(e.doc,et(t.line+1,0)));var n=r(e,t);return new bi(n.from,n.to)}function ha(e,t,r,n){var i,o;if(t.touches)i=t.touches[0].clientX,o=t.touches[0].clientY;else try{i=t.clientX,o=t.clientY}catch(t){return!1}if(i>=Math.floor(e.display.gutters.getBoundingClientRect().right))return!1;n&&we(t);var a=e.display,s=a.lineDiv.getBoundingClientRect();if(o>s.bottom||!ye(e,r))return _e(t);o-=s.top-a.viewOffset;for(var l=0;l<e.display.gutterSpecs.length;++l){var c=a.gutters.childNodes[l];if(c&&c.getBoundingClientRect().right>=i)return me(e,r,e,Ze(e.doc,o),e.display.gutterSpecs[l].className,t),_e(t)}}function ma(e,t){return ha(e,t,"gutterClick",!0)}function ga(e,t){xr(e.display,t)||function(e,t){if(!ye(e,"gutterContextMenu"))return!1;return ha(e,t,"gutterContextMenu",!1)}(e,t)||ge(e,t,"contextmenu")||k||e.display.input.onContextMenu(t)}function va(e){e.display.wrapper.className=e.display.wrapper.className.replace(/\s*cm-s-\S+/g,"")+e.options.theme.replace(/(^|\s)\s*/g," cm-s-"),Fr(e)}fa.prototype.compare=function(e,t,r){return this.time+400>e&&0==tt(t,this.pos)&&r==this.button};var ya={toString:function(){return"CodeMirror.Init"}},ba={},wa={};function xa(e,t,r){if(!t!=!(r&&r!=ya)){var n=e.display.dragFunctions,i=t?de:he;i(e.display.scroller,"dragstart",n.start),i(e.display.scroller,"dragenter",n.enter),i(e.display.scroller,"dragover",n.over),i(e.display.scroller,"dragleave",n.leave),i(e.display.scroller,"drop",n.drop)}}function _a(e){e.options.lineWrapping?(D(e.display.wrapper,"CodeMirror-wrap"),e.display.sizer.style.minWidth="",e.display.sizerWidth=null):(T(e.display.wrapper,"CodeMirror-wrap"),Bt(e)),an(e),cn(e),Fr(e),setTimeout(function(){return Fn(e)},100)}function ka(e,t){var n=this;if(!(this instanceof ka))return new ka(e,t);this.options=t=t?R(t):{},R(ba,t,!1);var i=t.value;"string"==typeof i?i=new Mo(i,t.mode,null,t.lineSeparator,t.direction):t.mode&&(i.modeOption=t.mode),this.doc=i;var o=new ka.inputStyles[t.inputStyle](this),c=this.display=new function(e,t,n,i){var o=this;this.input=n,o.scrollbarFiller=A("div",null,"CodeMirror-scrollbar-filler"),o.scrollbarFiller.setAttribute("cm-not-content","true"),o.gutterFiller=A("div",null,"CodeMirror-gutter-filler"),o.gutterFiller.setAttribute("cm-not-content","true"),o.lineDiv=O("div",null,"CodeMirror-code"),o.selectionDiv=A("div",null,null,"position: relative; z-index: 1"),o.cursorDiv=A("div",null,"CodeMirror-cursors"),o.measure=A("div",null,"CodeMirror-measure"),o.lineMeasure=A("div",null,"CodeMirror-measure"),o.lineSpace=O("div",[o.measure,o.lineMeasure,o.selectionDiv,o.cursorDiv,o.lineDiv],null,"position: relative; outline: none");var c=O("div",[o.lineSpace],"CodeMirror-lines");o.mover=A("div",[c],null,"position: relative"),o.sizer=A("div",[o.mover],"CodeMirror-sizer"),o.sizerWidth=null,o.heightForcer=A("div",null,null,"position: absolute; height: "+W+"px; width: 1px;"),o.gutters=A("div",null,"CodeMirror-gutters"),o.lineGutter=null,o.scroller=A("div",[o.sizer,o.heightForcer,o.gutters],"CodeMirror-scroll"),o.scroller.setAttribute("tabIndex","-1"),o.wrapper=A("div",[o.scrollbarFiller,o.gutterFiller,o.scroller],"CodeMirror"),a&&s<8&&(o.gutters.style.zIndex=-1,o.scroller.style.paddingRight=0),l||r&&v||(o.scroller.draggable=!0),e&&(e.appendChild?e.appendChild(o.wrapper):e(o.wrapper)),o.viewFrom=o.viewTo=t.first,o.reportedViewFrom=o.reportedViewTo=t.first,o.view=[],o.renderedView=null,o.externalMeasured=null,o.viewOffset=0,o.lastWrapHeight=o.lastWrapWidth=0,o.updateLineNumbers=null,o.nativeBarWidth=o.barHeight=o.barWidth=0,o.scrollbarsClipped=!1,o.lineNumWidth=o.lineNumInnerWidth=o.lineNumChars=null,o.alignWidgets=!1,o.cachedCharWidth=o.cachedTextHeight=o.cachedPaddingH=null,o.maxLine=null,o.maxLineLength=0,o.maxLineChanged=!1,o.wheelDX=o.wheelDY=o.wheelStartX=o.wheelStartY=null,o.shift=!1,o.selForContextMenu=null,o.activeTouch=null,o.gutterSpecs=ui(i.gutters,i.lineNumbers),fi(o),n.init(o)}(e,i,o,t);for(var u in c.wrapper.CodeMirror=this,va(this),t.lineWrapping&&(this.display.wrapper.className+=" CodeMirror-wrap"),qn(this),this.state={keyMaps:[],overlays:[],modeGen:0,overwrite:!1,delayingBlurEvent:!1,focused:!1,suppressEdits:!1,pasteIncoming:-1,cutIncoming:-1,selectingText:!1,draggingText:!1,highlight:new F,keySeq:null,specialChars:null},t.autofocus&&!v&&c.input.focus(),a&&s<11&&setTimeout(function(){return n.display.input.reset(!0)},20),function(e){var t=e.display;de(t.scroller,"mousedown",Zn(e,da)),de(t.scroller,"dblclick",a&&s<11?Zn(e,function(t){if(!ge(e,t)){var r=sn(e,t);if(r&&!ma(e,t)&&!xr(e.display,t)){we(t);var n=e.findWordAt(r);Ui(e.doc,n.anchor,n.head)}}}):function(t){return ge(e,t)||we(t)});de(t.scroller,"contextmenu",function(t){return ga(e,t)});var r,n={end:0};function i(){t.activeTouch&&(r=setTimeout(function(){return t.activeTouch=null},1e3),(n=t.activeTouch).end=+new Date)}function o(e,t){if(null==t.left)return!0;var r=t.left-e.left,n=t.top-e.top;return r*r+n*n>400}de(t.scroller,"touchstart",function(i){if(!ge(e,i)&&!function(e){if(1!=e.touches.length)return!1;var t=e.touches[0];return t.radiusX<=1&&t.radiusY<=1}(i)&&!ma(e,i)){t.input.ensurePolled(),clearTimeout(r);var o=+new Date;t.activeTouch={start:o,moved:!1,prev:o-n.end<=300?n:null},1==i.touches.length&&(t.activeTouch.left=i.touches[0].pageX,t.activeTouch.top=i.touches[0].pageY)}}),de(t.scroller,"touchmove",function(){t.activeTouch&&(t.activeTouch.moved=!0)}),de(t.scroller,"touchend",function(r){var n=t.activeTouch;if(n&&!xr(t,r)&&null!=n.left&&!n.moved&&new Date-n.start<300){var a,s=e.coordsChar(t.activeTouch,"page");a=!n.prev||o(n,n.prev)?new bi(s,s):!n.prev.prev||o(n,n.prev.prev)?e.findWordAt(s):new bi(et(s.line,0),st(e.doc,et(s.line+1,0))),e.setSelection(a.anchor,a.head),e.focus(),we(r)}i()}),de(t.scroller,"touchcancel",i),de(t.scroller,"scroll",function(){t.scroller.clientHeight&&(Dn(e,t.scroller.scrollTop),Pn(e,t.scroller.scrollLeft,!0),me(e,"scroll",e))}),de(t.scroller,"mousewheel",function(t){return vi(e,t)}),de(t.scroller,"DOMMouseScroll",function(t){return vi(e,t)}),de(t.wrapper,"scroll",function(){return t.wrapper.scrollTop=t.wrapper.scrollLeft=0}),t.dragFunctions={enter:function(t){ge(e,t)||ke(t)},over:function(t){ge(e,t)||(!function(e,t){var r=sn(e,t);if(r){var n=document.createDocumentFragment();gn(e,r,n),e.display.dragCursor||(e.display.dragCursor=A("div",null,"CodeMirror-cursors CodeMirror-dragcursors"),e.display.lineSpace.insertBefore(e.display.dragCursor,e.display.cursorDiv)),L(e.display.dragCursor,n)}}(e,t),ke(t))},start:function(t){return function(e,t){if(a&&(!e.state.draggingText||+new Date-Lo<100))ke(t);else if(!ge(e,t)&&!xr(e.display,t)&&(t.dataTransfer.setData("Text",e.getSelection()),t.dataTransfer.effectAllowed="copyMove",t.dataTransfer.setDragImage&&!d)){var r=A("img",null,null,"position: fixed; left: 0; top: 0;");r.src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==",f&&(r.width=r.height=1,e.display.wrapper.appendChild(r),r._top=r.offsetTop),t.dataTransfer.setDragImage(r,0,0),f&&r.parentNode.removeChild(r)}}(e,t)},drop:Zn(e,Ao),leave:function(t){ge(e,t)||Oo(e)}};var l=t.input.getField();de(l,"keyup",function(t){return sa.call(e,t)}),de(l,"keydown",Zn(e,aa)),de(l,"keypress",Zn(e,la)),de(l,"focus",function(t){return _n(e,t)}),de(l,"blur",function(t){return kn(e,t)})}(this),Do(),Un(this),this.curOp.forceUpdate=!0,Ni(this,i),t.autofocus&&!v||this.hasFocus()?setTimeout(z(_n,this),20):kn(this),wa)wa.hasOwnProperty(u)&&wa[u](n,t[u],ya);ci(this),t.finishInit&&t.finishInit(this);for(var p=0;p<Ca.length;++p)Ca[p](n);Bn(this),l&&t.lineWrapping&&"optimizelegibility"==getComputedStyle(c.lineDiv).textRendering&&(c.lineDiv.style.textRendering="auto")}ka.defaults=ba,ka.optionHandlers=wa;var Ca=[];function Sa(e,t,r,n){var i,o=e.doc;null==r&&(r="add"),"smart"==r&&(o.mode.indent?i=pt(e,t).state:r="prev");var a=e.options.tabSize,s=Ge(o,t),l=$(s.text,null,a);s.stateAfter&&(s.stateAfter=null);var c,u=s.text.match(/^\s*/)[0];if(n||/\S/.test(s.text)){if("smart"==r&&((c=o.mode.indent(i,s.text.slice(u.length),s.text))==q||c>150)){if(!n)return;r="prev"}}else c=0,r="not";"prev"==r?c=t>o.first?$(Ge(o,t-1).text,null,a):0:"add"==r?c=l+e.options.indentUnit:"subtract"==r?c=l-e.options.indentUnit:"number"==typeof r&&(c=l+r),c=Math.max(0,c);var f="",d=0;if(e.options.indentWithTabs)for(var p=Math.floor(c/a);p;--p)d+=a,f+="\t";if(d<c&&(f+=K(c-d)),f!=u)return uo(o,f,et(t,0),et(t,u.length),"+input"),s.stateAfter=null,!0;for(var h=0;h<o.sel.ranges.length;h++){var m=o.sel.ranges[h];if(m.head.line==t&&m.head.ch<u.length){var g=et(t,u.length);Gi(o,h,new bi(g,g));break}}}ka.defineInitHook=function(e){return Ca.push(e)};var Ta=null;function Ma(e){Ta=e}function La(e,t,r,n,i){var o=e.doc;e.display.shift=!1,n||(n=o.sel);var a=+new Date-200,s="paste"==i||e.state.pasteIncoming>a,l=Ee(t),c=null;if(s&&n.ranges.length>1)if(Ta&&Ta.text.join("\n")==t){if(n.ranges.length%Ta.text.length==0){c=[];for(var u=0;u<Ta.text.length;u++)c.push(o.splitLines(Ta.text[u]))}}else l.length==n.ranges.length&&e.options.pasteLinesPerSelection&&(c=Y(l,function(e){return[e]}));for(var f=e.curOp.updateInput,d=n.ranges.length-1;d>=0;d--){var p=n.ranges[d],h=p.from(),m=p.to();p.empty()&&(r&&r>0?h=et(h.line,h.ch-r):e.state.overwrite&&!s?m=et(m.line,Math.min(Ge(o,m.line).text.length,m.ch+X(l).length)):s&&Ta&&Ta.lineWise&&Ta.text.join("\n")==t&&(h=m=et(h.line,0)));var g={from:h,to:m,text:c?c[d%c.length]:l,origin:i||(s?"paste":e.state.cutIncoming>a?"cut":"+input")};oo(e.doc,g),sr(e,"inputRead",e,g)}t&&!s&&Oa(e,t),An(e),e.curOp.updateInput<2&&(e.curOp.updateInput=f),e.curOp.typing=!0,e.state.pasteIncoming=e.state.cutIncoming=-1}function Aa(e,t){var r=e.clipboardData&&e.clipboardData.getData("Text");if(r)return e.preventDefault(),t.isReadOnly()||t.options.disableInput||Yn(t,function(){return La(t,r,0,null,"paste")}),!0}function Oa(e,t){if(e.options.electricChars&&e.options.smartIndent)for(var r=e.doc.sel,n=r.ranges.length-1;n>=0;n--){var i=r.ranges[n];if(!(i.head.ch>100||n&&r.ranges[n-1].head.line==i.head.line)){var o=e.getModeAt(i.head),a=!1;if(o.electricChars){for(var s=0;s<o.electricChars.length;s++)if(t.indexOf(o.electricChars.charAt(s))>-1){a=Sa(e,i.head.line,"smart");break}}else o.electricInput&&o.electricInput.test(Ge(e.doc,i.head.line).text.slice(0,i.head.ch))&&(a=Sa(e,i.head.line,"smart"));a&&sr(e,"electricInput",e,i.head.line)}}}function Na(e){for(var t=[],r=[],n=0;n<e.doc.sel.ranges.length;n++){var i=e.doc.sel.ranges[n].head.line,o={anchor:et(i,0),head:et(i+1,0)};r.push(o),t.push(e.getRange(o.anchor,o.head))}return{text:t,ranges:r}}function Ea(e,t,r,n){e.setAttribute("autocorrect",r?"":"off"),e.setAttribute("autocapitalize",n?"":"off"),e.setAttribute("spellcheck",!!t)}function Da(){var e=A("textarea",null,null,"position: absolute; bottom: -1em; padding: 0; width: 1px; height: 1em; outline: none"),t=A("div",[e],null,"overflow: hidden; position: relative; width: 3px; height: 0px;");return l?e.style.width="1000px":e.setAttribute("wrap","off"),m&&(e.style.border="1px solid black"),Ea(e),t}function Ia(e,t,r,n,i){var o=t,a=r,s=Ge(e,t.line);function l(n){var o,a;if(null==(o=i?function(e,t,r,n){var i=ue(t,e.doc.direction);if(!i)return Xo(t,r,n);r.ch>=t.text.length?(r.ch=t.text.length,r.sticky="before"):r.ch<=0&&(r.ch=0,r.sticky="after");var o=le(i,r.ch,r.sticky),a=i[o];if("ltr"==e.doc.direction&&a.level%2==0&&(n>0?a.to>r.ch:a.from<r.ch))return Xo(t,r,n);var s,l=function(e,r){return Ko(t,e instanceof et?e.ch:e,r)},c=function(r){return e.options.lineWrapping?(s=s||Nr(e,t),Zr(e,t,s,r)):{begin:0,end:t.text.length}},u=c("before"==r.sticky?l(r,-1):r.ch);if("rtl"==e.doc.direction||1==a.level){var f=1==a.level==n<0,d=l(r,f?1:-1);if(null!=d&&(f?d<=a.to&&d<=u.end:d>=a.from&&d>=u.begin)){var p=f?"before":"after";return new et(r.line,d,p)}}var h=function(e,t,n){for(var o=function(e,t){return t?new et(r.line,l(e,1),"before"):new et(r.line,e,"after")};e>=0&&e<i.length;e+=t){var a=i[e],s=t>0==(1!=a.level),c=s?n.begin:l(n.end,-1);if(a.from<=c&&c<a.to)return o(c,s);if(c=s?a.from:l(a.to,-1),n.begin<=c&&c<n.end)return o(c,s)}},m=h(o+n,n,u);if(m)return m;var g=n>0?u.end:l(u.begin,-1);return null==g||n>0&&g==t.text.length||!(m=h(n>0?0:i.length-1,n,c(g)))?null:m}(e.cm,s,t,r):Xo(s,t,r))){if(n||(a=t.line+r)<e.first||a>=e.first+e.size||(t=new et(a,t.ch,t.sticky),!(s=Ge(e,a))))return!1;t=Yo(i,e.cm,s,t.line,r)}else t=o;return!0}if("char"==n)l();else if("column"==n)l(!0);else if("word"==n||"group"==n)for(var c=null,u="group"==n,f=e.cm&&e.cm.getHelper(t,"wordChars"),d=!0;!(r<0)||l(!d);d=!1){var p=s.text.charAt(t.ch)||"\n",h=te(p,f)?"w":u&&"\n"==p?"n":!u||/\s/.test(p)?null:"p";if(!u||d||h||(h="s"),c&&c!=h){r<0&&(r=1,l(),t.sticky="after");break}if(h&&(c=h),r>0&&!l(!d))break}var m=to(e,t,o,a,!0);return rt(o,m)&&(m.hitSide=!0),m}function Pa(e,t,r,n){var i,o,a=e.doc,s=t.left;if("page"==n){var l=Math.min(e.display.wrapper.clientHeight,window.innerHeight||document.documentElement.clientHeight),c=Math.max(l-.5*en(e.display),3);i=(r>0?t.bottom:t.top)+r*c}else"line"==n&&(i=r>0?t.bottom+3:t.top-3);for(;(o=Xr(e,s,i)).outside;){if(r<0?i<=0:i>=a.height){o.hitSide=!0;break}i+=5*r}return o}var za=function(e){this.cm=e,this.lastAnchorNode=this.lastAnchorOffset=this.lastFocusNode=this.lastFocusOffset=null,this.polling=new F,this.composing=null,this.gracePeriod=!1,this.readDOMTimeout=null};function Ra(e,t){var r=Or(e,t.line);if(!r||r.hidden)return null;var n=Ge(e.doc,t.line),i=Lr(r,n,t.line),o=ue(n,e.doc.direction),a="left";o&&(a=le(o,t.ch)%2?"right":"left");var s=Pr(i.map,t.ch,a);return s.offset="right"==s.collapse?s.end:s.start,s}function $a(e,t){return t&&(e.bad=!0),e}function Fa(e,t,r){var n;if(t==e.display.lineDiv){if(!(n=e.display.lineDiv.childNodes[r]))return $a(e.clipPos(et(e.display.viewTo-1)),!0);t=null,r=0}else for(n=t;;n=n.parentNode){if(!n||n==e.display.lineDiv)return null;if(n.parentNode&&n.parentNode==e.display.lineDiv)break}for(var i=0;i<e.display.view.length;i++){var o=e.display.view[i];if(o.node==n)return ja(o,t,r)}}function ja(e,t,r){var n=e.text.firstChild,i=!1;if(!t||!N(n,t))return $a(et(Ye(e.line),0),!0);if(t==n&&(i=!0,t=n.childNodes[r],r=0,!t)){var o=e.rest?X(e.rest):e.line;return $a(et(Ye(o),o.text.length),i)}var a=3==t.nodeType?t:null,s=t;for(a||1!=t.childNodes.length||3!=t.firstChild.nodeType||(a=t.firstChild,r&&(r=a.nodeValue.length));s.parentNode!=n;)s=s.parentNode;var l=e.measure,c=l.maps;function u(t,r,n){for(var i=-1;i<(c?c.length:0);i++)for(var o=i<0?l.map:c[i],a=0;a<o.length;a+=3){var s=o[a+2];if(s==t||s==r){var u=Ye(i<0?e.line:e.rest[i]),f=o[a]+n;return(n<0||s!=t)&&(f=o[a+(n?1:0)]),et(u,f)}}}var f=u(a,s,r);if(f)return $a(f,i);for(var d=s.nextSibling,p=a?a.nodeValue.length-r:0;d;d=d.nextSibling){if(f=u(d,d.firstChild,0))return $a(et(f.line,f.ch-p),i);p+=d.textContent.length}for(var h=s.previousSibling,m=r;h;h=h.previousSibling){if(f=u(h,h.firstChild,-1))return $a(et(f.line,f.ch+m),i);m+=h.textContent.length}}za.prototype.init=function(e){var t=this,r=this,n=r.cm,i=r.div=e.lineDiv;function o(e){if(!ge(n,e)){if(n.somethingSelected())Ma({lineWise:!1,text:n.getSelections()}),"cut"==e.type&&n.replaceSelection("",null,"cut");else{if(!n.options.lineWiseCopyCut)return;var t=Na(n);Ma({lineWise:!0,text:t.text}),"cut"==e.type&&n.operation(function(){n.setSelections(t.ranges,0,H),n.replaceSelection("",null,"cut")})}if(e.clipboardData){e.clipboardData.clearData();var o=Ta.text.join("\n");if(e.clipboardData.setData("Text",o),e.clipboardData.getData("Text")==o)return void e.preventDefault()}var a=Da(),s=a.firstChild;n.display.lineSpace.insertBefore(a,n.display.lineSpace.firstChild),s.value=Ta.text.join("\n");var l=document.activeElement;P(s),setTimeout(function(){n.display.lineSpace.removeChild(a),l.focus(),l==i&&r.showPrimarySelection()},50)}}Ea(i,n.options.spellcheck,n.options.autocorrect,n.options.autocapitalize),de(i,"paste",function(e){ge(n,e)||Aa(e,n)||s<=11&&setTimeout(Zn(n,function(){return t.updateFromDOM()}),20)}),de(i,"compositionstart",function(e){t.composing={data:e.data,done:!1}}),de(i,"compositionupdate",function(e){t.composing||(t.composing={data:e.data,done:!1})}),de(i,"compositionend",function(e){t.composing&&(e.data!=t.composing.data&&t.readFromDOMSoon(),t.composing.done=!0)}),de(i,"touchstart",function(){return r.forceCompositionEnd()}),de(i,"input",function(){t.composing||t.readFromDOMSoon()}),de(i,"copy",o),de(i,"cut",o)},za.prototype.prepareSelection=function(){var e=mn(this.cm,!1);return e.focus=this.cm.state.focused,e},za.prototype.showSelection=function(e,t){e&&this.cm.display.view.length&&((e.focus||t)&&this.showPrimarySelection(),this.showMultipleSelections(e))},za.prototype.getSelection=function(){return this.cm.display.wrapper.ownerDocument.getSelection()},za.prototype.showPrimarySelection=function(){var e=this.getSelection(),t=this.cm,n=t.doc.sel.primary(),i=n.from(),o=n.to();if(t.display.viewTo==t.display.viewFrom||i.line>=t.display.viewTo||o.line<t.display.viewFrom)e.removeAllRanges();else{var a=Fa(t,e.anchorNode,e.anchorOffset),s=Fa(t,e.focusNode,e.focusOffset);if(!a||a.bad||!s||s.bad||0!=tt(ot(a,s),i)||0!=tt(it(a,s),o)){var l=t.display.view,c=i.line>=t.display.viewFrom&&Ra(t,i)||{node:l[0].measure.map[2],offset:0},u=o.line<t.display.viewTo&&Ra(t,o);if(!u){var f=l[l.length-1].measure,d=f.maps?f.maps[f.maps.length-1]:f.map;u={node:d[d.length-1],offset:d[d.length-2]-d[d.length-3]}}if(c&&u){var p,h=e.rangeCount&&e.getRangeAt(0);try{p=S(c.node,c.offset,u.offset,u.node)}catch(e){}p&&(!r&&t.state.focused?(e.collapse(c.node,c.offset),p.collapsed||(e.removeAllRanges(),e.addRange(p))):(e.removeAllRanges(),e.addRange(p)),h&&null==e.anchorNode?e.addRange(h):r&&this.startGracePeriod()),this.rememberSelection()}else e.removeAllRanges()}}},za.prototype.startGracePeriod=function(){var e=this;clearTimeout(this.gracePeriod),this.gracePeriod=setTimeout(function(){e.gracePeriod=!1,e.selectionChanged()&&e.cm.operation(function(){return e.cm.curOp.selectionChanged=!0})},20)},za.prototype.showMultipleSelections=function(e){L(this.cm.display.cursorDiv,e.cursors),L(this.cm.display.selectionDiv,e.selection)},za.prototype.rememberSelection=function(){var e=this.getSelection();this.lastAnchorNode=e.anchorNode,this.lastAnchorOffset=e.anchorOffset,this.lastFocusNode=e.focusNode,this.lastFocusOffset=e.focusOffset},za.prototype.selectionInEditor=function(){var e=this.getSelection();if(!e.rangeCount)return!1;var t=e.getRangeAt(0).commonAncestorContainer;return N(this.div,t)},za.prototype.focus=function(){"nocursor"!=this.cm.options.readOnly&&(this.selectionInEditor()||this.showSelection(this.prepareSelection(),!0),this.div.focus())},za.prototype.blur=function(){this.div.blur()},za.prototype.getField=function(){return this.div},za.prototype.supportsTouch=function(){return!0},za.prototype.receivedFocus=function(){var e=this;this.selectionInEditor()?this.pollSelection():Yn(this.cm,function(){return e.cm.curOp.selectionChanged=!0}),this.polling.set(this.cm.options.pollInterval,function t(){e.cm.state.focused&&(e.pollSelection(),e.polling.set(e.cm.options.pollInterval,t))})},za.prototype.selectionChanged=function(){var e=this.getSelection();return e.anchorNode!=this.lastAnchorNode||e.anchorOffset!=this.lastAnchorOffset||e.focusNode!=this.lastFocusNode||e.focusOffset!=this.lastFocusOffset},za.prototype.pollSelection=function(){if(null==this.readDOMTimeout&&!this.gracePeriod&&this.selectionChanged()){var e=this.getSelection(),t=this.cm;if(g&&u&&this.cm.display.gutterSpecs.length&&function(e){for(var t=e;t;t=t.parentNode)if(/CodeMirror-gutter-wrapper/.test(t.className))return!0;return!1}(e.anchorNode))return this.cm.triggerOnKeyDown({type:"keydown",keyCode:8,preventDefault:Math.abs}),this.blur(),void this.focus();if(!this.composing){this.rememberSelection();var r=Fa(t,e.anchorNode,e.anchorOffset),n=Fa(t,e.focusNode,e.focusOffset);r&&n&&Yn(t,function(){Xi(t.doc,xi(r,n),H),(r.bad||n.bad)&&(t.curOp.selectionChanged=!0)})}}},za.prototype.pollContent=function(){null!=this.readDOMTimeout&&(clearTimeout(this.readDOMTimeout),this.readDOMTimeout=null);var e,t,r,n=this.cm,i=n.display,o=n.doc.sel.primary(),a=o.from(),s=o.to();if(0==a.ch&&a.line>n.firstLine()&&(a=et(a.line-1,Ge(n.doc,a.line-1).length)),s.ch==Ge(n.doc,s.line).text.length&&s.line<n.lastLine()&&(s=et(s.line+1,0)),a.line<i.viewFrom||s.line>i.viewTo-1)return!1;a.line==i.viewFrom||0==(e=ln(n,a.line))?(t=Ye(i.view[0].line),r=i.view[0].node):(t=Ye(i.view[e].line),r=i.view[e-1].node.nextSibling);var l,c,u=ln(n,s.line);if(u==i.view.length-1?(l=i.viewTo-1,c=i.lineDiv.lastChild):(l=Ye(i.view[u+1].line)-1,c=i.view[u+1].node.previousSibling),!r)return!1;for(var f=n.doc.splitLines(function(e,t,r,n,i){var o="",a=!1,s=e.doc.lineSeparator(),l=!1;function c(){a&&(o+=s,l&&(o+=s),a=l=!1)}function u(e){e&&(c(),o+=e)}function f(t){if(1==t.nodeType){var r=t.getAttribute("cm-text");if(r)return void u(r);var o,d=t.getAttribute("cm-marker");if(d){var p=e.findMarks(et(n,0),et(i+1,0),(g=+d,function(e){return e.id==g}));return void(p.length&&(o=p[0].find(0))&&u(Ve(e.doc,o.from,o.to).join(s)))}if("false"==t.getAttribute("contenteditable"))return;var h=/^(pre|div|p|li|table|br)$/i.test(t.nodeName);if(!/^br$/i.test(t.nodeName)&&0==t.textContent.length)return;h&&c();for(var m=0;m<t.childNodes.length;m++)f(t.childNodes[m]);/^(pre|p)$/i.test(t.nodeName)&&(l=!0),h&&(a=!0)}else 3==t.nodeType&&u(t.nodeValue.replace(/\u200b/g,"").replace(/\u00a0/g," "));var g}for(;f(t),t!=r;)t=t.nextSibling,l=!1;return o}(n,r,c,t,l)),d=Ve(n.doc,et(t,0),et(l,Ge(n.doc,l).text.length));f.length>1&&d.length>1;)if(X(f)==X(d))f.pop(),d.pop(),l--;else{if(f[0]!=d[0])break;f.shift(),d.shift(),t++}for(var p=0,h=0,m=f[0],g=d[0],v=Math.min(m.length,g.length);p<v&&m.charCodeAt(p)==g.charCodeAt(p);)++p;for(var y=X(f),b=X(d),w=Math.min(y.length-(1==f.length?p:0),b.length-(1==d.length?p:0));h<w&&y.charCodeAt(y.length-h-1)==b.charCodeAt(b.length-h-1);)++h;if(1==f.length&&1==d.length&&t==a.line)for(;p&&p>a.ch&&y.charCodeAt(y.length-h-1)==b.charCodeAt(b.length-h-1);)p--,h++;f[f.length-1]=y.slice(0,y.length-h).replace(/^\u200b+/,""),f[0]=f[0].slice(p).replace(/\u200b+$/,"");var x=et(t,p),_=et(l,d.length?X(d).length-h:0);return f.length>1||f[0]||tt(x,_)?(uo(n.doc,f,x,_,"+input"),!0):void 0},za.prototype.ensurePolled=function(){this.forceCompositionEnd()},za.prototype.reset=function(){this.forceCompositionEnd()},za.prototype.forceCompositionEnd=function(){this.composing&&(clearTimeout(this.readDOMTimeout),this.composing=null,this.updateFromDOM(),this.div.blur(),this.div.focus())},za.prototype.readFromDOMSoon=function(){var e=this;null==this.readDOMTimeout&&(this.readDOMTimeout=setTimeout(function(){if(e.readDOMTimeout=null,e.composing){if(!e.composing.done)return;e.composing=null}e.updateFromDOM()},80))},za.prototype.updateFromDOM=function(){var e=this;!this.cm.isReadOnly()&&this.pollContent()||Yn(this.cm,function(){return cn(e.cm)})},za.prototype.setUneditable=function(e){e.contentEditable="false"},za.prototype.onKeyPress=function(e){0==e.charCode||this.composing||(e.preventDefault(),this.cm.isReadOnly()||Zn(this.cm,La)(this.cm,String.fromCharCode(null==e.charCode?e.keyCode:e.charCode),0))},za.prototype.readOnlyChanged=function(e){this.div.contentEditable=String("nocursor"!=e)},za.prototype.onContextMenu=function(){},za.prototype.resetPosition=function(){},za.prototype.needsContentAttribute=!0;var Wa=function(e){this.cm=e,this.prevInput="",this.pollingFast=!1,this.polling=new F,this.hasSelection=!1,this.composing=null};Wa.prototype.init=function(e){var t=this,r=this,n=this.cm;this.createField(e);var i=this.textarea;function o(e){if(!ge(n,e)){if(n.somethingSelected())Ma({lineWise:!1,text:n.getSelections()});else{if(!n.options.lineWiseCopyCut)return;var t=Na(n);Ma({lineWise:!0,text:t.text}),"cut"==e.type?n.setSelections(t.ranges,null,H):(r.prevInput="",i.value=t.text.join("\n"),P(i))}"cut"==e.type&&(n.state.cutIncoming=+new Date)}}e.wrapper.insertBefore(this.wrapper,e.wrapper.firstChild),m&&(i.style.width="0px"),de(i,"input",function(){a&&s>=9&&t.hasSelection&&(t.hasSelection=null),r.poll()}),de(i,"paste",function(e){ge(n,e)||Aa(e,n)||(n.state.pasteIncoming=+new Date,r.fastPoll())}),de(i,"cut",o),de(i,"copy",o),de(e.scroller,"paste",function(t){if(!xr(e,t)&&!ge(n,t)){if(!i.dispatchEvent)return n.state.pasteIncoming=+new Date,void r.focus();var o=new Event("paste");o.clipboardData=t.clipboardData,i.dispatchEvent(o)}}),de(e.lineSpace,"selectstart",function(t){xr(e,t)||we(t)}),de(i,"compositionstart",function(){var e=n.getCursor("from");r.composing&&r.composing.range.clear(),r.composing={start:e,range:n.markText(e,n.getCursor("to"),{className:"CodeMirror-composing"})}}),de(i,"compositionend",function(){r.composing&&(r.poll(),r.composing.range.clear(),r.composing=null)})},Wa.prototype.createField=function(e){this.wrapper=Da(),this.textarea=this.wrapper.firstChild},Wa.prototype.prepareSelection=function(){var e=this.cm,t=e.display,r=e.doc,n=mn(e);if(e.options.moveInputWithCursor){var i=Gr(e,r.sel.primary().head,"div"),o=t.wrapper.getBoundingClientRect(),a=t.lineDiv.getBoundingClientRect();n.teTop=Math.max(0,Math.min(t.wrapper.clientHeight-10,i.top+a.top-o.top)),n.teLeft=Math.max(0,Math.min(t.wrapper.clientWidth-10,i.left+a.left-o.left))}return n},Wa.prototype.showSelection=function(e){var t=this.cm.display;L(t.cursorDiv,e.cursors),L(t.selectionDiv,e.selection),null!=e.teTop&&(this.wrapper.style.top=e.teTop+"px",this.wrapper.style.left=e.teLeft+"px")},Wa.prototype.reset=function(e){if(!this.contextMenuPending&&!this.composing){var t=this.cm;if(t.somethingSelected()){this.prevInput="";var r=t.getSelection();this.textarea.value=r,t.state.focused&&P(this.textarea),a&&s>=9&&(this.hasSelection=r)}else e||(this.prevInput=this.textarea.value="",a&&s>=9&&(this.hasSelection=null))}},Wa.prototype.getField=function(){return this.textarea},Wa.prototype.supportsTouch=function(){return!1},Wa.prototype.focus=function(){if("nocursor"!=this.cm.options.readOnly&&(!v||E()!=this.textarea))try{this.textarea.focus()}catch(e){}},Wa.prototype.blur=function(){this.textarea.blur()},Wa.prototype.resetPosition=function(){this.wrapper.style.top=this.wrapper.style.left=0},Wa.prototype.receivedFocus=function(){this.slowPoll()},Wa.prototype.slowPoll=function(){var e=this;this.pollingFast||this.polling.set(this.cm.options.pollInterval,function(){e.poll(),e.cm.state.focused&&e.slowPoll()})},Wa.prototype.fastPoll=function(){var e=!1,t=this;t.pollingFast=!0,t.polling.set(20,function r(){t.poll()||e?(t.pollingFast=!1,t.slowPoll()):(e=!0,t.polling.set(60,r))})},Wa.prototype.poll=function(){var e=this,t=this.cm,r=this.textarea,n=this.prevInput;if(this.contextMenuPending||!t.state.focused||De(r)&&!n&&!this.composing||t.isReadOnly()||t.options.disableInput||t.state.keySeq)return!1;var i=r.value;if(i==n&&!t.somethingSelected())return!1;if(a&&s>=9&&this.hasSelection===i||y&&/[\uf700-\uf7ff]/.test(i))return t.display.input.reset(),!1;if(t.doc.sel==t.display.selForContextMenu){var o=i.charCodeAt(0);if(8203!=o||n||(n="​"),8666==o)return this.reset(),this.cm.execCommand("undo")}for(var l=0,c=Math.min(n.length,i.length);l<c&&n.charCodeAt(l)==i.charCodeAt(l);)++l;return Yn(t,function(){La(t,i.slice(l),n.length-l,null,e.composing?"*compose":null),i.length>1e3||i.indexOf("\n")>-1?r.value=e.prevInput="":e.prevInput=i,e.composing&&(e.composing.range.clear(),e.composing.range=t.markText(e.composing.start,t.getCursor("to"),{className:"CodeMirror-composing"}))}),!0},Wa.prototype.ensurePolled=function(){this.pollingFast&&this.poll()&&(this.pollingFast=!1)},Wa.prototype.onKeyPress=function(){a&&s>=9&&(this.hasSelection=null),this.fastPoll()},Wa.prototype.onContextMenu=function(e){var t=this,r=t.cm,n=r.display,i=t.textarea;t.contextMenuPending&&t.contextMenuPending();var o=sn(r,e),c=n.scroller.scrollTop;if(o&&!f){r.options.resetSelectionOnContextMenu&&-1==r.doc.sel.contains(o)&&Zn(r,Xi)(r.doc,xi(o),H);var u,d=i.style.cssText,p=t.wrapper.style.cssText,h=t.wrapper.offsetParent.getBoundingClientRect();if(t.wrapper.style.cssText="position: static",i.style.cssText="position: absolute; width: 30px; height: 30px;\n      top: "+(e.clientY-h.top-5)+"px; left: "+(e.clientX-h.left-5)+"px;\n      z-index: 1000; background: "+(a?"rgba(255, 255, 255, .05)":"transparent")+";\n      outline: none; border-width: 0; outline: none; overflow: hidden; opacity: .05; filter: alpha(opacity=5);",l&&(u=window.scrollY),n.input.focus(),l&&window.scrollTo(null,u),n.input.reset(),r.somethingSelected()||(i.value=t.prevInput=" "),t.contextMenuPending=v,n.selForContextMenu=r.doc.sel,clearTimeout(n.detectingSelectAll),a&&s>=9&&g(),k){ke(e);var m=function(){he(window,"mouseup",m),setTimeout(v,20)};de(window,"mouseup",m)}else setTimeout(v,50)}function g(){if(null!=i.selectionStart){var e=r.somethingSelected(),o="​"+(e?i.value:"");i.value="⇚",i.value=o,t.prevInput=e?"":"​",i.selectionStart=1,i.selectionEnd=o.length,n.selForContextMenu=r.doc.sel}}function v(){if(t.contextMenuPending==v&&(t.contextMenuPending=!1,t.wrapper.style.cssText=p,i.style.cssText=d,a&&s<9&&n.scrollbars.setScrollTop(n.scroller.scrollTop=c),null!=i.selectionStart)){(!a||a&&s<9)&&g();var e=0,o=function(){n.selForContextMenu==r.doc.sel&&0==i.selectionStart&&i.selectionEnd>0&&"​"==t.prevInput?Zn(r,no)(r):e++<10?n.detectingSelectAll=setTimeout(o,500):(n.selForContextMenu=null,n.input.reset())};n.detectingSelectAll=setTimeout(o,200)}}},Wa.prototype.readOnlyChanged=function(e){e||this.reset(),this.textarea.disabled="nocursor"==e},Wa.prototype.setUneditable=function(){},Wa.prototype.needsContentAttribute=!1,function(e){var t=e.optionHandlers;function r(r,n,i,o){e.defaults[r]=n,i&&(t[r]=o?function(e,t,r){r!=ya&&i(e,t,r)}:i)}e.defineOption=r,e.Init=ya,r("value","",function(e,t){return e.setValue(t)},!0),r("mode",null,function(e,t){e.doc.modeOption=t,Ti(e)},!0),r("indentUnit",2,Ti,!0),r("indentWithTabs",!1),r("smartIndent",!0),r("tabSize",4,function(e){Mi(e),Fr(e),cn(e)},!0),r("lineSeparator",null,function(e,t){if(e.doc.lineSep=t,t){var r=[],n=e.doc.first;e.doc.iter(function(e){for(var i=0;;){var o=e.text.indexOf(t,i);if(-1==o)break;i=o+t.length,r.push(et(n,o))}n++});for(var i=r.length-1;i>=0;i--)uo(e.doc,t,r[i],et(r[i].line,r[i].ch+t.length))}}),r("specialChars",/[\u0000-\u001f\u007f-\u009f\u00ad\u061c\u200b-\u200f\u2028\u2029\ufeff\ufff9-\ufffc]/g,function(e,t,r){e.state.specialChars=new RegExp(t.source+(t.test("\t")?"":"|\t"),"g"),r!=ya&&e.refresh()}),r("specialCharPlaceholder",Jt,function(e){return e.refresh()},!0),r("electricChars",!0),r("inputStyle",v?"contenteditable":"textarea",function(){throw new Error("inputStyle can not (yet) be changed in a running editor")},!0),r("spellcheck",!1,function(e,t){return e.getInputField().spellcheck=t},!0),r("autocorrect",!1,function(e,t){return e.getInputField().autocorrect=t},!0),r("autocapitalize",!1,function(e,t){return e.getInputField().autocapitalize=t},!0),r("rtlMoveVisually",!w),r("wholeLineUpdateBefore",!0),r("theme","default",function(e){va(e),di(e)},!0),r("keyMap","default",function(e,t,r){var n=Go(t),i=r!=ya&&Go(r);i&&i.detach&&i.detach(e,n),n.attach&&n.attach(e,i||null)}),r("extraKeys",null),r("configureMouse",null),r("lineWrapping",!1,_a,!0),r("gutters",[],function(e,t){e.display.gutterSpecs=ui(t,e.options.lineNumbers),di(e)},!0),r("fixedGutter",!0,function(e,t){e.display.gutters.style.left=t?nn(e.display)+"px":"0",e.refresh()},!0),r("coverGutterNextToScrollbar",!1,function(e){return Fn(e)},!0),r("scrollbarStyle","native",function(e){qn(e),Fn(e),e.display.scrollbars.setScrollTop(e.doc.scrollTop),e.display.scrollbars.setScrollLeft(e.doc.scrollLeft)},!0),r("lineNumbers",!1,function(e,t){e.display.gutterSpecs=ui(e.options.gutters,t),di(e)},!0),r("firstLineNumber",1,di,!0),r("lineNumberFormatter",function(e){return e},di,!0),r("showCursorWhenSelecting",!1,hn,!0),r("resetSelectionOnContextMenu",!0),r("lineWiseCopyCut",!0),r("pasteLinesPerSelection",!0),r("selectionsMayTouch",!1),r("readOnly",!1,function(e,t){"nocursor"==t&&(kn(e),e.display.input.blur()),e.display.input.readOnlyChanged(t)}),r("disableInput",!1,function(e,t){t||e.display.input.reset()},!0),r("dragDrop",!0,xa),r("allowDropFileTypes",null),r("cursorBlinkRate",530),r("cursorScrollMargin",0),r("cursorHeight",1,hn,!0),r("singleCursorHeightPerLine",!0,hn,!0),r("workTime",100),r("workDelay",100),r("flattenSpans",!0,Mi,!0),r("addModeClass",!1,Mi,!0),r("pollInterval",100),r("undoDepth",200,function(e,t){return e.doc.history.undoDepth=t}),r("historyEventDelay",1250),r("viewportMargin",10,function(e){return e.refresh()},!0),r("maxHighlightLength",1e4,Mi,!0),r("moveInputWithCursor",!0,function(e,t){t||e.display.input.resetPosition()}),r("tabindex",null,function(e,t){return e.display.input.getField().tabIndex=t||""}),r("autofocus",null),r("direction","ltr",function(e,t){return e.doc.setDirection(t)},!0),r("phrases",null)}(ka),function(e){var t=e.optionHandlers,r=e.helpers={};e.prototype={constructor:e,focus:function(){window.focus(),this.display.input.focus()},setOption:function(e,r){var n=this.options,i=n[e];n[e]==r&&"mode"!=e||(n[e]=r,t.hasOwnProperty(e)&&Zn(this,t[e])(this,r,i),me(this,"optionChange",this,e))},getOption:function(e){return this.options[e]},getDoc:function(){return this.doc},addKeyMap:function(e,t){this.state.keyMaps[t?"push":"unshift"](Go(e))},removeKeyMap:function(e){for(var t=this.state.keyMaps,r=0;r<t.length;++r)if(t[r]==e||t[r].name==e)return t.splice(r,1),!0},addOverlay:Jn(function(t,r){var n=t.token?t:e.getMode(this.options,t);if(n.startState)throw new Error("Overlays may not be stateful.");!function(e,t,r){for(var n=0,i=r(t);n<e.length&&r(e[n])<=i;)n++;e.splice(n,0,t)}(this.state.overlays,{mode:n,modeSpec:t,opaque:r&&r.opaque,priority:r&&r.priority||0},function(e){return e.priority}),this.state.modeGen++,cn(this)}),removeOverlay:Jn(function(e){for(var t=this.state.overlays,r=0;r<t.length;++r){var n=t[r].modeSpec;if(n==e||"string"==typeof e&&n.name==e)return t.splice(r,1),this.state.modeGen++,void cn(this)}}),indentLine:Jn(function(e,t,r){"string"!=typeof t&&"number"!=typeof t&&(t=null==t?this.options.smartIndent?"smart":"prev":t?"add":"subtract"),Je(this.doc,e)&&Sa(this,e,t,r)}),indentSelection:Jn(function(e){for(var t=this.doc.sel.ranges,r=-1,n=0;n<t.length;n++){var i=t[n];if(i.empty())i.head.line>r&&(Sa(this,i.head.line,e,!0),r=i.head.line,n==this.doc.sel.primIndex&&An(this));else{var o=i.from(),a=i.to(),s=Math.max(r,o.line);r=Math.min(this.lastLine(),a.line-(a.ch?0:1))+1;for(var l=s;l<r;++l)Sa(this,l,e);var c=this.doc.sel.ranges;0==o.ch&&t.length==c.length&&c[n].from().ch>0&&Gi(this.doc,n,new bi(o,c[n].to()),H)}}}),getTokenAt:function(e,t){return yt(this,e,t)},getLineTokens:function(e,t){return yt(this,et(e),t,!0)},getTokenTypeAt:function(e){e=st(this.doc,e);var t,r=dt(this,Ge(this.doc,e.line)),n=0,i=(r.length-1)/2,o=e.ch;if(0==o)t=r[2];else for(;;){var a=n+i>>1;if((a?r[2*a-1]:0)>=o)i=a;else{if(!(r[2*a+1]<o)){t=r[2*a+2];break}n=a+1}}var s=t?t.indexOf("overlay "):-1;return s<0?t:0==s?null:t.slice(0,s-1)},getModeAt:function(t){var r=this.doc.mode;return r.innerMode?e.innerMode(r,this.getTokenAt(t).state).mode:r},getHelper:function(e,t){return this.getHelpers(e,t)[0]},getHelpers:function(e,t){var n=[];if(!r.hasOwnProperty(t))return n;var i=r[t],o=this.getModeAt(e);if("string"==typeof o[t])i[o[t]]&&n.push(i[o[t]]);else if(o[t])for(var a=0;a<o[t].length;a++){var s=i[o[t][a]];s&&n.push(s)}else o.helperType&&i[o.helperType]?n.push(i[o.helperType]):i[o.name]&&n.push(i[o.name]);for(var l=0;l<i._global.length;l++){var c=i._global[l];c.pred(o,this)&&-1==j(n,c.val)&&n.push(c.val)}return n},getStateAfter:function(e,t){var r=this.doc;return pt(this,(e=at(r,null==e?r.first+r.size-1:e))+1,t).state},cursorCoords:function(e,t){var r=this.doc.sel.primary();return Gr(this,null==e?r.head:"object"==typeof e?st(this.doc,e):e?r.from():r.to(),t||"page")},charCoords:function(e,t){return Br(this,st(this.doc,e),t||"page")},coordsChar:function(e,t){return Xr(this,(e=Ur(this,e,t||"page")).left,e.top)},lineAtHeight:function(e,t){return e=Ur(this,{top:e,left:0},t||"page").top,Ze(this.doc,e+this.display.viewOffset)},heightAtLine:function(e,t,r){var n,i=!1;if("number"==typeof e){var o=this.doc.first+this.doc.size-1;e<this.doc.first?e=this.doc.first:e>o&&(e=o,i=!0),n=Ge(this.doc,e)}else n=e;return Hr(this,n,{top:0,left:0},t||"page",r||i).top+(i?this.doc.height-Ht(n):0)},defaultTextHeight:function(){return en(this.display)},defaultCharWidth:function(){return tn(this.display)},getViewport:function(){return{from:this.display.viewFrom,to:this.display.viewTo}},addWidget:function(e,t,r,n,i){var o,a,s,l=this.display,c=(e=Gr(this,st(this.doc,e))).bottom,u=e.left;if(t.style.position="absolute",t.setAttribute("cm-ignore-events","true"),this.display.input.setUneditable(t),l.sizer.appendChild(t),"over"==n)c=e.top;else if("above"==n||"near"==n){var f=Math.max(l.wrapper.clientHeight,this.doc.height),d=Math.max(l.sizer.clientWidth,l.lineSpace.clientWidth);("above"==n||e.bottom+t.offsetHeight>f)&&e.top>t.offsetHeight?c=e.top-t.offsetHeight:e.bottom+t.offsetHeight<=f&&(c=e.bottom),u+t.offsetWidth>d&&(u=d-t.offsetWidth)}t.style.top=c+"px",t.style.left=t.style.right="","right"==i?(u=l.sizer.clientWidth-t.offsetWidth,t.style.right="0px"):("left"==i?u=0:"middle"==i&&(u=(l.sizer.clientWidth-t.offsetWidth)/2),t.style.left=u+"px"),r&&(o=this,a={left:u,top:c,right:u+t.offsetWidth,bottom:c+t.offsetHeight},null!=(s=Mn(o,a)).scrollTop&&Dn(o,s.scrollTop),null!=s.scrollLeft&&Pn(o,s.scrollLeft))},triggerOnKeyDown:Jn(aa),triggerOnKeyPress:Jn(la),triggerOnKeyUp:sa,triggerOnMouseDown:Jn(da),execCommand:function(e){if(Zo.hasOwnProperty(e))return Zo[e].call(null,this)},triggerElectric:Jn(function(e){Oa(this,e)}),findPosH:function(e,t,r,n){var i=1;t<0&&(i=-1,t=-t);for(var o=st(this.doc,e),a=0;a<t&&!(o=Ia(this.doc,o,i,r,n)).hitSide;++a);return o},moveH:Jn(function(e,t){var r=this;this.extendSelectionsBy(function(n){return r.display.shift||r.doc.extend||n.empty()?Ia(r.doc,n.head,e,t,r.options.rtlMoveVisually):e<0?n.from():n.to()},B)}),deleteH:Jn(function(e,t){var r=this.doc.sel,n=this.doc;r.somethingSelected()?n.replaceSelection("",null,"+delete"):Vo(this,function(r){var i=Ia(n,r.head,e,t,!1);return e<0?{from:i,to:r.head}:{from:r.head,to:i}})}),findPosV:function(e,t,r,n){var i=1,o=n;t<0&&(i=-1,t=-t);for(var a=st(this.doc,e),s=0;s<t;++s){var l=Gr(this,a,"div");if(null==o?o=l.left:l.left=o,(a=Pa(this,l,i,r)).hitSide)break}return a},moveV:Jn(function(e,t){var r=this,n=this.doc,i=[],o=!this.display.shift&&!n.extend&&n.sel.somethingSelected();if(n.extendSelectionsBy(function(a){if(o)return e<0?a.from():a.to();var s=Gr(r,a.head,"div");null!=a.goalColumn&&(s.left=a.goalColumn),i.push(s.left);var l=Pa(r,s,e,t);return"page"==t&&a==n.sel.primary()&&Ln(r,Br(r,l,"div").top-s.top),l},B),i.length)for(var a=0;a<n.sel.ranges.length;a++)n.sel.ranges[a].goalColumn=i[a]}),findWordAt:function(e){var t=Ge(this.doc,e.line).text,r=e.ch,n=e.ch;if(t){var i=this.getHelper(e,"wordChars");"before"!=e.sticky&&n!=t.length||!r?++n:--r;for(var o=t.charAt(r),a=te(o,i)?function(e){return te(e,i)}:/\s/.test(o)?function(e){return/\s/.test(e)}:function(e){return!/\s/.test(e)&&!te(e)};r>0&&a(t.charAt(r-1));)--r;for(;n<t.length&&a(t.charAt(n));)++n}return new bi(et(e.line,r),et(e.line,n))},toggleOverwrite:function(e){null!=e&&e==this.state.overwrite||((this.state.overwrite=!this.state.overwrite)?D(this.display.cursorDiv,"CodeMirror-overwrite"):T(this.display.cursorDiv,"CodeMirror-overwrite"),me(this,"overwriteToggle",this,this.state.overwrite))},hasFocus:function(){return this.display.input.getField()==E()},isReadOnly:function(){return!(!this.options.readOnly&&!this.doc.cantEdit)},scrollTo:Jn(function(e,t){On(this,e,t)}),getScrollInfo:function(){var e=this.display.scroller;return{left:e.scrollLeft,top:e.scrollTop,height:e.scrollHeight-Sr(this)-this.display.barHeight,width:e.scrollWidth-Sr(this)-this.display.barWidth,clientHeight:Mr(this),clientWidth:Tr(this)}},scrollIntoView:Jn(function(e,t){null==e?(e={from:this.doc.sel.primary().head,to:null},null==t&&(t=this.options.cursorScrollMargin)):"number"==typeof e?e={from:et(e,0),to:null}:null==e.from&&(e={from:e,to:null}),e.to||(e.to=e.from),e.margin=t||0,null!=e.from.line?function(e,t){Nn(e),e.curOp.scrollToPos=t}(this,e):En(this,e.from,e.to,e.margin)}),setSize:Jn(function(e,t){var r=this,n=function(e){return"number"==typeof e||/^\d+$/.test(String(e))?e+"px":e};null!=e&&(this.display.wrapper.style.width=n(e)),null!=t&&(this.display.wrapper.style.height=n(t)),this.options.lineWrapping&&$r(this);var i=this.display.viewFrom;this.doc.iter(i,this.display.viewTo,function(e){if(e.widgets)for(var t=0;t<e.widgets.length;t++)if(e.widgets[t].noHScroll){un(r,i,"widget");break}++i}),this.curOp.forceUpdate=!0,me(this,"refresh",this)}),operation:function(e){return Yn(this,e)},startOperation:function(){return Un(this)},endOperation:function(){return Bn(this)},refresh:Jn(function(){var e=this.display.cachedTextHeight;cn(this),this.curOp.forceUpdate=!0,Fr(this),On(this,this.doc.scrollLeft,this.doc.scrollTop),ai(this.display),(null==e||Math.abs(e-en(this.display))>.5)&&an(this),me(this,"refresh",this)}),swapDoc:Jn(function(e){var t=this.doc;return t.cm=null,this.state.selectingText&&this.state.selectingText(),Ni(this,e),Fr(this),this.display.input.reset(),On(this,e.scrollLeft,e.scrollTop),this.curOp.forceScroll=!0,sr(this,"swapDoc",this,t),t}),phrase:function(e){var t=this.options.phrases;return t&&Object.prototype.hasOwnProperty.call(t,e)?t[e]:e},getInputField:function(){return this.display.input.getField()},getWrapperElement:function(){return this.display.wrapper},getScrollerElement:function(){return this.display.scroller},getGutterElement:function(){return this.display.gutters}},be(e),e.registerHelper=function(t,n,i){r.hasOwnProperty(t)||(r[t]=e[t]={_global:[]}),r[t][n]=i},e.registerGlobalHelper=function(t,n,i,o){e.registerHelper(t,n,o),r[t]._global.push({pred:i,val:o})}}(ka);var qa="iter insert remove copy getEditor constructor".split(" ");for(var Ha in Mo.prototype)Mo.prototype.hasOwnProperty(Ha)&&j(qa,Ha)<0&&(ka.prototype[Ha]=function(e){return function(){return e.apply(this.doc,arguments)}}(Mo.prototype[Ha]));return be(Mo),ka.inputStyles={textarea:Wa,contenteditable:za},ka.defineMode=function(e){ka.defaults.mode||"null"==e||(ka.defaults.mode=e),function(e,t){arguments.length>2&&(t.dependencies=Array.prototype.slice.call(arguments,2)),ze[e]=t}.apply(this,arguments)},ka.defineMIME=function(e,t){Re[e]=t},ka.defineMode("null",function(){return{token:function(e){return e.skipToEnd()}}}),ka.defineMIME("text/plain","null"),ka.defineExtension=function(e,t){ka.prototype[e]=t},ka.defineDocExtension=function(e,t){Mo.prototype[e]=t},ka.fromTextArea=function(e,t){if((t=t?R(t):{}).value=e.value,!t.tabindex&&e.tabIndex&&(t.tabindex=e.tabIndex),!t.placeholder&&e.placeholder&&(t.placeholder=e.placeholder),null==t.autofocus){var r=E();t.autofocus=r==e||null!=e.getAttribute("autofocus")&&r==document.body}function n(){e.value=s.getValue()}var i;if(e.form&&(de(e.form,"submit",n),!t.leaveSubmitMethodAlone)){var o=e.form;i=o.submit;try{var a=o.submit=function(){n(),o.submit=i,o.submit(),o.submit=a}}catch(e){}}t.finishInit=function(t){t.save=n,t.getTextArea=function(){return e},t.toTextArea=function(){t.toTextArea=isNaN,n(),e.parentNode.removeChild(t.getWrapperElement()),e.style.display="",e.form&&(he(e.form,"submit",n),"function"==typeof e.form.submit&&(e.form.submit=i))}},e.style.display="none";var s=ka(function(t){return e.parentNode.insertBefore(t,e.nextSibling)},t);return s},function(e){e.off=he,e.on=de,e.wheelEventPixels=gi,e.Doc=Mo,e.splitLines=Ee,e.countColumn=$,e.findColumn=G,e.isWordChar=ee,e.Pass=q,e.signal=me,e.Line=Gt,e.changeEnd=_i,e.scrollbarModel=Wn,e.Pos=et,e.cmpPos=tt,e.modes=ze,e.mimeModes=Re,e.resolveMode=$e,e.getMode=Fe,e.modeExtensions=je,e.extendMode=We,e.copyState=qe,e.startState=Ue,e.innerMode=He,e.commands=Zo,e.keyMap=Fo,e.keyName=Bo,e.isModifierKey=Ho,e.lookupKey=qo,e.normalizeKeyMap=Wo,e.StringStream=Be,e.SharedTextMarker=ko,e.TextMarker=xo,e.LineWidget=yo,e.e_preventDefault=we,e.e_stopPropagation=xe,e.e_stop=ke,e.addClass=D,e.contains=N,e.rmClass=T,e.keyNames=Po}(ka),ka.version="5.48.2",ka},e.exports=n()},D2qy:function(e,t){e.exports={render:function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("main",{class:["layout",{"layout-columns":e.needsColumnLayout}],style:e.gridStyle},[r("tinker-input",{attrs:{path:e.path},on:{execute:e.handleExecute},model:{value:e.input,callback:function(t){e.input=t},expression:"input"}}),e._v(" "),r("hr",{ref:"gutter",staticClass:"layout-gutter"}),e._v(" "),r("tinker-output",{attrs:{value:e.output}})],1)},staticRenderFns:[]}},DQCr:function(e,t,r){"use strict";var n=r("cGG2");function i(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}e.exports=function(e,t,r){if(!t)return e;var o;if(r)o=r(t);else if(n.isURLSearchParams(t))o=t.toString();else{var a=[];n.forEach(t,function(e,t){null!==e&&void 0!==e&&(n.isArray(e)?t+="[]":e=[e],n.forEach(e,function(e){n.isDate(e)?e=e.toISOString():n.isObject(e)&&(e=JSON.stringify(e)),a.push(i(t)+"="+i(e))}))}),o=a.join("&")}if(o){var s=e.indexOf("#");-1!==s&&(e=e.slice(0,s)),e+=(-1===e.indexOf("?")?"?":"&")+o}return e}},DUeU:function(e,t,r){"use strict";var n=r("cGG2");e.exports=function(e,t){t=t||{};var r={},i=["url","method","data"],o=["headers","auth","proxy","params"],a=["baseURL","transformRequest","transformResponse","paramsSerializer","timeout","timeoutMessage","withCredentials","adapter","responseType","xsrfCookieName","xsrfHeaderName","onUploadProgress","onDownloadProgress","decompress","maxContentLength","maxBodyLength","maxRedirects","transport","httpAgent","httpsAgent","cancelToken","socketPath","responseEncoding"],s=["validateStatus"];function l(e,t){return n.isPlainObject(e)&&n.isPlainObject(t)?n.merge(e,t):n.isPlainObject(t)?n.merge({},t):n.isArray(t)?t.slice():t}function c(i){n.isUndefined(t[i])?n.isUndefined(e[i])||(r[i]=l(void 0,e[i])):r[i]=l(e[i],t[i])}n.forEach(i,function(e){n.isUndefined(t[e])||(r[e]=l(void 0,t[e]))}),n.forEach(o,c),n.forEach(a,function(i){n.isUndefined(t[i])?n.isUndefined(e[i])||(r[i]=l(void 0,e[i])):r[i]=l(void 0,t[i])}),n.forEach(s,function(n){n in t?r[n]=l(e[n],t[n]):n in e&&(r[n]=l(void 0,e[n]))});var u=i.concat(o).concat(a).concat(s),f=Object.keys(e).concat(Object.keys(t)).filter(function(e){return-1===u.indexOf(e)});return n.forEach(f,c),r}},DuR2:function(e,t){var r;r=function(){return this}();try{r=r||Function("return this")()||(0,eval)("this")}catch(e){"object"==typeof window&&(r=window)}e.exports=r},Egrn:function(e,t,r){var n=r("VU/8")(r("nmni"),r("dXuT"),!1,null,null,null);e.exports=n.exports},F1kH:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r("I3G/"),i=r.n(n),o=r("mtWM"),a=r.n(o),s=document.head.querySelector('meta[name="csrf-token"]');s&&(a.a.defaults.headers.common["X-CSRF-TOKEN"]=s.content),i.a.component("tinker",r("Xyju")),new i.a({el:"#web-tinker"})},"FZ+f":function(e,t){e.exports=function(e){var t=[];return t.toString=function(){return this.map(function(t){var r=function(e,t){var r=e[1]||"",n=e[3];if(!n)return r;if(t&&"function"==typeof btoa){var i=(a=n,"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(a))))+" */"),o=n.sources.map(function(e){return"/*# sourceURL="+n.sourceRoot+e+" */"});return[r].concat(o).concat([i]).join("\n")}var a;return[r].join("\n")}(t,e);return t[2]?"@media "+t[2]+"{"+r+"}":r}).join("")},t.i=function(e,r){"string"==typeof e&&(e=[[null,e,""]]);for(var n={},i=0;i<this.length;i++){var o=this[i][0];"number"==typeof o&&(n[o]=!0)}for(i=0;i<e.length;i++){var a=e[i];"number"==typeof a[0]&&n[a[0]]||(r&&!a[2]?a[2]=r:r&&(a[2]="("+a[2]+") and ("+r+")"),t.push(a))}},t}},FtD3:function(e,t,r){"use strict";var n=r("t8qj");e.exports=function(e,t,r,i,o){var a=new Error(e);return n(a,t,r,i,o)}},GHBc:function(e,t,r){"use strict";var n=r("cGG2");e.exports=n.isStandardBrowserEnv()?function(){var e,t=/(msie|trident)/i.test(navigator.userAgent),r=document.createElement("a");function i(e){var n=e;return t&&(r.setAttribute("href",n),n=r.href),r.setAttribute("href",n),{href:r.href,protocol:r.protocol?r.protocol.replace(/:$/,""):"",host:r.host,search:r.search?r.search.replace(/^\?/,""):"",hash:r.hash?r.hash.replace(/^#/,""):"",hostname:r.hostname,port:r.port,pathname:"/"===r.pathname.charAt(0)?r.pathname:"/"+r.pathname}}return e=i(window.location.href),function(t){var r=n.isString(t)?i(t):t;return r.protocol===e.protocol&&r.host===e.host}}():function(){return!0}},"I3G/":function(e,t,r){e.exports=r("aIlf")},Iynj:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r("5kR5"),i=r.n(n),o=r("Egrn"),a=r.n(o),s=function(e,t){return Number(e.slice(0,-1*t.length))},l=function(e){return e.endsWith("px")?{value:e,type:"px",numeric:s(e,"px")}:e.endsWith("fr")?{value:e,type:"fr",numeric:s(e,"fr")}:e.endsWith("%")?{value:e,type:"%",numeric:s(e,"%")}:"auto"===e?{value:e,type:"auto"}:null},c=function(e){return e.split(" ").map(l)},u=function(e,t,r){return t.concat(r).map(function(t){return t.style[e]}).filter(function(e){return void 0!==e&&""!==e})},f=function(e){for(var t=0;t<e.length;t++)if(e[t].numeric>0)return t;return null};function d(e){var t;return(t=[]).concat.apply(t,Array.from(e.ownerDocument.styleSheets).map(function(e){var t=[];try{t=Array.from(e.cssRules||[])}catch(e){}return t})).filter(function(t){var r=!1;try{r=e.matches(t.selectorText)}catch(e){}return r})}var p=function(){return!1},h=function(e,t,r){e.style[t]=r},m=function(e,t,r){var n=e[t];return void 0!==n?n:r},g=function(e,t,r){this.direction=e,this.element=t.element,this.track=t.track,this.trackTypes={},"column"===e?(this.gridTemplateProp="grid-template-columns",this.gridGapProp="grid-column-gap",this.cursor=m(r,"columnCursor",m(r,"cursor","col-resize")),this.snapOffset=m(r,"columnSnapOffset",m(r,"snapOffset",30)),this.dragInterval=m(r,"columnDragInterval",m(r,"dragInterval",1)),this.clientAxis="clientX",this.optionStyle=m(r,"gridTemplateColumns")):"row"===e&&(this.gridTemplateProp="grid-template-rows",this.gridGapProp="grid-row-gap",this.cursor=m(r,"rowCursor",m(r,"cursor","row-resize")),this.snapOffset=m(r,"rowSnapOffset",m(r,"snapOffset",30)),this.dragInterval=m(r,"rowDragInterval",m(r,"dragInterval",1)),this.clientAxis="clientY",this.optionStyle=m(r,"gridTemplateRows")),this.onDragStart=m(r,"onDragStart",p),this.onDragEnd=m(r,"onDragEnd",p),this.onDrag=m(r,"onDrag",p),this.writeStyle=m(r,"writeStyle",h),this.startDragging=this.startDragging.bind(this),this.stopDragging=this.stopDragging.bind(this),this.drag=this.drag.bind(this),this.minSizeStart=t.minSizeStart,this.minSizeEnd=t.minSizeEnd,t.element&&(this.element.addEventListener("mousedown",this.startDragging),this.element.addEventListener("touchstart",this.startDragging))};g.prototype.getDimensions=function(){var e=this.grid.getBoundingClientRect(),t=e.width,r=e.height,n=e.top,i=e.bottom,o=e.left,a=e.right;"column"===this.direction?(this.start=n,this.end=i,this.size=r):"row"===this.direction&&(this.start=o,this.end=a,this.size=t)},g.prototype.getSizeAtTrack=function(e,t){return function(e,t,r,n){void 0===r&&(r=0),void 0===n&&(n=!1);var i=n?e+1:e;return t.slice(0,i).reduce(function(e,t){return e+t.numeric},0)+(r?e*r:0)}(e,this.computedPixels,this.computedGapPixels,t)},g.prototype.getSizeOfTrack=function(e){return this.computedPixels[e].numeric},g.prototype.getRawTracks=function(){var e=u(this.gridTemplateProp,[this.grid],d(this.grid));if(!e.length){if(this.optionStyle)return this.optionStyle;throw Error("Unable to determine grid template tracks from styles.")}return e[0]},g.prototype.getGap=function(){var e=u(this.gridGapProp,[this.grid],d(this.grid));return e.length?e[0]:null},g.prototype.getRawComputedTracks=function(){return window.getComputedStyle(this.grid)[this.gridTemplateProp]},g.prototype.getRawComputedGap=function(){return window.getComputedStyle(this.grid)[this.gridGapProp]},g.prototype.setTracks=function(e){this.tracks=e.split(" "),this.trackValues=c(e)},g.prototype.setComputedTracks=function(e){this.computedTracks=e.split(" "),this.computedPixels=c(e)},g.prototype.setGap=function(e){this.gap=e},g.prototype.setComputedGap=function(e){var t,r;this.computedGap=e,this.computedGapPixels=(t="px",((r=this.computedGap).endsWith(t)?Number(r.slice(0,-1*t.length)):null)||0)},g.prototype.getMousePosition=function(e){return"touches"in e?e.touches[0][this.clientAxis]:e[this.clientAxis]},g.prototype.startDragging=function(e){if(!("button"in e&&0!==e.button)){e.preventDefault(),this.element?this.grid=this.element.parentNode:this.grid=e.target.parentNode,this.getDimensions(),this.setTracks(this.getRawTracks()),this.setComputedTracks(this.getRawComputedTracks()),this.setGap(this.getGap()),this.setComputedGap(this.getRawComputedGap());var t=this.trackValues.filter(function(e){return"%"===e.type}),r=this.trackValues.filter(function(e){return"fr"===e.type});if(this.totalFrs=r.length,this.totalFrs){var n=f(r);null!==n&&(this.frToPixels=this.computedPixels[n].numeric/r[n].numeric)}if(t.length){var i=f(t);null!==i&&(this.percentageToPixels=this.computedPixels[i].numeric/t[i].numeric)}var o=this.getSizeAtTrack(this.track,!1)+this.start;if(this.dragStartOffset=this.getMousePosition(e)-o,this.aTrack=this.track-1,!(this.track<this.tracks.length-1))throw Error("Invalid track index: "+this.track+". Track must be between two other tracks and only "+this.tracks.length+" tracks were found.");this.bTrack=this.track+1,this.aTrackStart=this.getSizeAtTrack(this.aTrack,!1)+this.start,this.bTrackEnd=this.getSizeAtTrack(this.bTrack,!0)+this.start,this.dragging=!0,window.addEventListener("mouseup",this.stopDragging),window.addEventListener("touchend",this.stopDragging),window.addEventListener("touchcancel",this.stopDragging),window.addEventListener("mousemove",this.drag),window.addEventListener("touchmove",this.drag),this.grid.addEventListener("selectstart",p),this.grid.addEventListener("dragstart",p),this.grid.style.userSelect="none",this.grid.style.webkitUserSelect="none",this.grid.style.MozUserSelect="none",this.grid.style.pointerEvents="none",this.grid.style.cursor=this.cursor,window.document.body.style.cursor=this.cursor,this.onDragStart(this.direction,this.track)}},g.prototype.stopDragging=function(){this.dragging=!1,this.cleanup(),this.onDragEnd(this.direction,this.track),this.needsDestroy&&(this.element&&(this.element.removeEventListener("mousedown",this.startDragging),this.element.removeEventListener("touchstart",this.startDragging)),this.destroyCb(),this.needsDestroy=!1,this.destroyCb=null)},g.prototype.drag=function(e){var t=this.getMousePosition(e),r=this.getSizeOfTrack(this.track),n=this.aTrackStart+this.minSizeStart+this.dragStartOffset+this.computedGapPixels,i=this.bTrackEnd-this.minSizeEnd-this.computedGapPixels-(r-this.dragStartOffset),o=n+this.snapOffset,a=i-this.snapOffset;t<o&&(t=n),t>a&&(t=i),t<n?t=n:t>i&&(t=i);var s=t-this.aTrackStart-this.dragStartOffset-this.computedGapPixels,l=this.bTrackEnd-t+this.dragStartOffset-r-this.computedGapPixels;if(this.dragInterval>1){var c=Math.round(s/this.dragInterval)*this.dragInterval;l-=c-s,s=c}if(s<this.minSizeStart&&(s=this.minSizeStart),l<this.minSizeEnd&&(l=this.minSizeEnd),"px"===this.trackValues[this.aTrack].type)this.tracks[this.aTrack]=s+"px";else if("fr"===this.trackValues[this.aTrack].type)if(1===this.totalFrs)this.tracks[this.aTrack]="1fr";else{var u=s/this.frToPixels;this.tracks[this.aTrack]=u+"fr"}else if("%"===this.trackValues[this.aTrack].type){var f=s/this.percentageToPixels;this.tracks[this.aTrack]=f+"%"}if("px"===this.trackValues[this.bTrack].type)this.tracks[this.bTrack]=l+"px";else if("fr"===this.trackValues[this.bTrack].type)if(1===this.totalFrs)this.tracks[this.bTrack]="1fr";else{var d=l/this.frToPixels;this.tracks[this.bTrack]=d+"fr"}else if("%"===this.trackValues[this.bTrack].type){var p=l/this.percentageToPixels;this.tracks[this.bTrack]=p+"%"}var h=this.tracks.join(" ");this.writeStyle(this.grid,this.gridTemplateProp,h),this.onDrag(this.direction,this.track,h)},g.prototype.cleanup=function(){window.removeEventListener("mouseup",this.stopDragging),window.removeEventListener("touchend",this.stopDragging),window.removeEventListener("touchcancel",this.stopDragging),window.removeEventListener("mousemove",this.drag),window.removeEventListener("touchmove",this.drag),this.grid&&(this.grid.removeEventListener("selectstart",p),this.grid.removeEventListener("dragstart",p),this.grid.style.userSelect="",this.grid.style.webkitUserSelect="",this.grid.style.MozUserSelect="",this.grid.style.pointerEvents="",this.grid.style.cursor=""),window.document.body.style.cursor=""},g.prototype.destroy=function(e,t){void 0===e&&(e=!0),e||!1===this.dragging?(this.cleanup(),this.element&&(this.element.removeEventListener("mousedown",this.startDragging),this.element.removeEventListener("touchstart",this.startDragging)),t&&t()):(this.needsDestroy=!0,t&&(this.destroyCb=t))};var v=function(e,t,r){return t in e?e[t]:r},y=function(e,t){return function(r){if(r.track<1)throw Error("Invalid track index: "+r.track+". Track must be between two other tracks.");var n="column"===e?t.columnMinSizes||{}:t.rowMinSizes||{},i="column"===e?"columnMinSize":"rowMinSize";return new g(e,Object.assign({},{minSizeStart:v(n,r.track-1,m(t,i,m(t,"minSize",0))),minSizeEnd:v(n,r.track+1,m(t,i,m(t,"minSize",0)))},r),t)}},b=function(e){var t=this;this.columnGutters={},this.rowGutters={},this.options=Object.assign({},{columnGutters:e.columnGutters||[],rowGutters:e.rowGutters||[],columnMinSizes:e.columnMinSizes||{},rowMinSizes:e.rowMinSizes||{}},e),this.options.columnGutters.forEach(function(r){t.columnGutters[e.track]=y("column",t.options)(r)}),this.options.rowGutters.forEach(function(r){t.rowGutters[e.track]=y("row",t.options)(r)})};b.prototype.addColumnGutter=function(e,t){this.columnGutters[t]&&this.columnGutters[t].destroy(),this.columnGutters[t]=y("column",this.options)({element:e,track:t})},b.prototype.addRowGutter=function(e,t){this.rowGutters[t]&&this.rowGutters[t].destroy(),this.rowGutters[t]=y("row",this.options)({element:e,track:t})},b.prototype.removeColumnGutter=function(e,t){var r=this;void 0===t&&(t=!0),this.columnGutters[e]&&this.columnGutters[e].destroy(t,function(){delete r.columnGutters[e]})},b.prototype.removeRowGutter=function(e,t){var r=this;void 0===t&&(t=!0),this.rowGutters[e]&&this.rowGutters[e].destroy(t,function(){delete r.rowGutters[e]})},b.prototype.handleDragStart=function(e,t,r){"column"===t?(this.columnGutters[r]&&this.columnGutters[r].destroy(),this.columnGutters[r]=y("column",this.options)({track:r}),this.columnGutters[r].startDragging(e)):"row"===t&&(this.rowGutters[r]&&this.rowGutters[r].destroy(),this.rowGutters[r]=y("row",this.options)({track:r}),this.rowGutters[r].startDragging(e))},b.prototype.destroy=function(e){var t=this;void 0===e&&(e=!0),Object.keys(this.columnGutters).forEach(function(r){return t.columnGutters[r].destroy(e,function(){delete t.columnGutters[r]})}),Object.keys(this.rowGutters).forEach(function(r){return t.rowGutters[r].destroy(e,function(){delete t.rowGutters[r]})})};var w=function(e){return new b(e)},x=r("OvQW"),_=r.n(x);function k(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}t.default={components:{TinkerInput:i.a,TinkerOutput:a.a},props:["path"],data:function(){return{windowWidth:window.innerWidth,gutterWidth:9,minSize:100,breakpoint:768,input:"",output:'<span class="text-dimmed">// Use cmd+enter or ctrl+enter to run.</span>'}},computed:{columnPercentage:function(){return(1-this.gutterWidth/window.innerWidth)/2*100+"%"},rowPercentage:function(){return(1-this.gutterWidth/window.innerHeight)/2*100+"%"},needsColumnLayout:function(){return this.windowWidth>this.breakpoint},gridStyle:function(){return this.needsColumnLayout?{gridTemplateColumns:this.columnPercentage+" "+this.gutterWidth+"px "+this.columnPercentage}:{gridTemplateRows:this.rowPercentage+" "+this.gutterWidth+"px "+this.rowPercentage}}},methods:{handleExecute:function(e){this.output=_.a.sanitize(e)},initSplit:function(){var e;this.destroySplit(),this.split=w((k(e={},this.needsColumnLayout?"columnGutters":"rowGutters",[{track:1,element:this.$refs.gutter}]),k(e,"minSize",this.minSize),e))},destroySplit:function(){this.split&&this.split.destroy()}},mounted:function(){var e=this;this.initSplit(),this.$watch("needsColumnLayout",this.initSplit),window.addEventListener("resize",function(){e.windowWidth=window.innerWidth})}}},"JP+z":function(e,t,r){"use strict";e.exports=function(e,t){return function(){for(var r=new Array(arguments.length),n=0;n<r.length;n++)r[n]=arguments[n];return e.apply(t,r)}}},KCLY:function(e,t,r){"use strict";(function(t){var n=r("cGG2"),i=r("5VQ+"),o={"Content-Type":"application/x-www-form-urlencoded"};function a(e,t){!n.isUndefined(e)&&n.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var s,l={adapter:("undefined"!=typeof XMLHttpRequest?s=r("7GwW"):void 0!==t&&"[object process]"===Object.prototype.toString.call(t)&&(s=r("7GwW")),s),transformRequest:[function(e,t){return i(t,"Accept"),i(t,"Content-Type"),n.isFormData(e)||n.isArrayBuffer(e)||n.isBuffer(e)||n.isStream(e)||n.isFile(e)||n.isBlob(e)?e:n.isArrayBufferView(e)?e.buffer:n.isURLSearchParams(e)?(a(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):n.isObject(e)?(a(t,"application/json;charset=utf-8"),JSON.stringify(e)):e}],transformResponse:[function(e){if("string"==typeof e)try{e=JSON.parse(e)}catch(e){}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,validateStatus:function(e){return e>=200&&e<300}};l.headers={common:{Accept:"application/json, text/plain, */*"}},n.forEach(["delete","get","head"],function(e){l.headers[e]={}}),n.forEach(["post","put","patch"],function(e){l.headers[e]=n.merge(o)}),e.exports=l}).call(t,r("W2nU"))},"Oi+a":function(e,t,r){"use strict";var n=r("dIwP"),i=r("qRfI");e.exports=function(e,t){return e&&!n(t)?i(e,t):t}},OvQW:function(e,t,r){var n;n=function(){"use strict";const{entries:e,setPrototypeOf:t,isFrozen:r,getPrototypeOf:n,getOwnPropertyDescriptor:i}=Object;let{freeze:o,seal:a,create:s}=Object,{apply:l,construct:c}="undefined"!=typeof Reflect&&Reflect;o||(o=function(e){return e}),a||(a=function(e){return e}),l||(l=function(e,t,r){return e.apply(t,r)}),c||(c=function(e,t){return new e(...t)});const u=k(Array.prototype.forEach),f=k(Array.prototype.pop),d=k(Array.prototype.push),p=k(String.prototype.toLowerCase),h=k(String.prototype.toString),m=k(String.prototype.match),g=k(String.prototype.replace),v=k(String.prototype.indexOf),y=k(String.prototype.trim),b=k(Object.prototype.hasOwnProperty),w=k(RegExp.prototype.test),x=(_=TypeError,function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return c(_,t)});var _;function k(e){return function(t){for(var r=arguments.length,n=new Array(r>1?r-1:0),i=1;i<r;i++)n[i-1]=arguments[i];return l(e,t,n)}}function C(e,n){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:p;t&&t(e,null);let o=n.length;for(;o--;){let t=n[o];if("string"==typeof t){const e=i(t);e!==t&&(r(n)||(n[o]=e),t=e)}e[t]=!0}return e}function S(e){for(let t=0;t<e.length;t++){b(e,t)||(e[t]=null)}return e}function T(t){const r=s(null);for(const[n,i]of e(t)){b(t,n)&&(Array.isArray(i)?r[n]=S(i):i&&"object"==typeof i&&i.constructor===Object?r[n]=T(i):r[n]=i)}return r}function M(e,t){for(;null!==e;){const r=i(e,t);if(r){if(r.get)return k(r.get);if("function"==typeof r.value)return k(r.value)}e=n(e)}return function(){return null}}const L=o(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),A=o(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),O=o(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),N=o(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),E=o(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),D=o(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),I=o(["#text"]),P=o(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),z=o(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),R=o(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),$=o(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),F=a(/\{\{[\w\W]*|[\w\W]*\}\}/gm),j=a(/<%[\w\W]*|[\w\W]*%>/gm),W=a(/\$\{[\w\W]*}/gm),q=a(/^data-[\-\w.\u00B7-\uFFFF]+$/),H=a(/^aria-[\-\w]+$/),U=a(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),B=a(/^(?:\w+script|data):/i),G=a(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),V=a(/^html$/i),K=a(/^[a-z][.\w]*(-[.\w]+)+$/i);var X=Object.freeze({__proto__:null,ARIA_ATTR:H,ATTR_WHITESPACE:G,CUSTOM_ELEMENT:K,DATA_ATTR:q,DOCTYPE_NAME:V,ERB_EXPR:j,IS_ALLOWED_URI:U,IS_SCRIPT_OR_DATA:B,MUSTACHE_EXPR:F,TMPLIT_EXPR:W});const Y={element:1,attribute:2,text:3,cdataSection:4,entityReference:5,entityNode:6,progressingInstruction:7,comment:8,document:9,documentType:10,documentFragment:11,notation:12},Z=function(){return"undefined"==typeof window?null:window},J=function(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let r=null;t&&t.hasAttribute("data-tt-policy-suffix")&&(r=t.getAttribute("data-tt-policy-suffix"));const n="dompurify"+(r?"#"+r:"");try{return e.createPolicy(n,{createHTML:e=>e,createScriptURL:e=>e})}catch(e){return console.warn("TrustedTypes policy "+n+" could not be created."),null}},Q=function(){return{afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}};return function t(){let r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Z();const n=e=>t(e);if(n.version="3.2.3",n.removed=[],!r||!r.document||r.document.nodeType!==Y.document)return n.isSupported=!1,n;let{document:i}=r;const a=i,l=a.currentScript,{DocumentFragment:c,HTMLTemplateElement:_,Node:k,Element:S,NodeFilter:F,NamedNodeMap:j=r.NamedNodeMap||r.MozNamedAttrMap,HTMLFormElement:W,DOMParser:q,trustedTypes:H}=r,B=S.prototype,G=M(B,"cloneNode"),K=M(B,"remove"),ee=M(B,"nextSibling"),te=M(B,"childNodes"),re=M(B,"parentNode");if("function"==typeof _){const e=i.createElement("template");e.content&&e.content.ownerDocument&&(i=e.content.ownerDocument)}let ne,ie="";const{implementation:oe,createNodeIterator:ae,createDocumentFragment:se,getElementsByTagName:le}=i,{importNode:ce}=a;let ue=Q();n.isSupported="function"==typeof e&&"function"==typeof re&&oe&&void 0!==oe.createHTMLDocument;const{MUSTACHE_EXPR:fe,ERB_EXPR:de,TMPLIT_EXPR:pe,DATA_ATTR:he,ARIA_ATTR:me,IS_SCRIPT_OR_DATA:ge,ATTR_WHITESPACE:ve,CUSTOM_ELEMENT:ye}=X;let{IS_ALLOWED_URI:be}=X,we=null;const xe=C({},[...L,...A,...O,...E,...I]);let _e=null;const ke=C({},[...P,...z,...R,...$]);let Ce=Object.seal(s(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),Se=null,Te=null,Me=!0,Le=!0,Ae=!1,Oe=!0,Ne=!1,Ee=!0,De=!1,Ie=!1,Pe=!1,ze=!1,Re=!1,$e=!1,Fe=!0,je=!1;let We=!0,qe=!1,He={},Ue=null;const Be=C({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let Ge=null;const Ve=C({},["audio","video","img","source","image","track"]);let Ke=null;const Xe=C({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),Ye="http://www.w3.org/1998/Math/MathML",Ze="http://www.w3.org/2000/svg",Je="http://www.w3.org/1999/xhtml";let Qe=Je,et=!1,tt=null;const rt=C({},[Ye,Ze,Je],h);let nt=C({},["mi","mo","mn","ms","mtext"]),it=C({},["annotation-xml"]);const ot=C({},["title","style","font","a","script"]);let at=null;const st=["application/xhtml+xml","text/html"];let lt=null,ct=null;const ut=i.createElement("form"),ft=function(e){return e instanceof RegExp||e instanceof Function},dt=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!ct||ct!==e){if(e&&"object"==typeof e||(e={}),e=T(e),at=-1===st.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE,lt="application/xhtml+xml"===at?h:p,we=b(e,"ALLOWED_TAGS")?C({},e.ALLOWED_TAGS,lt):xe,_e=b(e,"ALLOWED_ATTR")?C({},e.ALLOWED_ATTR,lt):ke,tt=b(e,"ALLOWED_NAMESPACES")?C({},e.ALLOWED_NAMESPACES,h):rt,Ke=b(e,"ADD_URI_SAFE_ATTR")?C(T(Xe),e.ADD_URI_SAFE_ATTR,lt):Xe,Ge=b(e,"ADD_DATA_URI_TAGS")?C(T(Ve),e.ADD_DATA_URI_TAGS,lt):Ve,Ue=b(e,"FORBID_CONTENTS")?C({},e.FORBID_CONTENTS,lt):Be,Se=b(e,"FORBID_TAGS")?C({},e.FORBID_TAGS,lt):{},Te=b(e,"FORBID_ATTR")?C({},e.FORBID_ATTR,lt):{},He=!!b(e,"USE_PROFILES")&&e.USE_PROFILES,Me=!1!==e.ALLOW_ARIA_ATTR,Le=!1!==e.ALLOW_DATA_ATTR,Ae=e.ALLOW_UNKNOWN_PROTOCOLS||!1,Oe=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,Ne=e.SAFE_FOR_TEMPLATES||!1,Ee=!1!==e.SAFE_FOR_XML,De=e.WHOLE_DOCUMENT||!1,ze=e.RETURN_DOM||!1,Re=e.RETURN_DOM_FRAGMENT||!1,$e=e.RETURN_TRUSTED_TYPE||!1,Pe=e.FORCE_BODY||!1,Fe=!1!==e.SANITIZE_DOM,je=e.SANITIZE_NAMED_PROPS||!1,We=!1!==e.KEEP_CONTENT,qe=e.IN_PLACE||!1,be=e.ALLOWED_URI_REGEXP||U,Qe=e.NAMESPACE||Je,nt=e.MATHML_TEXT_INTEGRATION_POINTS||nt,it=e.HTML_INTEGRATION_POINTS||it,Ce=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&ft(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(Ce.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&ft(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(Ce.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(Ce.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),Ne&&(Le=!1),Re&&(ze=!0),He&&(we=C({},I),_e=[],!0===He.html&&(C(we,L),C(_e,P)),!0===He.svg&&(C(we,A),C(_e,z),C(_e,$)),!0===He.svgFilters&&(C(we,O),C(_e,z),C(_e,$)),!0===He.mathMl&&(C(we,E),C(_e,R),C(_e,$))),e.ADD_TAGS&&(we===xe&&(we=T(we)),C(we,e.ADD_TAGS,lt)),e.ADD_ATTR&&(_e===ke&&(_e=T(_e)),C(_e,e.ADD_ATTR,lt)),e.ADD_URI_SAFE_ATTR&&C(Ke,e.ADD_URI_SAFE_ATTR,lt),e.FORBID_CONTENTS&&(Ue===Be&&(Ue=T(Ue)),C(Ue,e.FORBID_CONTENTS,lt)),We&&(we["#text"]=!0),De&&C(we,["html","head","body"]),we.table&&(C(we,["tbody"]),delete Se.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw x('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw x('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');ne=e.TRUSTED_TYPES_POLICY,ie=ne.createHTML("")}else void 0===ne&&(ne=J(H,l)),null!==ne&&"string"==typeof ie&&(ie=ne.createHTML(""));o&&o(e),ct=e}},pt=C({},[...A,...O,...N]),ht=C({},[...E,...D]),mt=function(e){d(n.removed,{element:e});try{re(e).removeChild(e)}catch(t){K(e)}},gt=function(e,t){try{d(n.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){d(n.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e)if(ze||Re)try{mt(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},vt=function(e){let t=null,r=null;if(Pe)e="<remove></remove>"+e;else{const t=m(e,/^[\r\n\t ]+/);r=t&&t[0]}"application/xhtml+xml"===at&&Qe===Je&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const n=ne?ne.createHTML(e):e;if(Qe===Je)try{t=(new q).parseFromString(n,at)}catch(e){}if(!t||!t.documentElement){t=oe.createDocument(Qe,"template",null);try{t.documentElement.innerHTML=et?ie:n}catch(e){}}const o=t.body||t.documentElement;return e&&r&&o.insertBefore(i.createTextNode(r),o.childNodes[0]||null),Qe===Je?le.call(t,De?"html":"body")[0]:De?t.documentElement:o},yt=function(e){return ae.call(e.ownerDocument||e,e,F.SHOW_ELEMENT|F.SHOW_COMMENT|F.SHOW_TEXT|F.SHOW_PROCESSING_INSTRUCTION|F.SHOW_CDATA_SECTION,null)},bt=function(e){return e instanceof W&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof j)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},wt=function(e){return"function"==typeof k&&e instanceof k};function xt(e,t,r){u(e,e=>{e.call(n,t,r,ct)})}const _t=function(e){let t=null;if(xt(ue.beforeSanitizeElements,e,null),bt(e))return mt(e),!0;const r=lt(e.nodeName);if(xt(ue.uponSanitizeElement,e,{tagName:r,allowedTags:we}),e.hasChildNodes()&&!wt(e.firstElementChild)&&w(/<[/\w]/g,e.innerHTML)&&w(/<[/\w]/g,e.textContent))return mt(e),!0;if(e.nodeType===Y.progressingInstruction)return mt(e),!0;if(Ee&&e.nodeType===Y.comment&&w(/<[/\w]/g,e.data))return mt(e),!0;if(!we[r]||Se[r]){if(!Se[r]&&Ct(r)){if(Ce.tagNameCheck instanceof RegExp&&w(Ce.tagNameCheck,r))return!1;if(Ce.tagNameCheck instanceof Function&&Ce.tagNameCheck(r))return!1}if(We&&!Ue[r]){const t=re(e)||e.parentNode,r=te(e)||e.childNodes;if(r&&t)for(let n=r.length-1;n>=0;--n){const i=G(r[n],!0);i.__removalCount=(e.__removalCount||0)+1,t.insertBefore(i,ee(e))}}return mt(e),!0}return e instanceof S&&!function(e){let t=re(e);t&&t.tagName||(t={namespaceURI:Qe,tagName:"template"});const r=p(e.tagName),n=p(t.tagName);return!!tt[e.namespaceURI]&&(e.namespaceURI===Ze?t.namespaceURI===Je?"svg"===r:t.namespaceURI===Ye?"svg"===r&&("annotation-xml"===n||nt[n]):Boolean(pt[r]):e.namespaceURI===Ye?t.namespaceURI===Je?"math"===r:t.namespaceURI===Ze?"math"===r&&it[n]:Boolean(ht[r]):e.namespaceURI===Je?!(t.namespaceURI===Ze&&!it[n])&&!(t.namespaceURI===Ye&&!nt[n])&&!ht[r]&&(ot[r]||!pt[r]):!("application/xhtml+xml"!==at||!tt[e.namespaceURI]))}(e)?(mt(e),!0):"noscript"!==r&&"noembed"!==r&&"noframes"!==r||!w(/<\/no(script|embed|frames)/i,e.innerHTML)?(Ne&&e.nodeType===Y.text&&(t=e.textContent,u([fe,de,pe],e=>{t=g(t,e," ")}),e.textContent!==t&&(d(n.removed,{element:e.cloneNode()}),e.textContent=t)),xt(ue.afterSanitizeElements,e,null),!1):(mt(e),!0)},kt=function(e,t,r){if(Fe&&("id"===t||"name"===t)&&(r in i||r in ut))return!1;if(Le&&!Te[t]&&w(he,t));else if(Me&&w(me,t));else if(!_e[t]||Te[t]){if(!(Ct(e)&&(Ce.tagNameCheck instanceof RegExp&&w(Ce.tagNameCheck,e)||Ce.tagNameCheck instanceof Function&&Ce.tagNameCheck(e))&&(Ce.attributeNameCheck instanceof RegExp&&w(Ce.attributeNameCheck,t)||Ce.attributeNameCheck instanceof Function&&Ce.attributeNameCheck(t))||"is"===t&&Ce.allowCustomizedBuiltInElements&&(Ce.tagNameCheck instanceof RegExp&&w(Ce.tagNameCheck,r)||Ce.tagNameCheck instanceof Function&&Ce.tagNameCheck(r))))return!1}else if(Ke[t]);else if(w(be,g(r,ve,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==v(r,"data:")||!Ge[e])if(Ae&&!w(ge,g(r,ve,"")));else if(r)return!1;return!0},Ct=function(e){return"annotation-xml"!==e&&m(e,ye)},St=function(e){xt(ue.beforeSanitizeAttributes,e,null);const{attributes:t}=e;if(!t||bt(e))return;const r={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:_e,forceKeepAttr:void 0};let i=t.length;for(;i--;){const o=t[i],{name:a,namespaceURI:s,value:l}=o,c=lt(a);let d="value"===a?l:y(l);if(r.attrName=c,r.attrValue=d,r.keepAttr=!0,r.forceKeepAttr=void 0,xt(ue.uponSanitizeAttribute,e,r),d=r.attrValue,!je||"id"!==c&&"name"!==c||(gt(a,e),d="user-content-"+d),Ee&&w(/((--!?|])>)|<\/(style|title)/i,d)){gt(a,e);continue}if(r.forceKeepAttr)continue;if(gt(a,e),!r.keepAttr)continue;if(!Oe&&w(/\/>/i,d)){gt(a,e);continue}Ne&&u([fe,de,pe],e=>{d=g(d,e," ")});const p=lt(e.nodeName);if(kt(p,c,d)){if(ne&&"object"==typeof H&&"function"==typeof H.getAttributeType)if(s);else switch(H.getAttributeType(p,c)){case"TrustedHTML":d=ne.createHTML(d);break;case"TrustedScriptURL":d=ne.createScriptURL(d)}try{s?e.setAttributeNS(s,a,d):e.setAttribute(a,d),bt(e)?mt(e):f(n.removed)}catch(e){}}}xt(ue.afterSanitizeAttributes,e,null)},Tt=function e(t){let r=null;const n=yt(t);for(xt(ue.beforeSanitizeShadowDOM,t,null);r=n.nextNode();)xt(ue.uponSanitizeShadowNode,r,null),_t(r),St(r),r.content instanceof c&&e(r.content);xt(ue.afterSanitizeShadowDOM,t,null)};return n.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=null,i=null,o=null,s=null;if((et=!e)&&(e="\x3c!--\x3e"),"string"!=typeof e&&!wt(e)){if("function"!=typeof e.toString)throw x("toString is not a function");if("string"!=typeof(e=e.toString()))throw x("dirty is not a string, aborting")}if(!n.isSupported)return e;if(Ie||dt(t),n.removed=[],"string"==typeof e&&(qe=!1),qe){if(e.nodeName){const t=lt(e.nodeName);if(!we[t]||Se[t])throw x("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof k)(i=(r=vt("\x3c!----\x3e")).ownerDocument.importNode(e,!0)).nodeType===Y.element&&"BODY"===i.nodeName?r=i:"HTML"===i.nodeName?r=i:r.appendChild(i);else{if(!ze&&!Ne&&!De&&-1===e.indexOf("<"))return ne&&$e?ne.createHTML(e):e;if(!(r=vt(e)))return ze?null:$e?ie:""}r&&Pe&&mt(r.firstChild);const l=yt(qe?e:r);for(;o=l.nextNode();)_t(o),St(o),o.content instanceof c&&Tt(o.content);if(qe)return e;if(ze){if(Re)for(s=se.call(r.ownerDocument);r.firstChild;)s.appendChild(r.firstChild);else s=r;return(_e.shadowroot||_e.shadowrootmode)&&(s=ce.call(a,s,!0)),s}let f=De?r.outerHTML:r.innerHTML;return De&&we["!doctype"]&&r.ownerDocument&&r.ownerDocument.doctype&&r.ownerDocument.doctype.name&&w(V,r.ownerDocument.doctype.name)&&(f="<!DOCTYPE "+r.ownerDocument.doctype.name+">\n"+f),Ne&&u([fe,de,pe],e=>{f=g(f,e," ")}),ne&&$e?ne.createHTML(f):f},n.setConfig=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};dt(e),Ie=!0},n.clearConfig=function(){ct=null,Ie=!1},n.isValidAttribute=function(e,t,r){ct||dt({});const n=lt(e),i=lt(t);return kt(n,i,r)},n.addHook=function(e,t){"function"==typeof t&&d(ue[e],t)},n.removeHook=function(e){return f(ue[e])},n.removeHooks=function(e){ue[e]=[]},n.removeAllHooks=function(){ue=Q()},n}()},e.exports=n()},SLDG:function(e,t,r){"use strict";e.exports=function(e){return"object"==typeof e&&!0===e.isAxiosError}},TNV1:function(e,t,r){"use strict";var n=r("cGG2");e.exports=function(e,t,r){return n.forEach(r,function(r){e=r(e,t)}),e}},"VU/8":function(e,t){e.exports=function(e,t,r,n,i,o){var a,s=e=e||{},l=typeof e.default;"object"!==l&&"function"!==l||(a=e,s=e.default);var c,u="function"==typeof s?s.options:s;if(t&&(u.render=t.render,u.staticRenderFns=t.staticRenderFns,u._compiled=!0),r&&(u.functional=!0),i&&(u._scopeId=i),o?(c=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),n&&n.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(o)},u._ssrRegister=c):n&&(c=n),c){var f=u.functional,d=f?u.render:u.beforeCreate;f?(u._injectStyles=c,u.render=function(e,t){return c.call(t),d(e,t)}):u.beforeCreate=d?[].concat(d,c):[c]}return{esModule:a,exports:s,options:u}}},W2nU:function(e,t){var r,n,i=e.exports={};function o(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function s(e){if(r===setTimeout)return setTimeout(e,0);if((r===o||!r)&&setTimeout)return r=setTimeout,setTimeout(e,0);try{return r(e,0)}catch(t){try{return r.call(null,e,0)}catch(t){return r.call(this,e,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:o}catch(e){r=o}try{n="function"==typeof clearTimeout?clearTimeout:a}catch(e){n=a}}();var l,c=[],u=!1,f=-1;function d(){u&&l&&(u=!1,l.length?c=l.concat(c):f=-1,c.length&&p())}function p(){if(!u){var e=s(d);u=!0;for(var t=c.length;t;){for(l=c,c=[];++f<t;)l&&l[f].run();f=-1,t=c.length}l=null,u=!1,function(e){if(n===clearTimeout)return clearTimeout(e);if((n===a||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(e);try{n(e)}catch(t){try{return n.call(null,e)}catch(t){return n.call(this,e)}}}(e)}}function h(e,t){this.fun=e,this.array=t}function m(){}i.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];c.push(new h(e,t)),1!==c.length||u||s(p)},h.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=m,i.addListener=m,i.once=m,i.off=m,i.removeListener=m,i.removeAllListeners=m,i.emit=m,i.prependListener=m,i.prependOnceListener=m,i.listeners=function(e){return[]},i.binding=function(e){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},XmWM:function(e,t,r){"use strict";var n=r("cGG2"),i=r("DQCr"),o=r("fuGk"),a=r("xLtR"),s=r("DUeU");function l(e){this.defaults=e,this.interceptors={request:new o,response:new o}}l.prototype.request=function(e){"string"==typeof e?(e=arguments[1]||{}).url=arguments[0]:e=e||{},(e=s(this.defaults,e)).method?e.method=e.method.toLowerCase():this.defaults.method?e.method=this.defaults.method.toLowerCase():e.method="get";var t=[a,void 0],r=Promise.resolve(e);for(this.interceptors.request.forEach(function(e){t.unshift(e.fulfilled,e.rejected)}),this.interceptors.response.forEach(function(e){t.push(e.fulfilled,e.rejected)});t.length;)r=r.then(t.shift(),t.shift());return r},l.prototype.getUri=function(e){return e=s(this.defaults,e),i(e.url,e.params,e.paramsSerializer).replace(/^\?/,"")},n.forEach(["delete","get","head","options"],function(e){l.prototype[e]=function(t,r){return this.request(s(r||{},{method:e,url:t,data:(r||{}).data}))}}),n.forEach(["post","put","patch"],function(e){l.prototype[e]=function(t,r,n){return this.request(s(n||{},{method:e,url:t,data:r}))}}),e.exports=l},Xyju:function(e,t,r){var n=r("VU/8")(r("Iynj"),r("D2qy"),!1,null,null,null);e.exports=n.exports},Zn2a:function(e,t,r){(e.exports=r("FZ+f")(!1)).push([e.i,'.CodeMirror{font-family:monospace;height:300px;color:#000;direction:ltr}.CodeMirror-lines{padding:4px 0}.CodeMirror pre{padding:0 4px}.CodeMirror-gutter-filler,.CodeMirror-scrollbar-filler{background-color:#fff}.CodeMirror-gutters{border-right:1px solid #ddd;background-color:#f7f7f7;white-space:nowrap}.CodeMirror-linenumber{padding:0 3px 0 5px;min-width:20px;text-align:right;color:#999;white-space:nowrap}.CodeMirror-guttermarker{color:#000}.CodeMirror-guttermarker-subtle{color:#999}.CodeMirror-cursor{border-left:1px solid #000;border-right:none;width:0}.CodeMirror div.CodeMirror-secondarycursor{border-left:1px solid silver}.cm-fat-cursor .CodeMirror-cursor{width:auto;border:0!important;background:#7e7}.cm-fat-cursor div.CodeMirror-cursors{z-index:1}.cm-fat-cursor-mark{background-color:rgba(20,255,20,.5)}.cm-animate-fat-cursor,.cm-fat-cursor-mark{-webkit-animation:blink 1.06s steps(1) infinite;animation:blink 1.06s steps(1) infinite}.cm-animate-fat-cursor{width:auto;border:0;background-color:#7e7}@-webkit-keyframes blink{50%{background-color:transparent}}@keyframes blink{50%{background-color:transparent}}.cm-tab{display:inline-block;text-decoration:inherit}.CodeMirror-rulers{position:absolute;left:0;right:0;top:-50px;bottom:-20px;overflow:hidden}.CodeMirror-ruler{border-left:1px solid #ccc;top:0;bottom:0;position:absolute}.cm-s-default .cm-header{color:blue}.cm-s-default .cm-quote{color:#090}.cm-negative{color:#d44}.cm-positive{color:#292}.cm-header,.cm-strong{font-weight:700}.cm-em{font-style:italic}.cm-link{text-decoration:underline}.cm-strikethrough{text-decoration:line-through}.cm-s-default .cm-keyword{color:#708}.cm-s-default .cm-atom{color:#219}.cm-s-default .cm-number{color:#164}.cm-s-default .cm-def{color:#00f}.cm-s-default .cm-variable-2{color:#05a}.cm-s-default .cm-type,.cm-s-default .cm-variable-3{color:#085}.cm-s-default .cm-comment{color:#a50}.cm-s-default .cm-string{color:#a11}.cm-s-default .cm-string-2{color:#f50}.cm-s-default .cm-meta,.cm-s-default .cm-qualifier{color:#555}.cm-s-default .cm-builtin{color:#30a}.cm-s-default .cm-bracket{color:#997}.cm-s-default .cm-tag{color:#170}.cm-s-default .cm-attribute{color:#00c}.cm-s-default .cm-hr{color:#999}.cm-s-default .cm-link{color:#00c}.cm-invalidchar,.cm-s-default .cm-error{color:red}.CodeMirror-composing{border-bottom:2px solid}div.CodeMirror span.CodeMirror-matchingbracket{color:#0b0}div.CodeMirror span.CodeMirror-nonmatchingbracket{color:#a22}.CodeMirror-matchingtag{background:rgba(255,150,0,.3)}.CodeMirror-activeline-background{background:#e8f2ff}.CodeMirror{position:relative;overflow:hidden;background:#fff}.CodeMirror-scroll{overflow:scroll!important;margin-bottom:-30px;margin-right:-30px;padding-bottom:30px;height:100%;outline:none;position:relative}.CodeMirror-sizer{position:relative;border-right:30px solid transparent}.CodeMirror-gutter-filler,.CodeMirror-hscrollbar,.CodeMirror-scrollbar-filler,.CodeMirror-vscrollbar{position:absolute;z-index:6;display:none}.CodeMirror-vscrollbar{right:0;top:0;overflow-x:hidden;overflow-y:scroll}.CodeMirror-hscrollbar{bottom:0;left:0;overflow-y:hidden;overflow-x:scroll}.CodeMirror-scrollbar-filler{right:0;bottom:0}.CodeMirror-gutter-filler{left:0;bottom:0}.CodeMirror-gutters{position:absolute;left:0;top:0;min-height:100%;z-index:3}.CodeMirror-gutter{white-space:normal;height:100%;display:inline-block;vertical-align:top;margin-bottom:-30px}.CodeMirror-gutter-wrapper{position:absolute;z-index:4;background:none!important;border:none!important}.CodeMirror-gutter-background{position:absolute;top:0;bottom:0;z-index:4}.CodeMirror-gutter-elt{position:absolute;cursor:default;z-index:4}.CodeMirror-gutter-wrapper ::selection{background-color:transparent}.CodeMirror-gutter-wrapper ::-moz-selection{background-color:transparent}.CodeMirror-lines{cursor:text;min-height:1px}.CodeMirror pre{border-radius:0;border-width:0;background:transparent;font-family:inherit;font-size:inherit;margin:0;white-space:pre;word-wrap:normal;line-height:inherit;color:inherit;z-index:2;position:relative;overflow:visible;-webkit-tap-highlight-color:transparent;-webkit-font-variant-ligatures:contextual;font-variant-ligatures:contextual}.CodeMirror-wrap pre{word-wrap:break-word;white-space:pre-wrap;word-break:normal}.CodeMirror-linebackground{position:absolute;left:0;right:0;top:0;bottom:0;z-index:0}.CodeMirror-linewidget{position:relative;z-index:2;padding:.1px}.CodeMirror-rtl pre{direction:rtl}.CodeMirror-code{outline:none}.CodeMirror-gutter,.CodeMirror-gutters,.CodeMirror-linenumber,.CodeMirror-scroll,.CodeMirror-sizer{-webkit-box-sizing:content-box;box-sizing:content-box}.CodeMirror-measure{position:absolute;width:100%;height:0;overflow:hidden;visibility:hidden}.CodeMirror-cursor{position:absolute;pointer-events:none}.CodeMirror-measure pre{position:static}div.CodeMirror-cursors{visibility:hidden;position:relative;z-index:3}.CodeMirror-focused div.CodeMirror-cursors,div.CodeMirror-dragcursors{visibility:visible}.CodeMirror-selected{background:#d9d9d9}.CodeMirror-focused .CodeMirror-selected{background:#d7d4f0}.CodeMirror-crosshair{cursor:crosshair}.CodeMirror-line::selection,.CodeMirror-line>span::selection,.CodeMirror-line>span>span::selection{background:#d7d4f0}.CodeMirror-line::-moz-selection,.CodeMirror-line>span::-moz-selection,.CodeMirror-line>span>span::-moz-selection{background:#d7d4f0}.cm-searching{background-color:#ffa;background-color:rgba(255,255,0,.4)}.cm-force-border{padding-right:.1px}@media print{.CodeMirror div.CodeMirror-cursors{visibility:hidden}}.cm-tab-wrap-hack:after{content:""}span.CodeMirror-selectedtext{background:none}',""])},aIlf:function(e,t,r){"use strict";(function(t,r){var n=Object.freeze({});function i(e){return null==e}function o(e){return null!=e}function a(e){return!0===e}function s(e){return"string"==typeof e||"number"==typeof e||"symbol"==typeof e||"boolean"==typeof e}function l(e){return null!==e&&"object"==typeof e}var c=Object.prototype.toString;function u(e){return"[object Object]"===c.call(e)}function f(e){var t=parseFloat(String(e));return t>=0&&Math.floor(t)===t&&isFinite(e)}function d(e){return o(e)&&"function"==typeof e.then&&"function"==typeof e.catch}function p(e){return null==e?"":Array.isArray(e)||u(e)&&e.toString===c?JSON.stringify(e,null,2):String(e)}function h(e){var t=parseFloat(e);return isNaN(t)?e:t}function m(e,t){for(var r=Object.create(null),n=e.split(","),i=0;i<n.length;i++)r[n[i]]=!0;return t?function(e){return r[e.toLowerCase()]}:function(e){return r[e]}}var g=m("slot,component",!0),v=m("key,ref,slot,slot-scope,is");function y(e,t){if(e.length){var r=e.indexOf(t);if(r>-1)return e.splice(r,1)}}var b=Object.prototype.hasOwnProperty;function w(e,t){return b.call(e,t)}function x(e){var t=Object.create(null);return function(r){return t[r]||(t[r]=e(r))}}var _=/-(\w)/g,k=x(function(e){return e.replace(_,function(e,t){return t?t.toUpperCase():""})}),C=x(function(e){return e.charAt(0).toUpperCase()+e.slice(1)}),S=/\B([A-Z])/g,T=x(function(e){return e.replace(S,"-$1").toLowerCase()}),M=Function.prototype.bind?function(e,t){return e.bind(t)}:function(e,t){function r(r){var n=arguments.length;return n?n>1?e.apply(t,arguments):e.call(t,r):e.call(t)}return r._length=e.length,r};function L(e,t){t=t||0;for(var r=e.length-t,n=new Array(r);r--;)n[r]=e[r+t];return n}function A(e,t){for(var r in t)e[r]=t[r];return e}function O(e){for(var t={},r=0;r<e.length;r++)e[r]&&A(t,e[r]);return t}function N(e,t,r){}var E=function(e,t,r){return!1},D=function(e){return e};function I(e,t){if(e===t)return!0;var r=l(e),n=l(t);if(!r||!n)return!r&&!n&&String(e)===String(t);try{var i=Array.isArray(e),o=Array.isArray(t);if(i&&o)return e.length===t.length&&e.every(function(e,r){return I(e,t[r])});if(e instanceof Date&&t instanceof Date)return e.getTime()===t.getTime();if(i||o)return!1;var a=Object.keys(e),s=Object.keys(t);return a.length===s.length&&a.every(function(r){return I(e[r],t[r])})}catch(e){return!1}}function P(e,t){for(var r=0;r<e.length;r++)if(I(e[r],t))return r;return-1}function z(e){var t=!1;return function(){t||(t=!0,e.apply(this,arguments))}}var R="data-server-rendered",$=["component","directive","filter"],F=["beforeCreate","created","beforeMount","mounted","beforeUpdate","updated","beforeDestroy","destroyed","activated","deactivated","errorCaptured","serverPrefetch"],j={optionMergeStrategies:Object.create(null),silent:!1,productionTip:!1,devtools:!1,performance:!1,errorHandler:null,warnHandler:null,ignoredElements:[],keyCodes:Object.create(null),isReservedTag:E,isReservedAttr:E,isUnknownElement:E,getTagNamespace:N,parsePlatformTagName:D,mustUseProp:E,async:!0,_lifecycleHooks:F},W=/a-zA-Z\u00B7\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u037D\u037F-\u1FFF\u200C-\u200D\u203F-\u2040\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD/;function q(e,t,r,n){Object.defineProperty(e,t,{value:r,enumerable:!!n,writable:!0,configurable:!0})}var H,U=new RegExp("[^"+W.source+".$_\\d]"),B="__proto__"in{},G="undefined"!=typeof window,V="undefined"!=typeof WXEnvironment&&!!WXEnvironment.platform,K=V&&WXEnvironment.platform.toLowerCase(),X=G&&window.navigator.userAgent.toLowerCase(),Y=X&&/msie|trident/.test(X),Z=X&&X.indexOf("msie 9.0")>0,J=X&&X.indexOf("edge/")>0,Q=(X&&X.indexOf("android"),X&&/iphone|ipad|ipod|ios/.test(X)||"ios"===K),ee=(X&&/chrome\/\d+/.test(X),X&&/phantomjs/.test(X),X&&X.match(/firefox\/(\d+)/)),te={}.watch,re=!1;if(G)try{var ne={};Object.defineProperty(ne,"passive",{get:function(){re=!0}}),window.addEventListener("test-passive",null,ne)}catch(n){}var ie=function(){return void 0===H&&(H=!G&&!V&&void 0!==t&&t.process&&"server"===t.process.env.VUE_ENV),H},oe=G&&window.__VUE_DEVTOOLS_GLOBAL_HOOK__;function ae(e){return"function"==typeof e&&/native code/.test(e.toString())}var se,le="undefined"!=typeof Symbol&&ae(Symbol)&&"undefined"!=typeof Reflect&&ae(Reflect.ownKeys);se="undefined"!=typeof Set&&ae(Set)?Set:function(){function e(){this.set=Object.create(null)}return e.prototype.has=function(e){return!0===this.set[e]},e.prototype.add=function(e){this.set[e]=!0},e.prototype.clear=function(){this.set=Object.create(null)},e}();var ce=N,ue=0,fe=function(){this.id=ue++,this.subs=[]};fe.prototype.addSub=function(e){this.subs.push(e)},fe.prototype.removeSub=function(e){y(this.subs,e)},fe.prototype.depend=function(){fe.target&&fe.target.addDep(this)},fe.prototype.notify=function(){for(var e=this.subs.slice(),t=0,r=e.length;t<r;t++)e[t].update()},fe.target=null;var de=[];function pe(e){de.push(e),fe.target=e}function he(){de.pop(),fe.target=de[de.length-1]}var me=function(e,t,r,n,i,o,a,s){this.tag=e,this.data=t,this.children=r,this.text=n,this.elm=i,this.ns=void 0,this.context=o,this.fnContext=void 0,this.fnOptions=void 0,this.fnScopeId=void 0,this.key=t&&t.key,this.componentOptions=a,this.componentInstance=void 0,this.parent=void 0,this.raw=!1,this.isStatic=!1,this.isRootInsert=!0,this.isComment=!1,this.isCloned=!1,this.isOnce=!1,this.asyncFactory=s,this.asyncMeta=void 0,this.isAsyncPlaceholder=!1},ge={child:{configurable:!0}};ge.child.get=function(){return this.componentInstance},Object.defineProperties(me.prototype,ge);var ve=function(e){void 0===e&&(e="");var t=new me;return t.text=e,t.isComment=!0,t};function ye(e){return new me(void 0,void 0,void 0,String(e))}function be(e){var t=new me(e.tag,e.data,e.children&&e.children.slice(),e.text,e.elm,e.context,e.componentOptions,e.asyncFactory);return t.ns=e.ns,t.isStatic=e.isStatic,t.key=e.key,t.isComment=e.isComment,t.fnContext=e.fnContext,t.fnOptions=e.fnOptions,t.fnScopeId=e.fnScopeId,t.asyncMeta=e.asyncMeta,t.isCloned=!0,t}var we=Array.prototype,xe=Object.create(we);["push","pop","shift","unshift","splice","sort","reverse"].forEach(function(e){var t=we[e];q(xe,e,function(){for(var r=[],n=arguments.length;n--;)r[n]=arguments[n];var i,o=t.apply(this,r),a=this.__ob__;switch(e){case"push":case"unshift":i=r;break;case"splice":i=r.slice(2)}return i&&a.observeArray(i),a.dep.notify(),o})});var _e=Object.getOwnPropertyNames(xe),ke=!0;function Ce(e){ke=e}var Se=function(e){var t;this.value=e,this.dep=new fe,this.vmCount=0,q(e,"__ob__",this),Array.isArray(e)?(B?(t=xe,e.__proto__=t):function(e,t,r){for(var n=0,i=r.length;n<i;n++){var o=r[n];q(e,o,t[o])}}(e,xe,_e),this.observeArray(e)):this.walk(e)};function Te(e,t){var r;if(l(e)&&!(e instanceof me))return w(e,"__ob__")&&e.__ob__ instanceof Se?r=e.__ob__:ke&&!ie()&&(Array.isArray(e)||u(e))&&Object.isExtensible(e)&&!e._isVue&&(r=new Se(e)),t&&r&&r.vmCount++,r}function Me(e,t,r,n,i){var o=new fe,a=Object.getOwnPropertyDescriptor(e,t);if(!a||!1!==a.configurable){var s=a&&a.get,l=a&&a.set;s&&!l||2!==arguments.length||(r=e[t]);var c=!i&&Te(r);Object.defineProperty(e,t,{enumerable:!0,configurable:!0,get:function(){var t=s?s.call(e):r;return fe.target&&(o.depend(),c&&(c.dep.depend(),Array.isArray(t)&&function e(t){for(var r=void 0,n=0,i=t.length;n<i;n++)(r=t[n])&&r.__ob__&&r.__ob__.dep.depend(),Array.isArray(r)&&e(r)}(t))),t},set:function(t){var n=s?s.call(e):r;t===n||t!=t&&n!=n||s&&!l||(l?l.call(e,t):r=t,c=!i&&Te(t),o.notify())}})}}function Le(e,t,r){if(Array.isArray(e)&&f(t))return e.length=Math.max(e.length,t),e.splice(t,1,r),r;if(t in e&&!(t in Object.prototype))return e[t]=r,r;var n=e.__ob__;return e._isVue||n&&n.vmCount?r:n?(Me(n.value,t,r),n.dep.notify(),r):(e[t]=r,r)}function Ae(e,t){if(Array.isArray(e)&&f(t))e.splice(t,1);else{var r=e.__ob__;e._isVue||r&&r.vmCount||w(e,t)&&(delete e[t],r&&r.dep.notify())}}Se.prototype.walk=function(e){for(var t=Object.keys(e),r=0;r<t.length;r++)Me(e,t[r])},Se.prototype.observeArray=function(e){for(var t=0,r=e.length;t<r;t++)Te(e[t])};var Oe=j.optionMergeStrategies;function Ne(e,t){if(!t)return e;for(var r,n,i,o=le?Reflect.ownKeys(t):Object.keys(t),a=0;a<o.length;a++)"__ob__"!==(r=o[a])&&(n=e[r],i=t[r],w(e,r)?n!==i&&u(n)&&u(i)&&Ne(n,i):Le(e,r,i));return e}function Ee(e,t,r){return r?function(){var n="function"==typeof t?t.call(r,r):t,i="function"==typeof e?e.call(r,r):e;return n?Ne(n,i):i}:t?e?function(){return Ne("function"==typeof t?t.call(this,this):t,"function"==typeof e?e.call(this,this):e)}:t:e}function De(e,t){var r=t?e?e.concat(t):Array.isArray(t)?t:[t]:e;return r?function(e){for(var t=[],r=0;r<e.length;r++)-1===t.indexOf(e[r])&&t.push(e[r]);return t}(r):r}function Ie(e,t,r,n){var i=Object.create(e||null);return t?A(i,t):i}Oe.data=function(e,t,r){return r?Ee(e,t,r):t&&"function"!=typeof t?e:Ee(e,t)},F.forEach(function(e){Oe[e]=De}),$.forEach(function(e){Oe[e+"s"]=Ie}),Oe.watch=function(e,t,r,n){if(e===te&&(e=void 0),t===te&&(t=void 0),!t)return Object.create(e||null);if(!e)return t;var i={};for(var o in A(i,e),t){var a=i[o],s=t[o];a&&!Array.isArray(a)&&(a=[a]),i[o]=a?a.concat(s):Array.isArray(s)?s:[s]}return i},Oe.props=Oe.methods=Oe.inject=Oe.computed=function(e,t,r,n){if(!e)return t;var i=Object.create(null);return A(i,e),t&&A(i,t),i},Oe.provide=Ee;var Pe=function(e,t){return void 0===t?e:t};function ze(e,t,r){if("function"==typeof t&&(t=t.options),function(e,t){var r=e.props;if(r){var n,i,o={};if(Array.isArray(r))for(n=r.length;n--;)"string"==typeof(i=r[n])&&(o[k(i)]={type:null});else if(u(r))for(var a in r)i=r[a],o[k(a)]=u(i)?i:{type:i};e.props=o}}(t),function(e,t){var r=e.inject;if(r){var n=e.inject={};if(Array.isArray(r))for(var i=0;i<r.length;i++)n[r[i]]={from:r[i]};else if(u(r))for(var o in r){var a=r[o];n[o]=u(a)?A({from:o},a):{from:a}}}}(t),function(e){var t=e.directives;if(t)for(var r in t){var n=t[r];"function"==typeof n&&(t[r]={bind:n,update:n})}}(t),!t._base&&(t.extends&&(e=ze(e,t.extends,r)),t.mixins))for(var n=0,i=t.mixins.length;n<i;n++)e=ze(e,t.mixins[n],r);var o,a={};for(o in e)s(o);for(o in t)w(e,o)||s(o);function s(n){var i=Oe[n]||Pe;a[n]=i(e[n],t[n],r,n)}return a}function Re(e,t,r,n){if("string"==typeof r){var i=e[t];if(w(i,r))return i[r];var o=k(r);if(w(i,o))return i[o];var a=C(o);return w(i,a)?i[a]:i[r]||i[o]||i[a]}}function $e(e,t,r,n){var i=t[e],o=!w(r,e),a=r[e],s=We(Boolean,i.type);if(s>-1)if(o&&!w(i,"default"))a=!1;else if(""===a||a===T(e)){var l=We(String,i.type);(l<0||s<l)&&(a=!0)}if(void 0===a){a=function(e,t,r){if(w(t,"default")){var n=t.default;return e&&e.$options.propsData&&void 0===e.$options.propsData[r]&&void 0!==e._props[r]?e._props[r]:"function"==typeof n&&"Function"!==Fe(t.type)?n.call(e):n}}(n,i,e);var c=ke;Ce(!0),Te(a),Ce(c)}return a}function Fe(e){var t=e&&e.toString().match(/^\s*function (\w+)/);return t?t[1]:""}function je(e,t){return Fe(e)===Fe(t)}function We(e,t){if(!Array.isArray(t))return je(t,e)?0:-1;for(var r=0,n=t.length;r<n;r++)if(je(t[r],e))return r;return-1}function qe(e,t,r){pe();try{if(t)for(var n=t;n=n.$parent;){var i=n.$options.errorCaptured;if(i)for(var o=0;o<i.length;o++)try{if(!1===i[o].call(n,e,t,r))return}catch(e){Ue(e,n,"errorCaptured hook")}}Ue(e,t,r)}finally{he()}}function He(e,t,r,n,i){var o;try{(o=r?e.apply(t,r):e.call(t))&&!o._isVue&&d(o)&&!o._handled&&(o.catch(function(e){return qe(e,n,i+" (Promise/async)")}),o._handled=!0)}catch(e){qe(e,n,i)}return o}function Ue(e,t,r){if(j.errorHandler)try{return j.errorHandler.call(null,e,t,r)}catch(t){t!==e&&Be(t,null,"config.errorHandler")}Be(e,t,r)}function Be(e,t,r){if(!G&&!V||"undefined"==typeof console)throw e;console.error(e)}var Ge,Ve=!1,Ke=[],Xe=!1;function Ye(){Xe=!1;var e=Ke.slice(0);Ke.length=0;for(var t=0;t<e.length;t++)e[t]()}if("undefined"!=typeof Promise&&ae(Promise)){var Ze=Promise.resolve();Ge=function(){Ze.then(Ye),Q&&setTimeout(N)},Ve=!0}else if(Y||"undefined"==typeof MutationObserver||!ae(MutationObserver)&&"[object MutationObserverConstructor]"!==MutationObserver.toString())Ge=void 0!==r&&ae(r)?function(){r(Ye)}:function(){setTimeout(Ye,0)};else{var Je=1,Qe=new MutationObserver(Ye),et=document.createTextNode(String(Je));Qe.observe(et,{characterData:!0}),Ge=function(){Je=(Je+1)%2,et.data=String(Je)},Ve=!0}function tt(e,t){var r;if(Ke.push(function(){if(e)try{e.call(t)}catch(e){qe(e,t,"nextTick")}else r&&r(t)}),Xe||(Xe=!0,Ge()),!e&&"undefined"!=typeof Promise)return new Promise(function(e){r=e})}var rt=new se;function nt(e){!function e(t,r){var n,i,o=Array.isArray(t);if(!(!o&&!l(t)||Object.isFrozen(t)||t instanceof me)){if(t.__ob__){var a=t.__ob__.dep.id;if(r.has(a))return;r.add(a)}if(o)for(n=t.length;n--;)e(t[n],r);else for(n=(i=Object.keys(t)).length;n--;)e(t[i[n]],r)}}(e,rt),rt.clear()}var it=x(function(e){var t="&"===e.charAt(0),r="~"===(e=t?e.slice(1):e).charAt(0),n="!"===(e=r?e.slice(1):e).charAt(0);return{name:e=n?e.slice(1):e,once:r,capture:n,passive:t}});function ot(e,t){function r(){var e=arguments,n=r.fns;if(!Array.isArray(n))return He(n,null,arguments,t,"v-on handler");for(var i=n.slice(),o=0;o<i.length;o++)He(i[o],null,e,t,"v-on handler")}return r.fns=e,r}function at(e,t,r,n,o,s){var l,c,u,f;for(l in e)c=e[l],u=t[l],f=it(l),i(c)||(i(u)?(i(c.fns)&&(c=e[l]=ot(c,s)),a(f.once)&&(c=e[l]=o(f.name,c,f.capture)),r(f.name,c,f.capture,f.passive,f.params)):c!==u&&(u.fns=c,e[l]=u));for(l in t)i(e[l])&&n((f=it(l)).name,t[l],f.capture)}function st(e,t,r){var n;e instanceof me&&(e=e.data.hook||(e.data.hook={}));var s=e[t];function l(){r.apply(this,arguments),y(n.fns,l)}i(s)?n=ot([l]):o(s.fns)&&a(s.merged)?(n=s).fns.push(l):n=ot([s,l]),n.merged=!0,e[t]=n}function lt(e,t,r,n,i){if(o(t)){if(w(t,r))return e[r]=t[r],i||delete t[r],!0;if(w(t,n))return e[r]=t[n],i||delete t[n],!0}return!1}function ct(e){return s(e)?[ye(e)]:Array.isArray(e)?function e(t,r){var n,l,c,u,f=[];for(n=0;n<t.length;n++)i(l=t[n])||"boolean"==typeof l||(u=f[c=f.length-1],Array.isArray(l)?l.length>0&&(ut((l=e(l,(r||"")+"_"+n))[0])&&ut(u)&&(f[c]=ye(u.text+l[0].text),l.shift()),f.push.apply(f,l)):s(l)?ut(u)?f[c]=ye(u.text+l):""!==l&&f.push(ye(l)):ut(l)&&ut(u)?f[c]=ye(u.text+l.text):(a(t._isVList)&&o(l.tag)&&i(l.key)&&o(r)&&(l.key="__vlist"+r+"_"+n+"__"),f.push(l)));return f}(e):void 0}function ut(e){return o(e)&&o(e.text)&&!1===e.isComment}function ft(e,t){if(e){for(var r=Object.create(null),n=le?Reflect.ownKeys(e):Object.keys(e),i=0;i<n.length;i++){var o=n[i];if("__ob__"!==o){for(var a=e[o].from,s=t;s;){if(s._provided&&w(s._provided,a)){r[o]=s._provided[a];break}s=s.$parent}if(!s&&"default"in e[o]){var l=e[o].default;r[o]="function"==typeof l?l.call(t):l}}}return r}}function dt(e,t){if(!e||!e.length)return{};for(var r={},n=0,i=e.length;n<i;n++){var o=e[n],a=o.data;if(a&&a.attrs&&a.attrs.slot&&delete a.attrs.slot,o.context!==t&&o.fnContext!==t||!a||null==a.slot)(r.default||(r.default=[])).push(o);else{var s=a.slot,l=r[s]||(r[s]=[]);"template"===o.tag?l.push.apply(l,o.children||[]):l.push(o)}}for(var c in r)r[c].every(pt)&&delete r[c];return r}function pt(e){return e.isComment&&!e.asyncFactory||" "===e.text}function ht(e,t,r){var i,o=Object.keys(t).length>0,a=e?!!e.$stable:!o,s=e&&e.$key;if(e){if(e._normalized)return e._normalized;if(a&&r&&r!==n&&s===r.$key&&!o&&!r.$hasNormal)return r;for(var l in i={},e)e[l]&&"$"!==l[0]&&(i[l]=mt(t,l,e[l]))}else i={};for(var c in t)c in i||(i[c]=gt(t,c));return e&&Object.isExtensible(e)&&(e._normalized=i),q(i,"$stable",a),q(i,"$key",s),q(i,"$hasNormal",o),i}function mt(e,t,r){var n=function(){var e=arguments.length?r.apply(null,arguments):r({});return(e=e&&"object"==typeof e&&!Array.isArray(e)?[e]:ct(e))&&(0===e.length||1===e.length&&e[0].isComment)?void 0:e};return r.proxy&&Object.defineProperty(e,t,{get:n,enumerable:!0,configurable:!0}),n}function gt(e,t){return function(){return e[t]}}function vt(e,t){var r,n,i,a,s;if(Array.isArray(e)||"string"==typeof e)for(r=new Array(e.length),n=0,i=e.length;n<i;n++)r[n]=t(e[n],n);else if("number"==typeof e)for(r=new Array(e),n=0;n<e;n++)r[n]=t(n+1,n);else if(l(e))if(le&&e[Symbol.iterator]){r=[];for(var c=e[Symbol.iterator](),u=c.next();!u.done;)r.push(t(u.value,r.length)),u=c.next()}else for(a=Object.keys(e),r=new Array(a.length),n=0,i=a.length;n<i;n++)s=a[n],r[n]=t(e[s],s,n);return o(r)||(r=[]),r._isVList=!0,r}function yt(e,t,r,n){var i,o=this.$scopedSlots[e];o?(r=r||{},n&&(r=A(A({},n),r)),i=o(r)||t):i=this.$slots[e]||t;var a=r&&r.slot;return a?this.$createElement("template",{slot:a},i):i}function bt(e){return Re(this.$options,"filters",e)||D}function wt(e,t){return Array.isArray(e)?-1===e.indexOf(t):e!==t}function xt(e,t,r,n,i){var o=j.keyCodes[t]||r;return i&&n&&!j.keyCodes[t]?wt(i,n):o?wt(o,e):n?T(n)!==t:void 0}function _t(e,t,r,n,i){if(r&&l(r)){var o;Array.isArray(r)&&(r=O(r));var a=function(a){if("class"===a||"style"===a||v(a))o=e;else{var s=e.attrs&&e.attrs.type;o=n||j.mustUseProp(t,s,a)?e.domProps||(e.domProps={}):e.attrs||(e.attrs={})}var l=k(a),c=T(a);l in o||c in o||(o[a]=r[a],i&&((e.on||(e.on={}))["update:"+a]=function(e){r[a]=e}))};for(var s in r)a(s)}return e}function kt(e,t){var r=this._staticTrees||(this._staticTrees=[]),n=r[e];return n&&!t?n:(St(n=r[e]=this.$options.staticRenderFns[e].call(this._renderProxy,null,this),"__static__"+e,!1),n)}function Ct(e,t,r){return St(e,"__once__"+t+(r?"_"+r:""),!0),e}function St(e,t,r){if(Array.isArray(e))for(var n=0;n<e.length;n++)e[n]&&"string"!=typeof e[n]&&Tt(e[n],t+"_"+n,r);else Tt(e,t,r)}function Tt(e,t,r){e.isStatic=!0,e.key=t,e.isOnce=r}function Mt(e,t){if(t&&u(t)){var r=e.on=e.on?A({},e.on):{};for(var n in t){var i=r[n],o=t[n];r[n]=i?[].concat(i,o):o}}return e}function Lt(e,t,r,n){t=t||{$stable:!r};for(var i=0;i<e.length;i++){var o=e[i];Array.isArray(o)?Lt(o,t,r):o&&(o.proxy&&(o.fn.proxy=!0),t[o.key]=o.fn)}return n&&(t.$key=n),t}function At(e,t){for(var r=0;r<t.length;r+=2){var n=t[r];"string"==typeof n&&n&&(e[t[r]]=t[r+1])}return e}function Ot(e,t){return"string"==typeof e?t+e:e}function Nt(e){e._o=Ct,e._n=h,e._s=p,e._l=vt,e._t=yt,e._q=I,e._i=P,e._m=kt,e._f=bt,e._k=xt,e._b=_t,e._v=ye,e._e=ve,e._u=Lt,e._g=Mt,e._d=At,e._p=Ot}function Et(e,t,r,i,o){var s,l=this,c=o.options;w(i,"_uid")?(s=Object.create(i))._original=i:(s=i,i=i._original);var u=a(c._compiled),f=!u;this.data=e,this.props=t,this.children=r,this.parent=i,this.listeners=e.on||n,this.injections=ft(c.inject,i),this.slots=function(){return l.$slots||ht(e.scopedSlots,l.$slots=dt(r,i)),l.$slots},Object.defineProperty(this,"scopedSlots",{enumerable:!0,get:function(){return ht(e.scopedSlots,this.slots())}}),u&&(this.$options=c,this.$slots=this.slots(),this.$scopedSlots=ht(e.scopedSlots,this.$slots)),c._scopeId?this._c=function(e,t,r,n){var o=Wt(s,e,t,r,n,f);return o&&!Array.isArray(o)&&(o.fnScopeId=c._scopeId,o.fnContext=i),o}:this._c=function(e,t,r,n){return Wt(s,e,t,r,n,f)}}function Dt(e,t,r,n,i){var o=be(e);return o.fnContext=r,o.fnOptions=n,t.slot&&((o.data||(o.data={})).slot=t.slot),o}function It(e,t){for(var r in t)e[k(r)]=t[r]}Nt(Et.prototype);var Pt={init:function(e,t){if(e.componentInstance&&!e.componentInstance._isDestroyed&&e.data.keepAlive){var r=e;Pt.prepatch(r,r)}else(e.componentInstance=function(e,t){var r={_isComponent:!0,_parentVnode:e,parent:Zt},n=e.data.inlineTemplate;return o(n)&&(r.render=n.render,r.staticRenderFns=n.staticRenderFns),new e.componentOptions.Ctor(r)}(e)).$mount(t?e.elm:void 0,t)},prepatch:function(e,t){var r=t.componentOptions;!function(e,t,r,i,o){var a=i.data.scopedSlots,s=e.$scopedSlots,l=!!(a&&!a.$stable||s!==n&&!s.$stable||a&&e.$scopedSlots.$key!==a.$key),c=!!(o||e.$options._renderChildren||l);if(e.$options._parentVnode=i,e.$vnode=i,e._vnode&&(e._vnode.parent=i),e.$options._renderChildren=o,e.$attrs=i.data.attrs||n,e.$listeners=r||n,t&&e.$options.props){Ce(!1);for(var u=e._props,f=e.$options._propKeys||[],d=0;d<f.length;d++){var p=f[d],h=e.$options.props;u[p]=$e(p,h,t,e)}Ce(!0),e.$options.propsData=t}r=r||n;var m=e.$options._parentListeners;e.$options._parentListeners=r,Yt(e,r,m),c&&(e.$slots=dt(o,i.context),e.$forceUpdate())}(t.componentInstance=e.componentInstance,r.propsData,r.listeners,t,r.children)},insert:function(e){var t,r=e.context,n=e.componentInstance;n._isMounted||(n._isMounted=!0,tr(n,"mounted")),e.data.keepAlive&&(r._isMounted?((t=n)._inactive=!1,nr.push(t)):er(n,!0))},destroy:function(e){var t=e.componentInstance;t._isDestroyed||(e.data.keepAlive?function e(t,r){if(!(r&&(t._directInactive=!0,Qt(t))||t._inactive)){t._inactive=!0;for(var n=0;n<t.$children.length;n++)e(t.$children[n]);tr(t,"deactivated")}}(t,!0):t.$destroy())}},zt=Object.keys(Pt);function Rt(e,t,r,s,c){if(!i(e)){var u=r.$options._base;if(l(e)&&(e=u.extend(e)),"function"==typeof e){var f;if(i(e.cid)&&void 0===(e=function(e,t){if(a(e.error)&&o(e.errorComp))return e.errorComp;if(o(e.resolved))return e.resolved;var r=Ht;if(r&&o(e.owners)&&-1===e.owners.indexOf(r)&&e.owners.push(r),a(e.loading)&&o(e.loadingComp))return e.loadingComp;if(r&&!o(e.owners)){var n=e.owners=[r],s=!0,c=null,u=null;r.$on("hook:destroyed",function(){return y(n,r)});var f=function(e){for(var t=0,r=n.length;t<r;t++)n[t].$forceUpdate();e&&(n.length=0,null!==c&&(clearTimeout(c),c=null),null!==u&&(clearTimeout(u),u=null))},p=z(function(r){e.resolved=Ut(r,t),s?n.length=0:f(!0)}),h=z(function(t){o(e.errorComp)&&(e.error=!0,f(!0))}),m=e(p,h);return l(m)&&(d(m)?i(e.resolved)&&m.then(p,h):d(m.component)&&(m.component.then(p,h),o(m.error)&&(e.errorComp=Ut(m.error,t)),o(m.loading)&&(e.loadingComp=Ut(m.loading,t),0===m.delay?e.loading=!0:c=setTimeout(function(){c=null,i(e.resolved)&&i(e.error)&&(e.loading=!0,f(!1))},m.delay||200)),o(m.timeout)&&(u=setTimeout(function(){u=null,i(e.resolved)&&h(null)},m.timeout)))),s=!1,e.loading?e.loadingComp:e.resolved}}(f=e,u)))return function(e,t,r,n,i){var o=ve();return o.asyncFactory=e,o.asyncMeta={data:t,context:r,children:n,tag:i},o}(f,t,r,s,c);t=t||{},_r(e),o(t.model)&&function(e,t){var r=e.model&&e.model.prop||"value",n=e.model&&e.model.event||"input";(t.attrs||(t.attrs={}))[r]=t.model.value;var i=t.on||(t.on={}),a=i[n],s=t.model.callback;o(a)?(Array.isArray(a)?-1===a.indexOf(s):a!==s)&&(i[n]=[s].concat(a)):i[n]=s}(e.options,t);var p=function(e,t,r){var n=t.options.props;if(!i(n)){var a={},s=e.attrs,l=e.props;if(o(s)||o(l))for(var c in n){var u=T(c);lt(a,l,c,u,!0)||lt(a,s,c,u,!1)}return a}}(t,e);if(a(e.options.functional))return function(e,t,r,i,a){var s=e.options,l={},c=s.props;if(o(c))for(var u in c)l[u]=$e(u,c,t||n);else o(r.attrs)&&It(l,r.attrs),o(r.props)&&It(l,r.props);var f=new Et(r,l,a,i,e),d=s.render.call(null,f._c,f);if(d instanceof me)return Dt(d,r,f.parent,s);if(Array.isArray(d)){for(var p=ct(d)||[],h=new Array(p.length),m=0;m<p.length;m++)h[m]=Dt(p[m],r,f.parent,s);return h}}(e,p,t,r,s);var h=t.on;if(t.on=t.nativeOn,a(e.options.abstract)){var m=t.slot;t={},m&&(t.slot=m)}!function(e){for(var t=e.hook||(e.hook={}),r=0;r<zt.length;r++){var n=zt[r],i=t[n],o=Pt[n];i===o||i&&i._merged||(t[n]=i?$t(o,i):o)}}(t);var g=e.options.name||c;return new me("vue-component-"+e.cid+(g?"-"+g:""),t,void 0,void 0,void 0,r,{Ctor:e,propsData:p,listeners:h,tag:c,children:s},f)}}}function $t(e,t){var r=function(r,n){e(r,n),t(r,n)};return r._merged=!0,r}var Ft=1,jt=2;function Wt(e,t,r,n,c,u){return(Array.isArray(r)||s(r))&&(c=n,n=r,r=void 0),a(u)&&(c=jt),function(e,t,r,n,s){if(o(r)&&o(r.__ob__))return ve();if(o(r)&&o(r.is)&&(t=r.is),!t)return ve();var c,u,f;(Array.isArray(n)&&"function"==typeof n[0]&&((r=r||{}).scopedSlots={default:n[0]},n.length=0),s===jt?n=ct(n):s===Ft&&(n=function(e){for(var t=0;t<e.length;t++)if(Array.isArray(e[t]))return Array.prototype.concat.apply([],e);return e}(n)),"string"==typeof t)?(u=e.$vnode&&e.$vnode.ns||j.getTagNamespace(t),c=j.isReservedTag(t)?new me(j.parsePlatformTagName(t),r,n,void 0,void 0,e):r&&r.pre||!o(f=Re(e.$options,"components",t))?new me(t,r,n,void 0,void 0,e):Rt(f,r,e,n,t)):c=Rt(t,r,e,n);return Array.isArray(c)?c:o(c)?(o(u)&&function e(t,r,n){if(t.ns=r,"foreignObject"===t.tag&&(r=void 0,n=!0),o(t.children))for(var s=0,l=t.children.length;s<l;s++){var c=t.children[s];o(c.tag)&&(i(c.ns)||a(n)&&"svg"!==c.tag)&&e(c,r,n)}}(c,u),o(r)&&function(e){l(e.style)&&nt(e.style),l(e.class)&&nt(e.class)}(r),c):ve()}(e,t,r,n,c)}var qt,Ht=null;function Ut(e,t){return(e.__esModule||le&&"Module"===e[Symbol.toStringTag])&&(e=e.default),l(e)?t.extend(e):e}function Bt(e){return e.isComment&&e.asyncFactory}function Gt(e){if(Array.isArray(e))for(var t=0;t<e.length;t++){var r=e[t];if(o(r)&&(o(r.componentOptions)||Bt(r)))return r}}function Vt(e,t){qt.$on(e,t)}function Kt(e,t){qt.$off(e,t)}function Xt(e,t){var r=qt;return function n(){null!==t.apply(null,arguments)&&r.$off(e,n)}}function Yt(e,t,r){qt=e,at(t,r||{},Vt,Kt,Xt,e),qt=void 0}var Zt=null;function Jt(e){var t=Zt;return Zt=e,function(){Zt=t}}function Qt(e){for(;e&&(e=e.$parent);)if(e._inactive)return!0;return!1}function er(e,t){if(t){if(e._directInactive=!1,Qt(e))return}else if(e._directInactive)return;if(e._inactive||null===e._inactive){e._inactive=!1;for(var r=0;r<e.$children.length;r++)er(e.$children[r]);tr(e,"activated")}}function tr(e,t){pe();var r=e.$options[t],n=t+" hook";if(r)for(var i=0,o=r.length;i<o;i++)He(r[i],e,null,e,n);e._hasHookEvent&&e.$emit("hook:"+t),he()}var rr=[],nr=[],ir={},or=!1,ar=!1,sr=0,lr=0,cr=Date.now;if(G&&!Y){var ur=window.performance;ur&&"function"==typeof ur.now&&cr()>document.createEvent("Event").timeStamp&&(cr=function(){return ur.now()})}function fr(){var e,t;for(lr=cr(),ar=!0,rr.sort(function(e,t){return e.id-t.id}),sr=0;sr<rr.length;sr++)(e=rr[sr]).before&&e.before(),t=e.id,ir[t]=null,e.run();var r=nr.slice(),n=rr.slice();sr=rr.length=nr.length=0,ir={},or=ar=!1,function(e){for(var t=0;t<e.length;t++)e[t]._inactive=!0,er(e[t],!0)}(r),function(e){for(var t=e.length;t--;){var r=e[t],n=r.vm;n._watcher===r&&n._isMounted&&!n._isDestroyed&&tr(n,"updated")}}(n),oe&&j.devtools&&oe.emit("flush")}var dr=0,pr=function(e,t,r,n,i){this.vm=e,i&&(e._watcher=this),e._watchers.push(this),n?(this.deep=!!n.deep,this.user=!!n.user,this.lazy=!!n.lazy,this.sync=!!n.sync,this.before=n.before):this.deep=this.user=this.lazy=this.sync=!1,this.cb=r,this.id=++dr,this.active=!0,this.dirty=this.lazy,this.deps=[],this.newDeps=[],this.depIds=new se,this.newDepIds=new se,this.expression="","function"==typeof t?this.getter=t:(this.getter=function(e){if(!U.test(e)){var t=e.split(".");return function(e){for(var r=0;r<t.length;r++){if(!e)return;e=e[t[r]]}return e}}}(t),this.getter||(this.getter=N)),this.value=this.lazy?void 0:this.get()};pr.prototype.get=function(){var e;pe(this);var t=this.vm;try{e=this.getter.call(t,t)}catch(e){if(!this.user)throw e;qe(e,t,'getter for watcher "'+this.expression+'"')}finally{this.deep&&nt(e),he(),this.cleanupDeps()}return e},pr.prototype.addDep=function(e){var t=e.id;this.newDepIds.has(t)||(this.newDepIds.add(t),this.newDeps.push(e),this.depIds.has(t)||e.addSub(this))},pr.prototype.cleanupDeps=function(){for(var e=this.deps.length;e--;){var t=this.deps[e];this.newDepIds.has(t.id)||t.removeSub(this)}var r=this.depIds;this.depIds=this.newDepIds,this.newDepIds=r,this.newDepIds.clear(),r=this.deps,this.deps=this.newDeps,this.newDeps=r,this.newDeps.length=0},pr.prototype.update=function(){this.lazy?this.dirty=!0:this.sync?this.run():function(e){var t=e.id;if(null==ir[t]){if(ir[t]=!0,ar){for(var r=rr.length-1;r>sr&&rr[r].id>e.id;)r--;rr.splice(r+1,0,e)}else rr.push(e);or||(or=!0,tt(fr))}}(this)},pr.prototype.run=function(){if(this.active){var e=this.get();if(e!==this.value||l(e)||this.deep){var t=this.value;if(this.value=e,this.user)try{this.cb.call(this.vm,e,t)}catch(e){qe(e,this.vm,'callback for watcher "'+this.expression+'"')}else this.cb.call(this.vm,e,t)}}},pr.prototype.evaluate=function(){this.value=this.get(),this.dirty=!1},pr.prototype.depend=function(){for(var e=this.deps.length;e--;)this.deps[e].depend()},pr.prototype.teardown=function(){if(this.active){this.vm._isBeingDestroyed||y(this.vm._watchers,this);for(var e=this.deps.length;e--;)this.deps[e].removeSub(this);this.active=!1}};var hr={enumerable:!0,configurable:!0,get:N,set:N};function mr(e,t,r){hr.get=function(){return this[t][r]},hr.set=function(e){this[t][r]=e},Object.defineProperty(e,r,hr)}var gr={lazy:!0};function vr(e,t,r){var n=!ie();"function"==typeof r?(hr.get=n?yr(t):br(r),hr.set=N):(hr.get=r.get?n&&!1!==r.cache?yr(t):br(r.get):N,hr.set=r.set||N),Object.defineProperty(e,t,hr)}function yr(e){return function(){var t=this._computedWatchers&&this._computedWatchers[e];if(t)return t.dirty&&t.evaluate(),fe.target&&t.depend(),t.value}}function br(e){return function(){return e.call(this,this)}}function wr(e,t,r,n){return u(r)&&(n=r,r=r.handler),"string"==typeof r&&(r=e[r]),e.$watch(t,r,n)}var xr=0;function _r(e){var t=e.options;if(e.super){var r=_r(e.super);if(r!==e.superOptions){e.superOptions=r;var n=function(e){var t,r=e.options,n=e.sealedOptions;for(var i in r)r[i]!==n[i]&&(t||(t={}),t[i]=r[i]);return t}(e);n&&A(e.extendOptions,n),(t=e.options=ze(r,e.extendOptions)).name&&(t.components[t.name]=e)}}return t}function kr(e){this._init(e)}function Cr(e){return e&&(e.Ctor.options.name||e.tag)}function Sr(e,t){return Array.isArray(e)?e.indexOf(t)>-1:"string"==typeof e?e.split(",").indexOf(t)>-1:(r=e,"[object RegExp]"===c.call(r)&&e.test(t));var r}function Tr(e,t){var r=e.cache,n=e.keys,i=e._vnode;for(var o in r){var a=r[o];if(a){var s=Cr(a.componentOptions);s&&!t(s)&&Mr(r,o,n,i)}}}function Mr(e,t,r,n){var i=e[t];!i||n&&i.tag===n.tag||i.componentInstance.$destroy(),e[t]=null,y(r,t)}kr.prototype._init=function(e){var t=this;t._uid=xr++,t._isVue=!0,e&&e._isComponent?function(e,t){var r=e.$options=Object.create(e.constructor.options),n=t._parentVnode;r.parent=t.parent,r._parentVnode=n;var i=n.componentOptions;r.propsData=i.propsData,r._parentListeners=i.listeners,r._renderChildren=i.children,r._componentTag=i.tag,t.render&&(r.render=t.render,r.staticRenderFns=t.staticRenderFns)}(t,e):t.$options=ze(_r(t.constructor),e||{},t),t._renderProxy=t,t._self=t,function(e){var t=e.$options,r=t.parent;if(r&&!t.abstract){for(;r.$options.abstract&&r.$parent;)r=r.$parent;r.$children.push(e)}e.$parent=r,e.$root=r?r.$root:e,e.$children=[],e.$refs={},e._watcher=null,e._inactive=null,e._directInactive=!1,e._isMounted=!1,e._isDestroyed=!1,e._isBeingDestroyed=!1}(t),function(e){e._events=Object.create(null),e._hasHookEvent=!1;var t=e.$options._parentListeners;t&&Yt(e,t)}(t),function(e){e._vnode=null,e._staticTrees=null;var t=e.$options,r=e.$vnode=t._parentVnode,i=r&&r.context;e.$slots=dt(t._renderChildren,i),e.$scopedSlots=n,e._c=function(t,r,n,i){return Wt(e,t,r,n,i,!1)},e.$createElement=function(t,r,n,i){return Wt(e,t,r,n,i,!0)};var o=r&&r.data;Me(e,"$attrs",o&&o.attrs||n,null,!0),Me(e,"$listeners",t._parentListeners||n,null,!0)}(t),tr(t,"beforeCreate"),function(e){var t=ft(e.$options.inject,e);t&&(Ce(!1),Object.keys(t).forEach(function(r){Me(e,r,t[r])}),Ce(!0))}(t),function(e){e._watchers=[];var t=e.$options;t.props&&function(e,t){var r=e.$options.propsData||{},n=e._props={},i=e.$options._propKeys=[];e.$parent&&Ce(!1);var o=function(o){i.push(o);var a=$e(o,t,r,e);Me(n,o,a),o in e||mr(e,"_props",o)};for(var a in t)o(a);Ce(!0)}(e,t.props),t.methods&&function(e,t){for(var r in e.$options.props,t)e[r]="function"!=typeof t[r]?N:M(t[r],e)}(e,t.methods),t.data?function(e){var t=e.$options.data;u(t=e._data="function"==typeof t?function(e,t){pe();try{return e.call(t,t)}catch(e){return qe(e,t,"data()"),{}}finally{he()}}(t,e):t||{})||(t={});for(var r,n=Object.keys(t),i=e.$options.props,o=(e.$options.methods,n.length);o--;){var a=n[o];i&&w(i,a)||36!==(r=(a+"").charCodeAt(0))&&95!==r&&mr(e,"_data",a)}Te(t,!0)}(e):Te(e._data={},!0),t.computed&&function(e,t){var r=e._computedWatchers=Object.create(null),n=ie();for(var i in t){var o=t[i],a="function"==typeof o?o:o.get;n||(r[i]=new pr(e,a||N,N,gr)),i in e||vr(e,i,o)}}(e,t.computed),t.watch&&t.watch!==te&&function(e,t){for(var r in t){var n=t[r];if(Array.isArray(n))for(var i=0;i<n.length;i++)wr(e,r,n[i]);else wr(e,r,n)}}(e,t.watch)}(t),function(e){var t=e.$options.provide;t&&(e._provided="function"==typeof t?t.call(e):t)}(t),tr(t,"created"),t.$options.el&&t.$mount(t.$options.el)},function(e){Object.defineProperty(e.prototype,"$data",{get:function(){return this._data}}),Object.defineProperty(e.prototype,"$props",{get:function(){return this._props}}),e.prototype.$set=Le,e.prototype.$delete=Ae,e.prototype.$watch=function(e,t,r){if(u(t))return wr(this,e,t,r);(r=r||{}).user=!0;var n=new pr(this,e,t,r);if(r.immediate)try{t.call(this,n.value)}catch(e){qe(e,this,'callback for immediate watcher "'+n.expression+'"')}return function(){n.teardown()}}}(kr),function(e){var t=/^hook:/;e.prototype.$on=function(e,r){var n=this;if(Array.isArray(e))for(var i=0,o=e.length;i<o;i++)n.$on(e[i],r);else(n._events[e]||(n._events[e]=[])).push(r),t.test(e)&&(n._hasHookEvent=!0);return n},e.prototype.$once=function(e,t){var r=this;function n(){r.$off(e,n),t.apply(r,arguments)}return n.fn=t,r.$on(e,n),r},e.prototype.$off=function(e,t){var r=this;if(!arguments.length)return r._events=Object.create(null),r;if(Array.isArray(e)){for(var n=0,i=e.length;n<i;n++)r.$off(e[n],t);return r}var o,a=r._events[e];if(!a)return r;if(!t)return r._events[e]=null,r;for(var s=a.length;s--;)if((o=a[s])===t||o.fn===t){a.splice(s,1);break}return r},e.prototype.$emit=function(e){var t=this._events[e];if(t){t=t.length>1?L(t):t;for(var r=L(arguments,1),n='event handler for "'+e+'"',i=0,o=t.length;i<o;i++)He(t[i],this,r,this,n)}return this}}(kr),function(e){e.prototype._update=function(e,t){var r=this,n=r.$el,i=r._vnode,o=Jt(r);r._vnode=e,r.$el=i?r.__patch__(i,e):r.__patch__(r.$el,e,t,!1),o(),n&&(n.__vue__=null),r.$el&&(r.$el.__vue__=r),r.$vnode&&r.$parent&&r.$vnode===r.$parent._vnode&&(r.$parent.$el=r.$el)},e.prototype.$forceUpdate=function(){this._watcher&&this._watcher.update()},e.prototype.$destroy=function(){var e=this;if(!e._isBeingDestroyed){tr(e,"beforeDestroy"),e._isBeingDestroyed=!0;var t=e.$parent;!t||t._isBeingDestroyed||e.$options.abstract||y(t.$children,e),e._watcher&&e._watcher.teardown();for(var r=e._watchers.length;r--;)e._watchers[r].teardown();e._data.__ob__&&e._data.__ob__.vmCount--,e._isDestroyed=!0,e.__patch__(e._vnode,null),tr(e,"destroyed"),e.$off(),e.$el&&(e.$el.__vue__=null),e.$vnode&&(e.$vnode.parent=null)}}}(kr),function(e){Nt(e.prototype),e.prototype.$nextTick=function(e){return tt(e,this)},e.prototype._render=function(){var e,t=this,r=t.$options,n=r.render,i=r._parentVnode;i&&(t.$scopedSlots=ht(i.data.scopedSlots,t.$slots,t.$scopedSlots)),t.$vnode=i;try{Ht=t,e=n.call(t._renderProxy,t.$createElement)}catch(r){qe(r,t,"render"),e=t._vnode}finally{Ht=null}return Array.isArray(e)&&1===e.length&&(e=e[0]),e instanceof me||(e=ve()),e.parent=i,e}}(kr);var Lr=[String,RegExp,Array],Ar={KeepAlive:{name:"keep-alive",abstract:!0,props:{include:Lr,exclude:Lr,max:[String,Number]},created:function(){this.cache=Object.create(null),this.keys=[]},destroyed:function(){for(var e in this.cache)Mr(this.cache,e,this.keys)},mounted:function(){var e=this;this.$watch("include",function(t){Tr(e,function(e){return Sr(t,e)})}),this.$watch("exclude",function(t){Tr(e,function(e){return!Sr(t,e)})})},render:function(){var e=this.$slots.default,t=Gt(e),r=t&&t.componentOptions;if(r){var n=Cr(r),i=this.include,o=this.exclude;if(i&&(!n||!Sr(i,n))||o&&n&&Sr(o,n))return t;var a=this.cache,s=this.keys,l=null==t.key?r.Ctor.cid+(r.tag?"::"+r.tag:""):t.key;a[l]?(t.componentInstance=a[l].componentInstance,y(s,l),s.push(l)):(a[l]=t,s.push(l),this.max&&s.length>parseInt(this.max)&&Mr(a,s[0],s,this._vnode)),t.data.keepAlive=!0}return t||e&&e[0]}}};!function(e){var t={get:function(){return j}};Object.defineProperty(e,"config",t),e.util={warn:ce,extend:A,mergeOptions:ze,defineReactive:Me},e.set=Le,e.delete=Ae,e.nextTick=tt,e.observable=function(e){return Te(e),e},e.options=Object.create(null),$.forEach(function(t){e.options[t+"s"]=Object.create(null)}),e.options._base=e,A(e.options.components,Ar),function(e){e.use=function(e){var t=this._installedPlugins||(this._installedPlugins=[]);if(t.indexOf(e)>-1)return this;var r=L(arguments,1);return r.unshift(this),"function"==typeof e.install?e.install.apply(e,r):"function"==typeof e&&e.apply(null,r),t.push(e),this}}(e),function(e){e.mixin=function(e){return this.options=ze(this.options,e),this}}(e),function(e){e.cid=0;var t=1;e.extend=function(e){e=e||{};var r=this,n=r.cid,i=e._Ctor||(e._Ctor={});if(i[n])return i[n];var o=e.name||r.options.name,a=function(e){this._init(e)};return(a.prototype=Object.create(r.prototype)).constructor=a,a.cid=t++,a.options=ze(r.options,e),a.super=r,a.options.props&&function(e){var t=e.options.props;for(var r in t)mr(e.prototype,"_props",r)}(a),a.options.computed&&function(e){var t=e.options.computed;for(var r in t)vr(e.prototype,r,t[r])}(a),a.extend=r.extend,a.mixin=r.mixin,a.use=r.use,$.forEach(function(e){a[e]=r[e]}),o&&(a.options.components[o]=a),a.superOptions=r.options,a.extendOptions=e,a.sealedOptions=A({},a.options),i[n]=a,a}}(e),function(e){$.forEach(function(t){e[t]=function(e,r){return r?("component"===t&&u(r)&&(r.name=r.name||e,r=this.options._base.extend(r)),"directive"===t&&"function"==typeof r&&(r={bind:r,update:r}),this.options[t+"s"][e]=r,r):this.options[t+"s"][e]}})}(e)}(kr),Object.defineProperty(kr.prototype,"$isServer",{get:ie}),Object.defineProperty(kr.prototype,"$ssrContext",{get:function(){return this.$vnode&&this.$vnode.ssrContext}}),Object.defineProperty(kr,"FunctionalRenderContext",{value:Et}),kr.version="2.6.10";var Or=m("style,class"),Nr=m("input,textarea,option,select,progress"),Er=function(e,t,r){return"value"===r&&Nr(e)&&"button"!==t||"selected"===r&&"option"===e||"checked"===r&&"input"===e||"muted"===r&&"video"===e},Dr=m("contenteditable,draggable,spellcheck"),Ir=m("events,caret,typing,plaintext-only"),Pr=function(e,t){return jr(t)||"false"===t?"false":"contenteditable"===e&&Ir(t)?t:"true"},zr=m("allowfullscreen,async,autofocus,autoplay,checked,compact,controls,declare,default,defaultchecked,defaultmuted,defaultselected,defer,disabled,enabled,formnovalidate,hidden,indeterminate,inert,ismap,itemscope,loop,multiple,muted,nohref,noresize,noshade,novalidate,nowrap,open,pauseonexit,readonly,required,reversed,scoped,seamless,selected,sortable,translate,truespeed,typemustmatch,visible"),Rr="http://www.w3.org/1999/xlink",$r=function(e){return":"===e.charAt(5)&&"xlink"===e.slice(0,5)},Fr=function(e){return $r(e)?e.slice(6,e.length):""},jr=function(e){return null==e||!1===e};function Wr(e,t){return{staticClass:qr(e.staticClass,t.staticClass),class:o(e.class)?[e.class,t.class]:t.class}}function qr(e,t){return e?t?e+" "+t:e:t||""}function Hr(e){return Array.isArray(e)?function(e){for(var t,r="",n=0,i=e.length;n<i;n++)o(t=Hr(e[n]))&&""!==t&&(r&&(r+=" "),r+=t);return r}(e):l(e)?function(e){var t="";for(var r in e)e[r]&&(t&&(t+=" "),t+=r);return t}(e):"string"==typeof e?e:""}var Ur={svg:"http://www.w3.org/2000/svg",math:"http://www.w3.org/1998/Math/MathML"},Br=m("html,body,base,head,link,meta,style,title,address,article,aside,footer,header,h1,h2,h3,h4,h5,h6,hgroup,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,rtc,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,menuitem,summary,content,element,shadow,template,blockquote,iframe,tfoot"),Gr=m("svg,animate,circle,clippath,cursor,defs,desc,ellipse,filter,font-face,foreignObject,g,glyph,image,line,marker,mask,missing-glyph,path,pattern,polygon,polyline,rect,switch,symbol,text,textpath,tspan,use,view",!0),Vr=function(e){return Br(e)||Gr(e)};function Kr(e){return Gr(e)?"svg":"math"===e?"math":void 0}var Xr=Object.create(null),Yr=m("text,number,password,search,email,tel,url");function Zr(e){return"string"==typeof e?document.querySelector(e)||document.createElement("div"):e}var Jr=Object.freeze({createElement:function(e,t){var r=document.createElement(e);return"select"!==e?r:(t.data&&t.data.attrs&&void 0!==t.data.attrs.multiple&&r.setAttribute("multiple","multiple"),r)},createElementNS:function(e,t){return document.createElementNS(Ur[e],t)},createTextNode:function(e){return document.createTextNode(e)},createComment:function(e){return document.createComment(e)},insertBefore:function(e,t,r){e.insertBefore(t,r)},removeChild:function(e,t){e.removeChild(t)},appendChild:function(e,t){e.appendChild(t)},parentNode:function(e){return e.parentNode},nextSibling:function(e){return e.nextSibling},tagName:function(e){return e.tagName},setTextContent:function(e,t){e.textContent=t},setStyleScope:function(e,t){e.setAttribute(t,"")}}),Qr={create:function(e,t){en(t)},update:function(e,t){e.data.ref!==t.data.ref&&(en(e,!0),en(t))},destroy:function(e){en(e,!0)}};function en(e,t){var r=e.data.ref;if(o(r)){var n=e.context,i=e.componentInstance||e.elm,a=n.$refs;t?Array.isArray(a[r])?y(a[r],i):a[r]===i&&(a[r]=void 0):e.data.refInFor?Array.isArray(a[r])?a[r].indexOf(i)<0&&a[r].push(i):a[r]=[i]:a[r]=i}}var tn=new me("",{},[]),rn=["create","activate","update","remove","destroy"];function nn(e,t){return e.key===t.key&&(e.tag===t.tag&&e.isComment===t.isComment&&o(e.data)===o(t.data)&&function(e,t){if("input"!==e.tag)return!0;var r,n=o(r=e.data)&&o(r=r.attrs)&&r.type,i=o(r=t.data)&&o(r=r.attrs)&&r.type;return n===i||Yr(n)&&Yr(i)}(e,t)||a(e.isAsyncPlaceholder)&&e.asyncFactory===t.asyncFactory&&i(t.asyncFactory.error))}function on(e,t,r){var n,i,a={};for(n=t;n<=r;++n)o(i=e[n].key)&&(a[i]=n);return a}var an={create:sn,update:sn,destroy:function(e){sn(e,tn)}};function sn(e,t){(e.data.directives||t.data.directives)&&function(e,t){var r,n,i,o=e===tn,a=t===tn,s=cn(e.data.directives,e.context),l=cn(t.data.directives,t.context),c=[],u=[];for(r in l)n=s[r],i=l[r],n?(i.oldValue=n.value,i.oldArg=n.arg,fn(i,"update",t,e),i.def&&i.def.componentUpdated&&u.push(i)):(fn(i,"bind",t,e),i.def&&i.def.inserted&&c.push(i));if(c.length){var f=function(){for(var r=0;r<c.length;r++)fn(c[r],"inserted",t,e)};o?st(t,"insert",f):f()}if(u.length&&st(t,"postpatch",function(){for(var r=0;r<u.length;r++)fn(u[r],"componentUpdated",t,e)}),!o)for(r in s)l[r]||fn(s[r],"unbind",e,e,a)}(e,t)}var ln=Object.create(null);function cn(e,t){var r,n,i=Object.create(null);if(!e)return i;for(r=0;r<e.length;r++)(n=e[r]).modifiers||(n.modifiers=ln),i[un(n)]=n,n.def=Re(t.$options,"directives",n.name);return i}function un(e){return e.rawName||e.name+"."+Object.keys(e.modifiers||{}).join(".")}function fn(e,t,r,n,i){var o=e.def&&e.def[t];if(o)try{o(r.elm,e,r,n,i)}catch(n){qe(n,r.context,"directive "+e.name+" "+t+" hook")}}var dn=[Qr,an];function pn(e,t){var r=t.componentOptions;if(!(o(r)&&!1===r.Ctor.options.inheritAttrs||i(e.data.attrs)&&i(t.data.attrs))){var n,a,s=t.elm,l=e.data.attrs||{},c=t.data.attrs||{};for(n in o(c.__ob__)&&(c=t.data.attrs=A({},c)),c)a=c[n],l[n]!==a&&hn(s,n,a);for(n in(Y||J)&&c.value!==l.value&&hn(s,"value",c.value),l)i(c[n])&&($r(n)?s.removeAttributeNS(Rr,Fr(n)):Dr(n)||s.removeAttribute(n))}}function hn(e,t,r){e.tagName.indexOf("-")>-1?mn(e,t,r):zr(t)?jr(r)?e.removeAttribute(t):(r="allowfullscreen"===t&&"EMBED"===e.tagName?"true":t,e.setAttribute(t,r)):Dr(t)?e.setAttribute(t,Pr(t,r)):$r(t)?jr(r)?e.removeAttributeNS(Rr,Fr(t)):e.setAttributeNS(Rr,t,r):mn(e,t,r)}function mn(e,t,r){if(jr(r))e.removeAttribute(t);else{if(Y&&!Z&&"TEXTAREA"===e.tagName&&"placeholder"===t&&""!==r&&!e.__ieph){var n=function(t){t.stopImmediatePropagation(),e.removeEventListener("input",n)};e.addEventListener("input",n),e.__ieph=!0}e.setAttribute(t,r)}}var gn={create:pn,update:pn};function vn(e,t){var r=t.elm,n=t.data,a=e.data;if(!(i(n.staticClass)&&i(n.class)&&(i(a)||i(a.staticClass)&&i(a.class)))){var s=function(e){for(var t=e.data,r=e,n=e;o(n.componentInstance);)(n=n.componentInstance._vnode)&&n.data&&(t=Wr(n.data,t));for(;o(r=r.parent);)r&&r.data&&(t=Wr(t,r.data));return function(e,t){return o(e)||o(t)?qr(e,Hr(t)):""}(t.staticClass,t.class)}(t),l=r._transitionClasses;o(l)&&(s=qr(s,Hr(l))),s!==r._prevClass&&(r.setAttribute("class",s),r._prevClass=s)}}var yn,bn,wn,xn,_n,kn,Cn={create:vn,update:vn},Sn=/[\w).+\-_$\]]/;function Tn(e){var t,r,n,i,o,a=!1,s=!1,l=!1,c=!1,u=0,f=0,d=0,p=0;for(n=0;n<e.length;n++)if(r=t,t=e.charCodeAt(n),a)39===t&&92!==r&&(a=!1);else if(s)34===t&&92!==r&&(s=!1);else if(l)96===t&&92!==r&&(l=!1);else if(c)47===t&&92!==r&&(c=!1);else if(124!==t||124===e.charCodeAt(n+1)||124===e.charCodeAt(n-1)||u||f||d){switch(t){case 34:s=!0;break;case 39:a=!0;break;case 96:l=!0;break;case 40:d++;break;case 41:d--;break;case 91:f++;break;case 93:f--;break;case 123:u++;break;case 125:u--}if(47===t){for(var h=n-1,m=void 0;h>=0&&" "===(m=e.charAt(h));h--);m&&Sn.test(m)||(c=!0)}}else void 0===i?(p=n+1,i=e.slice(0,n).trim()):g();function g(){(o||(o=[])).push(e.slice(p,n).trim()),p=n+1}if(void 0===i?i=e.slice(0,n).trim():0!==p&&g(),o)for(n=0;n<o.length;n++)i=Mn(i,o[n]);return i}function Mn(e,t){var r=t.indexOf("(");if(r<0)return'_f("'+t+'")('+e+")";var n=t.slice(0,r),i=t.slice(r+1);return'_f("'+n+'")('+e+(")"!==i?","+i:i)}function Ln(e,t){console.error("[Vue compiler]: "+e)}function An(e,t){return e?e.map(function(e){return e[t]}).filter(function(e){return e}):[]}function On(e,t,r,n,i){(e.props||(e.props=[])).push(Fn({name:t,value:r,dynamic:i},n)),e.plain=!1}function Nn(e,t,r,n,i){(i?e.dynamicAttrs||(e.dynamicAttrs=[]):e.attrs||(e.attrs=[])).push(Fn({name:t,value:r,dynamic:i},n)),e.plain=!1}function En(e,t,r,n){e.attrsMap[t]=r,e.attrsList.push(Fn({name:t,value:r},n))}function Dn(e,t,r,n,i,o,a,s){(e.directives||(e.directives=[])).push(Fn({name:t,rawName:r,value:n,arg:i,isDynamicArg:o,modifiers:a},s)),e.plain=!1}function In(e,t,r){return r?"_p("+t+',"'+e+'")':e+t}function Pn(e,t,r,i,o,a,s,l){var c;(i=i||n).right?l?t="("+t+")==='click'?'contextmenu':("+t+")":"click"===t&&(t="contextmenu",delete i.right):i.middle&&(l?t="("+t+")==='click'?'mouseup':("+t+")":"click"===t&&(t="mouseup")),i.capture&&(delete i.capture,t=In("!",t,l)),i.once&&(delete i.once,t=In("~",t,l)),i.passive&&(delete i.passive,t=In("&",t,l)),i.native?(delete i.native,c=e.nativeEvents||(e.nativeEvents={})):c=e.events||(e.events={});var u=Fn({value:r.trim(),dynamic:l},s);i!==n&&(u.modifiers=i);var f=c[t];Array.isArray(f)?o?f.unshift(u):f.push(u):c[t]=f?o?[u,f]:[f,u]:u,e.plain=!1}function zn(e,t,r){var n=Rn(e,":"+t)||Rn(e,"v-bind:"+t);if(null!=n)return Tn(n);if(!1!==r){var i=Rn(e,t);if(null!=i)return JSON.stringify(i)}}function Rn(e,t,r){var n;if(null!=(n=e.attrsMap[t]))for(var i=e.attrsList,o=0,a=i.length;o<a;o++)if(i[o].name===t){i.splice(o,1);break}return r&&delete e.attrsMap[t],n}function $n(e,t){for(var r=e.attrsList,n=0,i=r.length;n<i;n++){var o=r[n];if(t.test(o.name))return r.splice(n,1),o}}function Fn(e,t){return t&&(null!=t.start&&(e.start=t.start),null!=t.end&&(e.end=t.end)),e}function jn(e,t,r){var n=r||{},i=n.number,o="$$v";n.trim&&(o="(typeof $$v === 'string'? $$v.trim(): $$v)"),i&&(o="_n("+o+")");var a=Wn(t,o);e.model={value:"("+t+")",expression:JSON.stringify(t),callback:"function ($$v) {"+a+"}"}}function Wn(e,t){var r=function(e){if(e=e.trim(),yn=e.length,e.indexOf("[")<0||e.lastIndexOf("]")<yn-1)return(xn=e.lastIndexOf("."))>-1?{exp:e.slice(0,xn),key:'"'+e.slice(xn+1)+'"'}:{exp:e,key:null};for(bn=e,xn=_n=kn=0;!Hn();)Un(wn=qn())?Gn(wn):91===wn&&Bn(wn);return{exp:e.slice(0,_n),key:e.slice(_n+1,kn)}}(e);return null===r.key?e+"="+t:"$set("+r.exp+", "+r.key+", "+t+")"}function qn(){return bn.charCodeAt(++xn)}function Hn(){return xn>=yn}function Un(e){return 34===e||39===e}function Bn(e){var t=1;for(_n=xn;!Hn();)if(Un(e=qn()))Gn(e);else if(91===e&&t++,93===e&&t--,0===t){kn=xn;break}}function Gn(e){for(var t=e;!Hn()&&(e=qn())!==t;);}var Vn,Kn="__r",Xn="__c";function Yn(e,t,r){var n=Vn;return function i(){null!==t.apply(null,arguments)&&Qn(e,i,r,n)}}var Zn=Ve&&!(ee&&Number(ee[1])<=53);function Jn(e,t,r,n){if(Zn){var i=lr,o=t;t=o._wrapper=function(e){if(e.target===e.currentTarget||e.timeStamp>=i||e.timeStamp<=0||e.target.ownerDocument!==document)return o.apply(this,arguments)}}Vn.addEventListener(e,t,re?{capture:r,passive:n}:r)}function Qn(e,t,r,n){(n||Vn).removeEventListener(e,t._wrapper||t,r)}function ei(e,t){if(!i(e.data.on)||!i(t.data.on)){var r=t.data.on||{},n=e.data.on||{};Vn=t.elm,function(e){if(o(e[Kn])){var t=Y?"change":"input";e[t]=[].concat(e[Kn],e[t]||[]),delete e[Kn]}o(e[Xn])&&(e.change=[].concat(e[Xn],e.change||[]),delete e[Xn])}(r),at(r,n,Jn,Qn,Yn,t.context),Vn=void 0}}var ti,ri={create:ei,update:ei};function ni(e,t){if(!i(e.data.domProps)||!i(t.data.domProps)){var r,n,a=t.elm,s=e.data.domProps||{},l=t.data.domProps||{};for(r in o(l.__ob__)&&(l=t.data.domProps=A({},l)),s)r in l||(a[r]="");for(r in l){if(n=l[r],"textContent"===r||"innerHTML"===r){if(t.children&&(t.children.length=0),n===s[r])continue;1===a.childNodes.length&&a.removeChild(a.childNodes[0])}if("value"===r&&"PROGRESS"!==a.tagName){a._value=n;var c=i(n)?"":String(n);ii(a,c)&&(a.value=c)}else if("innerHTML"===r&&Gr(a.tagName)&&i(a.innerHTML)){(ti=ti||document.createElement("div")).innerHTML="<svg>"+n+"</svg>";for(var u=ti.firstChild;a.firstChild;)a.removeChild(a.firstChild);for(;u.firstChild;)a.appendChild(u.firstChild)}else if(n!==s[r])try{a[r]=n}catch(e){}}}}function ii(e,t){return!e.composing&&("OPTION"===e.tagName||function(e,t){var r=!0;try{r=document.activeElement!==e}catch(e){}return r&&e.value!==t}(e,t)||function(e,t){var r=e.value,n=e._vModifiers;if(o(n)){if(n.number)return h(r)!==h(t);if(n.trim)return r.trim()!==t.trim()}return r!==t}(e,t))}var oi={create:ni,update:ni},ai=x(function(e){var t={},r=/:(.+)/;return e.split(/;(?![^(]*\))/g).forEach(function(e){if(e){var n=e.split(r);n.length>1&&(t[n[0].trim()]=n[1].trim())}}),t});function si(e){var t=li(e.style);return e.staticStyle?A(e.staticStyle,t):t}function li(e){return Array.isArray(e)?O(e):"string"==typeof e?ai(e):e}var ci,ui=/^--/,fi=/\s*!important$/,di=function(e,t,r){if(ui.test(t))e.style.setProperty(t,r);else if(fi.test(r))e.style.setProperty(T(t),r.replace(fi,""),"important");else{var n=hi(t);if(Array.isArray(r))for(var i=0,o=r.length;i<o;i++)e.style[n]=r[i];else e.style[n]=r}},pi=["Webkit","Moz","ms"],hi=x(function(e){if(ci=ci||document.createElement("div").style,"filter"!==(e=k(e))&&e in ci)return e;for(var t=e.charAt(0).toUpperCase()+e.slice(1),r=0;r<pi.length;r++){var n=pi[r]+t;if(n in ci)return n}});function mi(e,t){var r=t.data,n=e.data;if(!(i(r.staticStyle)&&i(r.style)&&i(n.staticStyle)&&i(n.style))){var a,s,l=t.elm,c=n.staticStyle,u=n.normalizedStyle||n.style||{},f=c||u,d=li(t.data.style)||{};t.data.normalizedStyle=o(d.__ob__)?A({},d):d;var p=function(e,t){for(var r,n={},i=e;i.componentInstance;)(i=i.componentInstance._vnode)&&i.data&&(r=si(i.data))&&A(n,r);(r=si(e.data))&&A(n,r);for(var o=e;o=o.parent;)o.data&&(r=si(o.data))&&A(n,r);return n}(t);for(s in f)i(p[s])&&di(l,s,"");for(s in p)(a=p[s])!==f[s]&&di(l,s,null==a?"":a)}}var gi={create:mi,update:mi},vi=/\s+/;function yi(e,t){if(t&&(t=t.trim()))if(e.classList)t.indexOf(" ")>-1?t.split(vi).forEach(function(t){return e.classList.add(t)}):e.classList.add(t);else{var r=" "+(e.getAttribute("class")||"")+" ";r.indexOf(" "+t+" ")<0&&e.setAttribute("class",(r+t).trim())}}function bi(e,t){if(t&&(t=t.trim()))if(e.classList)t.indexOf(" ")>-1?t.split(vi).forEach(function(t){return e.classList.remove(t)}):e.classList.remove(t),e.classList.length||e.removeAttribute("class");else{for(var r=" "+(e.getAttribute("class")||"")+" ",n=" "+t+" ";r.indexOf(n)>=0;)r=r.replace(n," ");(r=r.trim())?e.setAttribute("class",r):e.removeAttribute("class")}}function wi(e){if(e){if("object"==typeof e){var t={};return!1!==e.css&&A(t,xi(e.name||"v")),A(t,e),t}return"string"==typeof e?xi(e):void 0}}var xi=x(function(e){return{enterClass:e+"-enter",enterToClass:e+"-enter-to",enterActiveClass:e+"-enter-active",leaveClass:e+"-leave",leaveToClass:e+"-leave-to",leaveActiveClass:e+"-leave-active"}}),_i=G&&!Z,ki="transition",Ci="animation",Si="transition",Ti="transitionend",Mi="animation",Li="animationend";_i&&(void 0===window.ontransitionend&&void 0!==window.onwebkittransitionend&&(Si="WebkitTransition",Ti="webkitTransitionEnd"),void 0===window.onanimationend&&void 0!==window.onwebkitanimationend&&(Mi="WebkitAnimation",Li="webkitAnimationEnd"));var Ai=G?window.requestAnimationFrame?window.requestAnimationFrame.bind(window):setTimeout:function(e){return e()};function Oi(e){Ai(function(){Ai(e)})}function Ni(e,t){var r=e._transitionClasses||(e._transitionClasses=[]);r.indexOf(t)<0&&(r.push(t),yi(e,t))}function Ei(e,t){e._transitionClasses&&y(e._transitionClasses,t),bi(e,t)}function Di(e,t,r){var n=Pi(e,t),i=n.type,o=n.timeout,a=n.propCount;if(!i)return r();var s=i===ki?Ti:Li,l=0,c=function(){e.removeEventListener(s,u),r()},u=function(t){t.target===e&&++l>=a&&c()};setTimeout(function(){l<a&&c()},o+1),e.addEventListener(s,u)}var Ii=/\b(transform|all)(,|$)/;function Pi(e,t){var r,n=window.getComputedStyle(e),i=(n[Si+"Delay"]||"").split(", "),o=(n[Si+"Duration"]||"").split(", "),a=zi(i,o),s=(n[Mi+"Delay"]||"").split(", "),l=(n[Mi+"Duration"]||"").split(", "),c=zi(s,l),u=0,f=0;return t===ki?a>0&&(r=ki,u=a,f=o.length):t===Ci?c>0&&(r=Ci,u=c,f=l.length):f=(r=(u=Math.max(a,c))>0?a>c?ki:Ci:null)?r===ki?o.length:l.length:0,{type:r,timeout:u,propCount:f,hasTransform:r===ki&&Ii.test(n[Si+"Property"])}}function zi(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max.apply(null,t.map(function(t,r){return Ri(t)+Ri(e[r])}))}function Ri(e){return 1e3*Number(e.slice(0,-1).replace(",","."))}function $i(e,t){var r=e.elm;o(r._leaveCb)&&(r._leaveCb.cancelled=!0,r._leaveCb());var n=wi(e.data.transition);if(!i(n)&&!o(r._enterCb)&&1===r.nodeType){for(var a=n.css,s=n.type,c=n.enterClass,u=n.enterToClass,f=n.enterActiveClass,d=n.appearClass,p=n.appearToClass,m=n.appearActiveClass,g=n.beforeEnter,v=n.enter,y=n.afterEnter,b=n.enterCancelled,w=n.beforeAppear,x=n.appear,_=n.afterAppear,k=n.appearCancelled,C=n.duration,S=Zt,T=Zt.$vnode;T&&T.parent;)S=T.context,T=T.parent;var M=!S._isMounted||!e.isRootInsert;if(!M||x||""===x){var L=M&&d?d:c,A=M&&m?m:f,O=M&&p?p:u,N=M&&w||g,E=M&&"function"==typeof x?x:v,D=M&&_||y,I=M&&k||b,P=h(l(C)?C.enter:C),R=!1!==a&&!Z,$=Wi(E),F=r._enterCb=z(function(){R&&(Ei(r,O),Ei(r,A)),F.cancelled?(R&&Ei(r,L),I&&I(r)):D&&D(r),r._enterCb=null});e.data.show||st(e,"insert",function(){var t=r.parentNode,n=t&&t._pending&&t._pending[e.key];n&&n.tag===e.tag&&n.elm._leaveCb&&n.elm._leaveCb(),E&&E(r,F)}),N&&N(r),R&&(Ni(r,L),Ni(r,A),Oi(function(){Ei(r,L),F.cancelled||(Ni(r,O),$||(ji(P)?setTimeout(F,P):Di(r,s,F)))})),e.data.show&&(t&&t(),E&&E(r,F)),R||$||F()}}}function Fi(e,t){var r=e.elm;o(r._enterCb)&&(r._enterCb.cancelled=!0,r._enterCb());var n=wi(e.data.transition);if(i(n)||1!==r.nodeType)return t();if(!o(r._leaveCb)){var a=n.css,s=n.type,c=n.leaveClass,u=n.leaveToClass,f=n.leaveActiveClass,d=n.beforeLeave,p=n.leave,m=n.afterLeave,g=n.leaveCancelled,v=n.delayLeave,y=n.duration,b=!1!==a&&!Z,w=Wi(p),x=h(l(y)?y.leave:y),_=r._leaveCb=z(function(){r.parentNode&&r.parentNode._pending&&(r.parentNode._pending[e.key]=null),b&&(Ei(r,u),Ei(r,f)),_.cancelled?(b&&Ei(r,c),g&&g(r)):(t(),m&&m(r)),r._leaveCb=null});v?v(k):k()}function k(){_.cancelled||(!e.data.show&&r.parentNode&&((r.parentNode._pending||(r.parentNode._pending={}))[e.key]=e),d&&d(r),b&&(Ni(r,c),Ni(r,f),Oi(function(){Ei(r,c),_.cancelled||(Ni(r,u),w||(ji(x)?setTimeout(_,x):Di(r,s,_)))})),p&&p(r,_),b||w||_())}}function ji(e){return"number"==typeof e&&!isNaN(e)}function Wi(e){if(i(e))return!1;var t=e.fns;return o(t)?Wi(Array.isArray(t)?t[0]:t):(e._length||e.length)>1}function qi(e,t){!0!==t.data.show&&$i(t)}var Hi=function(e){var t,r,n={},l=e.modules,c=e.nodeOps;for(t=0;t<rn.length;++t)for(n[rn[t]]=[],r=0;r<l.length;++r)o(l[r][rn[t]])&&n[rn[t]].push(l[r][rn[t]]);function u(e){var t=c.parentNode(e);o(t)&&c.removeChild(t,e)}function f(e,t,r,i,s,l,u){if(o(e.elm)&&o(l)&&(e=l[u]=be(e)),e.isRootInsert=!s,!function(e,t,r,i){var s=e.data;if(o(s)){var l=o(e.componentInstance)&&s.keepAlive;if(o(s=s.hook)&&o(s=s.init)&&s(e,!1),o(e.componentInstance))return d(e,t),p(r,e.elm,i),a(l)&&function(e,t,r,i){for(var a,s=e;s.componentInstance;)if(o(a=(s=s.componentInstance._vnode).data)&&o(a=a.transition)){for(a=0;a<n.activate.length;++a)n.activate[a](tn,s);t.push(s);break}p(r,e.elm,i)}(e,t,r,i),!0}}(e,t,r,i)){var f=e.data,m=e.children,g=e.tag;o(g)?(e.elm=e.ns?c.createElementNS(e.ns,g):c.createElement(g,e),y(e),h(e,m,t),o(f)&&v(e,t),p(r,e.elm,i)):a(e.isComment)?(e.elm=c.createComment(e.text),p(r,e.elm,i)):(e.elm=c.createTextNode(e.text),p(r,e.elm,i))}}function d(e,t){o(e.data.pendingInsert)&&(t.push.apply(t,e.data.pendingInsert),e.data.pendingInsert=null),e.elm=e.componentInstance.$el,g(e)?(v(e,t),y(e)):(en(e),t.push(e))}function p(e,t,r){o(e)&&(o(r)?c.parentNode(r)===e&&c.insertBefore(e,t,r):c.appendChild(e,t))}function h(e,t,r){if(Array.isArray(t))for(var n=0;n<t.length;++n)f(t[n],r,e.elm,null,!0,t,n);else s(e.text)&&c.appendChild(e.elm,c.createTextNode(String(e.text)))}function g(e){for(;e.componentInstance;)e=e.componentInstance._vnode;return o(e.tag)}function v(e,r){for(var i=0;i<n.create.length;++i)n.create[i](tn,e);o(t=e.data.hook)&&(o(t.create)&&t.create(tn,e),o(t.insert)&&r.push(e))}function y(e){var t;if(o(t=e.fnScopeId))c.setStyleScope(e.elm,t);else for(var r=e;r;)o(t=r.context)&&o(t=t.$options._scopeId)&&c.setStyleScope(e.elm,t),r=r.parent;o(t=Zt)&&t!==e.context&&t!==e.fnContext&&o(t=t.$options._scopeId)&&c.setStyleScope(e.elm,t)}function b(e,t,r,n,i,o){for(;n<=i;++n)f(r[n],o,e,t,!1,r,n)}function w(e){var t,r,i=e.data;if(o(i))for(o(t=i.hook)&&o(t=t.destroy)&&t(e),t=0;t<n.destroy.length;++t)n.destroy[t](e);if(o(t=e.children))for(r=0;r<e.children.length;++r)w(e.children[r])}function x(e,t,r,n){for(;r<=n;++r){var i=t[r];o(i)&&(o(i.tag)?(_(i),w(i)):u(i.elm))}}function _(e,t){if(o(t)||o(e.data)){var r,i=n.remove.length+1;for(o(t)?t.listeners+=i:t=function(e,t){function r(){0==--r.listeners&&u(e)}return r.listeners=t,r}(e.elm,i),o(r=e.componentInstance)&&o(r=r._vnode)&&o(r.data)&&_(r,t),r=0;r<n.remove.length;++r)n.remove[r](e,t);o(r=e.data.hook)&&o(r=r.remove)?r(e,t):t()}else u(e.elm)}function k(e,t,r,n){for(var i=r;i<n;i++){var a=t[i];if(o(a)&&nn(e,a))return i}}function C(e,t,r,s,l,u){if(e!==t){o(t.elm)&&o(s)&&(t=s[l]=be(t));var d=t.elm=e.elm;if(a(e.isAsyncPlaceholder))o(t.asyncFactory.resolved)?M(e.elm,t,r):t.isAsyncPlaceholder=!0;else if(a(t.isStatic)&&a(e.isStatic)&&t.key===e.key&&(a(t.isCloned)||a(t.isOnce)))t.componentInstance=e.componentInstance;else{var p,h=t.data;o(h)&&o(p=h.hook)&&o(p=p.prepatch)&&p(e,t);var m=e.children,v=t.children;if(o(h)&&g(t)){for(p=0;p<n.update.length;++p)n.update[p](e,t);o(p=h.hook)&&o(p=p.update)&&p(e,t)}i(t.text)?o(m)&&o(v)?m!==v&&function(e,t,r,n,a){for(var s,l,u,d=0,p=0,h=t.length-1,m=t[0],g=t[h],v=r.length-1,y=r[0],w=r[v],_=!a;d<=h&&p<=v;)i(m)?m=t[++d]:i(g)?g=t[--h]:nn(m,y)?(C(m,y,n,r,p),m=t[++d],y=r[++p]):nn(g,w)?(C(g,w,n,r,v),g=t[--h],w=r[--v]):nn(m,w)?(C(m,w,n,r,v),_&&c.insertBefore(e,m.elm,c.nextSibling(g.elm)),m=t[++d],w=r[--v]):nn(g,y)?(C(g,y,n,r,p),_&&c.insertBefore(e,g.elm,m.elm),g=t[--h],y=r[++p]):(i(s)&&(s=on(t,d,h)),i(l=o(y.key)?s[y.key]:k(y,t,d,h))?f(y,n,e,m.elm,!1,r,p):nn(u=t[l],y)?(C(u,y,n,r,p),t[l]=void 0,_&&c.insertBefore(e,u.elm,m.elm)):f(y,n,e,m.elm,!1,r,p),y=r[++p]);d>h?b(e,i(r[v+1])?null:r[v+1].elm,r,p,v,n):p>v&&x(0,t,d,h)}(d,m,v,r,u):o(v)?(o(e.text)&&c.setTextContent(d,""),b(d,null,v,0,v.length-1,r)):o(m)?x(0,m,0,m.length-1):o(e.text)&&c.setTextContent(d,""):e.text!==t.text&&c.setTextContent(d,t.text),o(h)&&o(p=h.hook)&&o(p=p.postpatch)&&p(e,t)}}}function S(e,t,r){if(a(r)&&o(e.parent))e.parent.data.pendingInsert=t;else for(var n=0;n<t.length;++n)t[n].data.hook.insert(t[n])}var T=m("attrs,class,staticClass,staticStyle,key");function M(e,t,r,n){var i,s=t.tag,l=t.data,c=t.children;if(n=n||l&&l.pre,t.elm=e,a(t.isComment)&&o(t.asyncFactory))return t.isAsyncPlaceholder=!0,!0;if(o(l)&&(o(i=l.hook)&&o(i=i.init)&&i(t,!0),o(i=t.componentInstance)))return d(t,r),!0;if(o(s)){if(o(c))if(e.hasChildNodes())if(o(i=l)&&o(i=i.domProps)&&o(i=i.innerHTML)){if(i!==e.innerHTML)return!1}else{for(var u=!0,f=e.firstChild,p=0;p<c.length;p++){if(!f||!M(f,c[p],r,n)){u=!1;break}f=f.nextSibling}if(!u||f)return!1}else h(t,c,r);if(o(l)){var m=!1;for(var g in l)if(!T(g)){m=!0,v(t,r);break}!m&&l.class&&nt(l.class)}}else e.data!==t.text&&(e.data=t.text);return!0}return function(e,t,r,s){if(!i(t)){var l,u=!1,d=[];if(i(e))u=!0,f(t,d);else{var p=o(e.nodeType);if(!p&&nn(e,t))C(e,t,d,null,null,s);else{if(p){if(1===e.nodeType&&e.hasAttribute(R)&&(e.removeAttribute(R),r=!0),a(r)&&M(e,t,d))return S(t,d,!0),e;l=e,e=new me(c.tagName(l).toLowerCase(),{},[],void 0,l)}var h=e.elm,m=c.parentNode(h);if(f(t,d,h._leaveCb?null:m,c.nextSibling(h)),o(t.parent))for(var v=t.parent,y=g(t);v;){for(var b=0;b<n.destroy.length;++b)n.destroy[b](v);if(v.elm=t.elm,y){for(var _=0;_<n.create.length;++_)n.create[_](tn,v);var k=v.data.hook.insert;if(k.merged)for(var T=1;T<k.fns.length;T++)k.fns[T]()}else en(v);v=v.parent}o(m)?x(0,[e],0,0):o(e.tag)&&w(e)}}return S(t,d,u),t.elm}o(e)&&w(e)}}({nodeOps:Jr,modules:[gn,Cn,ri,oi,gi,G?{create:qi,activate:qi,remove:function(e,t){!0!==e.data.show?Fi(e,t):t()}}:{}].concat(dn)});Z&&document.addEventListener("selectionchange",function(){var e=document.activeElement;e&&e.vmodel&&Zi(e,"input")});var Ui={inserted:function(e,t,r,n){"select"===r.tag?(n.elm&&!n.elm._vOptions?st(r,"postpatch",function(){Ui.componentUpdated(e,t,r)}):Bi(e,t,r.context),e._vOptions=[].map.call(e.options,Ki)):("textarea"===r.tag||Yr(e.type))&&(e._vModifiers=t.modifiers,t.modifiers.lazy||(e.addEventListener("compositionstart",Xi),e.addEventListener("compositionend",Yi),e.addEventListener("change",Yi),Z&&(e.vmodel=!0)))},componentUpdated:function(e,t,r){if("select"===r.tag){Bi(e,t,r.context);var n=e._vOptions,i=e._vOptions=[].map.call(e.options,Ki);i.some(function(e,t){return!I(e,n[t])})&&(e.multiple?t.value.some(function(e){return Vi(e,i)}):t.value!==t.oldValue&&Vi(t.value,i))&&Zi(e,"change")}}};function Bi(e,t,r){Gi(e,t,r),(Y||J)&&setTimeout(function(){Gi(e,t,r)},0)}function Gi(e,t,r){var n=t.value,i=e.multiple;if(!i||Array.isArray(n)){for(var o,a,s=0,l=e.options.length;s<l;s++)if(a=e.options[s],i)o=P(n,Ki(a))>-1,a.selected!==o&&(a.selected=o);else if(I(Ki(a),n))return void(e.selectedIndex!==s&&(e.selectedIndex=s));i||(e.selectedIndex=-1)}}function Vi(e,t){return t.every(function(t){return!I(t,e)})}function Ki(e){return"_value"in e?e._value:e.value}function Xi(e){e.target.composing=!0}function Yi(e){e.target.composing&&(e.target.composing=!1,Zi(e.target,"input"))}function Zi(e,t){var r=document.createEvent("HTMLEvents");r.initEvent(t,!0,!0),e.dispatchEvent(r)}function Ji(e){return!e.componentInstance||e.data&&e.data.transition?e:Ji(e.componentInstance._vnode)}var Qi={model:Ui,show:{bind:function(e,t,r){var n=t.value,i=(r=Ji(r)).data&&r.data.transition,o=e.__vOriginalDisplay="none"===e.style.display?"":e.style.display;n&&i?(r.data.show=!0,$i(r,function(){e.style.display=o})):e.style.display=n?o:"none"},update:function(e,t,r){var n=t.value;!n!=!t.oldValue&&((r=Ji(r)).data&&r.data.transition?(r.data.show=!0,n?$i(r,function(){e.style.display=e.__vOriginalDisplay}):Fi(r,function(){e.style.display="none"})):e.style.display=n?e.__vOriginalDisplay:"none")},unbind:function(e,t,r,n,i){i||(e.style.display=e.__vOriginalDisplay)}}},eo={name:String,appear:Boolean,css:Boolean,mode:String,type:String,enterClass:String,leaveClass:String,enterToClass:String,leaveToClass:String,enterActiveClass:String,leaveActiveClass:String,appearClass:String,appearActiveClass:String,appearToClass:String,duration:[Number,String,Object]};function to(e){var t=e&&e.componentOptions;return t&&t.Ctor.options.abstract?to(Gt(t.children)):e}function ro(e){var t={},r=e.$options;for(var n in r.propsData)t[n]=e[n];var i=r._parentListeners;for(var o in i)t[k(o)]=i[o];return t}function no(e,t){if(/\d-keep-alive$/.test(t.tag))return e("keep-alive",{props:t.componentOptions.propsData})}var io=function(e){return e.tag||Bt(e)},oo=function(e){return"show"===e.name},ao={name:"transition",props:eo,abstract:!0,render:function(e){var t=this,r=this.$slots.default;if(r&&(r=r.filter(io)).length){var n=this.mode,i=r[0];if(function(e){for(;e=e.parent;)if(e.data.transition)return!0}(this.$vnode))return i;var o=to(i);if(!o)return i;if(this._leaving)return no(e,i);var a="__transition-"+this._uid+"-";o.key=null==o.key?o.isComment?a+"comment":a+o.tag:s(o.key)?0===String(o.key).indexOf(a)?o.key:a+o.key:o.key;var l=(o.data||(o.data={})).transition=ro(this),c=this._vnode,u=to(c);if(o.data.directives&&o.data.directives.some(oo)&&(o.data.show=!0),u&&u.data&&!function(e,t){return t.key===e.key&&t.tag===e.tag}(o,u)&&!Bt(u)&&(!u.componentInstance||!u.componentInstance._vnode.isComment)){var f=u.data.transition=A({},l);if("out-in"===n)return this._leaving=!0,st(f,"afterLeave",function(){t._leaving=!1,t.$forceUpdate()}),no(e,i);if("in-out"===n){if(Bt(o))return c;var d,p=function(){d()};st(l,"afterEnter",p),st(l,"enterCancelled",p),st(f,"delayLeave",function(e){d=e})}}return i}}},so=A({tag:String,moveClass:String},eo);function lo(e){e.elm._moveCb&&e.elm._moveCb(),e.elm._enterCb&&e.elm._enterCb()}function co(e){e.data.newPos=e.elm.getBoundingClientRect()}function uo(e){var t=e.data.pos,r=e.data.newPos,n=t.left-r.left,i=t.top-r.top;if(n||i){e.data.moved=!0;var o=e.elm.style;o.transform=o.WebkitTransform="translate("+n+"px,"+i+"px)",o.transitionDuration="0s"}}delete so.mode;var fo={Transition:ao,TransitionGroup:{props:so,beforeMount:function(){var e=this,t=this._update;this._update=function(r,n){var i=Jt(e);e.__patch__(e._vnode,e.kept,!1,!0),e._vnode=e.kept,i(),t.call(e,r,n)}},render:function(e){for(var t=this.tag||this.$vnode.data.tag||"span",r=Object.create(null),n=this.prevChildren=this.children,i=this.$slots.default||[],o=this.children=[],a=ro(this),s=0;s<i.length;s++){var l=i[s];l.tag&&null!=l.key&&0!==String(l.key).indexOf("__vlist")&&(o.push(l),r[l.key]=l,(l.data||(l.data={})).transition=a)}if(n){for(var c=[],u=[],f=0;f<n.length;f++){var d=n[f];d.data.transition=a,d.data.pos=d.elm.getBoundingClientRect(),r[d.key]?c.push(d):u.push(d)}this.kept=e(t,null,c),this.removed=u}return e(t,null,o)},updated:function(){var e=this.prevChildren,t=this.moveClass||(this.name||"v")+"-move";e.length&&this.hasMove(e[0].elm,t)&&(e.forEach(lo),e.forEach(co),e.forEach(uo),this._reflow=document.body.offsetHeight,e.forEach(function(e){if(e.data.moved){var r=e.elm,n=r.style;Ni(r,t),n.transform=n.WebkitTransform=n.transitionDuration="",r.addEventListener(Ti,r._moveCb=function e(n){n&&n.target!==r||n&&!/transform$/.test(n.propertyName)||(r.removeEventListener(Ti,e),r._moveCb=null,Ei(r,t))})}}))},methods:{hasMove:function(e,t){if(!_i)return!1;if(this._hasMove)return this._hasMove;var r=e.cloneNode();e._transitionClasses&&e._transitionClasses.forEach(function(e){bi(r,e)}),yi(r,t),r.style.display="none",this.$el.appendChild(r);var n=Pi(r);return this.$el.removeChild(r),this._hasMove=n.hasTransform}}}};kr.config.mustUseProp=Er,kr.config.isReservedTag=Vr,kr.config.isReservedAttr=Or,kr.config.getTagNamespace=Kr,kr.config.isUnknownElement=function(e){if(!G)return!0;if(Vr(e))return!1;if(e=e.toLowerCase(),null!=Xr[e])return Xr[e];var t=document.createElement(e);return e.indexOf("-")>-1?Xr[e]=t.constructor===window.HTMLUnknownElement||t.constructor===window.HTMLElement:Xr[e]=/HTMLUnknownElement/.test(t.toString())},A(kr.options.directives,Qi),A(kr.options.components,fo),kr.prototype.__patch__=G?Hi:N,kr.prototype.$mount=function(e,t){return function(e,t,r){return e.$el=t,e.$options.render||(e.$options.render=ve),tr(e,"beforeMount"),new pr(e,function(){e._update(e._render(),r)},N,{before:function(){e._isMounted&&!e._isDestroyed&&tr(e,"beforeUpdate")}},!0),r=!1,null==e.$vnode&&(e._isMounted=!0,tr(e,"mounted")),e}(this,e=e&&G?Zr(e):void 0,t)},G&&setTimeout(function(){j.devtools&&oe&&oe.emit("init",kr)},0);var po,ho=/\{\{((?:.|\r?\n)+?)\}\}/g,mo=/[-.*+?^${}()|[\]\/\\]/g,go=x(function(e){var t=e[0].replace(mo,"\\$&"),r=e[1].replace(mo,"\\$&");return new RegExp(t+"((?:.|\\n)+?)"+r,"g")}),vo={staticKeys:["staticClass"],transformNode:function(e,t){t.warn;var r=Rn(e,"class");r&&(e.staticClass=JSON.stringify(r));var n=zn(e,"class",!1);n&&(e.classBinding=n)},genData:function(e){var t="";return e.staticClass&&(t+="staticClass:"+e.staticClass+","),e.classBinding&&(t+="class:"+e.classBinding+","),t}},yo={staticKeys:["staticStyle"],transformNode:function(e,t){t.warn;var r=Rn(e,"style");r&&(e.staticStyle=JSON.stringify(ai(r)));var n=zn(e,"style",!1);n&&(e.styleBinding=n)},genData:function(e){var t="";return e.staticStyle&&(t+="staticStyle:"+e.staticStyle+","),e.styleBinding&&(t+="style:("+e.styleBinding+"),"),t}},bo=m("area,base,br,col,embed,frame,hr,img,input,isindex,keygen,link,meta,param,source,track,wbr"),wo=m("colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr,source"),xo=m("address,article,aside,base,blockquote,body,caption,col,colgroup,dd,details,dialog,div,dl,dt,fieldset,figcaption,figure,footer,form,h1,h2,h3,h4,h5,h6,head,header,hgroup,hr,html,legend,li,menuitem,meta,optgroup,option,param,rp,rt,source,style,summary,tbody,td,tfoot,th,thead,title,tr,track"),_o=/^\s*([^\s"'<>\/=]+)(?:\s*(=)\s*(?:"([^"]*)"+|'([^']*)'+|([^\s"'=<>`]+)))?/,ko=/^\s*((?:v-[\w-]+:|@|:|#)\[[^=]+\][^\s"'<>\/=]*)(?:\s*(=)\s*(?:"([^"]*)"+|'([^']*)'+|([^\s"'=<>`]+)))?/,Co="[a-zA-Z_][\\-\\.0-9_a-zA-Z"+W.source+"]*",So="((?:"+Co+"\\:)?"+Co+")",To=new RegExp("^<"+So),Mo=/^\s*(\/?)>/,Lo=new RegExp("^<\\/"+So+"[^>]*>"),Ao=/^<!DOCTYPE [^>]+>/i,Oo=/^<!\--/,No=/^<!\[/,Eo=m("script,style,textarea",!0),Do={},Io={"&lt;":"<","&gt;":">","&quot;":'"',"&amp;":"&","&#10;":"\n","&#9;":"\t","&#39;":"'"},Po=/&(?:lt|gt|quot|amp|#39);/g,zo=/&(?:lt|gt|quot|amp|#39|#10|#9);/g,Ro=m("pre,textarea",!0),$o=function(e,t){return e&&Ro(e)&&"\n"===t[0]};function Fo(e,t){var r=t?zo:Po;return e.replace(r,function(e){return Io[e]})}var jo,Wo,qo,Ho,Uo,Bo,Go,Vo,Ko=/^@|^v-on:/,Xo=/^v-|^@|^:/,Yo=/([\s\S]*?)\s+(?:in|of)\s+([\s\S]*)/,Zo=/,([^,\}\]]*)(?:,([^,\}\]]*))?$/,Jo=/^\(|\)$/g,Qo=/^\[.*\]$/,ea=/:(.*)$/,ta=/^:|^\.|^v-bind:/,ra=/\.[^.\]]+(?=[^\]]*$)/g,na=/^v-slot(:|$)|^#/,ia=/[\r\n]/,oa=/\s+/g,aa=x(function(e){return(po=po||document.createElement("div")).innerHTML=e,po.textContent}),sa="_empty_";function la(e,t,r){return{type:1,tag:e,attrsList:t,attrsMap:function(e){for(var t={},r=0,n=e.length;r<n;r++)t[e[r].name]=e[r].value;return t}(t),rawAttrsMap:{},parent:r,children:[]}}function ca(e,t){var r,n;(n=zn(r=e,"key"))&&(r.key=n),e.plain=!e.key&&!e.scopedSlots&&!e.attrsList.length,function(e){var t=zn(e,"ref");t&&(e.ref=t,e.refInFor=function(e){for(var t=e;t;){if(void 0!==t.for)return!0;t=t.parent}return!1}(e))}(e),function(e){var t;"template"===e.tag?(t=Rn(e,"scope"),e.slotScope=t||Rn(e,"slot-scope")):(t=Rn(e,"slot-scope"))&&(e.slotScope=t);var r=zn(e,"slot");if(r&&(e.slotTarget='""'===r?'"default"':r,e.slotTargetDynamic=!(!e.attrsMap[":slot"]&&!e.attrsMap["v-bind:slot"]),"template"===e.tag||e.slotScope||Nn(e,"slot",r,function(e,t){return e.rawAttrsMap[":"+t]||e.rawAttrsMap["v-bind:"+t]||e.rawAttrsMap[t]}(e,"slot"))),"template"===e.tag){var n=$n(e,na);if(n){var i=da(n),o=i.name,a=i.dynamic;e.slotTarget=o,e.slotTargetDynamic=a,e.slotScope=n.value||sa}}else{var s=$n(e,na);if(s){var l=e.scopedSlots||(e.scopedSlots={}),c=da(s),u=c.name,f=c.dynamic,d=l[u]=la("template",[],e);d.slotTarget=u,d.slotTargetDynamic=f,d.children=e.children.filter(function(e){if(!e.slotScope)return e.parent=d,!0}),d.slotScope=s.value||sa,e.children=[],e.plain=!1}}}(e),function(e){"slot"===e.tag&&(e.slotName=zn(e,"name"))}(e),function(e){var t;(t=zn(e,"is"))&&(e.component=t),null!=Rn(e,"inline-template")&&(e.inlineTemplate=!0)}(e);for(var i=0;i<qo.length;i++)e=qo[i](e,t)||e;return function(e){var t,r,n,i,o,a,s,l,c=e.attrsList;for(t=0,r=c.length;t<r;t++)if(n=i=c[t].name,o=c[t].value,Xo.test(n))if(e.hasBindings=!0,(a=pa(n.replace(Xo,"")))&&(n=n.replace(ra,"")),ta.test(n))n=n.replace(ta,""),o=Tn(o),(l=Qo.test(n))&&(n=n.slice(1,-1)),a&&(a.prop&&!l&&"innerHtml"===(n=k(n))&&(n="innerHTML"),a.camel&&!l&&(n=k(n)),a.sync&&(s=Wn(o,"$event"),l?Pn(e,'"update:"+('+n+")",s,null,!1,0,c[t],!0):(Pn(e,"update:"+k(n),s,null,!1,0,c[t]),T(n)!==k(n)&&Pn(e,"update:"+T(n),s,null,!1,0,c[t])))),a&&a.prop||!e.component&&Go(e.tag,e.attrsMap.type,n)?On(e,n,o,c[t],l):Nn(e,n,o,c[t],l);else if(Ko.test(n))n=n.replace(Ko,""),(l=Qo.test(n))&&(n=n.slice(1,-1)),Pn(e,n,o,a,!1,0,c[t],l);else{var u=(n=n.replace(Xo,"")).match(ea),f=u&&u[1];l=!1,f&&(n=n.slice(0,-(f.length+1)),Qo.test(f)&&(f=f.slice(1,-1),l=!0)),Dn(e,n,i,o,f,l,a,c[t])}else Nn(e,n,JSON.stringify(o),c[t]),!e.component&&"muted"===n&&Go(e.tag,e.attrsMap.type,n)&&On(e,n,"true",c[t])}(e),e}function ua(e){var t;if(t=Rn(e,"v-for")){var r=function(e){var t=e.match(Yo);if(t){var r={};r.for=t[2].trim();var n=t[1].trim().replace(Jo,""),i=n.match(Zo);return i?(r.alias=n.replace(Zo,"").trim(),r.iterator1=i[1].trim(),i[2]&&(r.iterator2=i[2].trim())):r.alias=n,r}}(t);r&&A(e,r)}}function fa(e,t){e.ifConditions||(e.ifConditions=[]),e.ifConditions.push(t)}function da(e){var t=e.name.replace(na,"");return t||"#"!==e.name[0]&&(t="default"),Qo.test(t)?{name:t.slice(1,-1),dynamic:!0}:{name:'"'+t+'"',dynamic:!1}}function pa(e){var t=e.match(ra);if(t){var r={};return t.forEach(function(e){r[e.slice(1)]=!0}),r}}var ha=/^xmlns:NS\d+/,ma=/^NS\d+:/;function ga(e){return la(e.tag,e.attrsList.slice(),e.parent)}var va,ya,ba=[vo,yo,{preTransformNode:function(e,t){if("input"===e.tag){var r,n=e.attrsMap;if(!n["v-model"])return;if((n[":type"]||n["v-bind:type"])&&(r=zn(e,"type")),n.type||r||!n["v-bind"]||(r="("+n["v-bind"]+").type"),r){var i=Rn(e,"v-if",!0),o=i?"&&("+i+")":"",a=null!=Rn(e,"v-else",!0),s=Rn(e,"v-else-if",!0),l=ga(e);ua(l),En(l,"type","checkbox"),ca(l,t),l.processed=!0,l.if="("+r+")==='checkbox'"+o,fa(l,{exp:l.if,block:l});var c=ga(e);Rn(c,"v-for",!0),En(c,"type","radio"),ca(c,t),fa(l,{exp:"("+r+")==='radio'"+o,block:c});var u=ga(e);return Rn(u,"v-for",!0),En(u,":type",r),ca(u,t),fa(l,{exp:i,block:u}),a?l.else=!0:s&&(l.elseif=s),l}}}}],wa={expectHTML:!0,modules:ba,directives:{model:function(e,t,r){var n=t.value,i=t.modifiers,o=e.tag,a=e.attrsMap.type;if(e.component)return jn(e,n,i),!1;if("select"===o)!function(e,t,r){var n='var $$selectedVal = Array.prototype.filter.call($event.target.options,function(o){return o.selected}).map(function(o){var val = "_value" in o ? o._value : o.value;return '+(i&&i.number?"_n(val)":"val")+"});";Pn(e,"change",n=n+" "+Wn(t,"$event.target.multiple ? $$selectedVal : $$selectedVal[0]"),null,!0)}(e,n);else if("input"===o&&"checkbox"===a)!function(e,t,r){var n=r&&r.number,i=zn(e,"value")||"null",o=zn(e,"true-value")||"true",a=zn(e,"false-value")||"false";On(e,"checked","Array.isArray("+t+")?_i("+t+","+i+")>-1"+("true"===o?":("+t+")":":_q("+t+","+o+")")),Pn(e,"change","var $$a="+t+",$$el=$event.target,$$c=$$el.checked?("+o+"):("+a+");if(Array.isArray($$a)){var $$v="+(n?"_n("+i+")":i)+",$$i=_i($$a,$$v);if($$el.checked){$$i<0&&("+Wn(t,"$$a.concat([$$v])")+")}else{$$i>-1&&("+Wn(t,"$$a.slice(0,$$i).concat($$a.slice($$i+1))")+")}}else{"+Wn(t,"$$c")+"}",null,!0)}(e,n,i);else if("input"===o&&"radio"===a)!function(e,t,r){var n=r&&r.number,i=zn(e,"value")||"null";On(e,"checked","_q("+t+","+(i=n?"_n("+i+")":i)+")"),Pn(e,"change",Wn(t,i),null,!0)}(e,n,i);else if("input"===o||"textarea"===o)!function(e,t,r){var n=e.attrsMap.type,i=r||{},o=i.lazy,a=i.number,s=i.trim,l=!o&&"range"!==n,c=o?"change":"range"===n?Kn:"input",u="$event.target.value";s&&(u="$event.target.value.trim()"),a&&(u="_n("+u+")");var f=Wn(t,u);l&&(f="if($event.target.composing)return;"+f),On(e,"value","("+t+")"),Pn(e,c,f,null,!0),(s||a)&&Pn(e,"blur","$forceUpdate()")}(e,n,i);else if(!j.isReservedTag(o))return jn(e,n,i),!1;return!0},text:function(e,t){t.value&&On(e,"textContent","_s("+t.value+")",t)},html:function(e,t){t.value&&On(e,"innerHTML","_s("+t.value+")",t)}},isPreTag:function(e){return"pre"===e},isUnaryTag:bo,mustUseProp:Er,canBeLeftOpenTag:wo,isReservedTag:Vr,getTagNamespace:Kr,staticKeys:ba.reduce(function(e,t){return e.concat(t.staticKeys||[])},[]).join(",")},xa=x(function(e){return m("type,tag,attrsList,attrsMap,plain,parent,children,attrs,start,end,rawAttrsMap"+(e?","+e:""))});var _a=/^([\w$_]+|\([^)]*?\))\s*=>|^function\s*(?:[\w$]+)?\s*\(/,ka=/\([^)]*?\);*$/,Ca=/^[A-Za-z_$][\w$]*(?:\.[A-Za-z_$][\w$]*|\['[^']*?']|\["[^"]*?"]|\[\d+]|\[[A-Za-z_$][\w$]*])*$/,Sa={esc:27,tab:9,enter:13,space:32,up:38,left:37,right:39,down:40,delete:[8,46]},Ta={esc:["Esc","Escape"],tab:"Tab",enter:"Enter",space:[" ","Spacebar"],up:["Up","ArrowUp"],left:["Left","ArrowLeft"],right:["Right","ArrowRight"],down:["Down","ArrowDown"],delete:["Backspace","Delete","Del"]},Ma=function(e){return"if("+e+")return null;"},La={stop:"$event.stopPropagation();",prevent:"$event.preventDefault();",self:Ma("$event.target !== $event.currentTarget"),ctrl:Ma("!$event.ctrlKey"),shift:Ma("!$event.shiftKey"),alt:Ma("!$event.altKey"),meta:Ma("!$event.metaKey"),left:Ma("'button' in $event && $event.button !== 0"),middle:Ma("'button' in $event && $event.button !== 1"),right:Ma("'button' in $event && $event.button !== 2")};function Aa(e,t){var r=t?"nativeOn:":"on:",n="",i="";for(var o in e){var a=Oa(e[o]);e[o]&&e[o].dynamic?i+=o+","+a+",":n+='"'+o+'":'+a+","}return n="{"+n.slice(0,-1)+"}",i?r+"_d("+n+",["+i.slice(0,-1)+"])":r+n}function Oa(e){if(!e)return"function(){}";if(Array.isArray(e))return"["+e.map(function(e){return Oa(e)}).join(",")+"]";var t=Ca.test(e.value),r=_a.test(e.value),n=Ca.test(e.value.replace(ka,""));if(e.modifiers){var i="",o="",a=[];for(var s in e.modifiers)if(La[s])o+=La[s],Sa[s]&&a.push(s);else if("exact"===s){var l=e.modifiers;o+=Ma(["ctrl","shift","alt","meta"].filter(function(e){return!l[e]}).map(function(e){return"$event."+e+"Key"}).join("||"))}else a.push(s);return a.length&&(i+="if(!$event.type.indexOf('key')&&"+a.map(Na).join("&&")+")return null;"),o&&(i+=o),"function($event){"+i+(t?"return "+e.value+"($event)":r?"return ("+e.value+")($event)":n?"return "+e.value:e.value)+"}"}return t||r?e.value:"function($event){"+(n?"return "+e.value:e.value)+"}"}function Na(e){var t=parseInt(e,10);if(t)return"$event.keyCode!=="+t;var r=Sa[e],n=Ta[e];return"_k($event.keyCode,"+JSON.stringify(e)+","+JSON.stringify(r)+",$event.key,"+JSON.stringify(n)+")"}var Ea={on:function(e,t){e.wrapListeners=function(e){return"_g("+e+","+t.value+")"}},bind:function(e,t){e.wrapData=function(r){return"_b("+r+",'"+e.tag+"',"+t.value+","+(t.modifiers&&t.modifiers.prop?"true":"false")+(t.modifiers&&t.modifiers.sync?",true":"")+")"}},cloak:N},Da=function(e){this.options=e,this.warn=e.warn||Ln,this.transforms=An(e.modules,"transformCode"),this.dataGenFns=An(e.modules,"genData"),this.directives=A(A({},Ea),e.directives);var t=e.isReservedTag||E;this.maybeComponent=function(e){return!!e.component||!t(e.tag)},this.onceId=0,this.staticRenderFns=[],this.pre=!1};function Ia(e,t){var r=new Da(t);return{render:"with(this){return "+(e?Pa(e,r):'_c("div")')+"}",staticRenderFns:r.staticRenderFns}}function Pa(e,t){if(e.parent&&(e.pre=e.pre||e.parent.pre),e.staticRoot&&!e.staticProcessed)return za(e,t);if(e.once&&!e.onceProcessed)return Ra(e,t);if(e.for&&!e.forProcessed)return Fa(e,t);if(e.if&&!e.ifProcessed)return $a(e,t);if("template"!==e.tag||e.slotTarget||t.pre){if("slot"===e.tag)return function(e,t){var r=e.slotName||'"default"',n=Ha(e,t),i="_t("+r+(n?","+n:""),o=e.attrs||e.dynamicAttrs?Ga((e.attrs||[]).concat(e.dynamicAttrs||[]).map(function(e){return{name:k(e.name),value:e.value,dynamic:e.dynamic}})):null,a=e.attrsMap["v-bind"];return!o&&!a||n||(i+=",null"),o&&(i+=","+o),a&&(i+=(o?"":",null")+","+a),i+")"}(e,t);var r;if(e.component)r=function(e,t,r){var n=t.inlineTemplate?null:Ha(t,r,!0);return"_c("+e+","+ja(t,r)+(n?","+n:"")+")"}(e.component,e,t);else{var n;(!e.plain||e.pre&&t.maybeComponent(e))&&(n=ja(e,t));var i=e.inlineTemplate?null:Ha(e,t,!0);r="_c('"+e.tag+"'"+(n?","+n:"")+(i?","+i:"")+")"}for(var o=0;o<t.transforms.length;o++)r=t.transforms[o](e,r);return r}return Ha(e,t)||"void 0"}function za(e,t){e.staticProcessed=!0;var r=t.pre;return e.pre&&(t.pre=e.pre),t.staticRenderFns.push("with(this){return "+Pa(e,t)+"}"),t.pre=r,"_m("+(t.staticRenderFns.length-1)+(e.staticInFor?",true":"")+")"}function Ra(e,t){if(e.onceProcessed=!0,e.if&&!e.ifProcessed)return $a(e,t);if(e.staticInFor){for(var r="",n=e.parent;n;){if(n.for){r=n.key;break}n=n.parent}return r?"_o("+Pa(e,t)+","+t.onceId+++","+r+")":Pa(e,t)}return za(e,t)}function $a(e,t,r,n){return e.ifProcessed=!0,function e(t,r,n,i){if(!t.length)return i||"_e()";var o=t.shift();return o.exp?"("+o.exp+")?"+a(o.block)+":"+e(t,r,n,i):""+a(o.block);function a(e){return n?n(e,r):e.once?Ra(e,r):Pa(e,r)}}(e.ifConditions.slice(),t,r,n)}function Fa(e,t,r,n){var i=e.for,o=e.alias,a=e.iterator1?","+e.iterator1:"",s=e.iterator2?","+e.iterator2:"";return e.forProcessed=!0,(n||"_l")+"(("+i+"),function("+o+a+s+"){return "+(r||Pa)(e,t)+"})"}function ja(e,t){var r="{",n=function(e,t){var r=e.directives;if(r){var n,i,o,a,s="directives:[",l=!1;for(n=0,i=r.length;n<i;n++){o=r[n],a=!0;var c=t.directives[o.name];c&&(a=!!c(e,o,t.warn)),a&&(l=!0,s+='{name:"'+o.name+'",rawName:"'+o.rawName+'"'+(o.value?",value:("+o.value+"),expression:"+JSON.stringify(o.value):"")+(o.arg?",arg:"+(o.isDynamicArg?o.arg:'"'+o.arg+'"'):"")+(o.modifiers?",modifiers:"+JSON.stringify(o.modifiers):"")+"},")}return l?s.slice(0,-1)+"]":void 0}}(e,t);n&&(r+=n+","),e.key&&(r+="key:"+e.key+","),e.ref&&(r+="ref:"+e.ref+","),e.refInFor&&(r+="refInFor:true,"),e.pre&&(r+="pre:true,"),e.component&&(r+='tag:"'+e.tag+'",');for(var i=0;i<t.dataGenFns.length;i++)r+=t.dataGenFns[i](e);if(e.attrs&&(r+="attrs:"+Ga(e.attrs)+","),e.props&&(r+="domProps:"+Ga(e.props)+","),e.events&&(r+=Aa(e.events,!1)+","),e.nativeEvents&&(r+=Aa(e.nativeEvents,!0)+","),e.slotTarget&&!e.slotScope&&(r+="slot:"+e.slotTarget+","),e.scopedSlots&&(r+=function(e,t,r){var n=e.for||Object.keys(t).some(function(e){var r=t[e];return r.slotTargetDynamic||r.if||r.for||Wa(r)}),i=!!e.if;if(!n)for(var o=e.parent;o;){if(o.slotScope&&o.slotScope!==sa||o.for){n=!0;break}o.if&&(i=!0),o=o.parent}var a=Object.keys(t).map(function(e){return qa(t[e],r)}).join(",");return"scopedSlots:_u(["+a+"]"+(n?",null,true":"")+(!n&&i?",null,false,"+function(e){for(var t=5381,r=e.length;r;)t=33*t^e.charCodeAt(--r);return t>>>0}(a):"")+")"}(e,e.scopedSlots,t)+","),e.model&&(r+="model:{value:"+e.model.value+",callback:"+e.model.callback+",expression:"+e.model.expression+"},"),e.inlineTemplate){var o=function(e,t){var r=e.children[0];if(r&&1===r.type){var n=Ia(r,t.options);return"inlineTemplate:{render:function(){"+n.render+"},staticRenderFns:["+n.staticRenderFns.map(function(e){return"function(){"+e+"}"}).join(",")+"]}"}}(e,t);o&&(r+=o+",")}return r=r.replace(/,$/,"")+"}",e.dynamicAttrs&&(r="_b("+r+',"'+e.tag+'",'+Ga(e.dynamicAttrs)+")"),e.wrapData&&(r=e.wrapData(r)),e.wrapListeners&&(r=e.wrapListeners(r)),r}function Wa(e){return 1===e.type&&("slot"===e.tag||e.children.some(Wa))}function qa(e,t){var r=e.attrsMap["slot-scope"];if(e.if&&!e.ifProcessed&&!r)return $a(e,t,qa,"null");if(e.for&&!e.forProcessed)return Fa(e,t,qa);var n=e.slotScope===sa?"":String(e.slotScope),i="function("+n+"){return "+("template"===e.tag?e.if&&r?"("+e.if+")?"+(Ha(e,t)||"undefined")+":undefined":Ha(e,t)||"undefined":Pa(e,t))+"}",o=n?"":",proxy:true";return"{key:"+(e.slotTarget||'"default"')+",fn:"+i+o+"}"}function Ha(e,t,r,n,i){var o=e.children;if(o.length){var a=o[0];if(1===o.length&&a.for&&"template"!==a.tag&&"slot"!==a.tag){var s=r?t.maybeComponent(a)?",1":",0":"";return""+(n||Pa)(a,t)+s}var l=r?function(e,t){for(var r=0,n=0;n<e.length;n++){var i=e[n];if(1===i.type){if(Ua(i)||i.ifConditions&&i.ifConditions.some(function(e){return Ua(e.block)})){r=2;break}(t(i)||i.ifConditions&&i.ifConditions.some(function(e){return t(e.block)}))&&(r=1)}}return r}(o,t.maybeComponent):0,c=i||Ba;return"["+o.map(function(e){return c(e,t)}).join(",")+"]"+(l?","+l:"")}}function Ua(e){return void 0!==e.for||"template"===e.tag||"slot"===e.tag}function Ba(e,t){return 1===e.type?Pa(e,t):3===e.type&&e.isComment?(n=e,"_e("+JSON.stringify(n.text)+")"):"_v("+(2===(r=e).type?r.expression:Va(JSON.stringify(r.text)))+")";var r,n}function Ga(e){for(var t="",r="",n=0;n<e.length;n++){var i=e[n],o=Va(i.value);i.dynamic?r+=i.name+","+o+",":t+='"'+i.name+'":'+o+","}return t="{"+t.slice(0,-1)+"}",r?"_d("+t+",["+r.slice(0,-1)+"])":t}function Va(e){return e.replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029")}function Ka(e,t){try{return new Function(e)}catch(r){return t.push({err:r,code:e}),N}}new RegExp("\\b"+"do,if,for,let,new,try,var,case,else,with,await,break,catch,class,const,super,throw,while,yield,delete,export,import,return,switch,default,extends,finally,continue,debugger,function,arguments".split(",").join("\\b|\\b")+"\\b");var Xa,Ya,Za=(Xa=function(e,t){var r=function(e,t){jo=t.warn||Ln,Bo=t.isPreTag||E,Go=t.mustUseProp||E,Vo=t.getTagNamespace||E,t.isReservedTag,qo=An(t.modules,"transformNode"),Ho=An(t.modules,"preTransformNode"),Uo=An(t.modules,"postTransformNode"),Wo=t.delimiters;var r,n,i=[],o=!1!==t.preserveWhitespace,a=t.whitespace,s=!1,l=!1;function c(e){if(u(e),s||e.processed||(e=ca(e,t)),i.length||e===r||r.if&&(e.elseif||e.else)&&fa(r,{exp:e.elseif,block:e}),n&&!e.forbidden)if(e.elseif||e.else)a=e,(c=function(e){for(var t=e.length;t--;){if(1===e[t].type)return e[t];e.pop()}}(n.children))&&c.if&&fa(c,{exp:a.elseif,block:a});else{if(e.slotScope){var o=e.slotTarget||'"default"';(n.scopedSlots||(n.scopedSlots={}))[o]=e}n.children.push(e),e.parent=n}var a,c;e.children=e.children.filter(function(e){return!e.slotScope}),u(e),e.pre&&(s=!1),Bo(e.tag)&&(l=!1);for(var f=0;f<Uo.length;f++)Uo[f](e,t)}function u(e){if(!l)for(var t;(t=e.children[e.children.length-1])&&3===t.type&&" "===t.text;)e.children.pop()}return function(e,t){for(var r,n,i=[],o=t.expectHTML,a=t.isUnaryTag||E,s=t.canBeLeftOpenTag||E,l=0;e;){if(r=e,n&&Eo(n)){var c=0,u=n.toLowerCase(),f=Do[u]||(Do[u]=new RegExp("([\\s\\S]*?)(</"+u+"[^>]*>)","i")),d=e.replace(f,function(e,r,n){return c=n.length,Eo(u)||"noscript"===u||(r=r.replace(/<!\--([\s\S]*?)-->/g,"$1").replace(/<!\[CDATA\[([\s\S]*?)]]>/g,"$1")),$o(u,r)&&(r=r.slice(1)),t.chars&&t.chars(r),""});l+=e.length-d.length,e=d,T(u,l-c,l)}else{var p=e.indexOf("<");if(0===p){if(Oo.test(e)){var h=e.indexOf("--\x3e");if(h>=0){t.shouldKeepComment&&t.comment(e.substring(4,h),l,l+h+3),k(h+3);continue}}if(No.test(e)){var m=e.indexOf("]>");if(m>=0){k(m+2);continue}}var g=e.match(Ao);if(g){k(g[0].length);continue}var v=e.match(Lo);if(v){var y=l;k(v[0].length),T(v[1],y,l);continue}var b=C();if(b){S(b),$o(b.tagName,e)&&k(1);continue}}var w=void 0,x=void 0,_=void 0;if(p>=0){for(x=e.slice(p);!(Lo.test(x)||To.test(x)||Oo.test(x)||No.test(x)||(_=x.indexOf("<",1))<0);)p+=_,x=e.slice(p);w=e.substring(0,p)}p<0&&(w=e),w&&k(w.length),t.chars&&w&&t.chars(w,l-w.length,l)}if(e===r){t.chars&&t.chars(e);break}}function k(t){l+=t,e=e.substring(t)}function C(){var t=e.match(To);if(t){var r,n,i={tagName:t[1],attrs:[],start:l};for(k(t[0].length);!(r=e.match(Mo))&&(n=e.match(ko)||e.match(_o));)n.start=l,k(n[0].length),n.end=l,i.attrs.push(n);if(r)return i.unarySlash=r[1],k(r[0].length),i.end=l,i}}function S(e){var r=e.tagName,l=e.unarySlash;o&&("p"===n&&xo(r)&&T(n),s(r)&&n===r&&T(r));for(var c=a(r)||!!l,u=e.attrs.length,f=new Array(u),d=0;d<u;d++){var p=e.attrs[d],h=p[3]||p[4]||p[5]||"",m="a"===r&&"href"===p[1]?t.shouldDecodeNewlinesForHref:t.shouldDecodeNewlines;f[d]={name:p[1],value:Fo(h,m)}}c||(i.push({tag:r,lowerCasedTag:r.toLowerCase(),attrs:f,start:e.start,end:e.end}),n=r),t.start&&t.start(r,f,c,e.start,e.end)}function T(e,r,o){var a,s;if(null==r&&(r=l),null==o&&(o=l),e)for(s=e.toLowerCase(),a=i.length-1;a>=0&&i[a].lowerCasedTag!==s;a--);else a=0;if(a>=0){for(var c=i.length-1;c>=a;c--)t.end&&t.end(i[c].tag,r,o);i.length=a,n=a&&i[a-1].tag}else"br"===s?t.start&&t.start(e,[],!0,r,o):"p"===s&&(t.start&&t.start(e,[],!1,r,o),t.end&&t.end(e,r,o))}T()}(e,{warn:jo,expectHTML:t.expectHTML,isUnaryTag:t.isUnaryTag,canBeLeftOpenTag:t.canBeLeftOpenTag,shouldDecodeNewlines:t.shouldDecodeNewlines,shouldDecodeNewlinesForHref:t.shouldDecodeNewlinesForHref,shouldKeepComment:t.comments,outputSourceRange:t.outputSourceRange,start:function(e,o,a,u,f){var d=n&&n.ns||Vo(e);Y&&"svg"===d&&(o=function(e){for(var t=[],r=0;r<e.length;r++){var n=e[r];ha.test(n.name)||(n.name=n.name.replace(ma,""),t.push(n))}return t}(o));var p,h=la(e,o,n);d&&(h.ns=d),"style"!==(p=h).tag&&("script"!==p.tag||p.attrsMap.type&&"text/javascript"!==p.attrsMap.type)||ie()||(h.forbidden=!0);for(var m=0;m<Ho.length;m++)h=Ho[m](h,t)||h;s||(function(e){null!=Rn(e,"v-pre")&&(e.pre=!0)}(h),h.pre&&(s=!0)),Bo(h.tag)&&(l=!0),s?function(e){var t=e.attrsList,r=t.length;if(r)for(var n=e.attrs=new Array(r),i=0;i<r;i++)n[i]={name:t[i].name,value:JSON.stringify(t[i].value)},null!=t[i].start&&(n[i].start=t[i].start,n[i].end=t[i].end);else e.pre||(e.plain=!0)}(h):h.processed||(ua(h),function(e){var t=Rn(e,"v-if");if(t)e.if=t,fa(e,{exp:t,block:e});else{null!=Rn(e,"v-else")&&(e.else=!0);var r=Rn(e,"v-else-if");r&&(e.elseif=r)}}(h),function(e){null!=Rn(e,"v-once")&&(e.once=!0)}(h)),r||(r=h),a?c(h):(n=h,i.push(h))},end:function(e,t,r){var o=i[i.length-1];i.length-=1,n=i[i.length-1],c(o)},chars:function(e,t,r){if(n&&(!Y||"textarea"!==n.tag||n.attrsMap.placeholder!==e)){var i,c,u,f=n.children;(e=l||e.trim()?"script"===(i=n).tag||"style"===i.tag?e:aa(e):f.length?a?"condense"===a&&ia.test(e)?"":" ":o?" ":"":"")&&(l||"condense"!==a||(e=e.replace(oa," ")),!s&&" "!==e&&(c=function(e,t){var r=Wo?go(Wo):ho;if(r.test(e)){for(var n,i,o,a=[],s=[],l=r.lastIndex=0;n=r.exec(e);){(i=n.index)>l&&(s.push(o=e.slice(l,i)),a.push(JSON.stringify(o)));var c=Tn(n[1].trim());a.push("_s("+c+")"),s.push({"@binding":c}),l=i+n[0].length}return l<e.length&&(s.push(o=e.slice(l)),a.push(JSON.stringify(o))),{expression:a.join("+"),tokens:s}}}(e))?u={type:2,expression:c.expression,tokens:c.tokens,text:e}:" "===e&&f.length&&" "===f[f.length-1].text||(u={type:3,text:e}),u&&f.push(u))}},comment:function(e,t,r){if(n){var i={type:3,text:e,isComment:!0};n.children.push(i)}}}),r}(e.trim(),t);!1!==t.optimize&&function(e,t){e&&(va=xa(t.staticKeys||""),ya=t.isReservedTag||E,function e(t){if(t.static=function(e){return 2!==e.type&&(3===e.type||!(!e.pre&&(e.hasBindings||e.if||e.for||g(e.tag)||!ya(e.tag)||function(e){for(;e.parent;){if("template"!==(e=e.parent).tag)return!1;if(e.for)return!0}return!1}(e)||!Object.keys(e).every(va))))}(t),1===t.type){if(!ya(t.tag)&&"slot"!==t.tag&&null==t.attrsMap["inline-template"])return;for(var r=0,n=t.children.length;r<n;r++){var i=t.children[r];e(i),i.static||(t.static=!1)}if(t.ifConditions)for(var o=1,a=t.ifConditions.length;o<a;o++){var s=t.ifConditions[o].block;e(s),s.static||(t.static=!1)}}}(e),function e(t,r){if(1===t.type){if((t.static||t.once)&&(t.staticInFor=r),t.static&&t.children.length&&(1!==t.children.length||3!==t.children[0].type))return void(t.staticRoot=!0);if(t.staticRoot=!1,t.children)for(var n=0,i=t.children.length;n<i;n++)e(t.children[n],r||!!t.for);if(t.ifConditions)for(var o=1,a=t.ifConditions.length;o<a;o++)e(t.ifConditions[o].block,r)}}(e,!1))}(r,t);var n=Ia(r,t);return{ast:r,render:n.render,staticRenderFns:n.staticRenderFns}},function(e){function t(t,r){var n=Object.create(e),i=[],o=[];if(r)for(var a in r.modules&&(n.modules=(e.modules||[]).concat(r.modules)),r.directives&&(n.directives=A(Object.create(e.directives||null),r.directives)),r)"modules"!==a&&"directives"!==a&&(n[a]=r[a]);n.warn=function(e,t,r){(r?o:i).push(e)};var s=Xa(t.trim(),n);return s.errors=i,s.tips=o,s}return{compile:t,compileToFunctions:function(e){var t=Object.create(null);return function(r,n,i){(n=A({},n)).warn,delete n.warn;var o=n.delimiters?String(n.delimiters)+r:r;if(t[o])return t[o];var a=e(r,n),s={},l=[];return s.render=Ka(a.render,l),s.staticRenderFns=a.staticRenderFns.map(function(e){return Ka(e,l)}),t[o]=s}}(t)}})(wa),Ja=(Za.compile,Za.compileToFunctions);function Qa(e){return(Ya=Ya||document.createElement("div")).innerHTML=e?'<a href="\n"/>':'<div a="\n"/>',Ya.innerHTML.indexOf("&#10;")>0}var es=!!G&&Qa(!1),ts=!!G&&Qa(!0),rs=x(function(e){var t=Zr(e);return t&&t.innerHTML}),ns=kr.prototype.$mount;kr.prototype.$mount=function(e,t){if((e=e&&Zr(e))===document.body||e===document.documentElement)return this;var r=this.$options;if(!r.render){var n=r.template;if(n)if("string"==typeof n)"#"===n.charAt(0)&&(n=rs(n));else{if(!n.nodeType)return this;n=n.innerHTML}else e&&(n=function(e){if(e.outerHTML)return e.outerHTML;var t=document.createElement("div");return t.appendChild(e.cloneNode(!0)),t.innerHTML}(e));if(n){var i=Ja(n,{outputSourceRange:!1,shouldDecodeNewlines:es,shouldDecodeNewlinesForHref:ts,delimiters:r.delimiters,comments:r.comments},this),o=i.render,a=i.staticRenderFns;r.render=o,r.staticRenderFns=a}}return ns.call(this,e,t)},kr.compile=Ja,e.exports=kr}).call(t,r("DuR2"),r("162o").setImmediate)},c6RA:function(e,t,r){(function(e){"use strict";function t(e){for(var t={},r=e.split(" "),n=0;n<r.length;++n)t[r[n]]=!0;return t}function r(e,t,i){return 0==e.length?n(t):function(o,a){for(var s=e[0],l=0;l<s.length;l++)if(o.match(s[l][0]))return a.tokenize=r(e.slice(1),t),s[l][1];return a.tokenize=n(t,i),"string"}}function n(e,t){return function(n,i){return function(e,t,n,i){if(!1!==i&&e.match("${",!1)||e.match("{$",!1))return t.tokenize=null,"string";if(!1!==i&&e.match(/^\$[a-zA-Z_][a-zA-Z0-9_]*/))return e.match("[",!1)&&(t.tokenize=r([[["[",null]],[[/\d[\w\.]*/,"number"],[/\$[a-zA-Z_][a-zA-Z0-9_]*/,"variable-2"],[/[\w\$]+/,"variable"]],[["]",null]]],n,i)),e.match(/\-\>\w/,!1)&&(t.tokenize=r([[["->",null]],[[/[\w]+/,"variable"]]],n,i)),"variable-2";var o=!1;for(;!e.eol()&&(o||!1===i||!e.match("{$",!1)&&!e.match(/^(\$[a-zA-Z_][a-zA-Z0-9_]*|\$\{)/,!1));){if(!o&&e.match(n)){t.tokenize=null,t.tokStack.pop(),t.tokStack.pop();break}o="\\"==e.next()&&!o}return"string"}(n,i,e,t)}}var i="abstract and array as break case catch class clone const continue declare default do else elseif enddeclare endfor endforeach endif endswitch endwhile extends final for foreach function global goto if implements interface instanceof namespace new or private protected public static switch throw trait try use var while xor die echo empty exit eval include include_once isset list require require_once return print unset __halt_compiler self static parent yield insteadof finally",o="true false null TRUE FALSE NULL __CLASS__ __DIR__ __FILE__ __LINE__ __METHOD__ __FUNCTION__ __NAMESPACE__ __TRAIT__",a="func_num_args func_get_arg func_get_args strlen strcmp strncmp strcasecmp strncasecmp each error_reporting define defined trigger_error user_error set_error_handler restore_error_handler get_declared_classes get_loaded_extensions extension_loaded get_extension_funcs debug_backtrace constant bin2hex hex2bin sleep usleep time mktime gmmktime strftime gmstrftime strtotime date gmdate getdate localtime checkdate flush wordwrap htmlspecialchars htmlentities html_entity_decode md5 md5_file crc32 getimagesize image_type_to_mime_type phpinfo phpversion phpcredits strnatcmp strnatcasecmp substr_count strspn strcspn strtok strtoupper strtolower strpos strrpos strrev hebrev hebrevc nl2br basename dirname pathinfo stripslashes stripcslashes strstr stristr strrchr str_shuffle str_word_count strcoll substr substr_replace quotemeta ucfirst ucwords strtr addslashes addcslashes rtrim str_replace str_repeat count_chars chunk_split trim ltrim strip_tags similar_text explode implode setlocale localeconv parse_str str_pad chop strchr sprintf printf vprintf vsprintf sscanf fscanf parse_url urlencode urldecode rawurlencode rawurldecode readlink linkinfo link unlink exec system escapeshellcmd escapeshellarg passthru shell_exec proc_open proc_close rand srand getrandmax mt_rand mt_srand mt_getrandmax base64_decode base64_encode abs ceil floor round is_finite is_nan is_infinite bindec hexdec octdec decbin decoct dechex base_convert number_format fmod ip2long long2ip getenv putenv getopt microtime gettimeofday getrusage uniqid quoted_printable_decode set_time_limit get_cfg_var magic_quotes_runtime set_magic_quotes_runtime get_magic_quotes_gpc get_magic_quotes_runtime import_request_variables error_log serialize unserialize memory_get_usage var_dump var_export debug_zval_dump print_r highlight_file show_source highlight_string ini_get ini_get_all ini_set ini_alter ini_restore get_include_path set_include_path restore_include_path setcookie header headers_sent connection_aborted connection_status ignore_user_abort parse_ini_file is_uploaded_file move_uploaded_file intval floatval doubleval strval gettype settype is_null is_resource is_bool is_long is_float is_int is_integer is_double is_real is_numeric is_string is_array is_object is_scalar ereg ereg_replace eregi eregi_replace split spliti join sql_regcase dl pclose popen readfile rewind rmdir umask fclose feof fgetc fgets fgetss fread fopen fpassthru ftruncate fstat fseek ftell fflush fwrite fputs mkdir rename copy tempnam tmpfile file file_get_contents file_put_contents stream_select stream_context_create stream_context_set_params stream_context_set_option stream_context_get_options stream_filter_prepend stream_filter_append fgetcsv flock get_meta_tags stream_set_write_buffer set_file_buffer set_socket_blocking stream_set_blocking socket_set_blocking stream_get_meta_data stream_register_wrapper stream_wrapper_register stream_set_timeout socket_set_timeout socket_get_status realpath fnmatch fsockopen pfsockopen pack unpack get_browser crypt opendir closedir chdir getcwd rewinddir readdir dir glob fileatime filectime filegroup fileinode filemtime fileowner fileperms filesize filetype file_exists is_writable is_writeable is_readable is_executable is_file is_dir is_link stat lstat chown touch clearstatcache mail ob_start ob_flush ob_clean ob_end_flush ob_end_clean ob_get_flush ob_get_clean ob_get_length ob_get_level ob_get_status ob_get_contents ob_implicit_flush ob_list_handlers ksort krsort natsort natcasesort asort arsort sort rsort usort uasort uksort shuffle array_walk count end prev next reset current key min max in_array array_search extract compact array_fill range array_multisort array_push array_pop array_shift array_unshift array_splice array_slice array_merge array_merge_recursive array_keys array_values array_count_values array_reverse array_reduce array_pad array_flip array_change_key_case array_rand array_unique array_intersect array_intersect_assoc array_diff array_diff_assoc array_sum array_filter array_map array_chunk array_key_exists array_intersect_key array_combine array_column pos sizeof key_exists assert assert_options version_compare ftok str_rot13 aggregate session_name session_module_name session_save_path session_id session_regenerate_id session_decode session_register session_unregister session_is_registered session_encode session_start session_destroy session_unset session_set_save_handler session_cache_limiter session_cache_expire session_set_cookie_params session_get_cookie_params session_write_close preg_match preg_match_all preg_replace preg_replace_callback preg_split preg_quote preg_grep overload ctype_alnum ctype_alpha ctype_cntrl ctype_digit ctype_lower ctype_graph ctype_print ctype_punct ctype_space ctype_upper ctype_xdigit virtual apache_request_headers apache_note apache_lookup_uri apache_child_terminate apache_setenv apache_response_headers apache_get_version getallheaders mysql_connect mysql_pconnect mysql_close mysql_select_db mysql_create_db mysql_drop_db mysql_query mysql_unbuffered_query mysql_db_query mysql_list_dbs mysql_list_tables mysql_list_fields mysql_list_processes mysql_error mysql_errno mysql_affected_rows mysql_insert_id mysql_result mysql_num_rows mysql_num_fields mysql_fetch_row mysql_fetch_array mysql_fetch_assoc mysql_fetch_object mysql_data_seek mysql_fetch_lengths mysql_fetch_field mysql_field_seek mysql_free_result mysql_field_name mysql_field_table mysql_field_len mysql_field_type mysql_field_flags mysql_escape_string mysql_real_escape_string mysql_stat mysql_thread_id mysql_client_encoding mysql_get_client_info mysql_get_host_info mysql_get_proto_info mysql_get_server_info mysql_info mysql mysql_fieldname mysql_fieldtable mysql_fieldlen mysql_fieldtype mysql_fieldflags mysql_selectdb mysql_createdb mysql_dropdb mysql_freeresult mysql_numfields mysql_numrows mysql_listdbs mysql_listtables mysql_listfields mysql_db_name mysql_dbname mysql_tablename mysql_table_name pg_connect pg_pconnect pg_close pg_connection_status pg_connection_busy pg_connection_reset pg_host pg_dbname pg_port pg_tty pg_options pg_ping pg_query pg_send_query pg_cancel_query pg_fetch_result pg_fetch_row pg_fetch_assoc pg_fetch_array pg_fetch_object pg_fetch_all pg_affected_rows pg_get_result pg_result_seek pg_result_status pg_free_result pg_last_oid pg_num_rows pg_num_fields pg_field_name pg_field_num pg_field_size pg_field_type pg_field_prtlen pg_field_is_null pg_get_notify pg_get_pid pg_result_error pg_last_error pg_last_notice pg_put_line pg_end_copy pg_copy_to pg_copy_from pg_trace pg_untrace pg_lo_create pg_lo_unlink pg_lo_open pg_lo_close pg_lo_read pg_lo_write pg_lo_read_all pg_lo_import pg_lo_export pg_lo_seek pg_lo_tell pg_escape_string pg_escape_bytea pg_unescape_bytea pg_client_encoding pg_set_client_encoding pg_meta_data pg_convert pg_insert pg_update pg_delete pg_select pg_exec pg_getlastoid pg_cmdtuples pg_errormessage pg_numrows pg_numfields pg_fieldname pg_fieldsize pg_fieldtype pg_fieldnum pg_fieldprtlen pg_fieldisnull pg_freeresult pg_result pg_loreadall pg_locreate pg_lounlink pg_loopen pg_loclose pg_loread pg_lowrite pg_loimport pg_loexport http_response_code get_declared_traits getimagesizefromstring socket_import_stream stream_set_chunk_size trait_exists header_register_callback class_uses session_status session_register_shutdown echo print global static exit array empty eval isset unset die include require include_once require_once json_decode json_encode json_last_error json_last_error_msg curl_close curl_copy_handle curl_errno curl_error curl_escape curl_exec curl_file_create curl_getinfo curl_init curl_multi_add_handle curl_multi_close curl_multi_exec curl_multi_getcontent curl_multi_info_read curl_multi_init curl_multi_remove_handle curl_multi_select curl_multi_setopt curl_multi_strerror curl_pause curl_reset curl_setopt_array curl_setopt curl_share_close curl_share_init curl_share_setopt curl_strerror curl_unescape curl_version mysqli_affected_rows mysqli_autocommit mysqli_change_user mysqli_character_set_name mysqli_close mysqli_commit mysqli_connect_errno mysqli_connect_error mysqli_connect mysqli_data_seek mysqli_debug mysqli_dump_debug_info mysqli_errno mysqli_error_list mysqli_error mysqli_fetch_all mysqli_fetch_array mysqli_fetch_assoc mysqli_fetch_field_direct mysqli_fetch_field mysqli_fetch_fields mysqli_fetch_lengths mysqli_fetch_object mysqli_fetch_row mysqli_field_count mysqli_field_seek mysqli_field_tell mysqli_free_result mysqli_get_charset mysqli_get_client_info mysqli_get_client_stats mysqli_get_client_version mysqli_get_connection_stats mysqli_get_host_info mysqli_get_proto_info mysqli_get_server_info mysqli_get_server_version mysqli_info mysqli_init mysqli_insert_id mysqli_kill mysqli_more_results mysqli_multi_query mysqli_next_result mysqli_num_fields mysqli_num_rows mysqli_options mysqli_ping mysqli_prepare mysqli_query mysqli_real_connect mysqli_real_escape_string mysqli_real_query mysqli_reap_async_query mysqli_refresh mysqli_rollback mysqli_select_db mysqli_set_charset mysqli_set_local_infile_default mysqli_set_local_infile_handler mysqli_sqlstate mysqli_ssl_set mysqli_stat mysqli_stmt_init mysqli_store_result mysqli_thread_id mysqli_thread_safe mysqli_use_result mysqli_warning_count";e.registerHelper("hintWords","php",[i,o,a].join(" ").split(" ")),e.registerHelper("wordChars","php",/[\w$]/);var s={name:"clike",helperType:"php",keywords:t(i),blockKeywords:t("catch do else elseif for foreach if switch try while finally"),defKeywords:t("class function interface namespace trait"),atoms:t(o),builtin:t(a),multiLineStrings:!0,hooks:{$:function(e){return e.eatWhile(/[\w\$_]/),"variable-2"},"<":function(e,t){var r;if(r=e.match(/<<\s*/)){var i=e.eat(/['"]/);e.eatWhile(/[\w\.]/);var o=e.current().slice(r[0].length+(i?2:1));if(i&&e.eat(i),o)return(t.tokStack||(t.tokStack=[])).push(o,0),t.tokenize=n(o,"'"!=i),"string"}return!1},"#":function(e){for(;!e.eol()&&!e.match("?>",!1);)e.next();return"comment"},"/":function(e){if(e.eat("/")){for(;!e.eol()&&!e.match("?>",!1);)e.next();return"comment"}return!1},'"':function(e,t){return(t.tokStack||(t.tokStack=[])).push('"',0),t.tokenize=n('"'),"string"},"{":function(e,t){return t.tokStack&&t.tokStack.length&&t.tokStack[t.tokStack.length-1]++,!1},"}":function(e,t){return t.tokStack&&t.tokStack.length>0&&!--t.tokStack[t.tokStack.length-1]&&(t.tokenize=n(t.tokStack[t.tokStack.length-2])),!1}}};e.defineMode("php",function(t,r){var n=e.getMode(t,r&&r.htmlMode||"text/html"),i=e.getMode(t,s);return{startState:function(){var t=e.startState(n),o=r.startOpen?e.startState(i):null;return{html:t,php:o,curMode:r.startOpen?i:n,curState:r.startOpen?o:t,pending:null}},copyState:function(t){var r,o=t.html,a=e.copyState(n,o),s=t.php,l=s&&e.copyState(i,s);return r=t.curMode==n?a:l,{html:a,php:l,curMode:t.curMode,curState:r,pending:t.pending}},token:function(t,r){var o=r.curMode==i;if(t.sol()&&r.pending&&'"'!=r.pending&&"'"!=r.pending&&(r.pending=null),o)return o&&null==r.php.tokenize&&t.match("?>")?(r.curMode=n,r.curState=r.html,r.php.context.prev||(r.php=null),"meta"):i.token(t,r.curState);if(t.match(/^<\?\w*/))return r.curMode=i,r.php||(r.php=e.startState(i,n.indent(r.html,"",""))),r.curState=r.php,"meta";if('"'==r.pending||"'"==r.pending){for(;!t.eol()&&t.next()!=r.pending;);var a="string"}else r.pending&&t.pos<r.pending.end?(t.pos=r.pending.end,a=r.pending.style):a=n.token(t,r.curState);r.pending&&(r.pending=null);var s,l=t.current(),c=l.search(/<\?/);return-1!=c&&("string"==a&&(s=l.match(/[\'\"]$/))&&!/\?>/.test(l)?r.pending=s[0]:r.pending={end:t.pos,style:a},t.backUp(l.length-c)),a},indent:function(e,t,r){return e.curMode!=i&&/^\s*<\//.test(t)||e.curMode==i&&/^\?>/.test(t)?n.indent(e.html,t,r):e.curMode.indent(e.curState,t,r)},blockCommentStart:"/*",blockCommentEnd:"*/",lineComment:"//",innerMode:function(e){return{state:e.curState,mode:e.curMode}}}},"htmlmixed","clike"),e.defineMIME("application/x-httpd-php","php"),e.defineMIME("application/x-httpd-php-open",{name:"php",startOpen:!0}),e.defineMIME("text/x-php",s)})(r("8U58"),r("8Nur"),r("6S2o"))},cGG2:function(e,t,r){"use strict";var n=r("JP+z"),i=Object.prototype.toString;function o(e){return"[object Array]"===i.call(e)}function a(e){return void 0===e}function s(e){return null!==e&&"object"==typeof e}function l(e){if("[object Object]"!==i.call(e))return!1;var t=Object.getPrototypeOf(e);return null===t||t===Object.prototype}function c(e){return"[object Function]"===i.call(e)}function u(e,t){if(null!==e&&void 0!==e)if("object"!=typeof e&&(e=[e]),o(e))for(var r=0,n=e.length;r<n;r++)t.call(null,e[r],r,e);else for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.call(null,e[i],i,e)}e.exports={isArray:o,isArrayBuffer:function(e){return"[object ArrayBuffer]"===i.call(e)},isBuffer:function(e){return null!==e&&!a(e)&&null!==e.constructor&&!a(e.constructor)&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)},isFormData:function(e){return"undefined"!=typeof FormData&&e instanceof FormData},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:s,isPlainObject:l,isUndefined:a,isDate:function(e){return"[object Date]"===i.call(e)},isFile:function(e){return"[object File]"===i.call(e)},isBlob:function(e){return"[object Blob]"===i.call(e)},isFunction:c,isStream:function(e){return s(e)&&c(e.pipe)},isURLSearchParams:function(e){return"undefined"!=typeof URLSearchParams&&e instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&"undefined"!=typeof window&&"undefined"!=typeof document},forEach:u,merge:function e(){var t={};function r(r,n){l(t[n])&&l(r)?t[n]=e(t[n],r):l(r)?t[n]=e({},r):o(r)?t[n]=r.slice():t[n]=r}for(var n=0,i=arguments.length;n<i;n++)u(arguments[n],r);return t},extend:function(e,t,r){return u(t,function(t,i){e[i]=r&&"function"==typeof t?n(t,r):t}),e},trim:function(e){return e.replace(/^\s*/,"").replace(/\s*$/,"")},stripBOM:function(e){return 65279===e.charCodeAt(0)&&(e=e.slice(1)),e}}},cWxy:function(e,t,r){"use strict";var n=r("dVOP");function i(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise(function(e){t=e});var r=this;e(function(e){r.reason||(r.reason=new n(e),t(r.reason))})}i.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},i.source=function(){var e;return{token:new i(function(t){e=t}),cancel:e}},e.exports=i},dIwP:function(e,t,r){"use strict";e.exports=function(e){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(e)}},dVOP:function(e,t,r){"use strict";function n(e){this.message=e}n.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},n.prototype.__CANCEL__=!0,e.exports=n},dXuT:function(e,t){e.exports={render:function(){var e=this.$createElement,t=this._self._c||e;return t("section",{staticClass:"output"},[t("pre",[t("code",{domProps:{innerHTML:this._s(this.value)}})])])},staticRenderFns:[]}},e1YO:function(e,t){e.exports={render:function(){var e=this.$createElement,t=this._self._c||e;return t("section",{staticClass:"input"},[t("textarea",{ref:"codeEditor"})])},staticRenderFns:[]}},ezqs:function(e,t,r){(function(e){"use strict";var t={autoSelfClosers:{area:!0,base:!0,br:!0,col:!0,command:!0,embed:!0,frame:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0,menuitem:!0},implicitlyClosed:{dd:!0,li:!0,optgroup:!0,option:!0,p:!0,rp:!0,rt:!0,tbody:!0,td:!0,tfoot:!0,th:!0,tr:!0},contextGrabbers:{dd:{dd:!0,dt:!0},dt:{dd:!0,dt:!0},li:{li:!0},option:{option:!0,optgroup:!0},optgroup:{optgroup:!0},p:{address:!0,article:!0,aside:!0,blockquote:!0,dir:!0,div:!0,dl:!0,fieldset:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,menu:!0,nav:!0,ol:!0,p:!0,pre:!0,section:!0,table:!0,ul:!0},rp:{rp:!0,rt:!0},rt:{rp:!0,rt:!0},tbody:{tbody:!0,tfoot:!0},td:{td:!0,th:!0},tfoot:{tbody:!0},th:{td:!0,th:!0},thead:{tbody:!0,tfoot:!0},tr:{tr:!0}},doNotIndent:{pre:!0},allowUnquoted:!0,allowMissing:!0,caseFold:!0},r={autoSelfClosers:{},implicitlyClosed:{},contextGrabbers:{},doNotIndent:{},allowUnquoted:!1,allowMissing:!1,allowMissingTagName:!1,caseFold:!1};e.defineMode("xml",function(n,i){var o,a,s=n.indentUnit,l={},c=i.htmlMode?t:r;for(var u in c)l[u]=c[u];for(var u in i)l[u]=i[u];function f(e,t){function r(r){return t.tokenize=r,r(e,t)}var n=e.next();return"<"==n?e.eat("!")?e.eat("[")?e.match("CDATA[")?r(p("atom","]]>")):null:e.match("--")?r(p("comment","--\x3e")):e.match("DOCTYPE",!0,!0)?(e.eatWhile(/[\w\._\-]/),r(function e(t){return function(r,n){for(var i;null!=(i=r.next());){if("<"==i)return n.tokenize=e(t+1),n.tokenize(r,n);if(">"==i){if(1==t){n.tokenize=f;break}return n.tokenize=e(t-1),n.tokenize(r,n)}}return"meta"}}(1))):null:e.eat("?")?(e.eatWhile(/[\w\._\-]/),t.tokenize=p("meta","?>"),"meta"):(o=e.eat("/")?"closeTag":"openTag",t.tokenize=d,"tag bracket"):"&"==n?(e.eat("#")?e.eat("x")?e.eatWhile(/[a-fA-F\d]/)&&e.eat(";"):e.eatWhile(/[\d]/)&&e.eat(";"):e.eatWhile(/[\w\.\-:]/)&&e.eat(";"))?"atom":"error":(e.eatWhile(/[^&<]/),null)}function d(e,t){var r,n,i=e.next();if(">"==i||"/"==i&&e.eat(">"))return t.tokenize=f,o=">"==i?"endTag":"selfcloseTag","tag bracket";if("="==i)return o="equals",null;if("<"==i){t.tokenize=f,t.state=g,t.tagName=t.tagStart=null;var a=t.tokenize(e,t);return a?a+" tag error":"tag error"}return/[\'\"]/.test(i)?(t.tokenize=(r=i,(n=function(e,t){for(;!e.eol();)if(e.next()==r){t.tokenize=d;break}return"string"}).isInAttribute=!0,n),t.stringStartCol=e.column(),t.tokenize(e,t)):(e.match(/^[^\s\u00a0=<>\"\']*[^\s\u00a0=<>\"\'\/]/),"word")}function p(e,t){return function(r,n){for(;!r.eol();){if(r.match(t)){n.tokenize=f;break}r.next()}return e}}function h(e){e.context&&(e.context=e.context.prev)}function m(e,t){for(var r;;){if(!e.context)return;if(r=e.context.tagName,!l.contextGrabbers.hasOwnProperty(r)||!l.contextGrabbers[r].hasOwnProperty(t))return;h(e)}}function g(e,t,r){return"openTag"==e?(r.tagStart=t.column(),v):"closeTag"==e?y:g}function v(e,t,r){return"word"==e?(r.tagName=t.current(),a="tag",x):l.allowMissingTagName&&"endTag"==e?(a="tag bracket",x(e,t,r)):(a="error",v)}function y(e,t,r){if("word"==e){var n=t.current();return r.context&&r.context.tagName!=n&&l.implicitlyClosed.hasOwnProperty(r.context.tagName)&&h(r),r.context&&r.context.tagName==n||!1===l.matchClosing?(a="tag",b):(a="tag error",w)}return l.allowMissingTagName&&"endTag"==e?(a="tag bracket",b(e,t,r)):(a="error",w)}function b(e,t,r){return"endTag"!=e?(a="error",b):(h(r),g)}function w(e,t,r){return a="error",b(e,0,r)}function x(e,t,r){if("word"==e)return a="attribute",_;if("endTag"==e||"selfcloseTag"==e){var n=r.tagName,i=r.tagStart;return r.tagName=r.tagStart=null,"selfcloseTag"==e||l.autoSelfClosers.hasOwnProperty(n)?m(r,n):(m(r,n),r.context=new function(e,t,r){this.prev=e.context,this.tagName=t,this.indent=e.indented,this.startOfLine=r,(l.doNotIndent.hasOwnProperty(t)||e.context&&e.context.noIndent)&&(this.noIndent=!0)}(r,n,i==r.indented)),g}return a="error",x}function _(e,t,r){return"equals"==e?k:(l.allowMissing||(a="error"),x(e,0,r))}function k(e,t,r){return"string"==e?C:"word"==e&&l.allowUnquoted?(a="string",x):(a="error",x(e,0,r))}function C(e,t,r){return"string"==e?C:x(e,0,r)}return f.isInText=!0,{startState:function(e){var t={tokenize:f,state:g,indented:e||0,tagName:null,tagStart:null,context:null};return null!=e&&(t.baseIndent=e),t},token:function(e,t){if(!t.tagName&&e.sol()&&(t.indented=e.indentation()),e.eatSpace())return null;o=null;var r=t.tokenize(e,t);return(r||o)&&"comment"!=r&&(a=null,t.state=t.state(o||r,e,t),a&&(r="error"==a?r+" error":a)),r},indent:function(t,r,n){var i=t.context;if(t.tokenize.isInAttribute)return t.tagStart==t.indented?t.stringStartCol+1:t.indented+s;if(i&&i.noIndent)return e.Pass;if(t.tokenize!=d&&t.tokenize!=f)return n?n.match(/^(\s*)/)[0].length:0;if(t.tagName)return!1!==l.multilineTagIndentPastTag?t.tagStart+t.tagName.length+2:t.tagStart+s*(l.multilineTagIndentFactor||1);if(l.alignCDATA&&/<!\[CDATA\[/.test(r))return 0;var o=r&&/^<(\/)?([\w_:\.-]*)/.exec(r);if(o&&o[1])for(;i;){if(i.tagName==o[2]){i=i.prev;break}if(!l.implicitlyClosed.hasOwnProperty(i.tagName))break;i=i.prev}else if(o)for(;i;){var a=l.contextGrabbers[i.tagName];if(!a||!a.hasOwnProperty(o[2]))break;i=i.prev}for(;i&&i.prev&&!i.startOfLine;)i=i.prev;return i?i.indent+s:t.baseIndent||0},electricInput:/<\/[\s\w:]+>$/,blockCommentStart:"\x3c!--",blockCommentEnd:"--\x3e",configuration:l.htmlMode?"html":"xml",helperType:l.htmlMode?"html":"xml",skipAttribute:function(e){e.state==k&&(e.state=x)}}}),e.defineMIME("text/xml","xml"),e.defineMIME("application/xml","xml"),e.mimeModes.hasOwnProperty("text/html")||e.defineMIME("text/html",{name:"xml",htmlMode:!0})})(r("8U58"))},fuGk:function(e,t,r){"use strict";var n=r("cGG2");function i(){this.handlers=[]}i.prototype.use=function(e,t){return this.handlers.push({fulfilled:e,rejected:t}),this.handlers.length-1},i.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},i.prototype.forEach=function(e){n.forEach(this.handlers,function(t){null!==t&&e(t)})},e.exports=i},hwvL:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r("c6RA"),i=(r.n(n),r("8U58")),o=r.n(i),a=r("mtWM"),s=r.n(a);t.default={data:function(){return{value:"",codeEditor:null}},props:["path"],mounted:function(){var e=this,t={autofocus:!0,extraKeys:{"Cmd-Enter":function(){e.executeCode()},"Ctrl-Enter":function(){e.executeCode()}},indentWithTabs:!0,lineNumbers:!0,lineWrapping:!0,mode:"text/x-php",tabSize:4,theme:"tinker"};this.codeEditor=o.a.fromTextArea(this.$refs.codeEditor,t),this.codeEditor.on("change",function(e){localStorage.setItem("tinker-tool",e.getValue())});var r=localStorage.getItem("tinker-tool");"string"==typeof r&&(this.codeEditor.setValue(r),this.codeEditor.execCommand("goDocEnd"))},methods:{executeCode:function(){var e=this,t=this.codeEditor.getValue().trim();""!==t?s.a.post(this.path,{code:t}).then(function(t){var r=t.data;e.$emit("execute",r)}):this.$emit("execute","<error>You must type some code to execute.</error>")}}}},iTlA:function(e,t){},isYl:function(e,t,r){(e.exports=r("FZ+f")(!1)).push([e.i,".cm-s-idea span.cm-meta{color:olive}.cm-s-idea span.cm-number{color:#00f}.cm-s-idea span.cm-keyword{line-height:1em;font-weight:700;color:navy}.cm-s-idea span.cm-atom{font-weight:700;color:navy}.cm-s-idea span.cm-def,.cm-s-idea span.cm-operator,.cm-s-idea span.cm-property,.cm-s-idea span.cm-type,.cm-s-idea span.cm-variable,.cm-s-idea span.cm-variable-2,.cm-s-idea span.cm-variable-3{color:#000}.cm-s-idea span.cm-comment{color:gray}.cm-s-idea span.cm-string,.cm-s-idea span.cm-string-2{color:green}.cm-s-idea span.cm-qualifier{color:#555}.cm-s-idea span.cm-error{color:red}.cm-s-idea span.cm-attribute{color:#00f}.cm-s-idea span.cm-tag{color:navy}.cm-s-idea span.cm-link{color:#00f}.cm-s-idea .CodeMirror-activeline-background{background:#fffae3}.cm-s-idea span.cm-builtin{color:#30a}.cm-s-idea span.cm-bracket{color:#cc7}.cm-s-idea{font-family:Consolas,Menlo,Monaco,Lucida Console,Liberation Mono,DejaVu Sans Mono,Bitstream Vera Sans Mono,Courier New,monospace,serif}.cm-s-idea .CodeMirror-matchingbracket{outline:1px solid grey;color:#000!important}.CodeMirror-hints.idea{font-family:Menlo,Monaco,Consolas,Courier New,monospace;color:#616569;background-color:#ebf3fd!important}.CodeMirror-hints.idea .CodeMirror-hint-active{background-color:#a2b8c9!important;color:#5c6065!important}",""])},mtWM:function(e,t,r){e.exports=r("tIFN")},mypn:function(e,t,r){(function(e,t){!function(e,r){"use strict";if(!e.setImmediate){var n,i,o,a,s,l=1,c={},u=!1,f=e.document,d=Object.getPrototypeOf&&Object.getPrototypeOf(e);d=d&&d.setTimeout?d:e,"[object process]"==={}.toString.call(e.process)?n=function(e){t.nextTick(function(){h(e)})}:!function(){if(e.postMessage&&!e.importScripts){var t=!0,r=e.onmessage;return e.onmessage=function(){t=!1},e.postMessage("","*"),e.onmessage=r,t}}()?e.MessageChannel?((o=new MessageChannel).port1.onmessage=function(e){h(e.data)},n=function(e){o.port2.postMessage(e)}):f&&"onreadystatechange"in f.createElement("script")?(i=f.documentElement,n=function(e){var t=f.createElement("script");t.onreadystatechange=function(){h(e),t.onreadystatechange=null,i.removeChild(t),t=null},i.appendChild(t)}):n=function(e){setTimeout(h,0,e)}:(a="setImmediate$"+Math.random()+"$",s=function(t){t.source===e&&"string"==typeof t.data&&0===t.data.indexOf(a)&&h(+t.data.slice(a.length))},e.addEventListener?e.addEventListener("message",s,!1):e.attachEvent("onmessage",s),n=function(t){e.postMessage(a+t,"*")}),d.setImmediate=function(e){"function"!=typeof e&&(e=new Function(""+e));for(var t=new Array(arguments.length-1),r=0;r<t.length;r++)t[r]=arguments[r+1];var i={callback:e,args:t};return c[l]=i,n(l),l++},d.clearImmediate=p}function p(e){delete c[e]}function h(e){if(u)setTimeout(h,0,e);else{var t=c[e];if(t){u=!0;try{!function(e){var t=e.callback,n=e.args;switch(n.length){case 0:t();break;case 1:t(n[0]);break;case 2:t(n[0],n[1]);break;case 3:t(n[0],n[1],n[2]);break;default:t.apply(r,n)}}(t)}finally{p(e),u=!1}}}}}("undefined"==typeof self?void 0===e?this:e:self)}).call(t,r("DuR2"),r("W2nU"))},nmni:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={props:["value"]}},noON:function(e,t,r){var n=r("isYl");"string"==typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);r("rjj0")("4374e7aa",n,!0,{})},oJlt:function(e,t,r){"use strict";var n=r("cGG2"),i=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];e.exports=function(e){var t,r,o,a={};return e?(n.forEach(e.split("\n"),function(e){if(o=e.indexOf(":"),t=n.trim(e.substr(0,o)).toLowerCase(),r=n.trim(e.substr(o+1)),t){if(a[t]&&i.indexOf(t)>=0)return;a[t]="set-cookie"===t?(a[t]?a[t]:[]).concat([r]):a[t]?a[t]+", "+r:r}}),a):a}},p1b6:function(e,t,r){"use strict";var n=r("cGG2");e.exports=n.isStandardBrowserEnv()?{write:function(e,t,r,i,o,a){var s=[];s.push(e+"="+encodeURIComponent(t)),n.isNumber(r)&&s.push("expires="+new Date(r).toGMTString()),n.isString(i)&&s.push("path="+i),n.isString(o)&&s.push("domain="+o),!0===a&&s.push("secure"),document.cookie=s.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}},pBtG:function(e,t,r){"use strict";e.exports=function(e){return!(!e||!e.__CANCEL__)}},puAj:function(e,t,r){(function(e){"use strict";function t(e){for(var t={},r=0;r<e.length;++r)t[e[r].toLowerCase()]=!0;return t}e.defineMode("css",function(t,r){var n=r.inline;r.propertyKeywords||(r=e.resolveMode("text/css"));var i,o,a=t.indentUnit,s=r.tokenHooks,l=r.documentTypes||{},c=r.mediaTypes||{},u=r.mediaFeatures||{},f=r.mediaValueKeywords||{},d=r.propertyKeywords||{},p=r.nonStandardPropertyKeywords||{},h=r.fontProperties||{},m=r.counterDescriptors||{},g=r.colorKeywords||{},v=r.valueKeywords||{},y=r.allowNested,b=r.lineComment,w=!0===r.supportsAtComponent;function x(e,t){return i=t,e}function _(e){return function(t,r){for(var n,i=!1;null!=(n=t.next());){if(n==e&&!i){")"==e&&t.backUp(1);break}i=!i&&"\\"==n}return(n==e||!i&&")"!=e)&&(r.tokenize=null),x("string","string")}}function k(e,t){return e.next(),e.match(/\s*[\"\')]/,!1)?t.tokenize=null:t.tokenize=_(")"),x(null,"(")}function C(e,t,r){this.type=e,this.indent=t,this.prev=r}function S(e,t,r,n){return e.context=new C(r,t.indentation()+(!1===n?0:a),e.context),r}function T(e){return e.context.prev&&(e.context=e.context.prev),e.context.type}function M(e,t,r){return O[r.context.type](e,t,r)}function L(e,t,r,n){for(var i=n||1;i>0;i--)r.context=r.context.prev;return M(e,t,r)}function A(e){var t=e.current().toLowerCase();o=v.hasOwnProperty(t)?"atom":g.hasOwnProperty(t)?"keyword":"variable"}var O={top:function(e,t,r){if("{"==e)return S(r,t,"block");if("}"==e&&r.context.prev)return T(r);if(w&&/@component/i.test(e))return S(r,t,"atComponentBlock");if(/^@(-moz-)?document$/i.test(e))return S(r,t,"documentTypes");if(/^@(media|supports|(-moz-)?document|import)$/i.test(e))return S(r,t,"atBlock");if(/^@(font-face|counter-style)/i.test(e))return r.stateArg=e,"restricted_atBlock_before";if(/^@(-(moz|ms|o|webkit)-)?keyframes$/i.test(e))return"keyframes";if(e&&"@"==e.charAt(0))return S(r,t,"at");if("hash"==e)o="builtin";else if("word"==e)o="tag";else{if("variable-definition"==e)return"maybeprop";if("interpolation"==e)return S(r,t,"interpolation");if(":"==e)return"pseudo";if(y&&"("==e)return S(r,t,"parens")}return r.context.type},block:function(e,t,r){if("word"==e){var n=t.current().toLowerCase();return d.hasOwnProperty(n)?(o="property","maybeprop"):p.hasOwnProperty(n)?(o="string-2","maybeprop"):y?(o=t.match(/^\s*:(?:\s|$)/,!1)?"property":"tag","block"):(o+=" error","maybeprop")}return"meta"==e?"block":y||"hash"!=e&&"qualifier"!=e?O.top(e,t,r):(o="error","block")},maybeprop:function(e,t,r){return":"==e?S(r,t,"prop"):M(e,t,r)},prop:function(e,t,r){if(";"==e)return T(r);if("{"==e&&y)return S(r,t,"propBlock");if("}"==e||"{"==e)return L(e,t,r);if("("==e)return S(r,t,"parens");if("hash"!=e||/^#([0-9a-fA-f]{3,4}|[0-9a-fA-f]{6}|[0-9a-fA-f]{8})$/.test(t.current())){if("word"==e)A(t);else if("interpolation"==e)return S(r,t,"interpolation")}else o+=" error";return"prop"},propBlock:function(e,t,r){return"}"==e?T(r):"word"==e?(o="property","maybeprop"):r.context.type},parens:function(e,t,r){return"{"==e||"}"==e?L(e,t,r):")"==e?T(r):"("==e?S(r,t,"parens"):"interpolation"==e?S(r,t,"interpolation"):("word"==e&&A(t),"parens")},pseudo:function(e,t,r){return"meta"==e?"pseudo":"word"==e?(o="variable-3",r.context.type):M(e,t,r)},documentTypes:function(e,t,r){return"word"==e&&l.hasOwnProperty(t.current())?(o="tag",r.context.type):O.atBlock(e,t,r)},atBlock:function(e,t,r){if("("==e)return S(r,t,"atBlock_parens");if("}"==e||";"==e)return L(e,t,r);if("{"==e)return T(r)&&S(r,t,y?"block":"top");if("interpolation"==e)return S(r,t,"interpolation");if("word"==e){var n=t.current().toLowerCase();o="only"==n||"not"==n||"and"==n||"or"==n?"keyword":c.hasOwnProperty(n)?"attribute":u.hasOwnProperty(n)?"property":f.hasOwnProperty(n)?"keyword":d.hasOwnProperty(n)?"property":p.hasOwnProperty(n)?"string-2":v.hasOwnProperty(n)?"atom":g.hasOwnProperty(n)?"keyword":"error"}return r.context.type},atComponentBlock:function(e,t,r){return"}"==e?L(e,t,r):"{"==e?T(r)&&S(r,t,y?"block":"top",!1):("word"==e&&(o="error"),r.context.type)},atBlock_parens:function(e,t,r){return")"==e?T(r):"{"==e||"}"==e?L(e,t,r,2):O.atBlock(e,t,r)},restricted_atBlock_before:function(e,t,r){return"{"==e?S(r,t,"restricted_atBlock"):"word"==e&&"@counter-style"==r.stateArg?(o="variable","restricted_atBlock_before"):M(e,t,r)},restricted_atBlock:function(e,t,r){return"}"==e?(r.stateArg=null,T(r)):"word"==e?(o="@font-face"==r.stateArg&&!h.hasOwnProperty(t.current().toLowerCase())||"@counter-style"==r.stateArg&&!m.hasOwnProperty(t.current().toLowerCase())?"error":"property","maybeprop"):"restricted_atBlock"},keyframes:function(e,t,r){return"word"==e?(o="variable","keyframes"):"{"==e?S(r,t,"top"):M(e,t,r)},at:function(e,t,r){return";"==e?T(r):"{"==e||"}"==e?L(e,t,r):("word"==e?o="tag":"hash"==e&&(o="builtin"),"at")},interpolation:function(e,t,r){return"}"==e?T(r):"{"==e||";"==e?L(e,t,r):("word"==e?o="variable":"variable"!=e&&"("!=e&&")"!=e&&(o="error"),"interpolation")}};return{startState:function(e){return{tokenize:null,state:n?"block":"top",stateArg:null,context:new C(n?"block":"top",e||0,null)}},token:function(e,t){if(!t.tokenize&&e.eatSpace())return null;var r=(t.tokenize||function(e,t){var r=e.next();if(s[r]){var n=s[r](e,t);if(!1!==n)return n}return"@"==r?(e.eatWhile(/[\w\\\-]/),x("def",e.current())):"="==r||("~"==r||"|"==r)&&e.eat("=")?x(null,"compare"):'"'==r||"'"==r?(t.tokenize=_(r),t.tokenize(e,t)):"#"==r?(e.eatWhile(/[\w\\\-]/),x("atom","hash")):"!"==r?(e.match(/^\s*\w*/),x("keyword","important")):/\d/.test(r)||"."==r&&e.eat(/\d/)?(e.eatWhile(/[\w.%]/),x("number","unit")):"-"!==r?/[,+>*\/]/.test(r)?x(null,"select-op"):"."==r&&e.match(/^-?[_a-z][_a-z0-9-]*/i)?x("qualifier","qualifier"):/[:;{}\[\]\(\)]/.test(r)?x(null,r):e.match(/[\w-.]+(?=\()/)?(/^(url(-prefix)?|domain|regexp)$/.test(e.current().toLowerCase())&&(t.tokenize=k),x("variable callee","variable")):/[\w\\\-]/.test(r)?(e.eatWhile(/[\w\\\-]/),x("property","word")):x(null,null):/[\d.]/.test(e.peek())?(e.eatWhile(/[\w.%]/),x("number","unit")):e.match(/^-[\w\\\-]*/)?(e.eatWhile(/[\w\\\-]/),e.match(/^\s*:/,!1)?x("variable-2","variable-definition"):x("variable-2","variable")):e.match(/^\w+-/)?x("meta","meta"):void 0})(e,t);return r&&"object"==typeof r&&(i=r[1],r=r[0]),o=r,"comment"!=i&&(t.state=O[t.state](i,e,t)),o},indent:function(e,t){var r=e.context,n=t&&t.charAt(0),i=r.indent;return"prop"!=r.type||"}"!=n&&")"!=n||(r=r.prev),r.prev&&("}"!=n||"block"!=r.type&&"top"!=r.type&&"interpolation"!=r.type&&"restricted_atBlock"!=r.type?(")"!=n||"parens"!=r.type&&"atBlock_parens"!=r.type)&&("{"!=n||"at"!=r.type&&"atBlock"!=r.type)||(i=Math.max(0,r.indent-a)):i=(r=r.prev).indent),i},electricChars:"}",blockCommentStart:"/*",blockCommentEnd:"*/",blockCommentContinue:" * ",lineComment:b,fold:"brace"}});var r=["domain","regexp","url","url-prefix"],n=t(r),i=["all","aural","braille","handheld","print","projection","screen","tty","tv","embossed"],o=t(i),a=["width","min-width","max-width","height","min-height","max-height","device-width","min-device-width","max-device-width","device-height","min-device-height","max-device-height","aspect-ratio","min-aspect-ratio","max-aspect-ratio","device-aspect-ratio","min-device-aspect-ratio","max-device-aspect-ratio","color","min-color","max-color","color-index","min-color-index","max-color-index","monochrome","min-monochrome","max-monochrome","resolution","min-resolution","max-resolution","scan","grid","orientation","device-pixel-ratio","min-device-pixel-ratio","max-device-pixel-ratio","pointer","any-pointer","hover","any-hover"],s=t(a),l=["landscape","portrait","none","coarse","fine","on-demand","hover","interlace","progressive"],c=t(l),u=["align-content","align-items","align-self","alignment-adjust","alignment-baseline","anchor-point","animation","animation-delay","animation-direction","animation-duration","animation-fill-mode","animation-iteration-count","animation-name","animation-play-state","animation-timing-function","appearance","azimuth","backface-visibility","background","background-attachment","background-blend-mode","background-clip","background-color","background-image","background-origin","background-position","background-repeat","background-size","baseline-shift","binding","bleed","bookmark-label","bookmark-level","bookmark-state","bookmark-target","border","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-style","border-bottom-width","border-collapse","border-color","border-image","border-image-outset","border-image-repeat","border-image-slice","border-image-source","border-image-width","border-left","border-left-color","border-left-style","border-left-width","border-radius","border-right","border-right-color","border-right-style","border-right-width","border-spacing","border-style","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-style","border-top-width","border-width","bottom","box-decoration-break","box-shadow","box-sizing","break-after","break-before","break-inside","caption-side","caret-color","clear","clip","color","color-profile","column-count","column-fill","column-gap","column-rule","column-rule-color","column-rule-style","column-rule-width","column-span","column-width","columns","content","counter-increment","counter-reset","crop","cue","cue-after","cue-before","cursor","direction","display","dominant-baseline","drop-initial-after-adjust","drop-initial-after-align","drop-initial-before-adjust","drop-initial-before-align","drop-initial-size","drop-initial-value","elevation","empty-cells","fit","fit-position","flex","flex-basis","flex-direction","flex-flow","flex-grow","flex-shrink","flex-wrap","float","float-offset","flow-from","flow-into","font","font-feature-settings","font-family","font-kerning","font-language-override","font-size","font-size-adjust","font-stretch","font-style","font-synthesis","font-variant","font-variant-alternates","font-variant-caps","font-variant-east-asian","font-variant-ligatures","font-variant-numeric","font-variant-position","font-weight","grid","grid-area","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-column","grid-column-end","grid-column-gap","grid-column-start","grid-gap","grid-row","grid-row-end","grid-row-gap","grid-row-start","grid-template","grid-template-areas","grid-template-columns","grid-template-rows","hanging-punctuation","height","hyphens","icon","image-orientation","image-rendering","image-resolution","inline-box-align","justify-content","justify-items","justify-self","left","letter-spacing","line-break","line-height","line-stacking","line-stacking-ruby","line-stacking-shift","line-stacking-strategy","list-style","list-style-image","list-style-position","list-style-type","margin","margin-bottom","margin-left","margin-right","margin-top","marks","marquee-direction","marquee-loop","marquee-play-count","marquee-speed","marquee-style","max-height","max-width","min-height","min-width","mix-blend-mode","move-to","nav-down","nav-index","nav-left","nav-right","nav-up","object-fit","object-position","opacity","order","orphans","outline","outline-color","outline-offset","outline-style","outline-width","overflow","overflow-style","overflow-wrap","overflow-x","overflow-y","padding","padding-bottom","padding-left","padding-right","padding-top","page","page-break-after","page-break-before","page-break-inside","page-policy","pause","pause-after","pause-before","perspective","perspective-origin","pitch","pitch-range","place-content","place-items","place-self","play-during","position","presentation-level","punctuation-trim","quotes","region-break-after","region-break-before","region-break-inside","region-fragment","rendering-intent","resize","rest","rest-after","rest-before","richness","right","rotation","rotation-point","ruby-align","ruby-overhang","ruby-position","ruby-span","shape-image-threshold","shape-inside","shape-margin","shape-outside","size","speak","speak-as","speak-header","speak-numeral","speak-punctuation","speech-rate","stress","string-set","tab-size","table-layout","target","target-name","target-new","target-position","text-align","text-align-last","text-decoration","text-decoration-color","text-decoration-line","text-decoration-skip","text-decoration-style","text-emphasis","text-emphasis-color","text-emphasis-position","text-emphasis-style","text-height","text-indent","text-justify","text-outline","text-overflow","text-shadow","text-size-adjust","text-space-collapse","text-transform","text-underline-position","text-wrap","top","transform","transform-origin","transform-style","transition","transition-delay","transition-duration","transition-property","transition-timing-function","unicode-bidi","user-select","vertical-align","visibility","voice-balance","voice-duration","voice-family","voice-pitch","voice-range","voice-rate","voice-stress","voice-volume","volume","white-space","widows","width","will-change","word-break","word-spacing","word-wrap","z-index","clip-path","clip-rule","mask","enable-background","filter","flood-color","flood-opacity","lighting-color","stop-color","stop-opacity","pointer-events","color-interpolation","color-interpolation-filters","color-rendering","fill","fill-opacity","fill-rule","image-rendering","marker","marker-end","marker-mid","marker-start","shape-rendering","stroke","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","text-rendering","baseline-shift","dominant-baseline","glyph-orientation-horizontal","glyph-orientation-vertical","text-anchor","writing-mode"],f=t(u),d=["scrollbar-arrow-color","scrollbar-base-color","scrollbar-dark-shadow-color","scrollbar-face-color","scrollbar-highlight-color","scrollbar-shadow-color","scrollbar-3d-light-color","scrollbar-track-color","shape-inside","searchfield-cancel-button","searchfield-decoration","searchfield-results-button","searchfield-results-decoration","zoom"],p=t(d),h=t(["font-family","src","unicode-range","font-variant","font-feature-settings","font-stretch","font-weight","font-style"]),m=t(["additive-symbols","fallback","negative","pad","prefix","range","speak-as","suffix","symbols","system"]),g=["aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","grey","green","greenyellow","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","rebeccapurple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","snow","springgreen","steelblue","tan","teal","thistle","tomato","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen"],v=t(g),y=["above","absolute","activeborder","additive","activecaption","afar","after-white-space","ahead","alias","all","all-scroll","alphabetic","alternate","always","amharic","amharic-abegede","antialiased","appworkspace","arabic-indic","armenian","asterisks","attr","auto","auto-flow","avoid","avoid-column","avoid-page","avoid-region","background","backwards","baseline","below","bidi-override","binary","bengali","blink","block","block-axis","bold","bolder","border","border-box","both","bottom","break","break-all","break-word","bullets","button","button-bevel","buttonface","buttonhighlight","buttonshadow","buttontext","calc","cambodian","capitalize","caps-lock-indicator","caption","captiontext","caret","cell","center","checkbox","circle","cjk-decimal","cjk-earthly-branch","cjk-heavenly-stem","cjk-ideographic","clear","clip","close-quote","col-resize","collapse","color","color-burn","color-dodge","column","column-reverse","compact","condensed","contain","content","contents","content-box","context-menu","continuous","copy","counter","counters","cover","crop","cross","crosshair","currentcolor","cursive","cyclic","darken","dashed","decimal","decimal-leading-zero","default","default-button","dense","destination-atop","destination-in","destination-out","destination-over","devanagari","difference","disc","discard","disclosure-closed","disclosure-open","document","dot-dash","dot-dot-dash","dotted","double","down","e-resize","ease","ease-in","ease-in-out","ease-out","element","ellipse","ellipsis","embed","end","ethiopic","ethiopic-abegede","ethiopic-abegede-am-et","ethiopic-abegede-gez","ethiopic-abegede-ti-er","ethiopic-abegede-ti-et","ethiopic-halehame-aa-er","ethiopic-halehame-aa-et","ethiopic-halehame-am-et","ethiopic-halehame-gez","ethiopic-halehame-om-et","ethiopic-halehame-sid-et","ethiopic-halehame-so-et","ethiopic-halehame-ti-er","ethiopic-halehame-ti-et","ethiopic-halehame-tig","ethiopic-numeric","ew-resize","exclusion","expanded","extends","extra-condensed","extra-expanded","fantasy","fast","fill","fixed","flat","flex","flex-end","flex-start","footnotes","forwards","from","geometricPrecision","georgian","graytext","grid","groove","gujarati","gurmukhi","hand","hangul","hangul-consonant","hard-light","hebrew","help","hidden","hide","higher","highlight","highlighttext","hiragana","hiragana-iroha","horizontal","hsl","hsla","hue","icon","ignore","inactiveborder","inactivecaption","inactivecaptiontext","infinite","infobackground","infotext","inherit","initial","inline","inline-axis","inline-block","inline-flex","inline-grid","inline-table","inset","inside","intrinsic","invert","italic","japanese-formal","japanese-informal","justify","kannada","katakana","katakana-iroha","keep-all","khmer","korean-hangul-formal","korean-hanja-formal","korean-hanja-informal","landscape","lao","large","larger","left","level","lighter","lighten","line-through","linear","linear-gradient","lines","list-item","listbox","listitem","local","logical","loud","lower","lower-alpha","lower-armenian","lower-greek","lower-hexadecimal","lower-latin","lower-norwegian","lower-roman","lowercase","ltr","luminosity","malayalam","match","matrix","matrix3d","media-controls-background","media-current-time-display","media-fullscreen-button","media-mute-button","media-play-button","media-return-to-realtime-button","media-rewind-button","media-seek-back-button","media-seek-forward-button","media-slider","media-sliderthumb","media-time-remaining-display","media-volume-slider","media-volume-slider-container","media-volume-sliderthumb","medium","menu","menulist","menulist-button","menulist-text","menulist-textfield","menutext","message-box","middle","min-intrinsic","mix","mongolian","monospace","move","multiple","multiply","myanmar","n-resize","narrower","ne-resize","nesw-resize","no-close-quote","no-drop","no-open-quote","no-repeat","none","normal","not-allowed","nowrap","ns-resize","numbers","numeric","nw-resize","nwse-resize","oblique","octal","opacity","open-quote","optimizeLegibility","optimizeSpeed","oriya","oromo","outset","outside","outside-shape","overlay","overline","padding","padding-box","painted","page","paused","persian","perspective","plus-darker","plus-lighter","pointer","polygon","portrait","pre","pre-line","pre-wrap","preserve-3d","progress","push-button","radial-gradient","radio","read-only","read-write","read-write-plaintext-only","rectangle","region","relative","repeat","repeating-linear-gradient","repeating-radial-gradient","repeat-x","repeat-y","reset","reverse","rgb","rgba","ridge","right","rotate","rotate3d","rotateX","rotateY","rotateZ","round","row","row-resize","row-reverse","rtl","run-in","running","s-resize","sans-serif","saturation","scale","scale3d","scaleX","scaleY","scaleZ","screen","scroll","scrollbar","scroll-position","se-resize","searchfield","searchfield-cancel-button","searchfield-decoration","searchfield-results-button","searchfield-results-decoration","self-start","self-end","semi-condensed","semi-expanded","separate","serif","show","sidama","simp-chinese-formal","simp-chinese-informal","single","skew","skewX","skewY","skip-white-space","slide","slider-horizontal","slider-vertical","sliderthumb-horizontal","sliderthumb-vertical","slow","small","small-caps","small-caption","smaller","soft-light","solid","somali","source-atop","source-in","source-out","source-over","space","space-around","space-between","space-evenly","spell-out","square","square-button","start","static","status-bar","stretch","stroke","sub","subpixel-antialiased","super","sw-resize","symbolic","symbols","system-ui","table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row","table-row-group","tamil","telugu","text","text-bottom","text-top","textarea","textfield","thai","thick","thin","threeddarkshadow","threedface","threedhighlight","threedlightshadow","threedshadow","tibetan","tigre","tigrinya-er","tigrinya-er-abegede","tigrinya-et","tigrinya-et-abegede","to","top","trad-chinese-formal","trad-chinese-informal","transform","translate","translate3d","translateX","translateY","translateZ","transparent","ultra-condensed","ultra-expanded","underline","unset","up","upper-alpha","upper-armenian","upper-greek","upper-hexadecimal","upper-latin","upper-norwegian","upper-roman","uppercase","urdu","url","var","vertical","vertical-text","visible","visibleFill","visiblePainted","visibleStroke","visual","w-resize","wait","wave","wider","window","windowframe","windowtext","words","wrap","wrap-reverse","x-large","x-small","xor","xx-large","xx-small"],b=t(y),w=r.concat(i).concat(a).concat(l).concat(u).concat(d).concat(g).concat(y);function x(e,t){for(var r,n=!1;null!=(r=e.next());){if(n&&"/"==r){t.tokenize=null;break}n="*"==r}return["comment","comment"]}e.registerHelper("hintWords","css",w),e.defineMIME("text/css",{documentTypes:n,mediaTypes:o,mediaFeatures:s,mediaValueKeywords:c,propertyKeywords:f,nonStandardPropertyKeywords:p,fontProperties:h,counterDescriptors:m,colorKeywords:v,valueKeywords:b,tokenHooks:{"/":function(e,t){return!!e.eat("*")&&(t.tokenize=x,x(e,t))}},name:"css"}),e.defineMIME("text/x-scss",{mediaTypes:o,mediaFeatures:s,mediaValueKeywords:c,propertyKeywords:f,nonStandardPropertyKeywords:p,colorKeywords:v,valueKeywords:b,fontProperties:h,allowNested:!0,lineComment:"//",tokenHooks:{"/":function(e,t){return e.eat("/")?(e.skipToEnd(),["comment","comment"]):e.eat("*")?(t.tokenize=x,x(e,t)):["operator","operator"]},":":function(e){return!!e.match(/\s*\{/,!1)&&[null,null]},$:function(e){return e.match(/^[\w-]+/),e.match(/^\s*:/,!1)?["variable-2","variable-definition"]:["variable-2","variable"]},"#":function(e){return!!e.eat("{")&&[null,"interpolation"]}},name:"css",helperType:"scss"}),e.defineMIME("text/x-less",{mediaTypes:o,mediaFeatures:s,mediaValueKeywords:c,propertyKeywords:f,nonStandardPropertyKeywords:p,colorKeywords:v,valueKeywords:b,fontProperties:h,allowNested:!0,lineComment:"//",tokenHooks:{"/":function(e,t){return e.eat("/")?(e.skipToEnd(),["comment","comment"]):e.eat("*")?(t.tokenize=x,x(e,t)):["operator","operator"]},"@":function(e){return e.eat("{")?[null,"interpolation"]:!e.match(/^(charset|document|font-face|import|(-(moz|ms|o|webkit)-)?keyframes|media|namespace|page|supports)\b/i,!1)&&(e.eatWhile(/[\w\\\-]/),e.match(/^\s*:/,!1)?["variable-2","variable-definition"]:["variable-2","variable"])},"&":function(){return["atom","atom"]}},name:"css",helperType:"less"}),e.defineMIME("text/x-gss",{documentTypes:n,mediaTypes:o,mediaFeatures:s,propertyKeywords:f,nonStandardPropertyKeywords:p,fontProperties:h,counterDescriptors:m,colorKeywords:v,valueKeywords:b,supportsAtComponent:!0,tokenHooks:{"/":function(e,t){return!!e.eat("*")&&(t.tokenize=x,x(e,t))}},name:"css",helperType:"gss"})})(r("8U58"))},pxG4:function(e,t,r){"use strict";e.exports=function(e){return function(t){return e.apply(null,t)}}},qRfI:function(e,t,r){"use strict";e.exports=function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}},rjj0:function(e,t,r){var n="undefined"!=typeof document;if("undefined"!=typeof DEBUG&&DEBUG&&!n)throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var i=r("tTVk"),o={},a=n&&(document.head||document.getElementsByTagName("head")[0]),s=null,l=0,c=!1,u=function(){},f=null,d="data-vue-ssr-id",p="undefined"!=typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function h(e){for(var t=0;t<e.length;t++){var r=e[t],n=o[r.id];if(n){n.refs++;for(var i=0;i<n.parts.length;i++)n.parts[i](r.parts[i]);for(;i<r.parts.length;i++)n.parts.push(g(r.parts[i]));n.parts.length>r.parts.length&&(n.parts.length=r.parts.length)}else{var a=[];for(i=0;i<r.parts.length;i++)a.push(g(r.parts[i]));o[r.id]={id:r.id,refs:1,parts:a}}}}function m(){var e=document.createElement("style");return e.type="text/css",a.appendChild(e),e}function g(e){var t,r,n=document.querySelector("style["+d+'~="'+e.id+'"]');if(n){if(c)return u;n.parentNode.removeChild(n)}if(p){var i=l++;n=s||(s=m()),t=b.bind(null,n,i,!1),r=b.bind(null,n,i,!0)}else n=m(),t=function(e,t){var r=t.css,n=t.media,i=t.sourceMap;n&&e.setAttribute("media",n);f.ssrId&&e.setAttribute(d,t.id);i&&(r+="\n/*# sourceURL="+i.sources[0]+" */",r+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(i))))+" */");if(e.styleSheet)e.styleSheet.cssText=r;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(r))}}.bind(null,n),r=function(){n.parentNode.removeChild(n)};return t(e),function(n){if(n){if(n.css===e.css&&n.media===e.media&&n.sourceMap===e.sourceMap)return;t(e=n)}else r()}}e.exports=function(e,t,r,n){c=r,f=n||{};var a=i(e,t);return h(a),function(t){for(var r=[],n=0;n<a.length;n++){var s=a[n];(l=o[s.id]).refs--,r.push(l)}t?h(a=i(e,t)):a=[];for(n=0;n<r.length;n++){var l;if(0===(l=r[n]).refs){for(var c=0;c<l.parts.length;c++)l.parts[c]();delete o[l.id]}}}};var v,y=(v=[],function(e,t){return v[e]=t,v.filter(Boolean).join("\n")});function b(e,t,r,n){var i=r?"":n.css;if(e.styleSheet)e.styleSheet.cssText=y(t,i);else{var o=document.createTextNode(i),a=e.childNodes;a[t]&&e.removeChild(a[t]),a.length?e.insertBefore(o,a[t]):e.appendChild(o)}}},t8qj:function(e,t,r){"use strict";e.exports=function(e,t,r,n,i){return e.config=t,r&&(e.code=r),e.request=n,e.response=i,e.isAxiosError=!0,e.toJSON=function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code}},e}},tIFN:function(e,t,r){"use strict";var n=r("cGG2"),i=r("JP+z"),o=r("XmWM"),a=r("DUeU");function s(e){var t=new o(e),r=i(o.prototype.request,t);return n.extend(r,o.prototype,t),n.extend(r,t),r}var l=s(r("KCLY"));l.Axios=o,l.create=function(e){return s(a(l.defaults,e))},l.Cancel=r("dVOP"),l.CancelToken=r("cWxy"),l.isCancel=r("pBtG"),l.all=function(e){return Promise.all(e)},l.spread=r("pxG4"),l.isAxiosError=r("SLDG"),e.exports=l,e.exports.default=l},tTVk:function(e,t){e.exports=function(e,t){for(var r=[],n={},i=0;i<t.length;i++){var o=t[i],a=o[0],s={id:e+":"+i,css:o[1],media:o[2],sourceMap:o[3]};n[a]?n[a].parts.push(s):r.push(n[a]={id:a,parts:[s]})}return r}},xAuk:function(e,t,r){var n=r("Zn2a");"string"==typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);r("rjj0")("96bccb62",n,!0,{})},xLtR:function(e,t,r){"use strict";var n=r("cGG2"),i=r("TNV1"),o=r("pBtG"),a=r("KCLY");function s(e){e.cancelToken&&e.cancelToken.throwIfRequested()}e.exports=function(e){return s(e),e.headers=e.headers||{},e.data=i(e.data,e.headers,e.transformRequest),e.headers=n.merge(e.headers.common||{},e.headers[e.method]||{},e.headers),n.forEach(["delete","get","head","post","put","patch","common"],function(t){delete e.headers[t]}),(e.adapter||a.adapter)(e).then(function(t){return s(e),t.data=i(t.data,t.headers,e.transformResponse),t},function(t){return o(t)||(s(e),t&&t.response&&(t.response.data=i(t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)})}}});