var Ms=Object.create;var po=Object.defineProperty;var Es=Object.getOwnPropertyDescriptor;var Bs=Object.getOwnPropertyNames;var Ps=Object.getPrototypeOf,Ss=Object.prototype.hasOwnProperty;var Ts=(_,v,z)=>v in _?po(_,v,{enumerable:!0,configurable:!0,writable:!0,value:z}):_[v]=z;var Ds=(_,v)=>()=>(v||_((v={exports:{}}).exports,v),v.exports);var As=(_,v,z,A)=>{if(v&&typeof v=="object"||typeof v=="function")for(let V of Bs(v))!Ss.call(_,V)&&V!==z&&po(_,V,{get:()=>v[V],enumerable:!(A=Es(v,V))||A.enumerable});return _};var Fs=(_,v,z)=>(z=_!=null?Ms(Ps(_)):{},As(v||!_||!_.__esModule?po(z,"default",{value:_,enumerable:!0}):z,_));var ot=(_,v,z)=>Ts(_,typeof v!="symbol"?v+"":v,z);var Do=Ds((pa,To)=>{(function(_,v){typeof pa=="object"&&typeof To<"u"?v(pa):typeof define=="function"&&define.amd?define(["exports"],v):(_=typeof globalThis<"u"?globalThis:_||self,v(_.leaflet={}))})(pa,function(_){"use strict";var v="1.9.4";function z(t){var n,o,u,c;for(o=1,u=arguments.length;o<u;o++){c=arguments[o];for(n in c)t[n]=c[n]}return t}var A=Object.create||function(){function t(){}return function(n){return t.prototype=n,new t}}();function V(t,n){var o=Array.prototype.slice;if(t.bind)return t.bind.apply(t,o.call(arguments,1));var u=o.call(arguments,2);return function(){return t.apply(n,u.length?u.concat(o.call(arguments)):arguments)}}var q=0;function M(t){return"_leaflet_id"in t||(t._leaflet_id=++q),t._leaflet_id}function tt(t,n,o){var u,c,g,B;return B=function(){u=!1,c&&(g.apply(o,c),c=!1)},g=function(){u?c=arguments:(t.apply(o,arguments),setTimeout(B,n),u=!0)},g}function $(t,n,o){var u=n[1],c=n[0],g=u-c;return t===u&&o?t:((t-c)%g+g)%g+c}function et(){return!1}function lt(t,n){if(n===!1)return t;var o=Math.pow(10,n===void 0?6:n);return Math.round(t*o)/o}function St(t){return t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")}function ee(t){return St(t).split(/\s+/)}function kt(t,n){Object.prototype.hasOwnProperty.call(t,"options")||(t.options=t.options?A(t.options):{});for(var o in n)t.options[o]=n[o];return t.options}function or(t,n,o){var u=[];for(var c in t)u.push(encodeURIComponent(o?c.toUpperCase():c)+"="+encodeURIComponent(t[c]));return(!n||n.indexOf("?")===-1?"?":"&")+u.join("&")}var ya=/\{ *([\w_ -]+) *\}/g;function hi(t,n){return t.replace(ya,function(o,u){var c=n[u];if(c===void 0)throw new Error("No value provided for variable "+o);return typeof c=="function"&&(c=c(n)),c})}var de=Array.isArray||function(t){return Object.prototype.toString.call(t)==="[object Array]"};function ln(t,n){for(var o=0;o<t.length;o++)if(t[o]===n)return o;return-1}var Pi="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=";function hn(t){return window["webkit"+t]||window["moz"+t]||window["ms"+t]}var cn=0;function ze(t){var n=+new Date,o=Math.max(0,16-(n-cn));return cn=n+o,window.setTimeout(t,o)}var ci=window.requestAnimationFrame||hn("RequestAnimationFrame")||ze,sr=window.cancelAnimationFrame||hn("CancelAnimationFrame")||hn("CancelRequestAnimationFrame")||function(t){window.clearTimeout(t)};function Jt(t,n,o){if(o&&ci===ze)t.call(n);else return ci.call(window,V(t,n))}function Vt(t){t&&sr.call(window,t)}var Ne={__proto__:null,extend:z,create:A,bind:V,get lastId(){return q},stamp:M,throttle:tt,wrapNum:$,falseFn:et,formatNum:lt,trim:St,splitWords:ee,setOptions:kt,getParamString:or,template:hi,isArray:de,indexOf:ln,emptyImageUrl:Pi,requestFn:ci,cancelFn:sr,requestAnimFrame:Jt,cancelAnimFrame:Vt};function pe(){}pe.extend=function(t){var n=function(){kt(this),this.initialize&&this.initialize.apply(this,arguments),this.callInitHooks()},o=n.__super__=this.prototype,u=A(o);u.constructor=n,n.prototype=u;for(var c in this)Object.prototype.hasOwnProperty.call(this,c)&&c!=="prototype"&&c!=="__super__"&&(n[c]=this[c]);return t.statics&&z(n,t.statics),t.includes&&(va(t.includes),z.apply(null,[u].concat(t.includes))),z(u,t),delete u.statics,delete u.includes,u.options&&(u.options=o.options?A(o.options):{},z(u.options,t.options)),u._initHooks=[],u.callInitHooks=function(){if(!this._initHooksCalled){o.callInitHooks&&o.callInitHooks.call(this),this._initHooksCalled=!0;for(var g=0,B=u._initHooks.length;g<B;g++)u._initHooks[g].call(this)}},n},pe.include=function(t){var n=this.prototype.options;return z(this.prototype,t),t.options&&(this.prototype.options=n,this.mergeOptions(t.options)),this},pe.mergeOptions=function(t){return z(this.prototype.options,t),this},pe.addInitHook=function(t){var n=Array.prototype.slice.call(arguments,1),o=typeof t=="function"?t:function(){this[t].apply(this,n)};return this.prototype._initHooks=this.prototype._initHooks||[],this.prototype._initHooks.push(o),this};function va(t){if(!(typeof L>"u"||!L||!L.Mixin)){t=de(t)?t:[t];for(var n=0;n<t.length;n++)t[n]===L.Mixin.Events&&console.warn("Deprecated include of L.Mixin.Events: this property will be removed in future releases, please inherit from L.Evented instead.",new Error().stack)}}var ie={on:function(t,n,o){if(typeof t=="object")for(var u in t)this._on(u,t[u],n);else{t=ee(t);for(var c=0,g=t.length;c<g;c++)this._on(t[c],n,o)}return this},off:function(t,n,o){if(!arguments.length)delete this._events;else if(typeof t=="object")for(var u in t)this._off(u,t[u],n);else{t=ee(t);for(var c=arguments.length===1,g=0,B=t.length;g<B;g++)c?this._off(t[g]):this._off(t[g],n,o)}return this},_on:function(t,n,o,u){if(typeof n!="function"){console.warn("wrong listener type: "+typeof n);return}if(this._listens(t,n,o)===!1){o===this&&(o=void 0);var c={fn:n,ctx:o};u&&(c.once=!0),this._events=this._events||{},this._events[t]=this._events[t]||[],this._events[t].push(c)}},_off:function(t,n,o){var u,c,g;if(this._events&&(u=this._events[t],!!u)){if(arguments.length===1){if(this._firingCount)for(c=0,g=u.length;c<g;c++)u[c].fn=et;delete this._events[t];return}if(typeof n!="function"){console.warn("wrong listener type: "+typeof n);return}var B=this._listens(t,n,o);if(B!==!1){var Z=u[B];this._firingCount&&(Z.fn=et,this._events[t]=u=u.slice()),u.splice(B,1)}}},fire:function(t,n,o){if(!this.listens(t,o))return this;var u=z({},n,{type:t,target:this,sourceTarget:n&&n.sourceTarget||this});if(this._events){var c=this._events[t];if(c){this._firingCount=this._firingCount+1||1;for(var g=0,B=c.length;g<B;g++){var Z=c[g],H=Z.fn;Z.once&&this.off(t,H,Z.ctx),H.call(Z.ctx||this,u)}this._firingCount--}}return o&&this._propagateEvent(u),this},listens:function(t,n,o,u){typeof t!="string"&&console.warn('"string" type argument expected');var c=n;typeof n!="function"&&(u=!!n,c=void 0,o=void 0);var g=this._events&&this._events[t];if(g&&g.length&&this._listens(t,c,o)!==!1)return!0;if(u){for(var B in this._eventParents)if(this._eventParents[B].listens(t,n,o,u))return!0}return!1},_listens:function(t,n,o){if(!this._events)return!1;var u=this._events[t]||[];if(!n)return!!u.length;o===this&&(o=void 0);for(var c=0,g=u.length;c<g;c++)if(u[c].fn===n&&u[c].ctx===o)return c;return!1},once:function(t,n,o){if(typeof t=="object")for(var u in t)this._on(u,t[u],n,!0);else{t=ee(t);for(var c=0,g=t.length;c<g;c++)this._on(t[c],n,o,!0)}return this},addEventParent:function(t){return this._eventParents=this._eventParents||{},this._eventParents[M(t)]=t,this},removeEventParent:function(t){return this._eventParents&&delete this._eventParents[M(t)],this},_propagateEvent:function(t){for(var n in this._eventParents)this._eventParents[n].fire(t.type,z({layer:t.target,propagatedFrom:t.target},t),!0)}};ie.addEventListener=ie.on,ie.removeEventListener=ie.clearAllEventListeners=ie.off,ie.addOneTimeEventListener=ie.once,ie.fireEvent=ie.fire,ie.hasEventListeners=ie.listens;var di=pe.extend(ie);function dt(t,n,o){this.x=o?Math.round(t):t,this.y=o?Math.round(n):n}var ur=Math.trunc||function(t){return t>0?Math.floor(t):Math.ceil(t)};dt.prototype={clone:function(){return new dt(this.x,this.y)},add:function(t){return this.clone()._add(ht(t))},_add:function(t){return this.x+=t.x,this.y+=t.y,this},subtract:function(t){return this.clone()._subtract(ht(t))},_subtract:function(t){return this.x-=t.x,this.y-=t.y,this},divideBy:function(t){return this.clone()._divideBy(t)},_divideBy:function(t){return this.x/=t,this.y/=t,this},multiplyBy:function(t){return this.clone()._multiplyBy(t)},_multiplyBy:function(t){return this.x*=t,this.y*=t,this},scaleBy:function(t){return new dt(this.x*t.x,this.y*t.y)},unscaleBy:function(t){return new dt(this.x/t.x,this.y/t.y)},round:function(){return this.clone()._round()},_round:function(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this},floor:function(){return this.clone()._floor()},_floor:function(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this},ceil:function(){return this.clone()._ceil()},_ceil:function(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this},trunc:function(){return this.clone()._trunc()},_trunc:function(){return this.x=ur(this.x),this.y=ur(this.y),this},distanceTo:function(t){t=ht(t);var n=t.x-this.x,o=t.y-this.y;return Math.sqrt(n*n+o*o)},equals:function(t){return t=ht(t),t.x===this.x&&t.y===this.y},contains:function(t){return t=ht(t),Math.abs(t.x)<=Math.abs(this.x)&&Math.abs(t.y)<=Math.abs(this.y)},toString:function(){return"Point("+lt(this.x)+", "+lt(this.y)+")"}};function ht(t,n,o){return t instanceof dt?t:de(t)?new dt(t[0],t[1]):t==null?t:typeof t=="object"&&"x"in t&&"y"in t?new dt(t.x,t.y):new dt(t,n,o)}function Tt(t,n){if(t)for(var o=n?[t,n]:t,u=0,c=o.length;u<c;u++)this.extend(o[u])}Tt.prototype={extend:function(t){var n,o;if(!t)return this;if(t instanceof dt||typeof t[0]=="number"||"x"in t)n=o=ht(t);else if(t=Gt(t),n=t.min,o=t.max,!n||!o)return this;return!this.min&&!this.max?(this.min=n.clone(),this.max=o.clone()):(this.min.x=Math.min(n.x,this.min.x),this.max.x=Math.max(o.x,this.max.x),this.min.y=Math.min(n.y,this.min.y),this.max.y=Math.max(o.y,this.max.y)),this},getCenter:function(t){return ht((this.min.x+this.max.x)/2,(this.min.y+this.max.y)/2,t)},getBottomLeft:function(){return ht(this.min.x,this.max.y)},getTopRight:function(){return ht(this.max.x,this.min.y)},getTopLeft:function(){return this.min},getBottomRight:function(){return this.max},getSize:function(){return this.max.subtract(this.min)},contains:function(t){var n,o;return typeof t[0]=="number"||t instanceof dt?t=ht(t):t=Gt(t),t instanceof Tt?(n=t.min,o=t.max):n=o=t,n.x>=this.min.x&&o.x<=this.max.x&&n.y>=this.min.y&&o.y<=this.max.y},intersects:function(t){t=Gt(t);var n=this.min,o=this.max,u=t.min,c=t.max,g=c.x>=n.x&&u.x<=o.x,B=c.y>=n.y&&u.y<=o.y;return g&&B},overlaps:function(t){t=Gt(t);var n=this.min,o=this.max,u=t.min,c=t.max,g=c.x>n.x&&u.x<o.x,B=c.y>n.y&&u.y<o.y;return g&&B},isValid:function(){return!!(this.min&&this.max)},pad:function(t){var n=this.min,o=this.max,u=Math.abs(n.x-o.x)*t,c=Math.abs(n.y-o.y)*t;return Gt(ht(n.x-u,n.y-c),ht(o.x+u,o.y+c))},equals:function(t){return t?(t=Gt(t),this.min.equals(t.getTopLeft())&&this.max.equals(t.getBottomRight())):!1}};function Gt(t,n){return!t||t instanceof Tt?t:new Tt(t,n)}function Yt(t,n){if(t)for(var o=n?[t,n]:t,u=0,c=o.length;u<c;u++)this.extend(o[u])}Yt.prototype={extend:function(t){var n=this._southWest,o=this._northEast,u,c;if(t instanceof Ct)u=t,c=t;else if(t instanceof Yt){if(u=t._southWest,c=t._northEast,!u||!c)return this}else return t?this.extend(gt(t)||Ft(t)):this;return!n&&!o?(this._southWest=new Ct(u.lat,u.lng),this._northEast=new Ct(c.lat,c.lng)):(n.lat=Math.min(u.lat,n.lat),n.lng=Math.min(u.lng,n.lng),o.lat=Math.max(c.lat,o.lat),o.lng=Math.max(c.lng,o.lng)),this},pad:function(t){var n=this._southWest,o=this._northEast,u=Math.abs(n.lat-o.lat)*t,c=Math.abs(n.lng-o.lng)*t;return new Yt(new Ct(n.lat-u,n.lng-c),new Ct(o.lat+u,o.lng+c))},getCenter:function(){return new Ct((this._southWest.lat+this._northEast.lat)/2,(this._southWest.lng+this._northEast.lng)/2)},getSouthWest:function(){return this._southWest},getNorthEast:function(){return this._northEast},getNorthWest:function(){return new Ct(this.getNorth(),this.getWest())},getSouthEast:function(){return new Ct(this.getSouth(),this.getEast())},getWest:function(){return this._southWest.lng},getSouth:function(){return this._southWest.lat},getEast:function(){return this._northEast.lng},getNorth:function(){return this._northEast.lat},contains:function(t){typeof t[0]=="number"||t instanceof Ct||"lat"in t?t=gt(t):t=Ft(t);var n=this._southWest,o=this._northEast,u,c;return t instanceof Yt?(u=t.getSouthWest(),c=t.getNorthEast()):u=c=t,u.lat>=n.lat&&c.lat<=o.lat&&u.lng>=n.lng&&c.lng<=o.lng},intersects:function(t){t=Ft(t);var n=this._southWest,o=this._northEast,u=t.getSouthWest(),c=t.getNorthEast(),g=c.lat>=n.lat&&u.lat<=o.lat,B=c.lng>=n.lng&&u.lng<=o.lng;return g&&B},overlaps:function(t){t=Ft(t);var n=this._southWest,o=this._northEast,u=t.getSouthWest(),c=t.getNorthEast(),g=c.lat>n.lat&&u.lat<o.lat,B=c.lng>n.lng&&u.lng<o.lng;return g&&B},toBBoxString:function(){return[this.getWest(),this.getSouth(),this.getEast(),this.getNorth()].join(",")},equals:function(t,n){return t?(t=Ft(t),this._southWest.equals(t.getSouthWest(),n)&&this._northEast.equals(t.getNorthEast(),n)):!1},isValid:function(){return!!(this._southWest&&this._northEast)}};function Ft(t,n){return t instanceof Yt?t:new Yt(t,n)}function Ct(t,n,o){if(isNaN(t)||isNaN(n))throw new Error("Invalid LatLng object: ("+t+", "+n+")");this.lat=+t,this.lng=+n,o!==void 0&&(this.alt=+o)}Ct.prototype={equals:function(t,n){if(!t)return!1;t=gt(t);var o=Math.max(Math.abs(this.lat-t.lat),Math.abs(this.lng-t.lng));return o<=(n===void 0?1e-9:n)},toString:function(t){return"LatLng("+lt(this.lat,t)+", "+lt(this.lng,t)+")"},distanceTo:function(t){return Ae.distance(this,gt(t))},wrap:function(){return Ae.wrapLatLng(this)},toBounds:function(t){var n=180*t/40075017,o=n/Math.cos(Math.PI/180*this.lat);return Ft([this.lat-n,this.lng-o],[this.lat+n,this.lng+o])},clone:function(){return new Ct(this.lat,this.lng,this.alt)}};function gt(t,n,o){return t instanceof Ct?t:de(t)&&typeof t[0]!="object"?t.length===3?new Ct(t[0],t[1],t[2]):t.length===2?new Ct(t[0],t[1]):null:t==null?t:typeof t=="object"&&"lat"in t?new Ct(t.lat,"lng"in t?t.lng:t.lon,t.alt):n===void 0?null:new Ct(t,n,o)}var Me={latLngToPoint:function(t,n){var o=this.projection.project(t),u=this.scale(n);return this.transformation._transform(o,u)},pointToLatLng:function(t,n){var o=this.scale(n),u=this.transformation.untransform(t,o);return this.projection.unproject(u)},project:function(t){return this.projection.project(t)},unproject:function(t){return this.projection.unproject(t)},scale:function(t){return 256*Math.pow(2,t)},zoom:function(t){return Math.log(t/256)/Math.LN2},getProjectedBounds:function(t){if(this.infinite)return null;var n=this.projection.bounds,o=this.scale(t),u=this.transformation.transform(n.min,o),c=this.transformation.transform(n.max,o);return new Tt(u,c)},infinite:!1,wrapLatLng:function(t){var n=this.wrapLng?$(t.lng,this.wrapLng,!0):t.lng,o=this.wrapLat?$(t.lat,this.wrapLat,!0):t.lat,u=t.alt;return new Ct(o,n,u)},wrapLatLngBounds:function(t){var n=t.getCenter(),o=this.wrapLatLng(n),u=n.lat-o.lat,c=n.lng-o.lng;if(u===0&&c===0)return t;var g=t.getSouthWest(),B=t.getNorthEast(),Z=new Ct(g.lat-u,g.lng-c),H=new Ct(B.lat-u,B.lng-c);return new Yt(Z,H)}},Ae=z({},Me,{wrapLng:[-180,180],R:6371e3,distance:function(t,n){var o=Math.PI/180,u=t.lat*o,c=n.lat*o,g=Math.sin((n.lat-t.lat)*o/2),B=Math.sin((n.lng-t.lng)*o/2),Z=g*g+Math.cos(u)*Math.cos(c)*B*B,H=2*Math.atan2(Math.sqrt(Z),Math.sqrt(1-Z));return this.R*H}}),lr=6378137,dn={R:lr,MAX_LATITUDE:85.0511287798,project:function(t){var n=Math.PI/180,o=this.MAX_LATITUDE,u=Math.max(Math.min(o,t.lat),-o),c=Math.sin(u*n);return new dt(this.R*t.lng*n,this.R*Math.log((1+c)/(1-c))/2)},unproject:function(t){var n=180/Math.PI;return new Ct((2*Math.atan(Math.exp(t.y/this.R))-Math.PI/2)*n,t.x*n/this.R)},bounds:function(){var t=lr*Math.PI;return new Tt([-t,-t],[t,t])}()};function Je(t,n,o,u){if(de(t)){this._a=t[0],this._b=t[1],this._c=t[2],this._d=t[3];return}this._a=t,this._b=n,this._c=o,this._d=u}Je.prototype={transform:function(t,n){return this._transform(t.clone(),n)},_transform:function(t,n){return n=n||1,t.x=n*(this._a*t.x+this._b),t.y=n*(this._c*t.y+this._d),t},untransform:function(t,n){return n=n||1,new dt((t.x/n-this._b)/this._a,(t.y/n-this._d)/this._c)}};function pi(t,n,o,u){return new Je(t,n,o,u)}var pn=z({},Ae,{code:"EPSG:3857",projection:dn,transformation:function(){var t=.5/(Math.PI*dn.R);return pi(t,.5,-t,.5)}()}),La=z({},pn,{code:"EPSG:900913"});function hr(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}function fn(t,n){var o="",u,c,g,B,Z,H;for(u=0,g=t.length;u<g;u++){for(Z=t[u],c=0,B=Z.length;c<B;c++)H=Z[c],o+=(c?"L":"M")+H.x+" "+H.y;o+=n?at.svg?"z":"x":""}return o||"M0 0"}var _n=document.documentElement.style,Si="ActiveXObject"in window,cr=Si&&!document.addEventListener,Ti="msLaunchUri"in navigator&&!("documentMode"in document),Di=ve("webkit"),dr=ve("android"),pr=ve("android 2")||ve("android 3"),ba=parseInt(/WebKit\/([0-9]+)|$/.exec(navigator.userAgent)[1],10),xa=dr&&ve("Google")&&ba<537&&!("AudioNode"in window),mn=!!window.opera,fr=!Ti&&ve("chrome"),_r=ve("gecko")&&!Di&&!mn&&!Si,Ca=!fr&&ve("safari"),mr=ve("phantom"),gn="OTransition"in _n,gr=navigator.platform.indexOf("Win")===0,yr=Si&&"transition"in _n,Fe="WebKitCSSMatrix"in window&&"m11"in new window.WebKitCSSMatrix&&!pr,vr="MozPerspective"in _n,Lr=!window.L_DISABLE_3D&&(yr||Fe||vr)&&!gn&&!mr,Ee=typeof orientation<"u"||ve("mobile"),br=Ee&&Di,yn=Ee&&Fe,xr=!window.PointerEvent&&window.MSPointerEvent,Cr=!!(window.PointerEvent||xr),vn="ontouchstart"in window||!!window.TouchEvent,wa=!window.L_NO_TOUCH&&(vn||Cr),ka=Ee&&mn,Ma=Ee&&_r,Ea=(window.devicePixelRatio||window.screen.deviceXDPI/window.screen.logicalXDPI)>1,wr=function(){var t=!1;try{var n=Object.defineProperty({},"passive",{get:function(){t=!0}});window.addEventListener("testPassiveEventSupport",et,n),window.removeEventListener("testPassiveEventSupport",et,n)}catch{}return t}(),kr=function(){return!!document.createElement("canvas").getContext}(),Ln=!!(document.createElementNS&&hr("svg").createSVGRect),Ba=!!Ln&&function(){var t=document.createElement("div");return t.innerHTML="<svg/>",(t.firstChild&&t.firstChild.namespaceURI)==="http://www.w3.org/2000/svg"}(),Pa=!Ln&&function(){try{var t=document.createElement("div");t.innerHTML='<v:shape adj="1"/>';var n=t.firstChild;return n.style.behavior="url(#default#VML)",n&&typeof n.adj=="object"}catch{return!1}}(),Mr=navigator.platform.indexOf("Mac")===0,Sa=navigator.platform.indexOf("Linux")===0;function ve(t){return navigator.userAgent.toLowerCase().indexOf(t)>=0}var at={ie:Si,ielt9:cr,edge:Ti,webkit:Di,android:dr,android23:pr,androidStock:xa,opera:mn,chrome:fr,gecko:_r,safari:Ca,phantom:mr,opera12:gn,win:gr,ie3d:yr,webkit3d:Fe,gecko3d:vr,any3d:Lr,mobile:Ee,mobileWebkit:br,mobileWebkit3d:yn,msPointer:xr,pointer:Cr,touch:wa,touchNative:vn,mobileOpera:ka,mobileGecko:Ma,retina:Ea,passiveEvents:wr,canvas:kr,svg:Ln,vml:Pa,inlineSvg:Ba,mac:Mr,linux:Sa},bn=at.msPointer?"MSPointerDown":"pointerdown",Er=at.msPointer?"MSPointerMove":"pointermove",Br=at.msPointer?"MSPointerUp":"pointerup",Pr=at.msPointer?"MSPointerCancel":"pointercancel",Ai={touchstart:bn,touchmove:Er,touchend:Br,touchcancel:Pr},Sr={touchstart:xn,touchmove:$e,touchend:$e,touchcancel:$e},Ye={},Tr=!1;function Ta(t,n,o){return n==="touchstart"&&Oa(),Sr[n]?(o=Sr[n].bind(this,o),t.addEventListener(Ai[n],o,!1),o):(console.warn("wrong event specified:",n),et)}function Da(t,n,o){if(!Ai[n]){console.warn("wrong event specified:",n);return}t.removeEventListener(Ai[n],o,!1)}function Aa(t){Ye[t.pointerId]=t}function Fa(t){Ye[t.pointerId]&&(Ye[t.pointerId]=t)}function Dr(t){delete Ye[t.pointerId]}function Oa(){Tr||(document.addEventListener(bn,Aa,!0),document.addEventListener(Er,Fa,!0),document.addEventListener(Br,Dr,!0),document.addEventListener(Pr,Dr,!0),Tr=!0)}function $e(t,n){if(n.pointerType!==(n.MSPOINTER_TYPE_MOUSE||"mouse")){n.touches=[];for(var o in Ye)n.touches.push(Ye[o]);n.changedTouches=[n],t(n)}}function xn(t,n){n.MSPOINTER_TYPE_TOUCH&&n.pointerType===n.MSPOINTER_TYPE_TOUCH&&Zt(n),$e(t,n)}function Ra(t){var n={},o,u;for(u in t)o=t[u],n[u]=o&&o.bind?o.bind(t):o;return t=n,n.type="dblclick",n.detail=2,n.isTrusted=!1,n._simulated=!0,n}var Ia=200;function za(t,n){t.addEventListener("dblclick",n);var o=0,u;function c(g){if(g.detail!==1){u=g.detail;return}if(!(g.pointerType==="mouse"||g.sourceCapabilities&&!g.sourceCapabilities.firesTouchEvents)){var B=Ir(g);if(!(B.some(function(H){return H instanceof HTMLLabelElement&&H.attributes.for})&&!B.some(function(H){return H instanceof HTMLInputElement||H instanceof HTMLSelectElement}))){var Z=Date.now();Z-o<=Ia?(u++,u===2&&n(Ra(g))):u=1,o=Z}}}return t.addEventListener("click",c),{dblclick:n,simDblclick:c}}function Na(t,n){t.removeEventListener("dblclick",n.dblclick),t.removeEventListener("click",n.simDblclick)}var Cn=Ii(["transform","webkitTransform","OTransform","MozTransform","msTransform"]),fi=Ii(["webkitTransition","transition","OTransition","MozTransition","msTransition"]),Ar=fi==="webkitTransition"||fi==="OTransition"?fi+"End":"transitionend";function Fr(t){return typeof t=="string"?document.getElementById(t):t}function _i(t,n){var o=t.style[n]||t.currentStyle&&t.currentStyle[n];if((!o||o==="auto")&&document.defaultView){var u=document.defaultView.getComputedStyle(t,null);o=u?u[n]:null}return o==="auto"?null:o}function bt(t,n,o){var u=document.createElement(t);return u.className=n||"",o&&o.appendChild(u),u}function Mt(t){var n=t.parentNode;n&&n.removeChild(t)}function Fi(t){for(;t.firstChild;)t.removeChild(t.firstChild)}function Ge(t){var n=t.parentNode;n&&n.lastChild!==t&&n.appendChild(t)}function Ze(t){var n=t.parentNode;n&&n.firstChild!==t&&n.insertBefore(t,n.firstChild)}function wn(t,n){if(t.classList!==void 0)return t.classList.contains(n);var o=Ri(t);return o.length>0&&new RegExp("(^|\\s)"+n+"(\\s|$)").test(o)}function ft(t,n){if(t.classList!==void 0)for(var o=ee(n),u=0,c=o.length;u<c;u++)t.classList.add(o[u]);else if(!wn(t,n)){var g=Ri(t);Oi(t,(g?g+" ":"")+n)}}function At(t,n){t.classList!==void 0?t.classList.remove(n):Oi(t,St((" "+Ri(t)+" ").replace(" "+n+" "," ")))}function Oi(t,n){t.className.baseVal===void 0?t.className=n:t.className.baseVal=n}function Ri(t){return t.correspondingElement&&(t=t.correspondingElement),t.className.baseVal===void 0?t.className:t.className.baseVal}function oe(t,n){"opacity"in t.style?t.style.opacity=n:"filter"in t.style&&Ga(t,n)}function Ga(t,n){var o=!1,u="DXImageTransform.Microsoft.Alpha";try{o=t.filters.item(u)}catch{if(n===1)return}n=Math.round(n*100),o?(o.Enabled=n!==100,o.Opacity=n):t.style.filter+=" progid:"+u+"(opacity="+n+")"}function Ii(t){for(var n=document.documentElement.style,o=0;o<t.length;o++)if(t[o]in n)return t[o];return!1}function je(t,n,o){var u=n||new dt(0,0);t.style[Cn]=(at.ie3d?"translate("+u.x+"px,"+u.y+"px)":"translate3d("+u.x+"px,"+u.y+"px,0)")+(o?" scale("+o+")":"")}function Ot(t,n){t._leaflet_pos=n,at.any3d?je(t,n):(t.style.left=n.x+"px",t.style.top=n.y+"px")}function Ue(t){return t._leaflet_pos||new dt(0,0)}var mi,gi,kn;if("onselectstart"in document)mi=function(){pt(window,"selectstart",Zt)},gi=function(){Et(window,"selectstart",Zt)};else{var yi=Ii(["userSelect","WebkitUserSelect","OUserSelect","MozUserSelect","msUserSelect"]);mi=function(){if(yi){var t=document.documentElement.style;kn=t[yi],t[yi]="none"}},gi=function(){yi&&(document.documentElement.style[yi]=kn,kn=void 0)}}function Mn(){pt(window,"dragstart",Zt)}function zi(){Et(window,"dragstart",Zt)}var Ni,En;function Bn(t){for(;t.tabIndex===-1;)t=t.parentNode;t.style&&(Gi(),Ni=t,En=t.style.outlineStyle,t.style.outlineStyle="none",pt(window,"keydown",Gi))}function Gi(){Ni&&(Ni.style.outlineStyle=En,Ni=void 0,En=void 0,Et(window,"keydown",Gi))}function Or(t){do t=t.parentNode;while((!t.offsetWidth||!t.offsetHeight)&&t!==document.body);return t}function Pn(t){var n=t.getBoundingClientRect();return{x:n.width/t.offsetWidth||1,y:n.height/t.offsetHeight||1,boundingClientRect:n}}var Za={__proto__:null,TRANSFORM:Cn,TRANSITION:fi,TRANSITION_END:Ar,get:Fr,getStyle:_i,create:bt,remove:Mt,empty:Fi,toFront:Ge,toBack:Ze,hasClass:wn,addClass:ft,removeClass:At,setClass:Oi,getClass:Ri,setOpacity:oe,testProp:Ii,setTransform:je,setPosition:Ot,getPosition:Ue,get disableTextSelection(){return mi},get enableTextSelection(){return gi},disableImageDrag:Mn,enableImageDrag:zi,preventOutline:Bn,restoreOutline:Gi,getSizedParentNode:Or,getScale:Pn};function pt(t,n,o,u){if(n&&typeof n=="object")for(var c in n)Tn(t,c,n[c],o);else{n=ee(n);for(var g=0,B=n.length;g<B;g++)Tn(t,n[g],o,u)}return this}var Le="_leaflet_events";function Et(t,n,o,u){if(arguments.length===1)Rr(t),delete t[Le];else if(n&&typeof n=="object")for(var c in n)Dn(t,c,n[c],o);else if(n=ee(n),arguments.length===2)Rr(t,function(Z){return ln(n,Z)!==-1});else for(var g=0,B=n.length;g<B;g++)Dn(t,n[g],o,u);return this}function Rr(t,n){for(var o in t[Le]){var u=o.split(/\d/)[0];(!n||n(u))&&Dn(t,u,null,null,o)}}var Sn={mouseenter:"mouseover",mouseleave:"mouseout",wheel:!("onwheel"in window)&&"mousewheel"};function Tn(t,n,o,u){var c=n+M(o)+(u?"_"+M(u):"");if(t[Le]&&t[Le][c])return this;var g=function(Z){return o.call(u||t,Z||window.event)},B=g;!at.touchNative&&at.pointer&&n.indexOf("touch")===0?g=Ta(t,n,g):at.touch&&n==="dblclick"?g=za(t,g):"addEventListener"in t?n==="touchstart"||n==="touchmove"||n==="wheel"||n==="mousewheel"?t.addEventListener(Sn[n]||n,g,at.passiveEvents?{passive:!1}:!1):n==="mouseenter"||n==="mouseleave"?(g=function(Z){Z=Z||window.event,An(t,Z)&&B(Z)},t.addEventListener(Sn[n],g,!1)):t.addEventListener(n,B,!1):t.attachEvent("on"+n,g),t[Le]=t[Le]||{},t[Le][c]=g}function Dn(t,n,o,u,c){c=c||n+M(o)+(u?"_"+M(u):"");var g=t[Le]&&t[Le][c];if(!g)return this;!at.touchNative&&at.pointer&&n.indexOf("touch")===0?Da(t,n,g):at.touch&&n==="dblclick"?Na(t,g):"removeEventListener"in t?t.removeEventListener(Sn[n]||n,g,!1):t.detachEvent("on"+n,g),t[Le][c]=null}function Ve(t){return t.stopPropagation?t.stopPropagation():t.originalEvent?t.originalEvent._stopped=!0:t.cancelBubble=!0,this}function Oe(t){return Tn(t,"wheel",Ve),this}function vi(t){return pt(t,"mousedown touchstart dblclick contextmenu",Ve),t._leaflet_disable_click=!0,this}function Zt(t){return t.preventDefault?t.preventDefault():t.returnValue=!1,this}function He(t){return Zt(t),Ve(t),this}function Ir(t){if(t.composedPath)return t.composedPath();for(var n=[],o=t.target;o;)n.push(o),o=o.parentNode;return n}function zr(t,n){if(!n)return new dt(t.clientX,t.clientY);var o=Pn(n),u=o.boundingClientRect;return new dt((t.clientX-u.left)/o.x-n.clientLeft,(t.clientY-u.top)/o.y-n.clientTop)}var ja=at.linux&&at.chrome?window.devicePixelRatio:at.mac?window.devicePixelRatio*3:window.devicePixelRatio>0?2*window.devicePixelRatio:1;function Nr(t){return at.edge?t.wheelDeltaY/2:t.deltaY&&t.deltaMode===0?-t.deltaY/ja:t.deltaY&&t.deltaMode===1?-t.deltaY*20:t.deltaY&&t.deltaMode===2?-t.deltaY*60:t.deltaX||t.deltaZ?0:t.wheelDelta?(t.wheelDeltaY||t.wheelDelta)/2:t.detail&&Math.abs(t.detail)<32765?-t.detail*20:t.detail?t.detail/-32765*60:0}function An(t,n){var o=n.relatedTarget;if(!o)return!0;try{for(;o&&o!==t;)o=o.parentNode}catch{return!1}return o!==t}var Ua={__proto__:null,on:pt,off:Et,stopPropagation:Ve,disableScrollPropagation:Oe,disableClickPropagation:vi,preventDefault:Zt,stop:He,getPropagationPath:Ir,getMousePosition:zr,getWheelDelta:Nr,isExternalTarget:An,addListener:pt,removeListener:Et},Gr=di.extend({run:function(t,n,o,u){this.stop(),this._el=t,this._inProgress=!0,this._duration=o||.25,this._easeOutPower=1/Math.max(u||.5,.2),this._startPos=Ue(t),this._offset=n.subtract(this._startPos),this._startTime=+new Date,this.fire("start"),this._animate()},stop:function(){this._inProgress&&(this._step(!0),this._complete())},_animate:function(){this._animId=Jt(this._animate,this),this._step()},_step:function(t){var n=+new Date-this._startTime,o=this._duration*1e3;n<o?this._runFrame(this._easeOut(n/o),t):(this._runFrame(1),this._complete())},_runFrame:function(t,n){var o=this._startPos.add(this._offset.multiplyBy(t));n&&o._round(),Ot(this._el,o),this.fire("step")},_complete:function(){Vt(this._animId),this._inProgress=!1,this.fire("end")},_easeOut:function(t){return 1-Math.pow(1-t,this._easeOutPower)}}),mt=di.extend({options:{crs:pn,center:void 0,zoom:void 0,minZoom:void 0,maxZoom:void 0,layers:[],maxBounds:void 0,renderer:void 0,zoomAnimation:!0,zoomAnimationThreshold:4,fadeAnimation:!0,markerZoomAnimation:!0,transform3DLimit:8388608,zoomSnap:1,zoomDelta:1,trackResize:!0},initialize:function(t,n){n=kt(this,n),this._handlers=[],this._layers={},this._zoomBoundLayers={},this._sizeChanged=!0,this._initContainer(t),this._initLayout(),this._onResize=V(this._onResize,this),this._initEvents(),n.maxBounds&&this.setMaxBounds(n.maxBounds),n.zoom!==void 0&&(this._zoom=this._limitZoom(n.zoom)),n.center&&n.zoom!==void 0&&this.setView(gt(n.center),n.zoom,{reset:!0}),this.callInitHooks(),this._zoomAnimated=fi&&at.any3d&&!at.mobileOpera&&this.options.zoomAnimation,this._zoomAnimated&&(this._createAnimProxy(),pt(this._proxy,Ar,this._catchTransitionEnd,this)),this._addLayers(this.options.layers)},setView:function(t,n,o){if(n=n===void 0?this._zoom:this._limitZoom(n),t=this._limitCenter(gt(t),n,this.options.maxBounds),o=o||{},this._stop(),this._loaded&&!o.reset&&o!==!0){o.animate!==void 0&&(o.zoom=z({animate:o.animate},o.zoom),o.pan=z({animate:o.animate,duration:o.duration},o.pan));var u=this._zoom!==n?this._tryAnimatedZoom&&this._tryAnimatedZoom(t,n,o.zoom):this._tryAnimatedPan(t,o.pan);if(u)return clearTimeout(this._sizeTimer),this}return this._resetView(t,n,o.pan&&o.pan.noMoveStart),this},setZoom:function(t,n){return this._loaded?this.setView(this.getCenter(),t,{zoom:n}):(this._zoom=t,this)},zoomIn:function(t,n){return t=t||(at.any3d?this.options.zoomDelta:1),this.setZoom(this._zoom+t,n)},zoomOut:function(t,n){return t=t||(at.any3d?this.options.zoomDelta:1),this.setZoom(this._zoom-t,n)},setZoomAround:function(t,n,o){var u=this.getZoomScale(n),c=this.getSize().divideBy(2),g=t instanceof dt?t:this.latLngToContainerPoint(t),B=g.subtract(c).multiplyBy(1-1/u),Z=this.containerPointToLatLng(c.add(B));return this.setView(Z,n,{zoom:o})},_getBoundsCenterZoom:function(t,n){n=n||{},t=t.getBounds?t.getBounds():Ft(t);var o=ht(n.paddingTopLeft||n.padding||[0,0]),u=ht(n.paddingBottomRight||n.padding||[0,0]),c=this.getBoundsZoom(t,!1,o.add(u));if(c=typeof n.maxZoom=="number"?Math.min(n.maxZoom,c):c,c===1/0)return{center:t.getCenter(),zoom:c};var g=u.subtract(o).divideBy(2),B=this.project(t.getSouthWest(),c),Z=this.project(t.getNorthEast(),c),H=this.unproject(B.add(Z).divideBy(2).add(g),c);return{center:H,zoom:c}},fitBounds:function(t,n){if(t=Ft(t),!t.isValid())throw new Error("Bounds are not valid.");var o=this._getBoundsCenterZoom(t,n);return this.setView(o.center,o.zoom,n)},fitWorld:function(t){return this.fitBounds([[-90,-180],[90,180]],t)},panTo:function(t,n){return this.setView(t,this._zoom,{pan:n})},panBy:function(t,n){if(t=ht(t).round(),n=n||{},!t.x&&!t.y)return this.fire("moveend");if(n.animate!==!0&&!this.getSize().contains(t))return this._resetView(this.unproject(this.project(this.getCenter()).add(t)),this.getZoom()),this;if(this._panAnim||(this._panAnim=new Gr,this._panAnim.on({step:this._onPanTransitionStep,end:this._onPanTransitionEnd},this)),n.noMoveStart||this.fire("movestart"),n.animate!==!1){ft(this._mapPane,"leaflet-pan-anim");var o=this._getMapPanePos().subtract(t).round();this._panAnim.run(this._mapPane,o,n.duration||.25,n.easeLinearity)}else this._rawPanBy(t),this.fire("move").fire("moveend");return this},flyTo:function(t,n,o){if(o=o||{},o.animate===!1||!at.any3d)return this.setView(t,n,o);this._stop();var u=this.project(this.getCenter()),c=this.project(t),g=this.getSize(),B=this._zoom;t=gt(t),n=n===void 0?B:n;var Z=Math.max(g.x,g.y),H=Z*this.getZoomScale(B,n),Y=c.distanceTo(u)||1,Q=1.42,ut=Q*Q;function _t(Dt){var en=Dt?-1:1,so=Dt?H:Z,aa=H*H-Z*Z+en*ut*ut*Y*Y,oa=2*so*ut*Y,tr=aa/oa,ge=Math.sqrt(tr*tr+1)-tr,uo=ge<1e-9?-18:Math.log(ge);return uo}function Kt(Dt){return(Math.exp(Dt)-Math.exp(-Dt))/2}function Nt(Dt){return(Math.exp(Dt)+Math.exp(-Dt))/2}function ce(Dt){return Kt(Dt)/Nt(Dt)}var Xt=_t(0);function ui(Dt){return Z*(Nt(Xt)/Nt(Xt+Q*Dt))}function Xn(Dt){return Z*(Nt(Xt)*ce(Xt+Q*Dt)-Kt(Xt))/ut}function Te(Dt){return 1-Math.pow(1-Dt,1.5)}var Bi=Date.now(),Qi=(_t(1)-Xt)/Q,tn=o.duration?1e3*o.duration:1e3*Qi*.8;function Qn(){var Dt=(Date.now()-Bi)/tn,en=Te(Dt)*Qi;Dt<=1?(this._flyToFrame=Jt(Qn,this),this._move(this.unproject(u.add(c.subtract(u).multiplyBy(Xn(en)/Y)),B),this.getScaleZoom(Z/ui(en),B),{flyTo:!0})):this._move(t,n)._moveEnd(!0)}return this._moveStart(!0,o.noMoveStart),Qn.call(this),this},flyToBounds:function(t,n){var o=this._getBoundsCenterZoom(t,n);return this.flyTo(o.center,o.zoom,n)},setMaxBounds:function(t){return t=Ft(t),this.listens("moveend",this._panInsideMaxBounds)&&this.off("moveend",this._panInsideMaxBounds),t.isValid()?(this.options.maxBounds=t,this._loaded&&this._panInsideMaxBounds(),this.on("moveend",this._panInsideMaxBounds)):(this.options.maxBounds=null,this)},setMinZoom:function(t){var n=this.options.minZoom;return this.options.minZoom=t,this._loaded&&n!==t&&(this.fire("zoomlevelschange"),this.getZoom()<this.options.minZoom)?this.setZoom(t):this},setMaxZoom:function(t){var n=this.options.maxZoom;return this.options.maxZoom=t,this._loaded&&n!==t&&(this.fire("zoomlevelschange"),this.getZoom()>this.options.maxZoom)?this.setZoom(t):this},panInsideBounds:function(t,n){this._enforcingBounds=!0;var o=this.getCenter(),u=this._limitCenter(o,this._zoom,Ft(t));return o.equals(u)||this.panTo(u,n),this._enforcingBounds=!1,this},panInside:function(t,n){n=n||{};var o=ht(n.paddingTopLeft||n.padding||[0,0]),u=ht(n.paddingBottomRight||n.padding||[0,0]),c=this.project(this.getCenter()),g=this.project(t),B=this.getPixelBounds(),Z=Gt([B.min.add(o),B.max.subtract(u)]),H=Z.getSize();if(!Z.contains(g)){this._enforcingBounds=!0;var Y=g.subtract(Z.getCenter()),Q=Z.extend(g).getSize().subtract(H);c.x+=Y.x<0?-Q.x:Q.x,c.y+=Y.y<0?-Q.y:Q.y,this.panTo(this.unproject(c),n),this._enforcingBounds=!1}return this},invalidateSize:function(t){if(!this._loaded)return this;t=z({animate:!1,pan:!0},t===!0?{animate:!0}:t);var n=this.getSize();this._sizeChanged=!0,this._lastCenter=null;var o=this.getSize(),u=n.divideBy(2).round(),c=o.divideBy(2).round(),g=u.subtract(c);return!g.x&&!g.y?this:(t.animate&&t.pan?this.panBy(g):(t.pan&&this._rawPanBy(g),this.fire("move"),t.debounceMoveend?(clearTimeout(this._sizeTimer),this._sizeTimer=setTimeout(V(this.fire,this,"moveend"),200)):this.fire("moveend")),this.fire("resize",{oldSize:n,newSize:o}))},stop:function(){return this.setZoom(this._limitZoom(this._zoom)),this.options.zoomSnap||this.fire("viewreset"),this._stop()},locate:function(t){if(t=this._locateOptions=z({timeout:1e4,watch:!1},t),!("geolocation"in navigator))return this._handleGeolocationError({code:0,message:"Geolocation not supported."}),this;var n=V(this._handleGeolocationResponse,this),o=V(this._handleGeolocationError,this);return t.watch?this._locationWatchId=navigator.geolocation.watchPosition(n,o,t):navigator.geolocation.getCurrentPosition(n,o,t),this},stopLocate:function(){return navigator.geolocation&&navigator.geolocation.clearWatch&&navigator.geolocation.clearWatch(this._locationWatchId),this._locateOptions&&(this._locateOptions.setView=!1),this},_handleGeolocationError:function(t){if(this._container._leaflet_id){var n=t.code,o=t.message||(n===1?"permission denied":n===2?"position unavailable":"timeout");this._locateOptions.setView&&!this._loaded&&this.fitWorld(),this.fire("locationerror",{code:n,message:"Geolocation error: "+o+"."})}},_handleGeolocationResponse:function(t){if(this._container._leaflet_id){var n=t.coords.latitude,o=t.coords.longitude,u=new Ct(n,o),c=u.toBounds(t.coords.accuracy*2),g=this._locateOptions;if(g.setView){var B=this.getBoundsZoom(c);this.setView(u,g.maxZoom?Math.min(B,g.maxZoom):B)}var Z={latlng:u,bounds:c,timestamp:t.timestamp};for(var H in t.coords)typeof t.coords[H]=="number"&&(Z[H]=t.coords[H]);this.fire("locationfound",Z)}},addHandler:function(t,n){if(!n)return this;var o=this[t]=new n(this);return this._handlers.push(o),this.options[t]&&o.enable(),this},remove:function(){if(this._initEvents(!0),this.options.maxBounds&&this.off("moveend",this._panInsideMaxBounds),this._containerId!==this._container._leaflet_id)throw new Error("Map container is being reused by another instance");try{delete this._container._leaflet_id,delete this._containerId}catch{this._container._leaflet_id=void 0,this._containerId=void 0}this._locationWatchId!==void 0&&this.stopLocate(),this._stop(),Mt(this._mapPane),this._clearControlPos&&this._clearControlPos(),this._resizeRequest&&(Vt(this._resizeRequest),this._resizeRequest=null),this._clearHandlers(),this._loaded&&this.fire("unload");var t;for(t in this._layers)this._layers[t].remove();for(t in this._panes)Mt(this._panes[t]);return this._layers=[],this._panes=[],delete this._mapPane,delete this._renderer,this},createPane:function(t,n){var o="leaflet-pane"+(t?" leaflet-"+t.replace("Pane","")+"-pane":""),u=bt("div",o,n||this._mapPane);return t&&(this._panes[t]=u),u},getCenter:function(){return this._checkIfLoaded(),this._lastCenter&&!this._moved()?this._lastCenter.clone():this.layerPointToLatLng(this._getCenterLayerPoint())},getZoom:function(){return this._zoom},getBounds:function(){var t=this.getPixelBounds(),n=this.unproject(t.getBottomLeft()),o=this.unproject(t.getTopRight());return new Yt(n,o)},getMinZoom:function(){return this.options.minZoom===void 0?this._layersMinZoom||0:this.options.minZoom},getMaxZoom:function(){return this.options.maxZoom===void 0?this._layersMaxZoom===void 0?1/0:this._layersMaxZoom:this.options.maxZoom},getBoundsZoom:function(t,n,o){t=Ft(t),o=ht(o||[0,0]);var u=this.getZoom()||0,c=this.getMinZoom(),g=this.getMaxZoom(),B=t.getNorthWest(),Z=t.getSouthEast(),H=this.getSize().subtract(o),Y=Gt(this.project(Z,u),this.project(B,u)).getSize(),Q=at.any3d?this.options.zoomSnap:1,ut=H.x/Y.x,_t=H.y/Y.y,Kt=n?Math.max(ut,_t):Math.min(ut,_t);return u=this.getScaleZoom(Kt,u),Q&&(u=Math.round(u/(Q/100))*(Q/100),u=n?Math.ceil(u/Q)*Q:Math.floor(u/Q)*Q),Math.max(c,Math.min(g,u))},getSize:function(){return(!this._size||this._sizeChanged)&&(this._size=new dt(this._container.clientWidth||0,this._container.clientHeight||0),this._sizeChanged=!1),this._size.clone()},getPixelBounds:function(t,n){var o=this._getTopLeftPoint(t,n);return new Tt(o,o.add(this.getSize()))},getPixelOrigin:function(){return this._checkIfLoaded(),this._pixelOrigin},getPixelWorldBounds:function(t){return this.options.crs.getProjectedBounds(t===void 0?this.getZoom():t)},getPane:function(t){return typeof t=="string"?this._panes[t]:t},getPanes:function(){return this._panes},getContainer:function(){return this._container},getZoomScale:function(t,n){var o=this.options.crs;return n=n===void 0?this._zoom:n,o.scale(t)/o.scale(n)},getScaleZoom:function(t,n){var o=this.options.crs;n=n===void 0?this._zoom:n;var u=o.zoom(t*o.scale(n));return isNaN(u)?1/0:u},project:function(t,n){return n=n===void 0?this._zoom:n,this.options.crs.latLngToPoint(gt(t),n)},unproject:function(t,n){return n=n===void 0?this._zoom:n,this.options.crs.pointToLatLng(ht(t),n)},layerPointToLatLng:function(t){var n=ht(t).add(this.getPixelOrigin());return this.unproject(n)},latLngToLayerPoint:function(t){var n=this.project(gt(t))._round();return n._subtract(this.getPixelOrigin())},wrapLatLng:function(t){return this.options.crs.wrapLatLng(gt(t))},wrapLatLngBounds:function(t){return this.options.crs.wrapLatLngBounds(Ft(t))},distance:function(t,n){return this.options.crs.distance(gt(t),gt(n))},containerPointToLayerPoint:function(t){return ht(t).subtract(this._getMapPanePos())},layerPointToContainerPoint:function(t){return ht(t).add(this._getMapPanePos())},containerPointToLatLng:function(t){var n=this.containerPointToLayerPoint(ht(t));return this.layerPointToLatLng(n)},latLngToContainerPoint:function(t){return this.layerPointToContainerPoint(this.latLngToLayerPoint(gt(t)))},mouseEventToContainerPoint:function(t){return zr(t,this._container)},mouseEventToLayerPoint:function(t){return this.containerPointToLayerPoint(this.mouseEventToContainerPoint(t))},mouseEventToLatLng:function(t){return this.layerPointToLatLng(this.mouseEventToLayerPoint(t))},_initContainer:function(t){var n=this._container=Fr(t);if(n){if(n._leaflet_id)throw new Error("Map container is already initialized.")}else throw new Error("Map container not found.");pt(n,"scroll",this._onScroll,this),this._containerId=M(n)},_initLayout:function(){var t=this._container;this._fadeAnimated=this.options.fadeAnimation&&at.any3d,ft(t,"leaflet-container"+(at.touch?" leaflet-touch":"")+(at.retina?" leaflet-retina":"")+(at.ielt9?" leaflet-oldie":"")+(at.safari?" leaflet-safari":"")+(this._fadeAnimated?" leaflet-fade-anim":""));var n=_i(t,"position");n!=="absolute"&&n!=="relative"&&n!=="fixed"&&n!=="sticky"&&(t.style.position="relative"),this._initPanes(),this._initControlPos&&this._initControlPos()},_initPanes:function(){var t=this._panes={};this._paneRenderers={},this._mapPane=this.createPane("mapPane",this._container),Ot(this._mapPane,new dt(0,0)),this.createPane("tilePane"),this.createPane("overlayPane"),this.createPane("shadowPane"),this.createPane("markerPane"),this.createPane("tooltipPane"),this.createPane("popupPane"),this.options.markerZoomAnimation||(ft(t.markerPane,"leaflet-zoom-hide"),ft(t.shadowPane,"leaflet-zoom-hide"))},_resetView:function(t,n,o){Ot(this._mapPane,new dt(0,0));var u=!this._loaded;this._loaded=!0,n=this._limitZoom(n),this.fire("viewprereset");var c=this._zoom!==n;this._moveStart(c,o)._move(t,n)._moveEnd(c),this.fire("viewreset"),u&&this.fire("load")},_moveStart:function(t,n){return t&&this.fire("zoomstart"),n||this.fire("movestart"),this},_move:function(t,n,o,u){n===void 0&&(n=this._zoom);var c=this._zoom!==n;return this._zoom=n,this._lastCenter=t,this._pixelOrigin=this._getNewPixelOrigin(t),u?o&&o.pinch&&this.fire("zoom",o):((c||o&&o.pinch)&&this.fire("zoom",o),this.fire("move",o)),this},_moveEnd:function(t){return t&&this.fire("zoomend"),this.fire("moveend")},_stop:function(){return Vt(this._flyToFrame),this._panAnim&&this._panAnim.stop(),this},_rawPanBy:function(t){Ot(this._mapPane,this._getMapPanePos().subtract(t))},_getZoomSpan:function(){return this.getMaxZoom()-this.getMinZoom()},_panInsideMaxBounds:function(){this._enforcingBounds||this.panInsideBounds(this.options.maxBounds)},_checkIfLoaded:function(){if(!this._loaded)throw new Error("Set map center and zoom first.")},_initEvents:function(t){this._targets={},this._targets[M(this._container)]=this;var n=t?Et:pt;n(this._container,"click dblclick mousedown mouseup mouseover mouseout mousemove contextmenu keypress keydown keyup",this._handleDOMEvent,this),this.options.trackResize&&n(window,"resize",this._onResize,this),at.any3d&&this.options.transform3DLimit&&(t?this.off:this.on).call(this,"moveend",this._onMoveEnd)},_onResize:function(){Vt(this._resizeRequest),this._resizeRequest=Jt(function(){this.invalidateSize({debounceMoveend:!0})},this)},_onScroll:function(){this._container.scrollTop=0,this._container.scrollLeft=0},_onMoveEnd:function(){var t=this._getMapPanePos();Math.max(Math.abs(t.x),Math.abs(t.y))>=this.options.transform3DLimit&&this._resetView(this.getCenter(),this.getZoom())},_findEventTargets:function(t,n){for(var o=[],u,c=n==="mouseout"||n==="mouseover",g=t.target||t.srcElement,B=!1;g;){if(u=this._targets[M(g)],u&&(n==="click"||n==="preclick")&&this._draggableMoved(u)){B=!0;break}if(u&&u.listens(n,!0)&&(c&&!An(g,t)||(o.push(u),c))||g===this._container)break;g=g.parentNode}return!o.length&&!B&&!c&&this.listens(n,!0)&&(o=[this]),o},_isClickDisabled:function(t){for(;t&&t!==this._container;){if(t._leaflet_disable_click)return!0;t=t.parentNode}},_handleDOMEvent:function(t){var n=t.target||t.srcElement;if(!(!this._loaded||n._leaflet_disable_events||t.type==="click"&&this._isClickDisabled(n))){var o=t.type;o==="mousedown"&&Bn(n),this._fireDOMEvent(t,o)}},_mouseEvents:["click","dblclick","mouseover","mouseout","contextmenu"],_fireDOMEvent:function(t,n,o){if(t.type==="click"){var u=z({},t);u.type="preclick",this._fireDOMEvent(u,u.type,o)}var c=this._findEventTargets(t,n);if(o){for(var g=[],B=0;B<o.length;B++)o[B].listens(n,!0)&&g.push(o[B]);c=g.concat(c)}if(c.length){n==="contextmenu"&&Zt(t);var Z=c[0],H={originalEvent:t};if(t.type!=="keypress"&&t.type!=="keydown"&&t.type!=="keyup"){var Y=Z.getLatLng&&(!Z._radius||Z._radius<=10);H.containerPoint=Y?this.latLngToContainerPoint(Z.getLatLng()):this.mouseEventToContainerPoint(t),H.layerPoint=this.containerPointToLayerPoint(H.containerPoint),H.latlng=Y?Z.getLatLng():this.layerPointToLatLng(H.layerPoint)}for(B=0;B<c.length;B++)if(c[B].fire(n,H,!0),H.originalEvent._stopped||c[B].options.bubblingMouseEvents===!1&&ln(this._mouseEvents,n)!==-1)return}},_draggableMoved:function(t){return t=t.dragging&&t.dragging.enabled()?t:this,t.dragging&&t.dragging.moved()||this.boxZoom&&this.boxZoom.moved()},_clearHandlers:function(){for(var t=0,n=this._handlers.length;t<n;t++)this._handlers[t].disable()},whenReady:function(t,n){return this._loaded?t.call(n||this,{target:this}):this.on("load",t,n),this},_getMapPanePos:function(){return Ue(this._mapPane)||new dt(0,0)},_moved:function(){var t=this._getMapPanePos();return t&&!t.equals([0,0])},_getTopLeftPoint:function(t,n){var o=t&&n!==void 0?this._getNewPixelOrigin(t,n):this.getPixelOrigin();return o.subtract(this._getMapPanePos())},_getNewPixelOrigin:function(t,n){var o=this.getSize()._divideBy(2);return this.project(t,n)._subtract(o)._add(this._getMapPanePos())._round()},_latLngToNewLayerPoint:function(t,n,o){var u=this._getNewPixelOrigin(o,n);return this.project(t,n)._subtract(u)},_latLngBoundsToNewLayerBounds:function(t,n,o){var u=this._getNewPixelOrigin(o,n);return Gt([this.project(t.getSouthWest(),n)._subtract(u),this.project(t.getNorthWest(),n)._subtract(u),this.project(t.getSouthEast(),n)._subtract(u),this.project(t.getNorthEast(),n)._subtract(u)])},_getCenterLayerPoint:function(){return this.containerPointToLayerPoint(this.getSize()._divideBy(2))},_getCenterOffset:function(t){return this.latLngToLayerPoint(t).subtract(this._getCenterLayerPoint())},_limitCenter:function(t,n,o){if(!o)return t;var u=this.project(t,n),c=this.getSize().divideBy(2),g=new Tt(u.subtract(c),u.add(c)),B=this._getBoundsOffset(g,o,n);return Math.abs(B.x)<=1&&Math.abs(B.y)<=1?t:this.unproject(u.add(B),n)},_limitOffset:function(t,n){if(!n)return t;var o=this.getPixelBounds(),u=new Tt(o.min.add(t),o.max.add(t));return t.add(this._getBoundsOffset(u,n))},_getBoundsOffset:function(t,n,o){var u=Gt(this.project(n.getNorthEast(),o),this.project(n.getSouthWest(),o)),c=u.min.subtract(t.min),g=u.max.subtract(t.max),B=this._rebound(c.x,-g.x),Z=this._rebound(c.y,-g.y);return new dt(B,Z)},_rebound:function(t,n){return t+n>0?Math.round(t-n)/2:Math.max(0,Math.ceil(t))-Math.max(0,Math.floor(n))},_limitZoom:function(t){var n=this.getMinZoom(),o=this.getMaxZoom(),u=at.any3d?this.options.zoomSnap:1;return u&&(t=Math.round(t/u)*u),Math.max(n,Math.min(o,t))},_onPanTransitionStep:function(){this.fire("move")},_onPanTransitionEnd:function(){At(this._mapPane,"leaflet-pan-anim"),this.fire("moveend")},_tryAnimatedPan:function(t,n){var o=this._getCenterOffset(t)._trunc();return(n&&n.animate)!==!0&&!this.getSize().contains(o)?!1:(this.panBy(o,n),!0)},_createAnimProxy:function(){var t=this._proxy=bt("div","leaflet-proxy leaflet-zoom-animated");this._panes.mapPane.appendChild(t),this.on("zoomanim",function(n){var o=Cn,u=this._proxy.style[o];je(this._proxy,this.project(n.center,n.zoom),this.getZoomScale(n.zoom,1)),u===this._proxy.style[o]&&this._animatingZoom&&this._onZoomTransitionEnd()},this),this.on("load moveend",this._animMoveEnd,this),this._on("unload",this._destroyAnimProxy,this)},_destroyAnimProxy:function(){Mt(this._proxy),this.off("load moveend",this._animMoveEnd,this),delete this._proxy},_animMoveEnd:function(){var t=this.getCenter(),n=this.getZoom();je(this._proxy,this.project(t,n),this.getZoomScale(n,1))},_catchTransitionEnd:function(t){this._animatingZoom&&t.propertyName.indexOf("transform")>=0&&this._onZoomTransitionEnd()},_nothingToAnimate:function(){return!this._container.getElementsByClassName("leaflet-zoom-animated").length},_tryAnimatedZoom:function(t,n,o){if(this._animatingZoom)return!0;if(o=o||{},!this._zoomAnimated||o.animate===!1||this._nothingToAnimate()||Math.abs(n-this._zoom)>this.options.zoomAnimationThreshold)return!1;var u=this.getZoomScale(n),c=this._getCenterOffset(t)._divideBy(1-1/u);return o.animate!==!0&&!this.getSize().contains(c)?!1:(Jt(function(){this._moveStart(!0,o.noMoveStart||!1)._animateZoom(t,n,!0)},this),!0)},_animateZoom:function(t,n,o,u){this._mapPane&&(o&&(this._animatingZoom=!0,this._animateToCenter=t,this._animateToZoom=n,ft(this._mapPane,"leaflet-zoom-anim")),this.fire("zoomanim",{center:t,zoom:n,noUpdate:u}),this._tempFireZoomEvent||(this._tempFireZoomEvent=this._zoom!==this._animateToZoom),this._move(this._animateToCenter,this._animateToZoom,void 0,!0),setTimeout(V(this._onZoomTransitionEnd,this),250))},_onZoomTransitionEnd:function(){this._animatingZoom&&(this._mapPane&&At(this._mapPane,"leaflet-zoom-anim"),this._animatingZoom=!1,this._move(this._animateToCenter,this._animateToZoom,void 0,!0),this._tempFireZoomEvent&&this.fire("zoom"),delete this._tempFireZoomEvent,this.fire("move"),this._moveEnd(!0))}});function Va(t,n){return new mt(t,n)}var fe=pe.extend({options:{position:"topright"},initialize:function(t){kt(this,t)},getPosition:function(){return this.options.position},setPosition:function(t){var n=this._map;return n&&n.removeControl(this),this.options.position=t,n&&n.addControl(this),this},getContainer:function(){return this._container},addTo:function(t){this.remove(),this._map=t;var n=this._container=this.onAdd(t),o=this.getPosition(),u=t._controlCorners[o];return ft(n,"leaflet-control"),o.indexOf("bottom")!==-1?u.insertBefore(n,u.firstChild):u.appendChild(n),this._map.on("unload",this.remove,this),this},remove:function(){return this._map?(Mt(this._container),this.onRemove&&this.onRemove(this._map),this._map.off("unload",this.remove,this),this._map=null,this):this},_refocusOnMap:function(t){this._map&&t&&t.screenX>0&&t.screenY>0&&this._map.getContainer().focus()}}),Xe=function(t){return new fe(t)};mt.include({addControl:function(t){return t.addTo(this),this},removeControl:function(t){return t.remove(),this},_initControlPos:function(){var t=this._controlCorners={},n="leaflet-",o=this._controlContainer=bt("div",n+"control-container",this._container);function u(c,g){var B=n+c+" "+n+g;t[c+g]=bt("div",B,o)}u("top","left"),u("top","right"),u("bottom","left"),u("bottom","right")},_clearControlPos:function(){for(var t in this._controlCorners)Mt(this._controlCorners[t]);Mt(this._controlContainer),delete this._controlCorners,delete this._controlContainer}});var yt=fe.extend({options:{collapsed:!0,position:"topright",autoZIndex:!0,hideSingleBase:!1,sortLayers:!1,sortFunction:function(t,n,o,u){return o<u?-1:u<o?1:0}},initialize:function(t,n,o){kt(this,o),this._layerControlInputs=[],this._layers=[],this._lastZIndex=0,this._handlingClick=!1,this._preventClick=!1;for(var u in t)this._addLayer(t[u],u);for(u in n)this._addLayer(n[u],u,!0)},onAdd:function(t){this._initLayout(),this._update(),this._map=t,t.on("zoomend",this._checkDisabledLayers,this);for(var n=0;n<this._layers.length;n++)this._layers[n].layer.on("add remove",this._onLayerChange,this);return this._container},addTo:function(t){return fe.prototype.addTo.call(this,t),this._expandIfNotCollapsed()},onRemove:function(){this._map.off("zoomend",this._checkDisabledLayers,this);for(var t=0;t<this._layers.length;t++)this._layers[t].layer.off("add remove",this._onLayerChange,this)},addBaseLayer:function(t,n){return this._addLayer(t,n),this._map?this._update():this},addOverlay:function(t,n){return this._addLayer(t,n,!0),this._map?this._update():this},removeLayer:function(t){t.off("add remove",this._onLayerChange,this);var n=this._getLayer(M(t));return n&&this._layers.splice(this._layers.indexOf(n),1),this._map?this._update():this},expand:function(){ft(this._container,"leaflet-control-layers-expanded"),this._section.style.height=null;var t=this._map.getSize().y-(this._container.offsetTop+50);return t<this._section.clientHeight?(ft(this._section,"leaflet-control-layers-scrollbar"),this._section.style.height=t+"px"):At(this._section,"leaflet-control-layers-scrollbar"),this._checkDisabledLayers(),this},collapse:function(){return At(this._container,"leaflet-control-layers-expanded"),this},_initLayout:function(){var t="leaflet-control-layers",n=this._container=bt("div",t),o=this.options.collapsed;n.setAttribute("aria-haspopup",!0),vi(n),Oe(n);var u=this._section=bt("section",t+"-list");o&&(this._map.on("click",this.collapse,this),pt(n,{mouseenter:this._expandSafely,mouseleave:this.collapse},this));var c=this._layersLink=bt("a",t+"-toggle",n);c.href="#",c.title="Layers",c.setAttribute("role","button"),pt(c,{keydown:function(g){g.keyCode===13&&this._expandSafely()},click:function(g){Zt(g),this._expandSafely()}},this),o||this.expand(),this._baseLayersList=bt("div",t+"-base",u),this._separator=bt("div",t+"-separator",u),this._overlaysList=bt("div",t+"-overlays",u),n.appendChild(u)},_getLayer:function(t){for(var n=0;n<this._layers.length;n++)if(this._layers[n]&&M(this._layers[n].layer)===t)return this._layers[n]},_addLayer:function(t,n,o){this._map&&t.on("add remove",this._onLayerChange,this),this._layers.push({layer:t,name:n,overlay:o}),this.options.sortLayers&&this._layers.sort(V(function(u,c){return this.options.sortFunction(u.layer,c.layer,u.name,c.name)},this)),this.options.autoZIndex&&t.setZIndex&&(this._lastZIndex++,t.setZIndex(this._lastZIndex)),this._expandIfNotCollapsed()},_update:function(){if(!this._container)return this;Fi(this._baseLayersList),Fi(this._overlaysList),this._layerControlInputs=[];var t,n,o,u,c=0;for(o=0;o<this._layers.length;o++)u=this._layers[o],this._addItem(u),n=n||u.overlay,t=t||!u.overlay,c+=u.overlay?0:1;return this.options.hideSingleBase&&(t=t&&c>1,this._baseLayersList.style.display=t?"":"none"),this._separator.style.display=n&&t?"":"none",this},_onLayerChange:function(t){this._handlingClick||this._update();var n=this._getLayer(M(t.target)),o=n.overlay?t.type==="add"?"overlayadd":"overlayremove":t.type==="add"?"baselayerchange":null;o&&this._map.fire(o,n)},_createRadioElement:function(t,n){var o='<input type="radio" class="leaflet-control-layers-selector" name="'+t+'"'+(n?' checked="checked"':"")+"/>",u=document.createElement("div");return u.innerHTML=o,u.firstChild},_addItem:function(t){var n=document.createElement("label"),o=this._map.hasLayer(t.layer),u;t.overlay?(u=document.createElement("input"),u.type="checkbox",u.className="leaflet-control-layers-selector",u.defaultChecked=o):u=this._createRadioElement("leaflet-base-layers_"+M(this),o),this._layerControlInputs.push(u),u.layerId=M(t.layer),pt(u,"click",this._onInputClick,this);var c=document.createElement("span");c.innerHTML=" "+t.name;var g=document.createElement("span");n.appendChild(g),g.appendChild(u),g.appendChild(c);var B=t.overlay?this._overlaysList:this._baseLayersList;return B.appendChild(n),this._checkDisabledLayers(),n},_onInputClick:function(){if(!this._preventClick){var t=this._layerControlInputs,n,o,u=[],c=[];this._handlingClick=!0;for(var g=t.length-1;g>=0;g--)n=t[g],o=this._getLayer(n.layerId).layer,n.checked?u.push(o):n.checked||c.push(o);for(g=0;g<c.length;g++)this._map.hasLayer(c[g])&&this._map.removeLayer(c[g]);for(g=0;g<u.length;g++)this._map.hasLayer(u[g])||this._map.addLayer(u[g]);this._handlingClick=!1,this._refocusOnMap()}},_checkDisabledLayers:function(){for(var t=this._layerControlInputs,n,o,u=this._map.getZoom(),c=t.length-1;c>=0;c--)n=t[c],o=this._getLayer(n.layerId).layer,n.disabled=o.options.minZoom!==void 0&&u<o.options.minZoom||o.options.maxZoom!==void 0&&u>o.options.maxZoom},_expandIfNotCollapsed:function(){return this._map&&!this.options.collapsed&&this.expand(),this},_expandSafely:function(){var t=this._section;this._preventClick=!0,pt(t,"click",Zt),this.expand();var n=this;setTimeout(function(){Et(t,"click",Zt),n._preventClick=!1})}}),Fn=function(t,n,o){return new yt(t,n,o)},Qe=fe.extend({options:{position:"topleft",zoomInText:'<span aria-hidden="true">+</span>',zoomInTitle:"Zoom in",zoomOutText:'<span aria-hidden="true">&#x2212;</span>',zoomOutTitle:"Zoom out"},onAdd:function(t){var n="leaflet-control-zoom",o=bt("div",n+" leaflet-bar"),u=this.options;return this._zoomInButton=this._createButton(u.zoomInText,u.zoomInTitle,n+"-in",o,this._zoomIn),this._zoomOutButton=this._createButton(u.zoomOutText,u.zoomOutTitle,n+"-out",o,this._zoomOut),this._updateDisabled(),t.on("zoomend zoomlevelschange",this._updateDisabled,this),o},onRemove:function(t){t.off("zoomend zoomlevelschange",this._updateDisabled,this)},disable:function(){return this._disabled=!0,this._updateDisabled(),this},enable:function(){return this._disabled=!1,this._updateDisabled(),this},_zoomIn:function(t){!this._disabled&&this._map._zoom<this._map.getMaxZoom()&&this._map.zoomIn(this._map.options.zoomDelta*(t.shiftKey?3:1))},_zoomOut:function(t){!this._disabled&&this._map._zoom>this._map.getMinZoom()&&this._map.zoomOut(this._map.options.zoomDelta*(t.shiftKey?3:1))},_createButton:function(t,n,o,u,c){var g=bt("a",o,u);return g.innerHTML=t,g.href="#",g.title=n,g.setAttribute("role","button"),g.setAttribute("aria-label",n),vi(g),pt(g,"click",He),pt(g,"click",c,this),pt(g,"click",this._refocusOnMap,this),g},_updateDisabled:function(){var t=this._map,n="leaflet-disabled";At(this._zoomInButton,n),At(this._zoomOutButton,n),this._zoomInButton.setAttribute("aria-disabled","false"),this._zoomOutButton.setAttribute("aria-disabled","false"),(this._disabled||t._zoom===t.getMinZoom())&&(ft(this._zoomOutButton,n),this._zoomOutButton.setAttribute("aria-disabled","true")),(this._disabled||t._zoom===t.getMaxZoom())&&(ft(this._zoomInButton,n),this._zoomInButton.setAttribute("aria-disabled","true"))}});mt.mergeOptions({zoomControl:!0}),mt.addInitHook(function(){this.options.zoomControl&&(this.zoomControl=new Qe,this.addControl(this.zoomControl))});var Ha=function(t){return new Qe(t)},On=fe.extend({options:{position:"bottomleft",maxWidth:100,metric:!0,imperial:!0},onAdd:function(t){var n="leaflet-control-scale",o=bt("div",n),u=this.options;return this._addScales(u,n+"-line",o),t.on(u.updateWhenIdle?"moveend":"move",this._update,this),t.whenReady(this._update,this),o},onRemove:function(t){t.off(this.options.updateWhenIdle?"moveend":"move",this._update,this)},_addScales:function(t,n,o){t.metric&&(this._mScale=bt("div",n,o)),t.imperial&&(this._iScale=bt("div",n,o))},_update:function(){var t=this._map,n=t.getSize().y/2,o=t.distance(t.containerPointToLatLng([0,n]),t.containerPointToLatLng([this.options.maxWidth,n]));this._updateScales(o)},_updateScales:function(t){this.options.metric&&t&&this._updateMetric(t),this.options.imperial&&t&&this._updateImperial(t)},_updateMetric:function(t){var n=this._getRoundNum(t),o=n<1e3?n+" m":n/1e3+" km";this._updateScale(this._mScale,o,n/t)},_updateImperial:function(t){var n=t*3.2808399,o,u,c;n>5280?(o=n/5280,u=this._getRoundNum(o),this._updateScale(this._iScale,u+" mi",u/o)):(c=this._getRoundNum(n),this._updateScale(this._iScale,c+" ft",c/n))},_updateScale:function(t,n,o){t.style.width=Math.round(this.options.maxWidth*o)+"px",t.innerHTML=n},_getRoundNum:function(t){var n=Math.pow(10,(Math.floor(t)+"").length-1),o=t/n;return o=o>=10?10:o>=5?5:o>=3?3:o>=2?2:1,n*o}}),Ka=function(t){return new On(t)},Rn='<svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" class="leaflet-attribution-flag"><path fill="#4C7BE1" d="M0 0h12v4H0z"/><path fill="#FFD500" d="M0 4h12v3H0z"/><path fill="#E0BC00" d="M0 7h12v1H0z"/></svg>',ti=fe.extend({options:{position:"bottomright",prefix:'<a href="https://leafletjs.com" title="A JavaScript library for interactive maps">'+(at.inlineSvg?Rn+" ":"")+"Leaflet</a>"},initialize:function(t){kt(this,t),this._attributions={}},onAdd:function(t){t.attributionControl=this,this._container=bt("div","leaflet-control-attribution"),vi(this._container);for(var n in t._layers)t._layers[n].getAttribution&&this.addAttribution(t._layers[n].getAttribution());return this._update(),t.on("layeradd",this._addAttribution,this),this._container},onRemove:function(t){t.off("layeradd",this._addAttribution,this)},_addAttribution:function(t){t.layer.getAttribution&&(this.addAttribution(t.layer.getAttribution()),t.layer.once("remove",function(){this.removeAttribution(t.layer.getAttribution())},this))},setPrefix:function(t){return this.options.prefix=t,this._update(),this},addAttribution:function(t){return t?(this._attributions[t]||(this._attributions[t]=0),this._attributions[t]++,this._update(),this):this},removeAttribution:function(t){return t?(this._attributions[t]&&(this._attributions[t]--,this._update()),this):this},_update:function(){if(this._map){var t=[];for(var n in this._attributions)this._attributions[n]&&t.push(n);var o=[];this.options.prefix&&o.push(this.options.prefix),t.length&&o.push(t.join(", ")),this._container.innerHTML=o.join(' <span aria-hidden="true">|</span> ')}}});mt.mergeOptions({attributionControl:!0}),mt.addInitHook(function(){this.options.attributionControl&&new ti().addTo(this)});var qa=function(t){return new ti(t)};fe.Layers=yt,fe.Zoom=Qe,fe.Scale=On,fe.Attribution=ti,Xe.layers=Fn,Xe.zoom=Ha,Xe.scale=Ka,Xe.attribution=qa;var jt=pe.extend({initialize:function(t){this._map=t},enable:function(){return this._enabled?this:(this._enabled=!0,this.addHooks(),this)},disable:function(){return this._enabled?(this._enabled=!1,this.removeHooks(),this):this},enabled:function(){return!!this._enabled}});jt.addTo=function(t,n){return t.addHandler(n,this),this};var Zr={Events:ie},Ke=at.touch?"touchstart mousedown":"mousedown",Re=di.extend({options:{clickTolerance:3},initialize:function(t,n,o,u){kt(this,u),this._element=t,this._dragStartTarget=n||t,this._preventOutline=o},enable:function(){this._enabled||(pt(this._dragStartTarget,Ke,this._onDown,this),this._enabled=!0)},disable:function(){this._enabled&&(Re._dragging===this&&this.finishDrag(!0),Et(this._dragStartTarget,Ke,this._onDown,this),this._enabled=!1,this._moved=!1)},_onDown:function(t){if(this._enabled&&(this._moved=!1,!wn(this._element,"leaflet-zoom-anim"))){if(t.touches&&t.touches.length!==1){Re._dragging===this&&this.finishDrag();return}if(!(Re._dragging||t.shiftKey||t.which!==1&&t.button!==1&&!t.touches)&&(Re._dragging=this,this._preventOutline&&Bn(this._element),Mn(),mi(),!this._moving)){this.fire("down");var n=t.touches?t.touches[0]:t,o=Or(this._element);this._startPoint=new dt(n.clientX,n.clientY),this._startPos=Ue(this._element),this._parentScale=Pn(o);var u=t.type==="mousedown";pt(document,u?"mousemove":"touchmove",this._onMove,this),pt(document,u?"mouseup":"touchend touchcancel",this._onUp,this)}}},_onMove:function(t){if(this._enabled){if(t.touches&&t.touches.length>1){this._moved=!0;return}var n=t.touches&&t.touches.length===1?t.touches[0]:t,o=new dt(n.clientX,n.clientY)._subtract(this._startPoint);!o.x&&!o.y||Math.abs(o.x)+Math.abs(o.y)<this.options.clickTolerance||(o.x/=this._parentScale.x,o.y/=this._parentScale.y,Zt(t),this._moved||(this.fire("dragstart"),this._moved=!0,ft(document.body,"leaflet-dragging"),this._lastTarget=t.target||t.srcElement,window.SVGElementInstance&&this._lastTarget instanceof window.SVGElementInstance&&(this._lastTarget=this._lastTarget.correspondingUseElement),ft(this._lastTarget,"leaflet-drag-target")),this._newPos=this._startPos.add(o),this._moving=!0,this._lastEvent=t,this._updatePosition())}},_updatePosition:function(){var t={originalEvent:this._lastEvent};this.fire("predrag",t),Ot(this._element,this._newPos),this.fire("drag",t)},_onUp:function(){this._enabled&&this.finishDrag()},finishDrag:function(t){At(document.body,"leaflet-dragging"),this._lastTarget&&(At(this._lastTarget,"leaflet-drag-target"),this._lastTarget=null),Et(document,"mousemove touchmove",this._onMove,this),Et(document,"mouseup touchend touchcancel",this._onUp,this),zi(),gi();var n=this._moved&&this._moving;this._moving=!1,Re._dragging=!1,n&&this.fire("dragend",{noInertia:t,distance:this._newPos.distanceTo(this._startPos)})}});function jr(t,n,o){var u,c=[1,4,2,8],g,B,Z,H,Y,Q,ut,_t;for(g=0,Q=t.length;g<Q;g++)t[g]._code=se(t[g],n);for(Z=0;Z<4;Z++){for(ut=c[Z],u=[],g=0,Q=t.length,B=Q-1;g<Q;B=g++)H=t[g],Y=t[B],H._code&ut?Y._code&ut||(_t=Gn(Y,H,ut,n,o),_t._code=se(_t,n),u.push(_t)):(Y._code&ut&&(_t=Gn(Y,H,ut,n,o),_t._code=se(_t,n),u.push(_t)),u.push(H));t=u}return t}function Ur(t,n){var o,u,c,g,B,Z,H,Y,Q;if(!t||t.length===0)throw new Error("latlngs not passed");It(t)||(console.warn("latlngs are not flat! Only the first ring will be used"),t=t[0]);var ut=gt([0,0]),_t=Ft(t),Kt=_t.getNorthWest().distanceTo(_t.getSouthWest())*_t.getNorthEast().distanceTo(_t.getNorthWest());Kt<1700&&(ut=In(t));var Nt=t.length,ce=[];for(o=0;o<Nt;o++){var Xt=gt(t[o]);ce.push(n.project(gt([Xt.lat-ut.lat,Xt.lng-ut.lng])))}for(Z=H=Y=0,o=0,u=Nt-1;o<Nt;u=o++)c=ce[o],g=ce[u],B=c.y*g.x-g.y*c.x,H+=(c.x+g.x)*B,Y+=(c.y+g.y)*B,Z+=B*3;Z===0?Q=ce[0]:Q=[H/Z,Y/Z];var ui=n.unproject(ht(Q));return gt([ui.lat+ut.lat,ui.lng+ut.lng])}function In(t){for(var n=0,o=0,u=0,c=0;c<t.length;c++){var g=gt(t[c]);n+=g.lat,o+=g.lng,u++}return gt([n/u,o/u])}var Wa={__proto__:null,clipPolygon:jr,polygonCenter:Ur,centroid:In};function Vr(t,n){if(!n||!t.length)return t.slice();var o=n*n;return t=Ut(t,o),t=Kr(t,o),t}function Hr(t,n,o){return Math.sqrt(Be(t,n,o,!0))}function Ja(t,n,o){return Be(t,n,o)}function Kr(t,n){var o=t.length,u=typeof Uint8Array<"u"?Uint8Array:Array,c=new u(o);c[0]=c[o-1]=1,zn(t,c,n,0,o-1);var g,B=[];for(g=0;g<o;g++)c[g]&&B.push(t[g]);return B}function zn(t,n,o,u,c){var g=0,B,Z,H;for(Z=u+1;Z<=c-1;Z++)H=Be(t[Z],t[u],t[c],!0),H>g&&(B=Z,g=H);g>o&&(n[B]=1,zn(t,n,o,u,B),zn(t,n,o,B,c))}function Ut(t,n){for(var o=[t[0]],u=1,c=0,g=t.length;u<g;u++)ei(t[u],t[c])>n&&(o.push(t[u]),c=u);return c<g-1&&o.push(t[g-1]),o}var Ht;function Nn(t,n,o,u,c){var g=u?Ht:se(t,o),B=se(n,o),Z,H,Y;for(Ht=B;;){if(!(g|B))return[t,n];if(g&B)return!1;Z=g||B,H=Gn(t,n,Z,o,c),Y=se(H,o),Z===g?(t=H,g=Y):(n=H,B=Y)}}function Gn(t,n,o,u,c){var g=n.x-t.x,B=n.y-t.y,Z=u.min,H=u.max,Y,Q;return o&8?(Y=t.x+g*(H.y-t.y)/B,Q=H.y):o&4?(Y=t.x+g*(Z.y-t.y)/B,Q=Z.y):o&2?(Y=H.x,Q=t.y+B*(H.x-t.x)/g):o&1&&(Y=Z.x,Q=t.y+B*(Z.x-t.x)/g),new dt(Y,Q,c)}function se(t,n){var o=0;return t.x<n.min.x?o|=1:t.x>n.max.x&&(o|=2),t.y<n.min.y?o|=4:t.y>n.max.y&&(o|=8),o}function ei(t,n){var o=n.x-t.x,u=n.y-t.y;return o*o+u*u}function Be(t,n,o,u){var c=n.x,g=n.y,B=o.x-c,Z=o.y-g,H=B*B+Z*Z,Y;return H>0&&(Y=((t.x-c)*B+(t.y-g)*Z)/H,Y>1?(c=o.x,g=o.y):Y>0&&(c+=B*Y,g+=Z*Y)),B=t.x-c,Z=t.y-g,u?B*B+Z*Z:new dt(c,g)}function It(t){return!de(t[0])||typeof t[0][0]!="object"&&typeof t[0][0]<"u"}function qr(t){return console.warn("Deprecated use of _flat, please use L.LineUtil.isFlat instead."),It(t)}function Wr(t,n){var o,u,c,g,B,Z,H,Y;if(!t||t.length===0)throw new Error("latlngs not passed");It(t)||(console.warn("latlngs are not flat! Only the first ring will be used"),t=t[0]);var Q=gt([0,0]),ut=Ft(t),_t=ut.getNorthWest().distanceTo(ut.getSouthWest())*ut.getNorthEast().distanceTo(ut.getNorthWest());_t<1700&&(Q=In(t));var Kt=t.length,Nt=[];for(o=0;o<Kt;o++){var ce=gt(t[o]);Nt.push(n.project(gt([ce.lat-Q.lat,ce.lng-Q.lng])))}for(o=0,u=0;o<Kt-1;o++)u+=Nt[o].distanceTo(Nt[o+1])/2;if(u===0)Y=Nt[0];else for(o=0,g=0;o<Kt-1;o++)if(B=Nt[o],Z=Nt[o+1],c=B.distanceTo(Z),g+=c,g>u){H=(g-u)/c,Y=[Z.x-H*(Z.x-B.x),Z.y-H*(Z.y-B.y)];break}var Xt=n.unproject(ht(Y));return gt([Xt.lat+Q.lat,Xt.lng+Q.lng])}var Zn={__proto__:null,simplify:Vr,pointToSegmentDistance:Hr,closestPointOnSegment:Ja,clipSegment:Nn,_getEdgeIntersection:Gn,_getBitCode:se,_sqClosestPointOnSegment:Be,isFlat:It,_flat:qr,polylineCenter:Wr},ne={project:function(t){return new dt(t.lng,t.lat)},unproject:function(t){return new Ct(t.y,t.x)},bounds:new Tt([-180,-90],[180,90])},Zi={R:6378137,R_MINOR:6356752314245179e-9,bounds:new Tt([-2003750834279e-5,-1549657073972e-5],[2003750834279e-5,1876465623138e-5]),project:function(t){var n=Math.PI/180,o=this.R,u=t.lat*n,c=this.R_MINOR/o,g=Math.sqrt(1-c*c),B=g*Math.sin(u),Z=Math.tan(Math.PI/4-u/2)/Math.pow((1-B)/(1+B),g/2);return u=-o*Math.log(Math.max(Z,1e-10)),new dt(t.lng*n*o,u)},unproject:function(t){for(var n=180/Math.PI,o=this.R,u=this.R_MINOR/o,c=Math.sqrt(1-u*u),g=Math.exp(-t.y/o),B=Math.PI/2-2*Math.atan(g),Z=0,H=.1,Y;Z<15&&Math.abs(H)>1e-7;Z++)Y=c*Math.sin(B),Y=Math.pow((1-Y)/(1+Y),c/2),H=Math.PI/2-2*Math.atan(g*Y)-B,B+=H;return new Ct(B*n,t.x*n/o)}},ji={__proto__:null,LonLat:ne,Mercator:Zi,SphericalMercator:dn},Ya=z({},Ae,{code:"EPSG:3395",projection:Zi,transformation:function(){var t=.5/(Math.PI*Zi.R);return pi(t,.5,-t,.5)}()}),_e=z({},Ae,{code:"EPSG:4326",projection:ne,transformation:pi(1/180,1,-1/180,.5)}),be=z({},Me,{projection:ne,transformation:pi(1,0,-1,0),scale:function(t){return Math.pow(2,t)},zoom:function(t){return Math.log(t)/Math.LN2},distance:function(t,n){var o=n.lng-t.lng,u=n.lat-t.lat;return Math.sqrt(o*o+u*u)},infinite:!0});Me.Earth=Ae,Me.EPSG3395=Ya,Me.EPSG3857=pn,Me.EPSG900913=La,Me.EPSG4326=_e,Me.Simple=be;var $t=di.extend({options:{pane:"overlayPane",attribution:null,bubblingMouseEvents:!0},addTo:function(t){return t.addLayer(this),this},remove:function(){return this.removeFrom(this._map||this._mapToAdd)},removeFrom:function(t){return t&&t.removeLayer(this),this},getPane:function(t){return this._map.getPane(t?this.options[t]||t:this.options.pane)},addInteractiveTarget:function(t){return this._map._targets[M(t)]=this,this},removeInteractiveTarget:function(t){return delete this._map._targets[M(t)],this},getAttribution:function(){return this.options.attribution},_layerAdd:function(t){var n=t.target;if(n.hasLayer(this)){if(this._map=n,this._zoomAnimated=n._zoomAnimated,this.getEvents){var o=this.getEvents();n.on(o,this),this.once("remove",function(){n.off(o,this)},this)}this.onAdd(n),this.fire("add"),n.fire("layeradd",{layer:this})}}});mt.include({addLayer:function(t){if(!t._layerAdd)throw new Error("The provided object is not a Layer.");var n=M(t);return this._layers[n]?this:(this._layers[n]=t,t._mapToAdd=this,t.beforeAdd&&t.beforeAdd(this),this.whenReady(t._layerAdd,t),this)},removeLayer:function(t){var n=M(t);return this._layers[n]?(this._loaded&&t.onRemove(this),delete this._layers[n],this._loaded&&(this.fire("layerremove",{layer:t}),t.fire("remove")),t._map=t._mapToAdd=null,this):this},hasLayer:function(t){return M(t)in this._layers},eachLayer:function(t,n){for(var o in this._layers)t.call(n,this._layers[o]);return this},_addLayers:function(t){t=t?de(t)?t:[t]:[];for(var n=0,o=t.length;n<o;n++)this.addLayer(t[n])},_addZoomLimit:function(t){(!isNaN(t.options.maxZoom)||!isNaN(t.options.minZoom))&&(this._zoomBoundLayers[M(t)]=t,this._updateZoomLevels())},_removeZoomLimit:function(t){var n=M(t);this._zoomBoundLayers[n]&&(delete this._zoomBoundLayers[n],this._updateZoomLevels())},_updateZoomLevels:function(){var t=1/0,n=-1/0,o=this._getZoomSpan();for(var u in this._zoomBoundLayers){var c=this._zoomBoundLayers[u].options;t=c.minZoom===void 0?t:Math.min(t,c.minZoom),n=c.maxZoom===void 0?n:Math.max(n,c.maxZoom)}this._layersMaxZoom=n===-1/0?void 0:n,this._layersMinZoom=t===1/0?void 0:t,o!==this._getZoomSpan()&&this.fire("zoomlevelschange"),this.options.maxZoom===void 0&&this._layersMaxZoom&&this.getZoom()>this._layersMaxZoom&&this.setZoom(this._layersMaxZoom),this.options.minZoom===void 0&&this._layersMinZoom&&this.getZoom()<this._layersMinZoom&&this.setZoom(this._layersMinZoom)}});var qe=$t.extend({initialize:function(t,n){kt(this,n),this._layers={};var o,u;if(t)for(o=0,u=t.length;o<u;o++)this.addLayer(t[o])},addLayer:function(t){var n=this.getLayerId(t);return this._layers[n]=t,this._map&&this._map.addLayer(t),this},removeLayer:function(t){var n=t in this._layers?t:this.getLayerId(t);return this._map&&this._layers[n]&&this._map.removeLayer(this._layers[n]),delete this._layers[n],this},hasLayer:function(t){var n=typeof t=="number"?t:this.getLayerId(t);return n in this._layers},clearLayers:function(){return this.eachLayer(this.removeLayer,this)},invoke:function(t){var n=Array.prototype.slice.call(arguments,1),o,u;for(o in this._layers)u=this._layers[o],u[t]&&u[t].apply(u,n);return this},onAdd:function(t){this.eachLayer(t.addLayer,t)},onRemove:function(t){this.eachLayer(t.removeLayer,t)},eachLayer:function(t,n){for(var o in this._layers)t.call(n,this._layers[o]);return this},getLayer:function(t){return this._layers[t]},getLayers:function(){var t=[];return this.eachLayer(t.push,t),t},setZIndex:function(t){return this.invoke("setZIndex",t)},getLayerId:function(t){return M(t)}}),jn=function(t,n){return new qe(t,n)},re=qe.extend({addLayer:function(t){return this.hasLayer(t)?this:(t.addEventParent(this),qe.prototype.addLayer.call(this,t),this.fire("layeradd",{layer:t}))},removeLayer:function(t){return this.hasLayer(t)?(t in this._layers&&(t=this._layers[t]),t.removeEventParent(this),qe.prototype.removeLayer.call(this,t),this.fire("layerremove",{layer:t})):this},setStyle:function(t){return this.invoke("setStyle",t)},bringToFront:function(){return this.invoke("bringToFront")},bringToBack:function(){return this.invoke("bringToBack")},getBounds:function(){var t=new Yt;for(var n in this._layers){var o=this._layers[n];t.extend(o.getBounds?o.getBounds():o.getLatLng())}return t}}),$a=function(t,n){return new re(t,n)},ii=pe.extend({options:{popupAnchor:[0,0],tooltipAnchor:[0,0],crossOrigin:!1},initialize:function(t){kt(this,t)},createIcon:function(t){return this._createIcon("icon",t)},createShadow:function(t){return this._createIcon("shadow",t)},_createIcon:function(t,n){var o=this._getIconUrl(t);if(!o){if(t==="icon")throw new Error("iconUrl not set in Icon options (see the docs).");return null}var u=this._createImg(o,n&&n.tagName==="IMG"?n:null);return this._setIconStyles(u,t),(this.options.crossOrigin||this.options.crossOrigin==="")&&(u.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),u},_setIconStyles:function(t,n){var o=this.options,u=o[n+"Size"];typeof u=="number"&&(u=[u,u]);var c=ht(u),g=ht(n==="shadow"&&o.shadowAnchor||o.iconAnchor||c&&c.divideBy(2,!0));t.className="leaflet-marker-"+n+" "+(o.className||""),g&&(t.style.marginLeft=-g.x+"px",t.style.marginTop=-g.y+"px"),c&&(t.style.width=c.x+"px",t.style.height=c.y+"px")},_createImg:function(t,n){return n=n||document.createElement("img"),n.src=t,n},_getIconUrl:function(t){return at.retina&&this.options[t+"RetinaUrl"]||this.options[t+"Url"]}});function Un(t){return new ii(t)}var Li=ii.extend({options:{iconUrl:"marker-icon.png",iconRetinaUrl:"marker-icon-2x.png",shadowUrl:"marker-shadow.png",iconSize:[25,41],iconAnchor:[12,41],popupAnchor:[1,-34],tooltipAnchor:[16,-28],shadowSize:[41,41]},_getIconUrl:function(t){return typeof Li.imagePath!="string"&&(Li.imagePath=this._detectIconPath()),(this.options.imagePath||Li.imagePath)+ii.prototype._getIconUrl.call(this,t)},_stripUrl:function(t){var n=function(o,u,c){var g=u.exec(o);return g&&g[c]};return t=n(t,/^url\((['"])?(.+)\1\)$/,2),t&&n(t,/^(.*)marker-icon\.png$/,1)},_detectIconPath:function(){var t=bt("div","leaflet-default-icon-path",document.body),n=_i(t,"background-image")||_i(t,"backgroundImage");if(document.body.removeChild(t),n=this._stripUrl(n),n)return n;var o=document.querySelector('link[href$="leaflet.css"]');return o?o.href.substring(0,o.href.length-11-1):""}}),Jr=jt.extend({initialize:function(t){this._marker=t},addHooks:function(){var t=this._marker._icon;this._draggable||(this._draggable=new Re(t,t,!0)),this._draggable.on({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).enable(),ft(t,"leaflet-marker-draggable")},removeHooks:function(){this._draggable.off({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).disable(),this._marker._icon&&At(this._marker._icon,"leaflet-marker-draggable")},moved:function(){return this._draggable&&this._draggable._moved},_adjustPan:function(t){var n=this._marker,o=n._map,u=this._marker.options.autoPanSpeed,c=this._marker.options.autoPanPadding,g=Ue(n._icon),B=o.getPixelBounds(),Z=o.getPixelOrigin(),H=Gt(B.min._subtract(Z).add(c),B.max._subtract(Z).subtract(c));if(!H.contains(g)){var Y=ht((Math.max(H.max.x,g.x)-H.max.x)/(B.max.x-H.max.x)-(Math.min(H.min.x,g.x)-H.min.x)/(B.min.x-H.min.x),(Math.max(H.max.y,g.y)-H.max.y)/(B.max.y-H.max.y)-(Math.min(H.min.y,g.y)-H.min.y)/(B.min.y-H.min.y)).multiplyBy(u);o.panBy(Y,{animate:!1}),this._draggable._newPos._add(Y),this._draggable._startPos._add(Y),Ot(n._icon,this._draggable._newPos),this._onDrag(t),this._panRequest=Jt(this._adjustPan.bind(this,t))}},_onDragStart:function(){this._oldLatLng=this._marker.getLatLng(),this._marker.closePopup&&this._marker.closePopup(),this._marker.fire("movestart").fire("dragstart")},_onPreDrag:function(t){this._marker.options.autoPan&&(Vt(this._panRequest),this._panRequest=Jt(this._adjustPan.bind(this,t)))},_onDrag:function(t){var n=this._marker,o=n._shadow,u=Ue(n._icon),c=n._map.layerPointToLatLng(u);o&&Ot(o,u),n._latlng=c,t.latlng=c,t.oldLatLng=this._oldLatLng,n.fire("move",t).fire("drag",t)},_onDragEnd:function(t){Vt(this._panRequest),delete this._oldLatLng,this._marker.fire("moveend").fire("dragend",t)}}),Ui=$t.extend({options:{icon:new Li,interactive:!0,keyboard:!0,title:"",alt:"Marker",zIndexOffset:0,opacity:1,riseOnHover:!1,riseOffset:250,pane:"markerPane",shadowPane:"shadowPane",bubblingMouseEvents:!1,autoPanOnFocus:!0,draggable:!1,autoPan:!1,autoPanPadding:[50,50],autoPanSpeed:10},initialize:function(t,n){kt(this,n),this._latlng=gt(t)},onAdd:function(t){this._zoomAnimated=this._zoomAnimated&&t.options.markerZoomAnimation,this._zoomAnimated&&t.on("zoomanim",this._animateZoom,this),this._initIcon(),this.update()},onRemove:function(t){this.dragging&&this.dragging.enabled()&&(this.options.draggable=!0,this.dragging.removeHooks()),delete this.dragging,this._zoomAnimated&&t.off("zoomanim",this._animateZoom,this),this._removeIcon(),this._removeShadow()},getEvents:function(){return{zoom:this.update,viewreset:this.update}},getLatLng:function(){return this._latlng},setLatLng:function(t){var n=this._latlng;return this._latlng=gt(t),this.update(),this.fire("move",{oldLatLng:n,latlng:this._latlng})},setZIndexOffset:function(t){return this.options.zIndexOffset=t,this.update()},getIcon:function(){return this.options.icon},setIcon:function(t){return this.options.icon=t,this._map&&(this._initIcon(),this.update()),this._popup&&this.bindPopup(this._popup,this._popup.options),this},getElement:function(){return this._icon},update:function(){if(this._icon&&this._map){var t=this._map.latLngToLayerPoint(this._latlng).round();this._setPos(t)}return this},_initIcon:function(){var t=this.options,n="leaflet-zoom-"+(this._zoomAnimated?"animated":"hide"),o=t.icon.createIcon(this._icon),u=!1;o!==this._icon&&(this._icon&&this._removeIcon(),u=!0,t.title&&(o.title=t.title),o.tagName==="IMG"&&(o.alt=t.alt||"")),ft(o,n),t.keyboard&&(o.tabIndex="0",o.setAttribute("role","button")),this._icon=o,t.riseOnHover&&this.on({mouseover:this._bringToFront,mouseout:this._resetZIndex}),this.options.autoPanOnFocus&&pt(o,"focus",this._panOnFocus,this);var c=t.icon.createShadow(this._shadow),g=!1;c!==this._shadow&&(this._removeShadow(),g=!0),c&&(ft(c,n),c.alt=""),this._shadow=c,t.opacity<1&&this._updateOpacity(),u&&this.getPane().appendChild(this._icon),this._initInteraction(),c&&g&&this.getPane(t.shadowPane).appendChild(this._shadow)},_removeIcon:function(){this.options.riseOnHover&&this.off({mouseover:this._bringToFront,mouseout:this._resetZIndex}),this.options.autoPanOnFocus&&Et(this._icon,"focus",this._panOnFocus,this),Mt(this._icon),this.removeInteractiveTarget(this._icon),this._icon=null},_removeShadow:function(){this._shadow&&Mt(this._shadow),this._shadow=null},_setPos:function(t){this._icon&&Ot(this._icon,t),this._shadow&&Ot(this._shadow,t),this._zIndex=t.y+this.options.zIndexOffset,this._resetZIndex()},_updateZIndex:function(t){this._icon&&(this._icon.style.zIndex=this._zIndex+t)},_animateZoom:function(t){var n=this._map._latLngToNewLayerPoint(this._latlng,t.zoom,t.center).round();this._setPos(n)},_initInteraction:function(){if(this.options.interactive&&(ft(this._icon,"leaflet-interactive"),this.addInteractiveTarget(this._icon),Jr)){var t=this.options.draggable;this.dragging&&(t=this.dragging.enabled(),this.dragging.disable()),this.dragging=new Jr(this),t&&this.dragging.enable()}},setOpacity:function(t){return this.options.opacity=t,this._map&&this._updateOpacity(),this},_updateOpacity:function(){var t=this.options.opacity;this._icon&&oe(this._icon,t),this._shadow&&oe(this._shadow,t)},_bringToFront:function(){this._updateZIndex(this.options.riseOffset)},_resetZIndex:function(){this._updateZIndex(0)},_panOnFocus:function(){var t=this._map;if(t){var n=this.options.icon.options,o=n.iconSize?ht(n.iconSize):ht(0,0),u=n.iconAnchor?ht(n.iconAnchor):ht(0,0);t.panInside(this._latlng,{paddingTopLeft:u,paddingBottomRight:o.subtract(u)})}},_getPopupAnchor:function(){return this.options.icon.options.popupAnchor},_getTooltipAnchor:function(){return this.options.icon.options.tooltipAnchor}});function Xa(t,n){return new Ui(t,n)}var xe=$t.extend({options:{stroke:!0,color:"#3388ff",weight:3,opacity:1,lineCap:"round",lineJoin:"round",dashArray:null,dashOffset:null,fill:!1,fillColor:null,fillOpacity:.2,fillRule:"evenodd",interactive:!0,bubblingMouseEvents:!0},beforeAdd:function(t){this._renderer=t.getRenderer(this)},onAdd:function(){this._renderer._initPath(this),this._reset(),this._renderer._addPath(this)},onRemove:function(){this._renderer._removePath(this)},redraw:function(){return this._map&&this._renderer._updatePath(this),this},setStyle:function(t){return kt(this,t),this._renderer&&(this._renderer._updateStyle(this),this.options.stroke&&t&&Object.prototype.hasOwnProperty.call(t,"weight")&&this._updateBounds()),this},bringToFront:function(){return this._renderer&&this._renderer._bringToFront(this),this},bringToBack:function(){return this._renderer&&this._renderer._bringToBack(this),this},getElement:function(){return this._path},_reset:function(){this._project(),this._update()},_clickTolerance:function(){return(this.options.stroke?this.options.weight/2:0)+(this._renderer.options.tolerance||0)}}),Vi=xe.extend({options:{fill:!0,radius:10},initialize:function(t,n){kt(this,n),this._latlng=gt(t),this._radius=this.options.radius},setLatLng:function(t){var n=this._latlng;return this._latlng=gt(t),this.redraw(),this.fire("move",{oldLatLng:n,latlng:this._latlng})},getLatLng:function(){return this._latlng},setRadius:function(t){return this.options.radius=this._radius=t,this.redraw()},getRadius:function(){return this._radius},setStyle:function(t){var n=t&&t.radius||this._radius;return xe.prototype.setStyle.call(this,t),this.setRadius(n),this},_project:function(){this._point=this._map.latLngToLayerPoint(this._latlng),this._updateBounds()},_updateBounds:function(){var t=this._radius,n=this._radiusY||t,o=this._clickTolerance(),u=[t+o,n+o];this._pxBounds=new Tt(this._point.subtract(u),this._point.add(u))},_update:function(){this._map&&this._updatePath()},_updatePath:function(){this._renderer._updateCircle(this)},_empty:function(){return this._radius&&!this._renderer._bounds.intersects(this._pxBounds)},_containsPoint:function(t){return t.distanceTo(this._point)<=this._radius+this._clickTolerance()}});function Qa(t,n){return new Vi(t,n)}var Hi=Vi.extend({initialize:function(t,n,o){if(typeof n=="number"&&(n=z({},o,{radius:n})),kt(this,n),this._latlng=gt(t),isNaN(this.options.radius))throw new Error("Circle radius cannot be NaN");this._mRadius=this.options.radius},setRadius:function(t){return this._mRadius=t,this.redraw()},getRadius:function(){return this._mRadius},getBounds:function(){var t=[this._radius,this._radiusY||this._radius];return new Yt(this._map.layerPointToLatLng(this._point.subtract(t)),this._map.layerPointToLatLng(this._point.add(t)))},setStyle:xe.prototype.setStyle,_project:function(){var t=this._latlng.lng,n=this._latlng.lat,o=this._map,u=o.options.crs;if(u.distance===Ae.distance){var c=Math.PI/180,g=this._mRadius/Ae.R/c,B=o.project([n+g,t]),Z=o.project([n-g,t]),H=B.add(Z).divideBy(2),Y=o.unproject(H).lat,Q=Math.acos((Math.cos(g*c)-Math.sin(n*c)*Math.sin(Y*c))/(Math.cos(n*c)*Math.cos(Y*c)))/c;(isNaN(Q)||Q===0)&&(Q=g/Math.cos(Math.PI/180*n)),this._point=H.subtract(o.getPixelOrigin()),this._radius=isNaN(Q)?0:H.x-o.project([Y,t-Q]).x,this._radiusY=H.y-B.y}else{var ut=u.unproject(u.project(this._latlng).subtract([this._mRadius,0]));this._point=o.latLngToLayerPoint(this._latlng),this._radius=this._point.x-o.latLngToLayerPoint(ut).x}this._updateBounds()}});function We(t,n,o){return new Hi(t,n,o)}var Ce=xe.extend({options:{smoothFactor:1,noClip:!1},initialize:function(t,n){kt(this,n),this._setLatLngs(t)},getLatLngs:function(){return this._latlngs},setLatLngs:function(t){return this._setLatLngs(t),this.redraw()},isEmpty:function(){return!this._latlngs.length},closestLayerPoint:function(t){for(var n=1/0,o=null,u=Be,c,g,B=0,Z=this._parts.length;B<Z;B++)for(var H=this._parts[B],Y=1,Q=H.length;Y<Q;Y++){c=H[Y-1],g=H[Y];var ut=u(t,c,g,!0);ut<n&&(n=ut,o=u(t,c,g))}return o&&(o.distance=Math.sqrt(n)),o},getCenter:function(){if(!this._map)throw new Error("Must add layer to map before using getCenter()");return Wr(this._defaultShape(),this._map.options.crs)},getBounds:function(){return this._bounds},addLatLng:function(t,n){return n=n||this._defaultShape(),t=gt(t),n.push(t),this._bounds.extend(t),this.redraw()},_setLatLngs:function(t){this._bounds=new Yt,this._latlngs=this._convertLatLngs(t)},_defaultShape:function(){return It(this._latlngs)?this._latlngs:this._latlngs[0]},_convertLatLngs:function(t){for(var n=[],o=It(t),u=0,c=t.length;u<c;u++)o?(n[u]=gt(t[u]),this._bounds.extend(n[u])):n[u]=this._convertLatLngs(t[u]);return n},_project:function(){var t=new Tt;this._rings=[],this._projectLatlngs(this._latlngs,this._rings,t),this._bounds.isValid()&&t.isValid()&&(this._rawPxBounds=t,this._updateBounds())},_updateBounds:function(){var t=this._clickTolerance(),n=new dt(t,t);this._rawPxBounds&&(this._pxBounds=new Tt([this._rawPxBounds.min.subtract(n),this._rawPxBounds.max.add(n)]))},_projectLatlngs:function(t,n,o){var u=t[0]instanceof Ct,c=t.length,g,B;if(u){for(B=[],g=0;g<c;g++)B[g]=this._map.latLngToLayerPoint(t[g]),o.extend(B[g]);n.push(B)}else for(g=0;g<c;g++)this._projectLatlngs(t[g],n,o)},_clipPoints:function(){var t=this._renderer._bounds;if(this._parts=[],!(!this._pxBounds||!this._pxBounds.intersects(t))){if(this.options.noClip){this._parts=this._rings;return}var n=this._parts,o,u,c,g,B,Z,H;for(o=0,c=0,g=this._rings.length;o<g;o++)for(H=this._rings[o],u=0,B=H.length;u<B-1;u++)Z=Nn(H[u],H[u+1],t,u,!0),Z&&(n[c]=n[c]||[],n[c].push(Z[0]),(Z[1]!==H[u+1]||u===B-2)&&(n[c].push(Z[1]),c++))}},_simplifyPoints:function(){for(var t=this._parts,n=this.options.smoothFactor,o=0,u=t.length;o<u;o++)t[o]=Vr(t[o],n)},_update:function(){this._map&&(this._clipPoints(),this._simplifyPoints(),this._updatePath())},_updatePath:function(){this._renderer._updatePoly(this)},_containsPoint:function(t,n){var o,u,c,g,B,Z,H=this._clickTolerance();if(!this._pxBounds||!this._pxBounds.contains(t))return!1;for(o=0,g=this._parts.length;o<g;o++)for(Z=this._parts[o],u=0,B=Z.length,c=B-1;u<B;c=u++)if(!(!n&&u===0)&&Hr(t,Z[c],Z[u])<=H)return!0;return!1}});function to(t,n){return new Ce(t,n)}Ce._flat=qr;var ue=Ce.extend({options:{fill:!0},isEmpty:function(){return!this._latlngs.length||!this._latlngs[0].length},getCenter:function(){if(!this._map)throw new Error("Must add layer to map before using getCenter()");return Ur(this._defaultShape(),this._map.options.crs)},_convertLatLngs:function(t){var n=Ce.prototype._convertLatLngs.call(this,t),o=n.length;return o>=2&&n[0]instanceof Ct&&n[0].equals(n[o-1])&&n.pop(),n},_setLatLngs:function(t){Ce.prototype._setLatLngs.call(this,t),It(this._latlngs)&&(this._latlngs=[this._latlngs])},_defaultShape:function(){return It(this._latlngs[0])?this._latlngs[0]:this._latlngs[0][0]},_clipPoints:function(){var t=this._renderer._bounds,n=this.options.weight,o=new dt(n,n);if(t=new Tt(t.min.subtract(o),t.max.add(o)),this._parts=[],!(!this._pxBounds||!this._pxBounds.intersects(t))){if(this.options.noClip){this._parts=this._rings;return}for(var u=0,c=this._rings.length,g;u<c;u++)g=jr(this._rings[u],t,!0),g.length&&this._parts.push(g)}},_updatePath:function(){this._renderer._updatePoly(this,!0)},_containsPoint:function(t){var n=!1,o,u,c,g,B,Z,H,Y;if(!this._pxBounds||!this._pxBounds.contains(t))return!1;for(g=0,H=this._parts.length;g<H;g++)for(o=this._parts[g],B=0,Y=o.length,Z=Y-1;B<Y;Z=B++)u=o[B],c=o[Z],u.y>t.y!=c.y>t.y&&t.x<(c.x-u.x)*(t.y-u.y)/(c.y-u.y)+u.x&&(n=!n);return n||Ce.prototype._containsPoint.call(this,t,!0)}});function eo(t,n){return new ue(t,n)}var Pe=re.extend({initialize:function(t,n){kt(this,n),this._layers={},t&&this.addData(t)},addData:function(t){var n=de(t)?t:t.features,o,u,c;if(n){for(o=0,u=n.length;o<u;o++)c=n[o],(c.geometries||c.geometry||c.features||c.coordinates)&&this.addData(c);return this}var g=this.options;if(g.filter&&!g.filter(t))return this;var B=ni(t,g);return B?(B.feature=xi(t),B.defaultOptions=B.options,this.resetStyle(B),g.onEachFeature&&g.onEachFeature(t,B),this.addLayer(B)):this},resetStyle:function(t){return t===void 0?this.eachLayer(this.resetStyle,this):(t.options=z({},t.defaultOptions),this._setLayerStyle(t,this.options.style),this)},setStyle:function(t){return this.eachLayer(function(n){this._setLayerStyle(n,t)},this)},_setLayerStyle:function(t,n){t.setStyle&&(typeof n=="function"&&(n=n(t.feature)),t.setStyle(n))}});function ni(t,n){var o=t.type==="Feature"?t.geometry:t,u=o?o.coordinates:null,c=[],g=n&&n.pointToLayer,B=n&&n.coordsToLatLng||Vn,Z,H,Y,Q;if(!u&&!o)return null;switch(o.type){case"Point":return Z=B(u),ri(g,t,Z,n);case"MultiPoint":for(Y=0,Q=u.length;Y<Q;Y++)Z=B(u[Y]),c.push(ri(g,t,Z,n));return new re(c);case"LineString":case"MultiLineString":return H=Ki(u,o.type==="LineString"?0:1,B),new Ce(H,n);case"Polygon":case"MultiPolygon":return H=Ki(u,o.type==="Polygon"?1:2,B),new ue(H,n);case"GeometryCollection":for(Y=0,Q=o.geometries.length;Y<Q;Y++){var ut=ni({geometry:o.geometries[Y],type:"Feature",properties:t.properties},n);ut&&c.push(ut)}return new re(c);case"FeatureCollection":for(Y=0,Q=o.features.length;Y<Q;Y++){var _t=ni(o.features[Y],n);_t&&c.push(_t)}return new re(c);default:throw new Error("Invalid GeoJSON object.")}}function ri(t,n,o,u){return t?t(n,o):new Ui(o,u&&u.markersInheritOptions&&u)}function Vn(t){return new Ct(t[1],t[0],t[2])}function Ki(t,n,o){for(var u=[],c=0,g=t.length,B;c<g;c++)B=n?Ki(t[c],n-1,o):(o||Vn)(t[c]),u.push(B);return u}function Hn(t,n){return t=gt(t),t.alt!==void 0?[lt(t.lng,n),lt(t.lat,n),lt(t.alt,n)]:[lt(t.lng,n),lt(t.lat,n)]}function bi(t,n,o,u){for(var c=[],g=0,B=t.length;g<B;g++)c.push(n?bi(t[g],It(t[g])?0:n-1,o,u):Hn(t[g],u));return!n&&o&&c.length>0&&c.push(c[0].slice()),c}function ai(t,n){return t.feature?z({},t.feature,{geometry:n}):xi(n)}function xi(t){return t.type==="Feature"||t.type==="FeatureCollection"?t:{type:"Feature",properties:{},geometry:t}}var Kn={toGeoJSON:function(t){return ai(this,{type:"Point",coordinates:Hn(this.getLatLng(),t)})}};Ui.include(Kn),Hi.include(Kn),Vi.include(Kn),Ce.include({toGeoJSON:function(t){var n=!It(this._latlngs),o=bi(this._latlngs,n?1:0,!1,t);return ai(this,{type:(n?"Multi":"")+"LineString",coordinates:o})}}),ue.include({toGeoJSON:function(t){var n=!It(this._latlngs),o=n&&!It(this._latlngs[0]),u=bi(this._latlngs,o?2:n?1:0,!0,t);return n||(u=[u]),ai(this,{type:(o?"Multi":"")+"Polygon",coordinates:u})}}),qe.include({toMultiPoint:function(t){var n=[];return this.eachLayer(function(o){n.push(o.toGeoJSON(t).geometry.coordinates)}),ai(this,{type:"MultiPoint",coordinates:n})},toGeoJSON:function(t){var n=this.feature&&this.feature.geometry&&this.feature.geometry.type;if(n==="MultiPoint")return this.toMultiPoint(t);var o=n==="GeometryCollection",u=[];return this.eachLayer(function(c){if(c.toGeoJSON){var g=c.toGeoJSON(t);if(o)u.push(g.geometry);else{var B=xi(g);B.type==="FeatureCollection"?u.push.apply(u,B.features):u.push(B)}}}),o?ai(this,{geometries:u,type:"GeometryCollection"}):{type:"FeatureCollection",features:u}}});function Yr(t,n){return new Pe(t,n)}var io=Yr,Ci=$t.extend({options:{opacity:1,alt:"",interactive:!1,crossOrigin:!1,errorOverlayUrl:"",zIndex:1,className:""},initialize:function(t,n,o){this._url=t,this._bounds=Ft(n),kt(this,o)},onAdd:function(){this._image||(this._initImage(),this.options.opacity<1&&this._updateOpacity()),this.options.interactive&&(ft(this._image,"leaflet-interactive"),this.addInteractiveTarget(this._image)),this.getPane().appendChild(this._image),this._reset()},onRemove:function(){Mt(this._image),this.options.interactive&&this.removeInteractiveTarget(this._image)},setOpacity:function(t){return this.options.opacity=t,this._image&&this._updateOpacity(),this},setStyle:function(t){return t.opacity&&this.setOpacity(t.opacity),this},bringToFront:function(){return this._map&&Ge(this._image),this},bringToBack:function(){return this._map&&Ze(this._image),this},setUrl:function(t){return this._url=t,this._image&&(this._image.src=t),this},setBounds:function(t){return this._bounds=Ft(t),this._map&&this._reset(),this},getEvents:function(){var t={zoom:this._reset,viewreset:this._reset};return this._zoomAnimated&&(t.zoomanim=this._animateZoom),t},setZIndex:function(t){return this.options.zIndex=t,this._updateZIndex(),this},getBounds:function(){return this._bounds},getElement:function(){return this._image},_initImage:function(){var t=this._url.tagName==="IMG",n=this._image=t?this._url:bt("img");if(ft(n,"leaflet-image-layer"),this._zoomAnimated&&ft(n,"leaflet-zoom-animated"),this.options.className&&ft(n,this.options.className),n.onselectstart=et,n.onmousemove=et,n.onload=V(this.fire,this,"load"),n.onerror=V(this._overlayOnError,this,"error"),(this.options.crossOrigin||this.options.crossOrigin==="")&&(n.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),this.options.zIndex&&this._updateZIndex(),t){this._url=n.src;return}n.src=this._url,n.alt=this.options.alt},_animateZoom:function(t){var n=this._map.getZoomScale(t.zoom),o=this._map._latLngBoundsToNewLayerBounds(this._bounds,t.zoom,t.center).min;je(this._image,o,n)},_reset:function(){var t=this._image,n=new Tt(this._map.latLngToLayerPoint(this._bounds.getNorthWest()),this._map.latLngToLayerPoint(this._bounds.getSouthEast())),o=n.getSize();Ot(t,n.min),t.style.width=o.x+"px",t.style.height=o.y+"px"},_updateOpacity:function(){oe(this._image,this.options.opacity)},_updateZIndex:function(){this._image&&this.options.zIndex!==void 0&&this.options.zIndex!==null&&(this._image.style.zIndex=this.options.zIndex)},_overlayOnError:function(){this.fire("error");var t=this.options.errorOverlayUrl;t&&this._url!==t&&(this._url=t,this._image.src=t)},getCenter:function(){return this._bounds.getCenter()}}),qn=function(t,n,o){return new Ci(t,n,o)},Wn=Ci.extend({options:{autoplay:!0,loop:!0,keepAspectRatio:!0,muted:!1,playsInline:!0},_initImage:function(){var t=this._url.tagName==="VIDEO",n=this._image=t?this._url:bt("video");if(ft(n,"leaflet-image-layer"),this._zoomAnimated&&ft(n,"leaflet-zoom-animated"),this.options.className&&ft(n,this.options.className),n.onselectstart=et,n.onmousemove=et,n.onloadeddata=V(this.fire,this,"load"),t){for(var o=n.getElementsByTagName("source"),u=[],c=0;c<o.length;c++)u.push(o[c].src);this._url=o.length>0?u:[n.src];return}de(this._url)||(this._url=[this._url]),!this.options.keepAspectRatio&&Object.prototype.hasOwnProperty.call(n.style,"objectFit")&&(n.style.objectFit="fill"),n.autoplay=!!this.options.autoplay,n.loop=!!this.options.loop,n.muted=!!this.options.muted,n.playsInline=!!this.options.playsInline;for(var g=0;g<this._url.length;g++){var B=bt("source");B.src=this._url[g],n.appendChild(B)}}});function Jn(t,n,o){return new Wn(t,n,o)}var $r=Ci.extend({_initImage:function(){var t=this._image=this._url;ft(t,"leaflet-image-layer"),this._zoomAnimated&&ft(t,"leaflet-zoom-animated"),this.options.className&&ft(t,this.options.className),t.onselectstart=et,t.onmousemove=et}});function qi(t,n,o){return new $r(t,n,o)}var me=$t.extend({options:{interactive:!1,offset:[0,0],className:"",pane:void 0,content:""},initialize:function(t,n){t&&(t instanceof Ct||de(t))?(this._latlng=gt(t),kt(this,n)):(kt(this,t),this._source=n),this.options.content&&(this._content=this.options.content)},openOn:function(t){return t=arguments.length?t:this._source._map,t.hasLayer(this)||t.addLayer(this),this},close:function(){return this._map&&this._map.removeLayer(this),this},toggle:function(t){return this._map?this.close():(arguments.length?this._source=t:t=this._source,this._prepareOpen(),this.openOn(t._map)),this},onAdd:function(t){this._zoomAnimated=t._zoomAnimated,this._container||this._initLayout(),t._fadeAnimated&&oe(this._container,0),clearTimeout(this._removeTimeout),this.getPane().appendChild(this._container),this.update(),t._fadeAnimated&&oe(this._container,1),this.bringToFront(),this.options.interactive&&(ft(this._container,"leaflet-interactive"),this.addInteractiveTarget(this._container))},onRemove:function(t){t._fadeAnimated?(oe(this._container,0),this._removeTimeout=setTimeout(V(Mt,void 0,this._container),200)):Mt(this._container),this.options.interactive&&(At(this._container,"leaflet-interactive"),this.removeInteractiveTarget(this._container))},getLatLng:function(){return this._latlng},setLatLng:function(t){return this._latlng=gt(t),this._map&&(this._updatePosition(),this._adjustPan()),this},getContent:function(){return this._content},setContent:function(t){return this._content=t,this.update(),this},getElement:function(){return this._container},update:function(){this._map&&(this._container.style.visibility="hidden",this._updateContent(),this._updateLayout(),this._updatePosition(),this._container.style.visibility="",this._adjustPan())},getEvents:function(){var t={zoom:this._updatePosition,viewreset:this._updatePosition};return this._zoomAnimated&&(t.zoomanim=this._animateZoom),t},isOpen:function(){return!!this._map&&this._map.hasLayer(this)},bringToFront:function(){return this._map&&Ge(this._container),this},bringToBack:function(){return this._map&&Ze(this._container),this},_prepareOpen:function(t){var n=this._source;if(!n._map)return!1;if(n instanceof re){n=null;var o=this._source._layers;for(var u in o)if(o[u]._map){n=o[u];break}if(!n)return!1;this._source=n}if(!t)if(n.getCenter)t=n.getCenter();else if(n.getLatLng)t=n.getLatLng();else if(n.getBounds)t=n.getBounds().getCenter();else throw new Error("Unable to get source layer LatLng.");return this.setLatLng(t),this._map&&this.update(),!0},_updateContent:function(){if(this._content){var t=this._contentNode,n=typeof this._content=="function"?this._content(this._source||this):this._content;if(typeof n=="string")t.innerHTML=n;else{for(;t.hasChildNodes();)t.removeChild(t.firstChild);t.appendChild(n)}this.fire("contentupdate")}},_updatePosition:function(){if(this._map){var t=this._map.latLngToLayerPoint(this._latlng),n=ht(this.options.offset),o=this._getAnchor();this._zoomAnimated?Ot(this._container,t.add(o)):n=n.add(t).add(o);var u=this._containerBottom=-n.y,c=this._containerLeft=-Math.round(this._containerWidth/2)+n.x;this._container.style.bottom=u+"px",this._container.style.left=c+"px"}},_getAnchor:function(){return[0,0]}});mt.include({_initOverlay:function(t,n,o,u){var c=n;return c instanceof t||(c=new t(u).setContent(n)),o&&c.setLatLng(o),c}}),$t.include({_initOverlay:function(t,n,o,u){var c=o;return c instanceof t?(kt(c,u),c._source=this):(c=n&&!u?n:new t(u,this),c.setContent(o)),c}});var Wi=me.extend({options:{pane:"popupPane",offset:[0,7],maxWidth:300,minWidth:50,maxHeight:null,autoPan:!0,autoPanPaddingTopLeft:null,autoPanPaddingBottomRight:null,autoPanPadding:[5,5],keepInView:!1,closeButton:!0,autoClose:!0,closeOnEscapeKey:!0,className:""},openOn:function(t){return t=arguments.length?t:this._source._map,!t.hasLayer(this)&&t._popup&&t._popup.options.autoClose&&t.removeLayer(t._popup),t._popup=this,me.prototype.openOn.call(this,t)},onAdd:function(t){me.prototype.onAdd.call(this,t),t.fire("popupopen",{popup:this}),this._source&&(this._source.fire("popupopen",{popup:this},!0),this._source instanceof xe||this._source.on("preclick",Ve))},onRemove:function(t){me.prototype.onRemove.call(this,t),t.fire("popupclose",{popup:this}),this._source&&(this._source.fire("popupclose",{popup:this},!0),this._source instanceof xe||this._source.off("preclick",Ve))},getEvents:function(){var t=me.prototype.getEvents.call(this);return(this.options.closeOnClick!==void 0?this.options.closeOnClick:this._map.options.closePopupOnClick)&&(t.preclick=this.close),this.options.keepInView&&(t.moveend=this._adjustPan),t},_initLayout:function(){var t="leaflet-popup",n=this._container=bt("div",t+" "+(this.options.className||"")+" leaflet-zoom-animated"),o=this._wrapper=bt("div",t+"-content-wrapper",n);if(this._contentNode=bt("div",t+"-content",o),vi(n),Oe(this._contentNode),pt(n,"contextmenu",Ve),this._tipContainer=bt("div",t+"-tip-container",n),this._tip=bt("div",t+"-tip",this._tipContainer),this.options.closeButton){var u=this._closeButton=bt("a",t+"-close-button",n);u.setAttribute("role","button"),u.setAttribute("aria-label","Close popup"),u.href="#close",u.innerHTML='<span aria-hidden="true">&#215;</span>',pt(u,"click",function(c){Zt(c),this.close()},this)}},_updateLayout:function(){var t=this._contentNode,n=t.style;n.width="",n.whiteSpace="nowrap";var o=t.offsetWidth;o=Math.min(o,this.options.maxWidth),o=Math.max(o,this.options.minWidth),n.width=o+1+"px",n.whiteSpace="",n.height="";var u=t.offsetHeight,c=this.options.maxHeight,g="leaflet-popup-scrolled";c&&u>c?(n.height=c+"px",ft(t,g)):At(t,g),this._containerWidth=this._container.offsetWidth},_animateZoom:function(t){var n=this._map._latLngToNewLayerPoint(this._latlng,t.zoom,t.center),o=this._getAnchor();Ot(this._container,n.add(o))},_adjustPan:function(){if(this.options.autoPan){if(this._map._panAnim&&this._map._panAnim.stop(),this._autopanning){this._autopanning=!1;return}var t=this._map,n=parseInt(_i(this._container,"marginBottom"),10)||0,o=this._container.offsetHeight+n,u=this._containerWidth,c=new dt(this._containerLeft,-o-this._containerBottom);c._add(Ue(this._container));var g=t.layerPointToContainerPoint(c),B=ht(this.options.autoPanPadding),Z=ht(this.options.autoPanPaddingTopLeft||B),H=ht(this.options.autoPanPaddingBottomRight||B),Y=t.getSize(),Q=0,ut=0;g.x+u+H.x>Y.x&&(Q=g.x+u-Y.x+H.x),g.x-Q-Z.x<0&&(Q=g.x-Z.x),g.y+o+H.y>Y.y&&(ut=g.y+o-Y.y+H.y),g.y-ut-Z.y<0&&(ut=g.y-Z.y),(Q||ut)&&(this.options.keepInView&&(this._autopanning=!0),t.fire("autopanstart").panBy([Q,ut]))}},_getAnchor:function(){return ht(this._source&&this._source._getPopupAnchor?this._source._getPopupAnchor():[0,0])}}),no=function(t,n){return new Wi(t,n)};mt.mergeOptions({closePopupOnClick:!0}),mt.include({openPopup:function(t,n,o){return this._initOverlay(Wi,t,n,o).openOn(this),this},closePopup:function(t){return t=arguments.length?t:this._popup,t&&t.close(),this}}),$t.include({bindPopup:function(t,n){return this._popup=this._initOverlay(Wi,this._popup,t,n),this._popupHandlersAdded||(this.on({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!0),this},unbindPopup:function(){return this._popup&&(this.off({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!1,this._popup=null),this},openPopup:function(t){return this._popup&&(this instanceof re||(this._popup._source=this),this._popup._prepareOpen(t||this._latlng)&&this._popup.openOn(this._map)),this},closePopup:function(){return this._popup&&this._popup.close(),this},togglePopup:function(){return this._popup&&this._popup.toggle(this),this},isPopupOpen:function(){return this._popup?this._popup.isOpen():!1},setPopupContent:function(t){return this._popup&&this._popup.setContent(t),this},getPopup:function(){return this._popup},_openPopup:function(t){if(!(!this._popup||!this._map)){He(t);var n=t.layer||t.target;if(this._popup._source===n&&!(n instanceof xe)){this._map.hasLayer(this._popup)?this.closePopup():this.openPopup(t.latlng);return}this._popup._source=n,this.openPopup(t.latlng)}},_movePopup:function(t){this._popup.setLatLng(t.latlng)},_onKeyPress:function(t){t.originalEvent.keyCode===13&&this._openPopup(t)}});var Ji=me.extend({options:{pane:"tooltipPane",offset:[0,0],direction:"auto",permanent:!1,sticky:!1,opacity:.9},onAdd:function(t){me.prototype.onAdd.call(this,t),this.setOpacity(this.options.opacity),t.fire("tooltipopen",{tooltip:this}),this._source&&(this.addEventParent(this._source),this._source.fire("tooltipopen",{tooltip:this},!0))},onRemove:function(t){me.prototype.onRemove.call(this,t),t.fire("tooltipclose",{tooltip:this}),this._source&&(this.removeEventParent(this._source),this._source.fire("tooltipclose",{tooltip:this},!0))},getEvents:function(){var t=me.prototype.getEvents.call(this);return this.options.permanent||(t.preclick=this.close),t},_initLayout:function(){var t="leaflet-tooltip",n=t+" "+(this.options.className||"")+" leaflet-zoom-"+(this._zoomAnimated?"animated":"hide");this._contentNode=this._container=bt("div",n),this._container.setAttribute("role","tooltip"),this._container.setAttribute("id","leaflet-tooltip-"+M(this))},_updateLayout:function(){},_adjustPan:function(){},_setPosition:function(t){var n,o,u=this._map,c=this._container,g=u.latLngToContainerPoint(u.getCenter()),B=u.layerPointToContainerPoint(t),Z=this.options.direction,H=c.offsetWidth,Y=c.offsetHeight,Q=ht(this.options.offset),ut=this._getAnchor();Z==="top"?(n=H/2,o=Y):Z==="bottom"?(n=H/2,o=0):Z==="center"?(n=H/2,o=Y/2):Z==="right"?(n=0,o=Y/2):Z==="left"?(n=H,o=Y/2):B.x<g.x?(Z="right",n=0,o=Y/2):(Z="left",n=H+(Q.x+ut.x)*2,o=Y/2),t=t.subtract(ht(n,o,!0)).add(Q).add(ut),At(c,"leaflet-tooltip-right"),At(c,"leaflet-tooltip-left"),At(c,"leaflet-tooltip-top"),At(c,"leaflet-tooltip-bottom"),ft(c,"leaflet-tooltip-"+Z),Ot(c,t)},_updatePosition:function(){var t=this._map.latLngToLayerPoint(this._latlng);this._setPosition(t)},setOpacity:function(t){this.options.opacity=t,this._container&&oe(this._container,t)},_animateZoom:function(t){var n=this._map._latLngToNewLayerPoint(this._latlng,t.zoom,t.center);this._setPosition(n)},_getAnchor:function(){return ht(this._source&&this._source._getTooltipAnchor&&!this.options.sticky?this._source._getTooltipAnchor():[0,0])}}),wi=function(t,n){return new Ji(t,n)};mt.include({openTooltip:function(t,n,o){return this._initOverlay(Ji,t,n,o).openOn(this),this},closeTooltip:function(t){return t.close(),this}}),$t.include({bindTooltip:function(t,n){return this._tooltip&&this.isTooltipOpen()&&this.unbindTooltip(),this._tooltip=this._initOverlay(Ji,this._tooltip,t,n),this._initTooltipInteractions(),this._tooltip.options.permanent&&this._map&&this._map.hasLayer(this)&&this.openTooltip(),this},unbindTooltip:function(){return this._tooltip&&(this._initTooltipInteractions(!0),this.closeTooltip(),this._tooltip=null),this},_initTooltipInteractions:function(t){if(!(!t&&this._tooltipHandlersAdded)){var n=t?"off":"on",o={remove:this.closeTooltip,move:this._moveTooltip};this._tooltip.options.permanent?o.add=this._openTooltip:(o.mouseover=this._openTooltip,o.mouseout=this.closeTooltip,o.click=this._openTooltip,this._map?this._addFocusListeners():o.add=this._addFocusListeners),this._tooltip.options.sticky&&(o.mousemove=this._moveTooltip),this[n](o),this._tooltipHandlersAdded=!t}},openTooltip:function(t){return this._tooltip&&(this instanceof re||(this._tooltip._source=this),this._tooltip._prepareOpen(t)&&(this._tooltip.openOn(this._map),this.getElement?this._setAriaDescribedByOnLayer(this):this.eachLayer&&this.eachLayer(this._setAriaDescribedByOnLayer,this))),this},closeTooltip:function(){if(this._tooltip)return this._tooltip.close()},toggleTooltip:function(){return this._tooltip&&this._tooltip.toggle(this),this},isTooltipOpen:function(){return this._tooltip.isOpen()},setTooltipContent:function(t){return this._tooltip&&this._tooltip.setContent(t),this},getTooltip:function(){return this._tooltip},_addFocusListeners:function(){this.getElement?this._addFocusListenersOnLayer(this):this.eachLayer&&this.eachLayer(this._addFocusListenersOnLayer,this)},_addFocusListenersOnLayer:function(t){var n=typeof t.getElement=="function"&&t.getElement();n&&(pt(n,"focus",function(){this._tooltip._source=t,this.openTooltip()},this),pt(n,"blur",this.closeTooltip,this))},_setAriaDescribedByOnLayer:function(t){var n=typeof t.getElement=="function"&&t.getElement();n&&n.setAttribute("aria-describedby",this._tooltip._container.id)},_openTooltip:function(t){if(!(!this._tooltip||!this._map)){if(this._map.dragging&&this._map.dragging.moving()&&!this._openOnceFlag){this._openOnceFlag=!0;var n=this;this._map.once("moveend",function(){n._openOnceFlag=!1,n._openTooltip(t)});return}this._tooltip._source=t.layer||t.target,this.openTooltip(this._tooltip.options.sticky?t.latlng:void 0)}},_moveTooltip:function(t){var n=t.latlng,o,u;this._tooltip.options.sticky&&t.originalEvent&&(o=this._map.mouseEventToContainerPoint(t.originalEvent),u=this._map.containerPointToLayerPoint(o),n=this._map.layerPointToLatLng(u)),this._tooltip.setLatLng(n)}});var Xr=ii.extend({options:{iconSize:[12,12],html:!1,bgPos:null,className:"leaflet-div-icon"},createIcon:function(t){var n=t&&t.tagName==="DIV"?t:document.createElement("div"),o=this.options;if(o.html instanceof Element?(Fi(n),n.appendChild(o.html)):n.innerHTML=o.html!==!1?o.html:"",o.bgPos){var u=ht(o.bgPos);n.style.backgroundPosition=-u.x+"px "+-u.y+"px"}return this._setIconStyles(n,"icon"),n},createShadow:function(){return null}});function ro(t){return new Xr(t)}ii.Default=Li;var ki=$t.extend({options:{tileSize:256,opacity:1,updateWhenIdle:at.mobile,updateWhenZooming:!0,updateInterval:200,zIndex:1,bounds:null,minZoom:0,maxZoom:void 0,maxNativeZoom:void 0,minNativeZoom:void 0,noWrap:!1,pane:"tilePane",className:"",keepBuffer:2},initialize:function(t){kt(this,t)},onAdd:function(){this._initContainer(),this._levels={},this._tiles={},this._resetView()},beforeAdd:function(t){t._addZoomLimit(this)},onRemove:function(t){this._removeAllTiles(),Mt(this._container),t._removeZoomLimit(this),this._container=null,this._tileZoom=void 0},bringToFront:function(){return this._map&&(Ge(this._container),this._setAutoZIndex(Math.max)),this},bringToBack:function(){return this._map&&(Ze(this._container),this._setAutoZIndex(Math.min)),this},getContainer:function(){return this._container},setOpacity:function(t){return this.options.opacity=t,this._updateOpacity(),this},setZIndex:function(t){return this.options.zIndex=t,this._updateZIndex(),this},isLoading:function(){return this._loading},redraw:function(){if(this._map){this._removeAllTiles();var t=this._clampZoom(this._map.getZoom());t!==this._tileZoom&&(this._tileZoom=t,this._updateLevels()),this._update()}return this},getEvents:function(){var t={viewprereset:this._invalidateAll,viewreset:this._resetView,zoom:this._resetView,moveend:this._onMoveEnd};return this.options.updateWhenIdle||(this._onMove||(this._onMove=tt(this._onMoveEnd,this.options.updateInterval,this)),t.move=this._onMove),this._zoomAnimated&&(t.zoomanim=this._animateZoom),t},createTile:function(){return document.createElement("div")},getTileSize:function(){var t=this.options.tileSize;return t instanceof dt?t:new dt(t,t)},_updateZIndex:function(){this._container&&this.options.zIndex!==void 0&&this.options.zIndex!==null&&(this._container.style.zIndex=this.options.zIndex)},_setAutoZIndex:function(t){for(var n=this.getPane().children,o=-t(-1/0,1/0),u=0,c=n.length,g;u<c;u++)g=n[u].style.zIndex,n[u]!==this._container&&g&&(o=t(o,+g));isFinite(o)&&(this.options.zIndex=o+t(-1,1),this._updateZIndex())},_updateOpacity:function(){if(this._map&&!at.ielt9){oe(this._container,this.options.opacity);var t=+new Date,n=!1,o=!1;for(var u in this._tiles){var c=this._tiles[u];if(!(!c.current||!c.loaded)){var g=Math.min(1,(t-c.loaded)/200);oe(c.el,g),g<1?n=!0:(c.active?o=!0:this._onOpaqueTile(c),c.active=!0)}}o&&!this._noPrune&&this._pruneTiles(),n&&(Vt(this._fadeFrame),this._fadeFrame=Jt(this._updateOpacity,this))}},_onOpaqueTile:et,_initContainer:function(){this._container||(this._container=bt("div","leaflet-layer "+(this.options.className||"")),this._updateZIndex(),this.options.opacity<1&&this._updateOpacity(),this.getPane().appendChild(this._container))},_updateLevels:function(){var t=this._tileZoom,n=this.options.maxZoom;if(t!==void 0){for(var o in this._levels)o=Number(o),this._levels[o].el.children.length||o===t?(this._levels[o].el.style.zIndex=n-Math.abs(t-o),this._onUpdateLevel(o)):(Mt(this._levels[o].el),this._removeTilesAtZoom(o),this._onRemoveLevel(o),delete this._levels[o]);var u=this._levels[t],c=this._map;return u||(u=this._levels[t]={},u.el=bt("div","leaflet-tile-container leaflet-zoom-animated",this._container),u.el.style.zIndex=n,u.origin=c.project(c.unproject(c.getPixelOrigin()),t).round(),u.zoom=t,this._setZoomTransform(u,c.getCenter(),c.getZoom()),et(u.el.offsetWidth),this._onCreateLevel(u)),this._level=u,u}},_onUpdateLevel:et,_onRemoveLevel:et,_onCreateLevel:et,_pruneTiles:function(){if(this._map){var t,n,o=this._map.getZoom();if(o>this.options.maxZoom||o<this.options.minZoom){this._removeAllTiles();return}for(t in this._tiles)n=this._tiles[t],n.retain=n.current;for(t in this._tiles)if(n=this._tiles[t],n.current&&!n.active){var u=n.coords;this._retainParent(u.x,u.y,u.z,u.z-5)||this._retainChildren(u.x,u.y,u.z,u.z+2)}for(t in this._tiles)this._tiles[t].retain||this._removeTile(t)}},_removeTilesAtZoom:function(t){for(var n in this._tiles)this._tiles[n].coords.z===t&&this._removeTile(n)},_removeAllTiles:function(){for(var t in this._tiles)this._removeTile(t)},_invalidateAll:function(){for(var t in this._levels)Mt(this._levels[t].el),this._onRemoveLevel(Number(t)),delete this._levels[t];this._removeAllTiles(),this._tileZoom=void 0},_retainParent:function(t,n,o,u){var c=Math.floor(t/2),g=Math.floor(n/2),B=o-1,Z=new dt(+c,+g);Z.z=+B;var H=this._tileCoordsToKey(Z),Y=this._tiles[H];return Y&&Y.active?(Y.retain=!0,!0):(Y&&Y.loaded&&(Y.retain=!0),B>u?this._retainParent(c,g,B,u):!1)},_retainChildren:function(t,n,o,u){for(var c=2*t;c<2*t+2;c++)for(var g=2*n;g<2*n+2;g++){var B=new dt(c,g);B.z=o+1;var Z=this._tileCoordsToKey(B),H=this._tiles[Z];if(H&&H.active){H.retain=!0;continue}else H&&H.loaded&&(H.retain=!0);o+1<u&&this._retainChildren(c,g,o+1,u)}},_resetView:function(t){var n=t&&(t.pinch||t.flyTo);this._setView(this._map.getCenter(),this._map.getZoom(),n,n)},_animateZoom:function(t){this._setView(t.center,t.zoom,!0,t.noUpdate)},_clampZoom:function(t){var n=this.options;return n.minNativeZoom!==void 0&&t<n.minNativeZoom?n.minNativeZoom:n.maxNativeZoom!==void 0&&n.maxNativeZoom<t?n.maxNativeZoom:t},_setView:function(t,n,o,u){var c=Math.round(n);this.options.maxZoom!==void 0&&c>this.options.maxZoom||this.options.minZoom!==void 0&&c<this.options.minZoom?c=void 0:c=this._clampZoom(c);var g=this.options.updateWhenZooming&&c!==this._tileZoom;(!u||g)&&(this._tileZoom=c,this._abortLoading&&this._abortLoading(),this._updateLevels(),this._resetGrid(),c!==void 0&&this._update(t),o||this._pruneTiles(),this._noPrune=!!o),this._setZoomTransforms(t,n)},_setZoomTransforms:function(t,n){for(var o in this._levels)this._setZoomTransform(this._levels[o],t,n)},_setZoomTransform:function(t,n,o){var u=this._map.getZoomScale(o,t.zoom),c=t.origin.multiplyBy(u).subtract(this._map._getNewPixelOrigin(n,o)).round();at.any3d?je(t.el,c,u):Ot(t.el,c)},_resetGrid:function(){var t=this._map,n=t.options.crs,o=this._tileSize=this.getTileSize(),u=this._tileZoom,c=this._map.getPixelWorldBounds(this._tileZoom);c&&(this._globalTileRange=this._pxBoundsToTileRange(c)),this._wrapX=n.wrapLng&&!this.options.noWrap&&[Math.floor(t.project([0,n.wrapLng[0]],u).x/o.x),Math.ceil(t.project([0,n.wrapLng[1]],u).x/o.y)],this._wrapY=n.wrapLat&&!this.options.noWrap&&[Math.floor(t.project([n.wrapLat[0],0],u).y/o.x),Math.ceil(t.project([n.wrapLat[1],0],u).y/o.y)]},_onMoveEnd:function(){!this._map||this._map._animatingZoom||this._update()},_getTiledPixelBounds:function(t){var n=this._map,o=n._animatingZoom?Math.max(n._animateToZoom,n.getZoom()):n.getZoom(),u=n.getZoomScale(o,this._tileZoom),c=n.project(t,this._tileZoom).floor(),g=n.getSize().divideBy(u*2);return new Tt(c.subtract(g),c.add(g))},_update:function(t){var n=this._map;if(n){var o=this._clampZoom(n.getZoom());if(t===void 0&&(t=n.getCenter()),this._tileZoom!==void 0){var u=this._getTiledPixelBounds(t),c=this._pxBoundsToTileRange(u),g=c.getCenter(),B=[],Z=this.options.keepBuffer,H=new Tt(c.getBottomLeft().subtract([Z,-Z]),c.getTopRight().add([Z,-Z]));if(!(isFinite(c.min.x)&&isFinite(c.min.y)&&isFinite(c.max.x)&&isFinite(c.max.y)))throw new Error("Attempted to load an infinite number of tiles");for(var Y in this._tiles){var Q=this._tiles[Y].coords;(Q.z!==this._tileZoom||!H.contains(new dt(Q.x,Q.y)))&&(this._tiles[Y].current=!1)}if(Math.abs(o-this._tileZoom)>1){this._setView(t,o);return}for(var ut=c.min.y;ut<=c.max.y;ut++)for(var _t=c.min.x;_t<=c.max.x;_t++){var Kt=new dt(_t,ut);if(Kt.z=this._tileZoom,!!this._isValidTile(Kt)){var Nt=this._tiles[this._tileCoordsToKey(Kt)];Nt?Nt.current=!0:B.push(Kt)}}if(B.sort(function(Xt,ui){return Xt.distanceTo(g)-ui.distanceTo(g)}),B.length!==0){this._loading||(this._loading=!0,this.fire("loading"));var ce=document.createDocumentFragment();for(_t=0;_t<B.length;_t++)this._addTile(B[_t],ce);this._level.el.appendChild(ce)}}}},_isValidTile:function(t){var n=this._map.options.crs;if(!n.infinite){var o=this._globalTileRange;if(!n.wrapLng&&(t.x<o.min.x||t.x>o.max.x)||!n.wrapLat&&(t.y<o.min.y||t.y>o.max.y))return!1}if(!this.options.bounds)return!0;var u=this._tileCoordsToBounds(t);return Ft(this.options.bounds).overlaps(u)},_keyToBounds:function(t){return this._tileCoordsToBounds(this._keyToTileCoords(t))},_tileCoordsToNwSe:function(t){var n=this._map,o=this.getTileSize(),u=t.scaleBy(o),c=u.add(o),g=n.unproject(u,t.z),B=n.unproject(c,t.z);return[g,B]},_tileCoordsToBounds:function(t){var n=this._tileCoordsToNwSe(t),o=new Yt(n[0],n[1]);return this.options.noWrap||(o=this._map.wrapLatLngBounds(o)),o},_tileCoordsToKey:function(t){return t.x+":"+t.y+":"+t.z},_keyToTileCoords:function(t){var n=t.split(":"),o=new dt(+n[0],+n[1]);return o.z=+n[2],o},_removeTile:function(t){var n=this._tiles[t];n&&(Mt(n.el),delete this._tiles[t],this.fire("tileunload",{tile:n.el,coords:this._keyToTileCoords(t)}))},_initTile:function(t){ft(t,"leaflet-tile");var n=this.getTileSize();t.style.width=n.x+"px",t.style.height=n.y+"px",t.onselectstart=et,t.onmousemove=et,at.ielt9&&this.options.opacity<1&&oe(t,this.options.opacity)},_addTile:function(t,n){var o=this._getTilePos(t),u=this._tileCoordsToKey(t),c=this.createTile(this._wrapCoords(t),V(this._tileReady,this,t));this._initTile(c),this.createTile.length<2&&Jt(V(this._tileReady,this,t,null,c)),Ot(c,o),this._tiles[u]={el:c,coords:t,current:!0},n.appendChild(c),this.fire("tileloadstart",{tile:c,coords:t})},_tileReady:function(t,n,o){n&&this.fire("tileerror",{error:n,tile:o,coords:t});var u=this._tileCoordsToKey(t);o=this._tiles[u],o&&(o.loaded=+new Date,this._map._fadeAnimated?(oe(o.el,0),Vt(this._fadeFrame),this._fadeFrame=Jt(this._updateOpacity,this)):(o.active=!0,this._pruneTiles()),n||(ft(o.el,"leaflet-tile-loaded"),this.fire("tileload",{tile:o.el,coords:t})),this._noTilesToLoad()&&(this._loading=!1,this.fire("load"),at.ielt9||!this._map._fadeAnimated?Jt(this._pruneTiles,this):setTimeout(V(this._pruneTiles,this),250)))},_getTilePos:function(t){return t.scaleBy(this.getTileSize()).subtract(this._level.origin)},_wrapCoords:function(t){var n=new dt(this._wrapX?$(t.x,this._wrapX):t.x,this._wrapY?$(t.y,this._wrapY):t.y);return n.z=t.z,n},_pxBoundsToTileRange:function(t){var n=this.getTileSize();return new Tt(t.min.unscaleBy(n).floor(),t.max.unscaleBy(n).ceil().subtract([1,1]))},_noTilesToLoad:function(){for(var t in this._tiles)if(!this._tiles[t].loaded)return!1;return!0}});function ao(t){return new ki(t)}var oi=ki.extend({options:{minZoom:0,maxZoom:18,subdomains:"abc",errorTileUrl:"",zoomOffset:0,tms:!1,zoomReverse:!1,detectRetina:!1,crossOrigin:!1,referrerPolicy:!1},initialize:function(t,n){this._url=t,n=kt(this,n),n.detectRetina&&at.retina&&n.maxZoom>0?(n.tileSize=Math.floor(n.tileSize/2),n.zoomReverse?(n.zoomOffset--,n.minZoom=Math.min(n.maxZoom,n.minZoom+1)):(n.zoomOffset++,n.maxZoom=Math.max(n.minZoom,n.maxZoom-1)),n.minZoom=Math.max(0,n.minZoom)):n.zoomReverse?n.minZoom=Math.min(n.maxZoom,n.minZoom):n.maxZoom=Math.max(n.minZoom,n.maxZoom),typeof n.subdomains=="string"&&(n.subdomains=n.subdomains.split("")),this.on("tileunload",this._onTileRemove)},setUrl:function(t,n){return this._url===t&&n===void 0&&(n=!0),this._url=t,n||this.redraw(),this},createTile:function(t,n){var o=document.createElement("img");return pt(o,"load",V(this._tileOnLoad,this,n,o)),pt(o,"error",V(this._tileOnError,this,n,o)),(this.options.crossOrigin||this.options.crossOrigin==="")&&(o.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),typeof this.options.referrerPolicy=="string"&&(o.referrerPolicy=this.options.referrerPolicy),o.alt="",o.src=this.getTileUrl(t),o},getTileUrl:function(t){var n={r:at.retina?"@2x":"",s:this._getSubdomain(t),x:t.x,y:t.y,z:this._getZoomForUrl()};if(this._map&&!this._map.options.crs.infinite){var o=this._globalTileRange.max.y-t.y;this.options.tms&&(n.y=o),n["-y"]=o}return hi(this._url,z(n,this.options))},_tileOnLoad:function(t,n){at.ielt9?setTimeout(V(t,this,null,n),0):t(null,n)},_tileOnError:function(t,n,o){var u=this.options.errorTileUrl;u&&n.getAttribute("src")!==u&&(n.src=u),t(o,n)},_onTileRemove:function(t){t.tile.onload=null},_getZoomForUrl:function(){var t=this._tileZoom,n=this.options.maxZoom,o=this.options.zoomReverse,u=this.options.zoomOffset;return o&&(t=n-t),t+u},_getSubdomain:function(t){var n=Math.abs(t.x+t.y)%this.options.subdomains.length;return this.options.subdomains[n]},_abortLoading:function(){var t,n;for(t in this._tiles)if(this._tiles[t].coords.z!==this._tileZoom&&(n=this._tiles[t].el,n.onload=et,n.onerror=et,!n.complete)){n.src=Pi;var o=this._tiles[t].coords;Mt(n),delete this._tiles[t],this.fire("tileabort",{tile:n,coords:o})}},_removeTile:function(t){var n=this._tiles[t];if(n)return n.el.setAttribute("src",Pi),ki.prototype._removeTile.call(this,t)},_tileReady:function(t,n,o){if(!(!this._map||o&&o.getAttribute("src")===Pi))return ki.prototype._tileReady.call(this,t,n,o)}});function Qr(t,n){return new oi(t,n)}var ta=oi.extend({defaultWmsParams:{service:"WMS",request:"GetMap",layers:"",styles:"",format:"image/jpeg",transparent:!1,version:"1.1.1"},options:{crs:null,uppercase:!1},initialize:function(t,n){this._url=t;var o=z({},this.defaultWmsParams);for(var u in n)u in this.options||(o[u]=n[u]);n=kt(this,n);var c=n.detectRetina&&at.retina?2:1,g=this.getTileSize();o.width=g.x*c,o.height=g.y*c,this.wmsParams=o},onAdd:function(t){this._crs=this.options.crs||t.options.crs,this._wmsVersion=parseFloat(this.wmsParams.version);var n=this._wmsVersion>=1.3?"crs":"srs";this.wmsParams[n]=this._crs.code,oi.prototype.onAdd.call(this,t)},getTileUrl:function(t){var n=this._tileCoordsToNwSe(t),o=this._crs,u=Gt(o.project(n[0]),o.project(n[1])),c=u.min,g=u.max,B=(this._wmsVersion>=1.3&&this._crs===_e?[c.y,c.x,g.y,g.x]:[c.x,c.y,g.x,g.y]).join(","),Z=oi.prototype.getTileUrl.call(this,t);return Z+or(this.wmsParams,Z,this.options.uppercase)+(this.options.uppercase?"&BBOX=":"&bbox=")+B},setParams:function(t,n){return z(this.wmsParams,t),n||this.redraw(),this}});function oo(t,n){return new ta(t,n)}oi.WMS=ta,Qr.wms=oo;var we=$t.extend({options:{padding:.1},initialize:function(t){kt(this,t),M(this),this._layers=this._layers||{}},onAdd:function(){this._container||(this._initContainer(),ft(this._container,"leaflet-zoom-animated")),this.getPane().appendChild(this._container),this._update(),this.on("update",this._updatePaths,this)},onRemove:function(){this.off("update",this._updatePaths,this),this._destroyContainer()},getEvents:function(){var t={viewreset:this._reset,zoom:this._onZoom,moveend:this._update,zoomend:this._onZoomEnd};return this._zoomAnimated&&(t.zoomanim=this._onAnimZoom),t},_onAnimZoom:function(t){this._updateTransform(t.center,t.zoom)},_onZoom:function(){this._updateTransform(this._map.getCenter(),this._map.getZoom())},_updateTransform:function(t,n){var o=this._map.getZoomScale(n,this._zoom),u=this._map.getSize().multiplyBy(.5+this.options.padding),c=this._map.project(this._center,n),g=u.multiplyBy(-o).add(c).subtract(this._map._getNewPixelOrigin(t,n));at.any3d?je(this._container,g,o):Ot(this._container,g)},_reset:function(){this._update(),this._updateTransform(this._center,this._zoom);for(var t in this._layers)this._layers[t]._reset()},_onZoomEnd:function(){for(var t in this._layers)this._layers[t]._project()},_updatePaths:function(){for(var t in this._layers)this._layers[t]._update()},_update:function(){var t=this.options.padding,n=this._map.getSize(),o=this._map.containerPointToLayerPoint(n.multiplyBy(-t)).round();this._bounds=new Tt(o,o.add(n.multiplyBy(1+t*2)).round()),this._center=this._map.getCenter(),this._zoom=this._map.getZoom()}}),Yi=we.extend({options:{tolerance:0},getEvents:function(){var t=we.prototype.getEvents.call(this);return t.viewprereset=this._onViewPreReset,t},_onViewPreReset:function(){this._postponeUpdatePaths=!0},onAdd:function(){we.prototype.onAdd.call(this),this._draw()},_initContainer:function(){var t=this._container=document.createElement("canvas");pt(t,"mousemove",this._onMouseMove,this),pt(t,"click dblclick mousedown mouseup contextmenu",this._onClick,this),pt(t,"mouseout",this._handleMouseOut,this),t._leaflet_disable_events=!0,this._ctx=t.getContext("2d")},_destroyContainer:function(){Vt(this._redrawRequest),delete this._ctx,Mt(this._container),Et(this._container),delete this._container},_updatePaths:function(){if(!this._postponeUpdatePaths){var t;this._redrawBounds=null;for(var n in this._layers)t=this._layers[n],t._update();this._redraw()}},_update:function(){if(!(this._map._animatingZoom&&this._bounds)){we.prototype._update.call(this);var t=this._bounds,n=this._container,o=t.getSize(),u=at.retina?2:1;Ot(n,t.min),n.width=u*o.x,n.height=u*o.y,n.style.width=o.x+"px",n.style.height=o.y+"px",at.retina&&this._ctx.scale(2,2),this._ctx.translate(-t.min.x,-t.min.y),this.fire("update")}},_reset:function(){we.prototype._reset.call(this),this._postponeUpdatePaths&&(this._postponeUpdatePaths=!1,this._updatePaths())},_initPath:function(t){this._updateDashArray(t),this._layers[M(t)]=t;var n=t._order={layer:t,prev:this._drawLast,next:null};this._drawLast&&(this._drawLast.next=n),this._drawLast=n,this._drawFirst=this._drawFirst||this._drawLast},_addPath:function(t){this._requestRedraw(t)},_removePath:function(t){var n=t._order,o=n.next,u=n.prev;o?o.prev=u:this._drawLast=u,u?u.next=o:this._drawFirst=o,delete t._order,delete this._layers[M(t)],this._requestRedraw(t)},_updatePath:function(t){this._extendRedrawBounds(t),t._project(),t._update(),this._requestRedraw(t)},_updateStyle:function(t){this._updateDashArray(t),this._requestRedraw(t)},_updateDashArray:function(t){if(typeof t.options.dashArray=="string"){var n=t.options.dashArray.split(/[, ]+/),o=[],u,c;for(c=0;c<n.length;c++){if(u=Number(n[c]),isNaN(u))return;o.push(u)}t.options._dashArray=o}else t.options._dashArray=t.options.dashArray},_requestRedraw:function(t){this._map&&(this._extendRedrawBounds(t),this._redrawRequest=this._redrawRequest||Jt(this._redraw,this))},_extendRedrawBounds:function(t){if(t._pxBounds){var n=(t.options.weight||0)+1;this._redrawBounds=this._redrawBounds||new Tt,this._redrawBounds.extend(t._pxBounds.min.subtract([n,n])),this._redrawBounds.extend(t._pxBounds.max.add([n,n]))}},_redraw:function(){this._redrawRequest=null,this._redrawBounds&&(this._redrawBounds.min._floor(),this._redrawBounds.max._ceil()),this._clear(),this._draw(),this._redrawBounds=null},_clear:function(){var t=this._redrawBounds;if(t){var n=t.getSize();this._ctx.clearRect(t.min.x,t.min.y,n.x,n.y)}else this._ctx.save(),this._ctx.setTransform(1,0,0,1,0,0),this._ctx.clearRect(0,0,this._container.width,this._container.height),this._ctx.restore()},_draw:function(){var t,n=this._redrawBounds;if(this._ctx.save(),n){var o=n.getSize();this._ctx.beginPath(),this._ctx.rect(n.min.x,n.min.y,o.x,o.y),this._ctx.clip()}this._drawing=!0;for(var u=this._drawFirst;u;u=u.next)t=u.layer,(!n||t._pxBounds&&t._pxBounds.intersects(n))&&t._updatePath();this._drawing=!1,this._ctx.restore()},_updatePoly:function(t,n){if(this._drawing){var o,u,c,g,B=t._parts,Z=B.length,H=this._ctx;if(Z){for(H.beginPath(),o=0;o<Z;o++){for(u=0,c=B[o].length;u<c;u++)g=B[o][u],H[u?"lineTo":"moveTo"](g.x,g.y);n&&H.closePath()}this._fillStroke(H,t)}}},_updateCircle:function(t){if(!(!this._drawing||t._empty())){var n=t._point,o=this._ctx,u=Math.max(Math.round(t._radius),1),c=(Math.max(Math.round(t._radiusY),1)||u)/u;c!==1&&(o.save(),o.scale(1,c)),o.beginPath(),o.arc(n.x,n.y/c,u,0,Math.PI*2,!1),c!==1&&o.restore(),this._fillStroke(o,t)}},_fillStroke:function(t,n){var o=n.options;o.fill&&(t.globalAlpha=o.fillOpacity,t.fillStyle=o.fillColor||o.color,t.fill(o.fillRule||"evenodd")),o.stroke&&o.weight!==0&&(t.setLineDash&&t.setLineDash(n.options&&n.options._dashArray||[]),t.globalAlpha=o.opacity,t.lineWidth=o.weight,t.strokeStyle=o.color,t.lineCap=o.lineCap,t.lineJoin=o.lineJoin,t.stroke())},_onClick:function(t){for(var n=this._map.mouseEventToLayerPoint(t),o,u,c=this._drawFirst;c;c=c.next)o=c.layer,o.options.interactive&&o._containsPoint(n)&&(!(t.type==="click"||t.type==="preclick")||!this._map._draggableMoved(o))&&(u=o);this._fireEvent(u?[u]:!1,t)},_onMouseMove:function(t){if(!(!this._map||this._map.dragging.moving()||this._map._animatingZoom)){var n=this._map.mouseEventToLayerPoint(t);this._handleMouseHover(t,n)}},_handleMouseOut:function(t){var n=this._hoveredLayer;n&&(At(this._container,"leaflet-interactive"),this._fireEvent([n],t,"mouseout"),this._hoveredLayer=null,this._mouseHoverThrottled=!1)},_handleMouseHover:function(t,n){if(!this._mouseHoverThrottled){for(var o,u,c=this._drawFirst;c;c=c.next)o=c.layer,o.options.interactive&&o._containsPoint(n)&&(u=o);u!==this._hoveredLayer&&(this._handleMouseOut(t),u&&(ft(this._container,"leaflet-interactive"),this._fireEvent([u],t,"mouseover"),this._hoveredLayer=u)),this._fireEvent(this._hoveredLayer?[this._hoveredLayer]:!1,t),this._mouseHoverThrottled=!0,setTimeout(V(function(){this._mouseHoverThrottled=!1},this),32)}},_fireEvent:function(t,n,o){this._map._fireDOMEvent(n,o||n.type,t)},_bringToFront:function(t){var n=t._order;if(n){var o=n.next,u=n.prev;if(o)o.prev=u;else return;u?u.next=o:o&&(this._drawFirst=o),n.prev=this._drawLast,this._drawLast.next=n,n.next=null,this._drawLast=n,this._requestRedraw(t)}},_bringToBack:function(t){var n=t._order;if(n){var o=n.next,u=n.prev;if(u)u.next=o;else return;o?o.prev=u:u&&(this._drawLast=u),n.prev=null,n.next=this._drawFirst,this._drawFirst.prev=n,this._drawFirst=n,this._requestRedraw(t)}}});function ea(t){return at.canvas?new Yi(t):null}var Mi=function(){try{return document.namespaces.add("lvml","urn:schemas-microsoft-com:vml"),function(t){return document.createElement("<lvml:"+t+' class="lvml">')}}catch{}return function(t){return document.createElement("<"+t+' xmlns="urn:schemas-microsoft.com:vml" class="lvml">')}}(),ia={_initContainer:function(){this._container=bt("div","leaflet-vml-container")},_update:function(){this._map._animatingZoom||(we.prototype._update.call(this),this.fire("update"))},_initPath:function(t){var n=t._container=Mi("shape");ft(n,"leaflet-vml-shape "+(this.options.className||"")),n.coordsize="1 1",t._path=Mi("path"),n.appendChild(t._path),this._updateStyle(t),this._layers[M(t)]=t},_addPath:function(t){var n=t._container;this._container.appendChild(n),t.options.interactive&&t.addInteractiveTarget(n)},_removePath:function(t){var n=t._container;Mt(n),t.removeInteractiveTarget(n),delete this._layers[M(t)]},_updateStyle:function(t){var n=t._stroke,o=t._fill,u=t.options,c=t._container;c.stroked=!!u.stroke,c.filled=!!u.fill,u.stroke?(n||(n=t._stroke=Mi("stroke")),c.appendChild(n),n.weight=u.weight+"px",n.color=u.color,n.opacity=u.opacity,u.dashArray?n.dashStyle=de(u.dashArray)?u.dashArray.join(" "):u.dashArray.replace(/( *, *)/g," "):n.dashStyle="",n.endcap=u.lineCap.replace("butt","flat"),n.joinstyle=u.lineJoin):n&&(c.removeChild(n),t._stroke=null),u.fill?(o||(o=t._fill=Mi("fill")),c.appendChild(o),o.color=u.fillColor||u.color,o.opacity=u.fillOpacity):o&&(c.removeChild(o),t._fill=null)},_updateCircle:function(t){var n=t._point.round(),o=Math.round(t._radius),u=Math.round(t._radiusY||o);this._setPath(t,t._empty()?"M0 0":"AL "+n.x+","+n.y+" "+o+","+u+" 0,"+65535*360)},_setPath:function(t,n){t._path.v=n},_bringToFront:function(t){Ge(t._container)},_bringToBack:function(t){Ze(t._container)}},si=at.vml?Mi:hr,Ei=we.extend({_initContainer:function(){this._container=si("svg"),this._container.setAttribute("pointer-events","none"),this._rootGroup=si("g"),this._container.appendChild(this._rootGroup)},_destroyContainer:function(){Mt(this._container),Et(this._container),delete this._container,delete this._rootGroup,delete this._svgSize},_update:function(){if(!(this._map._animatingZoom&&this._bounds)){we.prototype._update.call(this);var t=this._bounds,n=t.getSize(),o=this._container;(!this._svgSize||!this._svgSize.equals(n))&&(this._svgSize=n,o.setAttribute("width",n.x),o.setAttribute("height",n.y)),Ot(o,t.min),o.setAttribute("viewBox",[t.min.x,t.min.y,n.x,n.y].join(" ")),this.fire("update")}},_initPath:function(t){var n=t._path=si("path");t.options.className&&ft(n,t.options.className),t.options.interactive&&ft(n,"leaflet-interactive"),this._updateStyle(t),this._layers[M(t)]=t},_addPath:function(t){this._rootGroup||this._initContainer(),this._rootGroup.appendChild(t._path),t.addInteractiveTarget(t._path)},_removePath:function(t){Mt(t._path),t.removeInteractiveTarget(t._path),delete this._layers[M(t)]},_updatePath:function(t){t._project(),t._update()},_updateStyle:function(t){var n=t._path,o=t.options;n&&(o.stroke?(n.setAttribute("stroke",o.color),n.setAttribute("stroke-opacity",o.opacity),n.setAttribute("stroke-width",o.weight),n.setAttribute("stroke-linecap",o.lineCap),n.setAttribute("stroke-linejoin",o.lineJoin),o.dashArray?n.setAttribute("stroke-dasharray",o.dashArray):n.removeAttribute("stroke-dasharray"),o.dashOffset?n.setAttribute("stroke-dashoffset",o.dashOffset):n.removeAttribute("stroke-dashoffset")):n.setAttribute("stroke","none"),o.fill?(n.setAttribute("fill",o.fillColor||o.color),n.setAttribute("fill-opacity",o.fillOpacity),n.setAttribute("fill-rule",o.fillRule||"evenodd")):n.setAttribute("fill","none"))},_updatePoly:function(t,n){this._setPath(t,fn(t._parts,n))},_updateCircle:function(t){var n=t._point,o=Math.max(Math.round(t._radius),1),u=Math.max(Math.round(t._radiusY),1)||o,c="a"+o+","+u+" 0 1,0 ",g=t._empty()?"M0 0":"M"+(n.x-o)+","+n.y+c+o*2+",0 "+c+-o*2+",0 ";this._setPath(t,g)},_setPath:function(t,n){t._path.setAttribute("d",n)},_bringToFront:function(t){Ge(t._path)},_bringToBack:function(t){Ze(t._path)}});at.vml&&Ei.include(ia);function na(t){return at.svg||at.vml?new Ei(t):null}mt.include({getRenderer:function(t){var n=t.options.renderer||this._getPaneRenderer(t.options.pane)||this.options.renderer||this._renderer;return n||(n=this._renderer=this._createRenderer()),this.hasLayer(n)||this.addLayer(n),n},_getPaneRenderer:function(t){if(t==="overlayPane"||t===void 0)return!1;var n=this._paneRenderers[t];return n===void 0&&(n=this._createRenderer({pane:t}),this._paneRenderers[t]=n),n},_createRenderer:function(t){return this.options.preferCanvas&&ea(t)||na(t)}});var $i=ue.extend({initialize:function(t,n){ue.prototype.initialize.call(this,this._boundsToLatLngs(t),n)},setBounds:function(t){return this.setLatLngs(this._boundsToLatLngs(t))},_boundsToLatLngs:function(t){return t=Ft(t),[t.getSouthWest(),t.getNorthWest(),t.getNorthEast(),t.getSouthEast()]}});function le(t,n){return new $i(t,n)}Ei.create=si,Ei.pointsToPath=fn,Pe.geometryToLayer=ni,Pe.coordsToLatLng=Vn,Pe.coordsToLatLngs=Ki,Pe.latLngToCoords=Hn,Pe.latLngsToCoords=bi,Pe.getFeature=ai,Pe.asFeature=xi,mt.mergeOptions({boxZoom:!0});var Wt=jt.extend({initialize:function(t){this._map=t,this._container=t._container,this._pane=t._panes.overlayPane,this._resetStateTimeout=0,t.on("unload",this._destroy,this)},addHooks:function(){pt(this._container,"mousedown",this._onMouseDown,this)},removeHooks:function(){Et(this._container,"mousedown",this._onMouseDown,this)},moved:function(){return this._moved},_destroy:function(){Mt(this._pane),delete this._pane},_resetState:function(){this._resetStateTimeout=0,this._moved=!1},_clearDeferredResetState:function(){this._resetStateTimeout!==0&&(clearTimeout(this._resetStateTimeout),this._resetStateTimeout=0)},_onMouseDown:function(t){if(!t.shiftKey||t.which!==1&&t.button!==1)return!1;this._clearDeferredResetState(),this._resetState(),mi(),Mn(),this._startPoint=this._map.mouseEventToContainerPoint(t),pt(document,{contextmenu:He,mousemove:this._onMouseMove,mouseup:this._onMouseUp,keydown:this._onKeyDown},this)},_onMouseMove:function(t){this._moved||(this._moved=!0,this._box=bt("div","leaflet-zoom-box",this._container),ft(this._container,"leaflet-crosshair"),this._map.fire("boxzoomstart")),this._point=this._map.mouseEventToContainerPoint(t);var n=new Tt(this._point,this._startPoint),o=n.getSize();Ot(this._box,n.min),this._box.style.width=o.x+"px",this._box.style.height=o.y+"px"},_finish:function(){this._moved&&(Mt(this._box),At(this._container,"leaflet-crosshair")),gi(),zi(),Et(document,{contextmenu:He,mousemove:this._onMouseMove,mouseup:this._onMouseUp,keydown:this._onKeyDown},this)},_onMouseUp:function(t){if(!(t.which!==1&&t.button!==1)&&(this._finish(),!!this._moved)){this._clearDeferredResetState(),this._resetStateTimeout=setTimeout(V(this._resetState,this),0);var n=new Yt(this._map.containerPointToLatLng(this._startPoint),this._map.containerPointToLatLng(this._point));this._map.fitBounds(n).fire("boxzoomend",{boxZoomBounds:n})}},_onKeyDown:function(t){t.keyCode===27&&(this._finish(),this._clearDeferredResetState(),this._resetState())}});mt.addInitHook("addHandler","boxZoom",Wt),mt.mergeOptions({doubleClickZoom:!0});var Yn=jt.extend({addHooks:function(){this._map.on("dblclick",this._onDoubleClick,this)},removeHooks:function(){this._map.off("dblclick",this._onDoubleClick,this)},_onDoubleClick:function(t){var n=this._map,o=n.getZoom(),u=n.options.zoomDelta,c=t.originalEvent.shiftKey?o-u:o+u;n.options.doubleClickZoom==="center"?n.setZoom(c):n.setZoomAround(t.containerPoint,c)}});mt.addInitHook("addHandler","doubleClickZoom",Yn),mt.mergeOptions({dragging:!0,inertia:!0,inertiaDeceleration:3400,inertiaMaxSpeed:1/0,easeLinearity:.2,worldCopyJump:!1,maxBoundsViscosity:0});var he=jt.extend({addHooks:function(){if(!this._draggable){var t=this._map;this._draggable=new Re(t._mapPane,t._container),this._draggable.on({dragstart:this._onDragStart,drag:this._onDrag,dragend:this._onDragEnd},this),this._draggable.on("predrag",this._onPreDragLimit,this),t.options.worldCopyJump&&(this._draggable.on("predrag",this._onPreDragWrap,this),t.on("zoomend",this._onZoomEnd,this),t.whenReady(this._onZoomEnd,this))}ft(this._map._container,"leaflet-grab leaflet-touch-drag"),this._draggable.enable(),this._positions=[],this._times=[]},removeHooks:function(){At(this._map._container,"leaflet-grab"),At(this._map._container,"leaflet-touch-drag"),this._draggable.disable()},moved:function(){return this._draggable&&this._draggable._moved},moving:function(){return this._draggable&&this._draggable._moving},_onDragStart:function(){var t=this._map;if(t._stop(),this._map.options.maxBounds&&this._map.options.maxBoundsViscosity){var n=Ft(this._map.options.maxBounds);this._offsetLimit=Gt(this._map.latLngToContainerPoint(n.getNorthWest()).multiplyBy(-1),this._map.latLngToContainerPoint(n.getSouthEast()).multiplyBy(-1).add(this._map.getSize())),this._viscosity=Math.min(1,Math.max(0,this._map.options.maxBoundsViscosity))}else this._offsetLimit=null;t.fire("movestart").fire("dragstart"),t.options.inertia&&(this._positions=[],this._times=[])},_onDrag:function(t){if(this._map.options.inertia){var n=this._lastTime=+new Date,o=this._lastPos=this._draggable._absPos||this._draggable._newPos;this._positions.push(o),this._times.push(n),this._prunePositions(n)}this._map.fire("move",t).fire("drag",t)},_prunePositions:function(t){for(;this._positions.length>1&&t-this._times[0]>50;)this._positions.shift(),this._times.shift()},_onZoomEnd:function(){var t=this._map.getSize().divideBy(2),n=this._map.latLngToLayerPoint([0,0]);this._initialWorldOffset=n.subtract(t).x,this._worldWidth=this._map.getPixelWorldBounds().getSize().x},_viscousLimit:function(t,n){return t-(t-n)*this._viscosity},_onPreDragLimit:function(){if(!(!this._viscosity||!this._offsetLimit)){var t=this._draggable._newPos.subtract(this._draggable._startPos),n=this._offsetLimit;t.x<n.min.x&&(t.x=this._viscousLimit(t.x,n.min.x)),t.y<n.min.y&&(t.y=this._viscousLimit(t.y,n.min.y)),t.x>n.max.x&&(t.x=this._viscousLimit(t.x,n.max.x)),t.y>n.max.y&&(t.y=this._viscousLimit(t.y,n.max.y)),this._draggable._newPos=this._draggable._startPos.add(t)}},_onPreDragWrap:function(){var t=this._worldWidth,n=Math.round(t/2),o=this._initialWorldOffset,u=this._draggable._newPos.x,c=(u-n+o)%t+n-o,g=(u+n+o)%t-n-o,B=Math.abs(c+o)<Math.abs(g+o)?c:g;this._draggable._absPos=this._draggable._newPos.clone(),this._draggable._newPos.x=B},_onDragEnd:function(t){var n=this._map,o=n.options,u=!o.inertia||t.noInertia||this._times.length<2;if(n.fire("dragend",t),u)n.fire("moveend");else{this._prunePositions(+new Date);var c=this._lastPos.subtract(this._positions[0]),g=(this._lastTime-this._times[0])/1e3,B=o.easeLinearity,Z=c.multiplyBy(B/g),H=Z.distanceTo([0,0]),Y=Math.min(o.inertiaMaxSpeed,H),Q=Z.multiplyBy(Y/H),ut=Y/(o.inertiaDeceleration*B),_t=Q.multiplyBy(-ut/2).round();!_t.x&&!_t.y?n.fire("moveend"):(_t=n._limitOffset(_t,n.options.maxBounds),Jt(function(){n.panBy(_t,{duration:ut,easeLinearity:B,noMoveStart:!0,animate:!0})}))}}});mt.addInitHook("addHandler","dragging",he),mt.mergeOptions({keyboard:!0,keyboardPanDelta:80});var ct=jt.extend({keyCodes:{left:[37],right:[39],down:[40],up:[38],zoomIn:[187,107,61,171],zoomOut:[189,109,54,173]},initialize:function(t){this._map=t,this._setPanDelta(t.options.keyboardPanDelta),this._setZoomDelta(t.options.zoomDelta)},addHooks:function(){var t=this._map._container;t.tabIndex<=0&&(t.tabIndex="0"),pt(t,{focus:this._onFocus,blur:this._onBlur,mousedown:this._onMouseDown},this),this._map.on({focus:this._addHooks,blur:this._removeHooks},this)},removeHooks:function(){this._removeHooks(),Et(this._map._container,{focus:this._onFocus,blur:this._onBlur,mousedown:this._onMouseDown},this),this._map.off({focus:this._addHooks,blur:this._removeHooks},this)},_onMouseDown:function(){if(!this._focused){var t=document.body,n=document.documentElement,o=t.scrollTop||n.scrollTop,u=t.scrollLeft||n.scrollLeft;this._map._container.focus(),window.scrollTo(u,o)}},_onFocus:function(){this._focused=!0,this._map.fire("focus")},_onBlur:function(){this._focused=!1,this._map.fire("blur")},_setPanDelta:function(t){var n=this._panKeys={},o=this.keyCodes,u,c;for(u=0,c=o.left.length;u<c;u++)n[o.left[u]]=[-1*t,0];for(u=0,c=o.right.length;u<c;u++)n[o.right[u]]=[t,0];for(u=0,c=o.down.length;u<c;u++)n[o.down[u]]=[0,t];for(u=0,c=o.up.length;u<c;u++)n[o.up[u]]=[0,-1*t]},_setZoomDelta:function(t){var n=this._zoomKeys={},o=this.keyCodes,u,c;for(u=0,c=o.zoomIn.length;u<c;u++)n[o.zoomIn[u]]=t;for(u=0,c=o.zoomOut.length;u<c;u++)n[o.zoomOut[u]]=-t},_addHooks:function(){pt(document,"keydown",this._onKeyDown,this)},_removeHooks:function(){Et(document,"keydown",this._onKeyDown,this)},_onKeyDown:function(t){if(!(t.altKey||t.ctrlKey||t.metaKey)){var n=t.keyCode,o=this._map,u;if(n in this._panKeys){if(!o._panAnim||!o._panAnim._inProgress)if(u=this._panKeys[n],t.shiftKey&&(u=ht(u).multiplyBy(3)),o.options.maxBounds&&(u=o._limitOffset(ht(u),o.options.maxBounds)),o.options.worldCopyJump){var c=o.wrapLatLng(o.unproject(o.project(o.getCenter()).add(u)));o.panTo(c)}else o.panBy(u)}else if(n in this._zoomKeys)o.setZoom(o.getZoom()+(t.shiftKey?3:1)*this._zoomKeys[n]);else if(n===27&&o._popup&&o._popup.options.closeOnEscapeKey)o.closePopup();else return;He(t)}}});mt.addInitHook("addHandler","keyboard",ct),mt.mergeOptions({scrollWheelZoom:!0,wheelDebounceTime:40,wheelPxPerZoomLevel:60});var Xi=jt.extend({addHooks:function(){pt(this._map._container,"wheel",this._onWheelScroll,this),this._delta=0},removeHooks:function(){Et(this._map._container,"wheel",this._onWheelScroll,this)},_onWheelScroll:function(t){var n=Nr(t),o=this._map.options.wheelDebounceTime;this._delta+=n,this._lastMousePos=this._map.mouseEventToContainerPoint(t),this._startTime||(this._startTime=+new Date);var u=Math.max(o-(+new Date-this._startTime),0);clearTimeout(this._timer),this._timer=setTimeout(V(this._performZoom,this),u),He(t)},_performZoom:function(){var t=this._map,n=t.getZoom(),o=this._map.options.zoomSnap||0;t._stop();var u=this._delta/(this._map.options.wheelPxPerZoomLevel*4),c=4*Math.log(2/(1+Math.exp(-Math.abs(u))))/Math.LN2,g=o?Math.ceil(c/o)*o:c,B=t._limitZoom(n+(this._delta>0?g:-g))-n;this._delta=0,this._startTime=null,B&&(t.options.scrollWheelZoom==="center"?t.setZoom(n+B):t.setZoomAround(this._lastMousePos,n+B))}});mt.addInitHook("addHandler","scrollWheelZoom",Xi);var $n=600;mt.mergeOptions({tapHold:at.touchNative&&at.safari&&at.mobile,tapTolerance:15});var Se=jt.extend({addHooks:function(){pt(this._map._container,"touchstart",this._onDown,this)},removeHooks:function(){Et(this._map._container,"touchstart",this._onDown,this)},_onDown:function(t){if(clearTimeout(this._holdTimeout),t.touches.length===1){var n=t.touches[0];this._startPos=this._newPos=new dt(n.clientX,n.clientY),this._holdTimeout=setTimeout(V(function(){this._cancel(),this._isTapValid()&&(pt(document,"touchend",Zt),pt(document,"touchend touchcancel",this._cancelClickPrevent),this._simulateEvent("contextmenu",n))},this),$n),pt(document,"touchend touchcancel contextmenu",this._cancel,this),pt(document,"touchmove",this._onMove,this)}},_cancelClickPrevent:function t(){Et(document,"touchend",Zt),Et(document,"touchend touchcancel",t)},_cancel:function(){clearTimeout(this._holdTimeout),Et(document,"touchend touchcancel contextmenu",this._cancel,this),Et(document,"touchmove",this._onMove,this)},_onMove:function(t){var n=t.touches[0];this._newPos=new dt(n.clientX,n.clientY)},_isTapValid:function(){return this._newPos.distanceTo(this._startPos)<=this._map.options.tapTolerance},_simulateEvent:function(t,n){var o=new MouseEvent(t,{bubbles:!0,cancelable:!0,view:window,screenX:n.screenX,screenY:n.screenY,clientX:n.clientX,clientY:n.clientY});o._simulated=!0,n.target.dispatchEvent(o)}});mt.addInitHook("addHandler","tapHold",Se),mt.mergeOptions({touchZoom:at.touch,bounceAtZoomLimits:!0});var zt=jt.extend({addHooks:function(){ft(this._map._container,"leaflet-touch-zoom"),pt(this._map._container,"touchstart",this._onTouchStart,this)},removeHooks:function(){At(this._map._container,"leaflet-touch-zoom"),Et(this._map._container,"touchstart",this._onTouchStart,this)},_onTouchStart:function(t){var n=this._map;if(!(!t.touches||t.touches.length!==2||n._animatingZoom||this._zooming)){var o=n.mouseEventToContainerPoint(t.touches[0]),u=n.mouseEventToContainerPoint(t.touches[1]);this._centerPoint=n.getSize()._divideBy(2),this._startLatLng=n.containerPointToLatLng(this._centerPoint),n.options.touchZoom!=="center"&&(this._pinchStartLatLng=n.containerPointToLatLng(o.add(u)._divideBy(2))),this._startDist=o.distanceTo(u),this._startZoom=n.getZoom(),this._moved=!1,this._zooming=!0,n._stop(),pt(document,"touchmove",this._onTouchMove,this),pt(document,"touchend touchcancel",this._onTouchEnd,this),Zt(t)}},_onTouchMove:function(t){if(!(!t.touches||t.touches.length!==2||!this._zooming)){var n=this._map,o=n.mouseEventToContainerPoint(t.touches[0]),u=n.mouseEventToContainerPoint(t.touches[1]),c=o.distanceTo(u)/this._startDist;if(this._zoom=n.getScaleZoom(c,this._startZoom),!n.options.bounceAtZoomLimits&&(this._zoom<n.getMinZoom()&&c<1||this._zoom>n.getMaxZoom()&&c>1)&&(this._zoom=n._limitZoom(this._zoom)),n.options.touchZoom==="center"){if(this._center=this._startLatLng,c===1)return}else{var g=o._add(u)._divideBy(2)._subtract(this._centerPoint);if(c===1&&g.x===0&&g.y===0)return;this._center=n.unproject(n.project(this._pinchStartLatLng,this._zoom).subtract(g),this._zoom)}this._moved||(n._moveStart(!0,!1),this._moved=!0),Vt(this._animRequest);var B=V(n._move,n,this._center,this._zoom,{pinch:!0,round:!1},void 0);this._animRequest=Jt(B,this,!0),Zt(t)}},_onTouchEnd:function(){if(!this._moved||!this._zooming){this._zooming=!1;return}this._zooming=!1,Vt(this._animRequest),Et(document,"touchmove",this._onTouchMove,this),Et(document,"touchend touchcancel",this._onTouchEnd,this),this._map.options.zoomAnimation?this._map._animateZoom(this._center,this._map._limitZoom(this._zoom),!0,this._map.options.zoomSnap):this._map._resetView(this._center,this._map._limitZoom(this._zoom))}});mt.addInitHook("addHandler","touchZoom",zt),mt.BoxZoom=Wt,mt.DoubleClickZoom=Yn,mt.Drag=he,mt.Keyboard=ct,mt.ScrollWheelZoom=Xi,mt.TapHold=Se,mt.TouchZoom=zt,_.Bounds=Tt,_.Browser=at,_.CRS=Me,_.Canvas=Yi,_.Circle=Hi,_.CircleMarker=Vi,_.Class=pe,_.Control=fe,_.DivIcon=Xr,_.DivOverlay=me,_.DomEvent=Ua,_.DomUtil=Za,_.Draggable=Re,_.Evented=di,_.FeatureGroup=re,_.GeoJSON=Pe,_.GridLayer=ki,_.Handler=jt,_.Icon=ii,_.ImageOverlay=Ci,_.LatLng=Ct,_.LatLngBounds=Yt,_.Layer=$t,_.LayerGroup=qe,_.LineUtil=Zn,_.Map=mt,_.Marker=Ui,_.Mixin=Zr,_.Path=xe,_.Point=dt,_.PolyUtil=Wa,_.Polygon=ue,_.Polyline=Ce,_.Popup=Wi,_.PosAnimation=Gr,_.Projection=ji,_.Rectangle=$i,_.Renderer=we,_.SVG=Ei,_.SVGOverlay=$r,_.TileLayer=oi,_.Tooltip=Ji,_.Transformation=Je,_.Util=Ne,_.VideoOverlay=Wn,_.bind=V,_.bounds=Gt,_.canvas=ea,_.circle=We,_.circleMarker=Qa,_.control=Xe,_.divIcon=ro,_.extend=z,_.featureGroup=$a,_.geoJSON=Yr,_.geoJson=io,_.gridLayer=ao,_.icon=Un,_.imageOverlay=qn,_.latLng=gt,_.latLngBounds=Ft,_.layerGroup=jn,_.map=Va,_.marker=Xa,_.point=ht,_.polygon=eo,_.polyline=to,_.popup=no,_.rectangle=le,_.setOptions=kt,_.stamp=M,_.svg=na,_.svgOverlay=qi,_.tileLayer=Qr,_.tooltip=wi,_.transformation=pi,_.version=v,_.videoOverlay=Jn;var ra=window.L;_.noConflict=function(){return window.L=ra,this},window.L=_})});var Lt=Fs(Do(),1);L.Control.Fullscreen=L.Control.extend({options:{position:"topleft",title:{false:"View Fullscreen",true:"Exit Fullscreen"}},onAdd:function(_){var v=L.DomUtil.create("div","leaflet-control-fullscreen leaflet-bar leaflet-control");return this.link=L.DomUtil.create("a","leaflet-control-fullscreen-button leaflet-bar-part",v),this.link.href="#",this._map=_,this._map.on("fullscreenchange",this._toggleTitle,this),this._toggleTitle(),L.DomEvent.on(this.link,"click",this._click,this),v},_click:function(_){L.DomEvent.stopPropagation(_),L.DomEvent.preventDefault(_),this._map.toggleFullscreen(this.options)},_toggleTitle:function(){this.link.title=this.options.title[this._map.isFullscreen()]}});L.Map.include({isFullscreen:function(){return this._isFullscreen||!1},toggleFullscreen:function(_){var v=this.getContainer();this.isFullscreen()?_&&_.pseudoFullscreen?this._disablePseudoFullscreen(v):document.exitFullscreen?document.exitFullscreen():document.mozCancelFullScreen?document.mozCancelFullScreen():document.webkitCancelFullScreen?document.webkitCancelFullScreen():document.msExitFullscreen?document.msExitFullscreen():this._disablePseudoFullscreen(v):_&&_.pseudoFullscreen?this._enablePseudoFullscreen(v):v.requestFullscreen?v.requestFullscreen():v.mozRequestFullScreen?v.mozRequestFullScreen():v.webkitRequestFullscreen?v.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT):v.msRequestFullscreen?v.msRequestFullscreen():this._enablePseudoFullscreen(v)},_enablePseudoFullscreen:function(_){L.DomUtil.addClass(_,"leaflet-pseudo-fullscreen"),this._setFullscreen(!0),this.fire("fullscreenchange")},_disablePseudoFullscreen:function(_){L.DomUtil.removeClass(_,"leaflet-pseudo-fullscreen"),this._setFullscreen(!1),this.fire("fullscreenchange")},_setFullscreen:function(_){this._isFullscreen=_;var v=this.getContainer();_?L.DomUtil.addClass(v,"leaflet-fullscreen-on"):L.DomUtil.removeClass(v,"leaflet-fullscreen-on"),this.invalidateSize()},_onFullscreenChange:function(_){var v=document.fullscreenElement||document.mozFullScreenElement||document.webkitFullscreenElement||document.msFullscreenElement;v===this.getContainer()&&!this._isFullscreen?(this._setFullscreen(!0),this.fire("fullscreenchange")):v!==this.getContainer()&&this._isFullscreen&&(this._setFullscreen(!1),this.fire("fullscreenchange"))}});L.Map.mergeOptions({fullscreenControl:!1});L.Map.addInitHook(function(){this.options.fullscreenControl&&(this.fullscreenControl=new L.Control.Fullscreen(this.options.fullscreenControl),this.addControl(this.fullscreenControl));var _;if("onfullscreenchange"in document?_="fullscreenchange":"onmozfullscreenchange"in document?_="mozfullscreenchange":"onwebkitfullscreenchange"in document?_="webkitfullscreenchange":"onmsfullscreenchange"in document&&(_="MSFullscreenChange"),_){var v=L.bind(this._onFullscreenChange,this);this.whenReady(function(){L.DomEvent.on(document,_,v)}),this.on("unload",function(){L.DomEvent.off(document,_,v)})}});L.control.fullscreen=function(_){return new L.Control.Fullscreen(_)};(()=>{var Po,So;var _=Object.create,v=Object.defineProperty,z=Object.getOwnPropertyDescriptor,A=Object.getOwnPropertyNames,V=Object.getPrototypeOf,q=Object.prototype.hasOwnProperty,M=(e,i)=>()=>(i||e((i={exports:{}}).exports,i),i.exports),tt=(e,i,r,a)=>{if(i&&typeof i=="object"||typeof i=="function")for(let s of A(i))!q.call(e,s)&&s!==r&&v(e,s,{get:()=>i[s],enumerable:!(a=z(i,s))||a.enumerable});return e},$=(e,i,r)=>(r=e!=null?_(V(e)):{},tt(i||!e||!e.__esModule?v(r,"default",{value:e,enumerable:!0}):r,e)),et=M((e,i)=>{function r(){this.__data__=[],this.size=0}i.exports=r}),lt=M((e,i)=>{function r(a,s){return a===s||a!==a&&s!==s}i.exports=r}),St=M((e,i)=>{var r=lt();function a(s,l){for(var h=s.length;h--;)if(r(s[h][0],l))return h;return-1}i.exports=a}),ee=M((e,i)=>{var r=St(),a=Array.prototype,s=a.splice;function l(h){var d=this.__data__,f=r(d,h);if(f<0)return!1;var m=d.length-1;return f==m?d.pop():s.call(d,f,1),--this.size,!0}i.exports=l}),kt=M((e,i)=>{var r=St();function a(s){var l=this.__data__,h=r(l,s);return h<0?void 0:l[h][1]}i.exports=a}),or=M((e,i)=>{var r=St();function a(s){return r(this.__data__,s)>-1}i.exports=a}),ya=M((e,i)=>{var r=St();function a(s,l){var h=this.__data__,d=r(h,s);return d<0?(++this.size,h.push([s,l])):h[d][1]=l,this}i.exports=a}),hi=M((e,i)=>{var r=et(),a=ee(),s=kt(),l=or(),h=ya();function d(f){var m=-1,E=f==null?0:f.length;for(this.clear();++m<E;){var P=f[m];this.set(P[0],P[1])}}d.prototype.clear=r,d.prototype.delete=a,d.prototype.get=s,d.prototype.has=l,d.prototype.set=h,i.exports=d}),de=M((e,i)=>{var r=hi();function a(){this.__data__=new r,this.size=0}i.exports=a}),ln=M((e,i)=>{function r(a){var s=this.__data__,l=s.delete(a);return this.size=s.size,l}i.exports=r}),Pi=M((e,i)=>{function r(a){return this.__data__.get(a)}i.exports=r}),hn=M((e,i)=>{function r(a){return this.__data__.has(a)}i.exports=r}),cn=M((e,i)=>{var r=typeof global=="object"&&global&&global.Object===Object&&global;i.exports=r}),ze=M((e,i)=>{var r=cn(),a=typeof self=="object"&&self&&self.Object===Object&&self,s=r||a||Function("return this")();i.exports=s}),ci=M((e,i)=>{var r=ze(),a=r.Symbol;i.exports=a}),sr=M((e,i)=>{var r=ci(),a=Object.prototype,s=a.hasOwnProperty,l=a.toString,h=r?r.toStringTag:void 0;function d(f){var m=s.call(f,h),E=f[h];try{f[h]=void 0;var P=!0}catch{}var j=l.call(f);return P&&(m?f[h]=E:delete f[h]),j}i.exports=d}),Jt=M((e,i)=>{var r=Object.prototype,a=r.toString;function s(l){return a.call(l)}i.exports=s}),Vt=M((e,i)=>{var r=ci(),a=sr(),s=Jt(),l="[object Null]",h="[object Undefined]",d=r?r.toStringTag:void 0;function f(m){return m==null?m===void 0?h:l:d&&d in Object(m)?a(m):s(m)}i.exports=f}),Ne=M((e,i)=>{function r(a){var s=typeof a;return a!=null&&(s=="object"||s=="function")}i.exports=r}),pe=M((e,i)=>{var r=Vt(),a=Ne(),s="[object AsyncFunction]",l="[object Function]",h="[object GeneratorFunction]",d="[object Proxy]";function f(m){if(!a(m))return!1;var E=r(m);return E==l||E==h||E==s||E==d}i.exports=f}),va=M((e,i)=>{var r=ze(),a=r["__core-js_shared__"];i.exports=a}),ie=M((e,i)=>{var r=va(),a=function(){var l=/[^.]+$/.exec(r&&r.keys&&r.keys.IE_PROTO||"");return l?"Symbol(src)_1."+l:""}();function s(l){return!!a&&a in l}i.exports=s}),di=M((e,i)=>{var r=Function.prototype,a=r.toString;function s(l){if(l!=null){try{return a.call(l)}catch{}try{return l+""}catch{}}return""}i.exports=s}),dt=M((e,i)=>{var r=pe(),a=ie(),s=Ne(),l=di(),h=/[\\^$.*+?()[\]{}|]/g,d=/^\[object .+?Constructor\]$/,f=Function.prototype,m=Object.prototype,E=f.toString,P=m.hasOwnProperty,j=RegExp("^"+E.call(P).replace(h,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function R(W){if(!s(W)||a(W))return!1;var X=r(W)?j:d;return X.test(l(W))}i.exports=R}),ur=M((e,i)=>{function r(a,s){return a?.[s]}i.exports=r}),ht=M((e,i)=>{var r=dt(),a=ur();function s(l,h){var d=a(l,h);return r(d)?d:void 0}i.exports=s}),Tt=M((e,i)=>{var r=ht(),a=ze(),s=r(a,"Map");i.exports=s}),Gt=M((e,i)=>{var r=ht(),a=r(Object,"create");i.exports=a}),Yt=M((e,i)=>{var r=Gt();function a(){this.__data__=r?r(null):{},this.size=0}i.exports=a}),Ft=M((e,i)=>{function r(a){var s=this.has(a)&&delete this.__data__[a];return this.size-=s?1:0,s}i.exports=r}),Ct=M((e,i)=>{var r=Gt(),a="__lodash_hash_undefined__",s=Object.prototype,l=s.hasOwnProperty;function h(d){var f=this.__data__;if(r){var m=f[d];return m===a?void 0:m}return l.call(f,d)?f[d]:void 0}i.exports=h}),gt=M((e,i)=>{var r=Gt(),a=Object.prototype,s=a.hasOwnProperty;function l(h){var d=this.__data__;return r?d[h]!==void 0:s.call(d,h)}i.exports=l}),Me=M((e,i)=>{var r=Gt(),a="__lodash_hash_undefined__";function s(l,h){var d=this.__data__;return this.size+=this.has(l)?0:1,d[l]=r&&h===void 0?a:h,this}i.exports=s}),Ae=M((e,i)=>{var r=Yt(),a=Ft(),s=Ct(),l=gt(),h=Me();function d(f){var m=-1,E=f==null?0:f.length;for(this.clear();++m<E;){var P=f[m];this.set(P[0],P[1])}}d.prototype.clear=r,d.prototype.delete=a,d.prototype.get=s,d.prototype.has=l,d.prototype.set=h,i.exports=d}),lr=M((e,i)=>{var r=Ae(),a=hi(),s=Tt();function l(){this.size=0,this.__data__={hash:new r,map:new(s||a),string:new r}}i.exports=l}),dn=M((e,i)=>{function r(a){var s=typeof a;return s=="string"||s=="number"||s=="symbol"||s=="boolean"?a!=="__proto__":a===null}i.exports=r}),Je=M((e,i)=>{var r=dn();function a(s,l){var h=s.__data__;return r(l)?h[typeof l=="string"?"string":"hash"]:h.map}i.exports=a}),pi=M((e,i)=>{var r=Je();function a(s){var l=r(this,s).delete(s);return this.size-=l?1:0,l}i.exports=a}),pn=M((e,i)=>{var r=Je();function a(s){return r(this,s).get(s)}i.exports=a}),La=M((e,i)=>{var r=Je();function a(s){return r(this,s).has(s)}i.exports=a}),hr=M((e,i)=>{var r=Je();function a(s,l){var h=r(this,s),d=h.size;return h.set(s,l),this.size+=h.size==d?0:1,this}i.exports=a}),fn=M((e,i)=>{var r=lr(),a=pi(),s=pn(),l=La(),h=hr();function d(f){var m=-1,E=f==null?0:f.length;for(this.clear();++m<E;){var P=f[m];this.set(P[0],P[1])}}d.prototype.clear=r,d.prototype.delete=a,d.prototype.get=s,d.prototype.has=l,d.prototype.set=h,i.exports=d}),_n=M((e,i)=>{var r=hi(),a=Tt(),s=fn(),l=200;function h(d,f){var m=this.__data__;if(m instanceof r){var E=m.__data__;if(!a||E.length<l-1)return E.push([d,f]),this.size=++m.size,this;m=this.__data__=new s(E)}return m.set(d,f),this.size=m.size,this}i.exports=h}),Si=M((e,i)=>{var r=hi(),a=de(),s=ln(),l=Pi(),h=hn(),d=_n();function f(m){var E=this.__data__=new r(m);this.size=E.size}f.prototype.clear=a,f.prototype.delete=s,f.prototype.get=l,f.prototype.has=h,f.prototype.set=d,i.exports=f}),cr=M((e,i)=>{var r=ht(),a=function(){try{var s=r(Object,"defineProperty");return s({},"",{}),s}catch{}}();i.exports=a}),Ti=M((e,i)=>{var r=cr();function a(s,l,h){l=="__proto__"&&r?r(s,l,{configurable:!0,enumerable:!0,value:h,writable:!0}):s[l]=h}i.exports=a}),Di=M((e,i)=>{var r=Ti(),a=lt();function s(l,h,d){(d!==void 0&&!a(l[h],d)||d===void 0&&!(h in l))&&r(l,h,d)}i.exports=s}),dr=M((e,i)=>{function r(a){return function(s,l,h){for(var d=-1,f=Object(s),m=h(s),E=m.length;E--;){var P=m[a?E:++d];if(l(f[P],P,f)===!1)break}return s}}i.exports=r}),pr=M((e,i)=>{var r=dr(),a=r();i.exports=a}),ba=M((e,i)=>{var r=ze(),a=typeof e=="object"&&e&&!e.nodeType&&e,s=a&&typeof i=="object"&&i&&!i.nodeType&&i,l=s&&s.exports===a,h=l?r.Buffer:void 0,d=h?h.allocUnsafe:void 0;function f(m,E){if(E)return m.slice();var P=m.length,j=d?d(P):new m.constructor(P);return m.copy(j),j}i.exports=f}),xa=M((e,i)=>{var r=ze(),a=r.Uint8Array;i.exports=a}),mn=M((e,i)=>{var r=xa();function a(s){var l=new s.constructor(s.byteLength);return new r(l).set(new r(s)),l}i.exports=a}),fr=M((e,i)=>{var r=mn();function a(s,l){var h=l?r(s.buffer):s.buffer;return new s.constructor(h,s.byteOffset,s.length)}i.exports=a}),_r=M((e,i)=>{function r(a,s){var l=-1,h=a.length;for(s||(s=Array(h));++l<h;)s[l]=a[l];return s}i.exports=r}),Ca=M((e,i)=>{var r=Ne(),a=Object.create,s=function(){function l(){}return function(h){if(!r(h))return{};if(a)return a(h);l.prototype=h;var d=new l;return l.prototype=void 0,d}}();i.exports=s}),mr=M((e,i)=>{function r(a,s){return function(l){return a(s(l))}}i.exports=r}),gn=M((e,i)=>{var r=mr(),a=r(Object.getPrototypeOf,Object);i.exports=a}),gr=M((e,i)=>{var r=Object.prototype;function a(s){var l=s&&s.constructor,h=typeof l=="function"&&l.prototype||r;return s===h}i.exports=a}),yr=M((e,i)=>{var r=Ca(),a=gn(),s=gr();function l(h){return typeof h.constructor=="function"&&!s(h)?r(a(h)):{}}i.exports=l}),Fe=M((e,i)=>{function r(a){return a!=null&&typeof a=="object"}i.exports=r}),vr=M((e,i)=>{var r=Vt(),a=Fe(),s="[object Arguments]";function l(h){return a(h)&&r(h)==s}i.exports=l}),Lr=M((e,i)=>{var r=vr(),a=Fe(),s=Object.prototype,l=s.hasOwnProperty,h=s.propertyIsEnumerable,d=r(function(){return arguments}())?r:function(f){return a(f)&&l.call(f,"callee")&&!h.call(f,"callee")};i.exports=d}),Ee=M((e,i)=>{var r=Array.isArray;i.exports=r}),br=M((e,i)=>{var r=9007199254740991;function a(s){return typeof s=="number"&&s>-1&&s%1==0&&s<=r}i.exports=a}),yn=M((e,i)=>{var r=pe(),a=br();function s(l){return l!=null&&a(l.length)&&!r(l)}i.exports=s}),xr=M((e,i)=>{var r=yn(),a=Fe();function s(l){return a(l)&&r(l)}i.exports=s}),Cr=M((e,i)=>{function r(){return!1}i.exports=r}),vn=M((e,i)=>{var r=ze(),a=Cr(),s=typeof e=="object"&&e&&!e.nodeType&&e,l=s&&typeof i=="object"&&i&&!i.nodeType&&i,h=l&&l.exports===s,d=h?r.Buffer:void 0,f=d?d.isBuffer:void 0,m=f||a;i.exports=m}),wa=M((e,i)=>{var r=Vt(),a=gn(),s=Fe(),l="[object Object]",h=Function.prototype,d=Object.prototype,f=h.toString,m=d.hasOwnProperty,E=f.call(Object);function P(j){if(!s(j)||r(j)!=l)return!1;var R=a(j);if(R===null)return!0;var W=m.call(R,"constructor")&&R.constructor;return typeof W=="function"&&W instanceof W&&f.call(W)==E}i.exports=P}),ka=M((e,i)=>{var r=Vt(),a=br(),s=Fe(),l="[object Arguments]",h="[object Array]",d="[object Boolean]",f="[object Date]",m="[object Error]",E="[object Function]",P="[object Map]",j="[object Number]",R="[object Object]",W="[object RegExp]",X="[object Set]",nt="[object String]",vt="[object WeakMap]",k="[object ArrayBuffer]",S="[object DataView]",O="[object Float32Array]",K="[object Float64Array]",I="[object Int8Array]",U="[object Int16Array]",p="[object Int32Array]",y="[object Uint8Array]",x="[object Uint8ClampedArray]",b="[object Uint16Array]",C="[object Uint32Array]",w={};w[O]=w[K]=w[I]=w[U]=w[p]=w[y]=w[x]=w[b]=w[C]=!0,w[l]=w[h]=w[k]=w[d]=w[S]=w[f]=w[m]=w[E]=w[P]=w[j]=w[R]=w[W]=w[X]=w[nt]=w[vt]=!1;function D(T){return s(T)&&a(T.length)&&!!w[r(T)]}i.exports=D}),Ma=M((e,i)=>{function r(a){return function(s){return a(s)}}i.exports=r}),Ea=M((e,i)=>{var r=cn(),a=typeof e=="object"&&e&&!e.nodeType&&e,s=a&&typeof i=="object"&&i&&!i.nodeType&&i,l=s&&s.exports===a,h=l&&r.process,d=function(){try{var f=s&&s.require&&s.require("util").types;return f||h&&h.binding&&h.binding("util")}catch{}}();i.exports=d}),wr=M((e,i)=>{var r=ka(),a=Ma(),s=Ea(),l=s&&s.isTypedArray,h=l?a(l):r;i.exports=h}),kr=M((e,i)=>{function r(a,s){if(!(s==="constructor"&&typeof a[s]=="function")&&s!="__proto__")return a[s]}i.exports=r}),Ln=M((e,i)=>{var r=Ti(),a=lt(),s=Object.prototype,l=s.hasOwnProperty;function h(d,f,m){var E=d[f];(!(l.call(d,f)&&a(E,m))||m===void 0&&!(f in d))&&r(d,f,m)}i.exports=h}),Ba=M((e,i)=>{var r=Ln(),a=Ti();function s(l,h,d,f){var m=!d;d||(d={});for(var E=-1,P=h.length;++E<P;){var j=h[E],R=f?f(d[j],l[j],j,d,l):void 0;R===void 0&&(R=l[j]),m?a(d,j,R):r(d,j,R)}return d}i.exports=s}),Pa=M((e,i)=>{function r(a,s){for(var l=-1,h=Array(a);++l<a;)h[l]=s(l);return h}i.exports=r}),Mr=M((e,i)=>{var r=9007199254740991,a=/^(?:0|[1-9]\d*)$/;function s(l,h){var d=typeof l;return h=h??r,!!h&&(d=="number"||d!="symbol"&&a.test(l))&&l>-1&&l%1==0&&l<h}i.exports=s}),Sa=M((e,i)=>{var r=Pa(),a=Lr(),s=Ee(),l=vn(),h=Mr(),d=wr(),f=Object.prototype,m=f.hasOwnProperty;function E(P,j){var R=s(P),W=!R&&a(P),X=!R&&!W&&l(P),nt=!R&&!W&&!X&&d(P),vt=R||W||X||nt,k=vt?r(P.length,String):[],S=k.length;for(var O in P)(j||m.call(P,O))&&!(vt&&(O=="length"||X&&(O=="offset"||O=="parent")||nt&&(O=="buffer"||O=="byteLength"||O=="byteOffset")||h(O,S)))&&k.push(O);return k}i.exports=E}),ve=M((e,i)=>{function r(a){var s=[];if(a!=null)for(var l in Object(a))s.push(l);return s}i.exports=r}),at=M((e,i)=>{var r=Ne(),a=gr(),s=ve(),l=Object.prototype,h=l.hasOwnProperty;function d(f){if(!r(f))return s(f);var m=a(f),E=[];for(var P in f)P=="constructor"&&(m||!h.call(f,P))||E.push(P);return E}i.exports=d}),bn=M((e,i)=>{var r=Sa(),a=at(),s=yn();function l(h){return s(h)?r(h,!0):a(h)}i.exports=l}),Er=M((e,i)=>{var r=Ba(),a=bn();function s(l){return r(l,a(l))}i.exports=s}),Br=M((e,i)=>{var r=Di(),a=ba(),s=fr(),l=_r(),h=yr(),d=Lr(),f=Ee(),m=xr(),E=vn(),P=pe(),j=Ne(),R=wa(),W=wr(),X=kr(),nt=Er();function vt(k,S,O,K,I,U,p){var y=X(k,O),x=X(S,O),b=p.get(x);if(b){r(k,O,b);return}var C=U?U(y,x,O+"",k,S,p):void 0,w=C===void 0;if(w){var D=f(x),T=!D&&E(x),F=!D&&!T&&W(x);C=x,D||T||F?f(y)?C=y:m(y)?C=l(y):T?(w=!1,C=a(x,!0)):F?(w=!1,C=s(x,!0)):C=[]:R(x)||d(x)?(C=y,d(y)?C=nt(y):(!j(y)||P(y))&&(C=h(x))):w=!1}w&&(p.set(x,C),I(C,x,K,U,p),p.delete(x)),r(k,O,C)}i.exports=vt}),Pr=M((e,i)=>{var r=Si(),a=Di(),s=pr(),l=Br(),h=Ne(),d=bn(),f=kr();function m(E,P,j,R,W){E!==P&&s(P,function(X,nt){if(W||(W=new r),h(X))l(E,P,nt,j,m,R,W);else{var vt=R?R(f(E,nt),X,nt+"",E,P,W):void 0;vt===void 0&&(vt=X),a(E,nt,vt)}},d)}i.exports=m}),Ai=M((e,i)=>{function r(a){return a}i.exports=r}),Sr=M((e,i)=>{function r(a,s,l){switch(l.length){case 0:return a.call(s);case 1:return a.call(s,l[0]);case 2:return a.call(s,l[0],l[1]);case 3:return a.call(s,l[0],l[1],l[2])}return a.apply(s,l)}i.exports=r}),Ye=M((e,i)=>{var r=Sr(),a=Math.max;function s(l,h,d){return h=a(h===void 0?l.length-1:h,0),function(){for(var f=arguments,m=-1,E=a(f.length-h,0),P=Array(E);++m<E;)P[m]=f[h+m];m=-1;for(var j=Array(h+1);++m<h;)j[m]=f[m];return j[h]=d(P),r(l,this,j)}}i.exports=s}),Tr=M((e,i)=>{function r(a){return function(){return a}}i.exports=r}),Ta=M((e,i)=>{var r=Tr(),a=cr(),s=Ai(),l=a?function(h,d){return a(h,"toString",{configurable:!0,enumerable:!1,value:r(d),writable:!0})}:s;i.exports=l}),Da=M((e,i)=>{var r=800,a=16,s=Date.now;function l(h){var d=0,f=0;return function(){var m=s(),E=a-(m-f);if(f=m,E>0){if(++d>=r)return arguments[0]}else d=0;return h.apply(void 0,arguments)}}i.exports=l}),Aa=M((e,i)=>{var r=Ta(),a=Da(),s=a(r);i.exports=s}),Fa=M((e,i)=>{var r=Ai(),a=Ye(),s=Aa();function l(h,d){return s(a(h,d,r),h+"")}i.exports=l}),Dr=M((e,i)=>{var r=lt(),a=yn(),s=Mr(),l=Ne();function h(d,f,m){if(!l(m))return!1;var E=typeof f;return(E=="number"?a(m)&&s(f,m.length):E=="string"&&f in m)?r(m[f],d):!1}i.exports=h}),Oa=M((e,i)=>{var r=Fa(),a=Dr();function s(l){return r(function(h,d){var f=-1,m=d.length,E=m>1?d[m-1]:void 0,P=m>2?d[2]:void 0;for(E=l.length>3&&typeof E=="function"?(m--,E):void 0,P&&a(d[0],d[1],P)&&(E=m<3?void 0:E,m=1),h=Object(h);++f<m;){var j=d[f];j&&l(h,j,f,E)}return h})}i.exports=s}),$e=M((e,i)=>{var r=Pr(),a=Oa(),s=a(function(l,h,d){r(l,h,d)});i.exports=s}),xn=M((e,i)=>{var r=Vt(),a=Fe(),s="[object Symbol]";function l(h){return typeof h=="symbol"||a(h)&&r(h)==s}i.exports=l}),Ra=M((e,i)=>{var r=Ee(),a=xn(),s=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,l=/^\w*$/;function h(d,f){if(r(d))return!1;var m=typeof d;return m=="number"||m=="symbol"||m=="boolean"||d==null||a(d)?!0:l.test(d)||!s.test(d)||f!=null&&d in Object(f)}i.exports=h}),Ia=M((e,i)=>{var r=fn(),a="Expected a function";function s(l,h){if(typeof l!="function"||h!=null&&typeof h!="function")throw new TypeError(a);var d=function(){var f=arguments,m=h?h.apply(this,f):f[0],E=d.cache;if(E.has(m))return E.get(m);var P=l.apply(this,f);return d.cache=E.set(m,P)||E,P};return d.cache=new(s.Cache||r),d}s.Cache=r,i.exports=s}),za=M((e,i)=>{var r=Ia(),a=500;function s(l){var h=r(l,function(f){return d.size===a&&d.clear(),f}),d=h.cache;return h}i.exports=s}),Na=M((e,i)=>{var r=za(),a=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,s=/\\(\\)?/g,l=r(function(h){var d=[];return h.charCodeAt(0)===46&&d.push(""),h.replace(a,function(f,m,E,P){d.push(E?P.replace(s,"$1"):m||f)}),d});i.exports=l}),Cn=M((e,i)=>{function r(a,s){for(var l=-1,h=a==null?0:a.length,d=Array(h);++l<h;)d[l]=s(a[l],l,a);return d}i.exports=r}),fi=M((e,i)=>{var r=ci(),a=Cn(),s=Ee(),l=xn(),h=1/0,d=r?r.prototype:void 0,f=d?d.toString:void 0;function m(E){if(typeof E=="string")return E;if(s(E))return a(E,m)+"";if(l(E))return f?f.call(E):"";var P=E+"";return P=="0"&&1/E==-h?"-0":P}i.exports=m}),Ar=M((e,i)=>{var r=fi();function a(s){return s==null?"":r(s)}i.exports=a}),Fr=M((e,i)=>{var r=Ee(),a=Ra(),s=Na(),l=Ar();function h(d,f){return r(d)?d:a(d,f)?[d]:s(l(d))}i.exports=h}),_i=M((e,i)=>{var r=xn(),a=1/0;function s(l){if(typeof l=="string"||r(l))return l;var h=l+"";return h=="0"&&1/l==-a?"-0":h}i.exports=s}),bt=M((e,i)=>{var r=Fr(),a=_i();function s(l,h){h=r(h,l);for(var d=0,f=h.length;l!=null&&d<f;)l=l[a(h[d++])];return d&&d==f?l:void 0}i.exports=s}),Mt=M((e,i)=>{var r=bt();function a(s,l,h){var d=s==null?void 0:r(s,l);return d===void 0?h:d}i.exports=a}),Fi=M((e,i)=>{(function(r,a){typeof e=="object"&&typeof i<"u"?i.exports=a():typeof define=="function"&&define.amd?define(a):(r=r||self).RBush=a()})(e,function(){"use strict";function r(k,S,O,K,I){(function U(p,y,x,b,C){for(;b>x;){if(b-x>600){var w=b-x+1,D=y-x+1,T=Math.log(w),F=.5*Math.exp(2*T/3),G=.5*Math.sqrt(T*F*(w-F)/w)*(D-w/2<0?-1:1),N=Math.max(x,Math.floor(y-D*F/w+G)),J=Math.min(b,Math.floor(y+(w-D)*F/w+G));U(p,y,N,J,C)}var it=p[y],rt=x,st=b;for(a(p,x,y),C(p[b],it)>0&&a(p,x,b);rt<st;){for(a(p,rt,st),rt++,st--;C(p[rt],it)<0;)rt++;for(;C(p[st],it)>0;)st--}C(p[x],it)===0?a(p,x,st):a(p,++st,b),st<=y&&(x=st+1),y<=st&&(b=st-1)}})(k,S,O||0,K||k.length-1,I||s)}function a(k,S,O){var K=k[S];k[S]=k[O],k[O]=K}function s(k,S){return k<S?-1:k>S?1:0}var l=function(k){k===void 0&&(k=9),this._maxEntries=Math.max(4,k),this._minEntries=Math.max(2,Math.ceil(.4*this._maxEntries)),this.clear()};function h(k,S,O){if(!O)return S.indexOf(k);for(var K=0;K<S.length;K++)if(O(k,S[K]))return K;return-1}function d(k,S){f(k,0,k.children.length,S,k)}function f(k,S,O,K,I){I||(I=nt(null)),I.minX=1/0,I.minY=1/0,I.maxX=-1/0,I.maxY=-1/0;for(var U=S;U<O;U++){var p=k.children[U];m(I,k.leaf?K(p):p)}return I}function m(k,S){return k.minX=Math.min(k.minX,S.minX),k.minY=Math.min(k.minY,S.minY),k.maxX=Math.max(k.maxX,S.maxX),k.maxY=Math.max(k.maxY,S.maxY),k}function E(k,S){return k.minX-S.minX}function P(k,S){return k.minY-S.minY}function j(k){return(k.maxX-k.minX)*(k.maxY-k.minY)}function R(k){return k.maxX-k.minX+(k.maxY-k.minY)}function W(k,S){return k.minX<=S.minX&&k.minY<=S.minY&&S.maxX<=k.maxX&&S.maxY<=k.maxY}function X(k,S){return S.minX<=k.maxX&&S.minY<=k.maxY&&S.maxX>=k.minX&&S.maxY>=k.minY}function nt(k){return{children:k,height:1,leaf:!0,minX:1/0,minY:1/0,maxX:-1/0,maxY:-1/0}}function vt(k,S,O,K,I){for(var U=[S,O];U.length;)if(!((O=U.pop())-(S=U.pop())<=K)){var p=S+Math.ceil((O-S)/K/2)*K;r(k,p,S,O,I),U.push(S,p,p,O)}}return l.prototype.all=function(){return this._all(this.data,[])},l.prototype.search=function(k){var S=this.data,O=[];if(!X(k,S))return O;for(var K=this.toBBox,I=[];S;){for(var U=0;U<S.children.length;U++){var p=S.children[U],y=S.leaf?K(p):p;X(k,y)&&(S.leaf?O.push(p):W(k,y)?this._all(p,O):I.push(p))}S=I.pop()}return O},l.prototype.collides=function(k){var S=this.data;if(!X(k,S))return!1;for(var O=[];S;){for(var K=0;K<S.children.length;K++){var I=S.children[K],U=S.leaf?this.toBBox(I):I;if(X(k,U)){if(S.leaf||W(k,U))return!0;O.push(I)}}S=O.pop()}return!1},l.prototype.load=function(k){if(!k||!k.length)return this;if(k.length<this._minEntries){for(var S=0;S<k.length;S++)this.insert(k[S]);return this}var O=this._build(k.slice(),0,k.length-1,0);if(this.data.children.length)if(this.data.height===O.height)this._splitRoot(this.data,O);else{if(this.data.height<O.height){var K=this.data;this.data=O,O=K}this._insert(O,this.data.height-O.height-1,!0)}else this.data=O;return this},l.prototype.insert=function(k){return k&&this._insert(k,this.data.height-1),this},l.prototype.clear=function(){return this.data=nt([]),this},l.prototype.remove=function(k,S){if(!k)return this;for(var O,K,I,U=this.data,p=this.toBBox(k),y=[],x=[];U||y.length;){if(U||(U=y.pop(),K=y[y.length-1],O=x.pop(),I=!0),U.leaf){var b=h(k,U.children,S);if(b!==-1)return U.children.splice(b,1),y.push(U),this._condense(y),this}I||U.leaf||!W(U,p)?K?(O++,U=K.children[O],I=!1):U=null:(y.push(U),x.push(O),O=0,K=U,U=U.children[0])}return this},l.prototype.toBBox=function(k){return k},l.prototype.compareMinX=function(k,S){return k.minX-S.minX},l.prototype.compareMinY=function(k,S){return k.minY-S.minY},l.prototype.toJSON=function(){return this.data},l.prototype.fromJSON=function(k){return this.data=k,this},l.prototype._all=function(k,S){for(var O=[];k;)k.leaf?S.push.apply(S,k.children):O.push.apply(O,k.children),k=O.pop();return S},l.prototype._build=function(k,S,O,K){var I,U=O-S+1,p=this._maxEntries;if(U<=p)return d(I=nt(k.slice(S,O+1)),this.toBBox),I;K||(K=Math.ceil(Math.log(U)/Math.log(p)),p=Math.ceil(U/Math.pow(p,K-1))),(I=nt([])).leaf=!1,I.height=K;var y=Math.ceil(U/p),x=y*Math.ceil(Math.sqrt(p));vt(k,S,O,x,this.compareMinX);for(var b=S;b<=O;b+=x){var C=Math.min(b+x-1,O);vt(k,b,C,y,this.compareMinY);for(var w=b;w<=C;w+=y){var D=Math.min(w+y-1,C);I.children.push(this._build(k,w,D,K-1))}}return d(I,this.toBBox),I},l.prototype._chooseSubtree=function(k,S,O,K){for(;K.push(S),!S.leaf&&K.length-1!==O;){for(var I=1/0,U=1/0,p=void 0,y=0;y<S.children.length;y++){var x=S.children[y],b=j(x),C=(w=k,D=x,(Math.max(D.maxX,w.maxX)-Math.min(D.minX,w.minX))*(Math.max(D.maxY,w.maxY)-Math.min(D.minY,w.minY))-b);C<U?(U=C,I=b<I?b:I,p=x):C===U&&b<I&&(I=b,p=x)}S=p||S.children[0]}var w,D;return S},l.prototype._insert=function(k,S,O){var K=O?k:this.toBBox(k),I=[],U=this._chooseSubtree(K,this.data,S,I);for(U.children.push(k),m(U,K);S>=0&&I[S].children.length>this._maxEntries;)this._split(I,S),S--;this._adjustParentBBoxes(K,I,S)},l.prototype._split=function(k,S){var O=k[S],K=O.children.length,I=this._minEntries;this._chooseSplitAxis(O,I,K);var U=this._chooseSplitIndex(O,I,K),p=nt(O.children.splice(U,O.children.length-U));p.height=O.height,p.leaf=O.leaf,d(O,this.toBBox),d(p,this.toBBox),S?k[S-1].children.push(p):this._splitRoot(O,p)},l.prototype._splitRoot=function(k,S){this.data=nt([k,S]),this.data.height=k.height+1,this.data.leaf=!1,d(this.data,this.toBBox)},l.prototype._chooseSplitIndex=function(k,S,O){for(var K,I,U,p,y,x,b,C=1/0,w=1/0,D=S;D<=O-S;D++){var T=f(k,0,D,this.toBBox),F=f(k,D,O,this.toBBox),G=(I=T,U=F,p=void 0,y=void 0,x=void 0,b=void 0,p=Math.max(I.minX,U.minX),y=Math.max(I.minY,U.minY),x=Math.min(I.maxX,U.maxX),b=Math.min(I.maxY,U.maxY),Math.max(0,x-p)*Math.max(0,b-y)),N=j(T)+j(F);G<C?(C=G,K=D,w=N<w?N:w):G===C&&N<w&&(w=N,K=D)}return K||O-S},l.prototype._chooseSplitAxis=function(k,S,O){var K=k.leaf?this.compareMinX:E,I=k.leaf?this.compareMinY:P;this._allDistMargin(k,S,O,K)<this._allDistMargin(k,S,O,I)&&k.children.sort(K)},l.prototype._allDistMargin=function(k,S,O,K){k.children.sort(K);for(var I=this.toBBox,U=f(k,0,S,I),p=f(k,O-S,O,I),y=R(U)+R(p),x=S;x<O-S;x++){var b=k.children[x];m(U,k.leaf?I(b):b),y+=R(U)}for(var C=O-S-1;C>=S;C--){var w=k.children[C];m(p,k.leaf?I(w):w),y+=R(p)}return y},l.prototype._adjustParentBBoxes=function(k,S,O){for(var K=O;K>=0;K--)m(S[K],k)},l.prototype._condense=function(k){for(var S=k.length-1,O=void 0;S>=0;S--)k[S].children.length===0?S>0?(O=k[S-1].children).splice(O.indexOf(k[S]),1):this.clear():d(k[S],this.toBBox)},l})}),Ge=M(e=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.earthRadius=63710088e-1,e.factors={centimeters:e.earthRadius*100,centimetres:e.earthRadius*100,degrees:e.earthRadius/111325,feet:e.earthRadius*3.28084,inches:e.earthRadius*39.37,kilometers:e.earthRadius/1e3,kilometres:e.earthRadius/1e3,meters:e.earthRadius,metres:e.earthRadius,miles:e.earthRadius/1609.344,millimeters:e.earthRadius*1e3,millimetres:e.earthRadius*1e3,nauticalmiles:e.earthRadius/1852,radians:1,yards:e.earthRadius*1.0936},e.unitsFactors={centimeters:100,centimetres:100,degrees:1/111325,feet:3.28084,inches:39.37,kilometers:1/1e3,kilometres:1/1e3,meters:1,metres:1,miles:1/1609.344,millimeters:1e3,millimetres:1e3,nauticalmiles:1/1852,radians:1/e.earthRadius,yards:1.0936133},e.areaFactors={acres:247105e-9,centimeters:1e4,centimetres:1e4,feet:10.763910417,hectares:1e-4,inches:1550.003100006,kilometers:1e-6,kilometres:1e-6,meters:1,metres:1,miles:386e-9,millimeters:1e6,millimetres:1e6,yards:1.195990046};function i(b,C,w){w===void 0&&(w={});var D={type:"Feature"};return(w.id===0||w.id)&&(D.id=w.id),w.bbox&&(D.bbox=w.bbox),D.properties=C||{},D.geometry=b,D}e.feature=i;function r(b,C,w){switch(w===void 0&&(w={}),b){case"Point":return a(C).geometry;case"LineString":return d(C).geometry;case"Polygon":return l(C).geometry;case"MultiPoint":return P(C).geometry;case"MultiLineString":return E(C).geometry;case"MultiPolygon":return j(C).geometry;default:throw new Error(b+" is invalid")}}e.geometry=r;function a(b,C,w){if(w===void 0&&(w={}),!b)throw new Error("coordinates is required");if(!Array.isArray(b))throw new Error("coordinates must be an Array");if(b.length<2)throw new Error("coordinates must be at least 2 numbers long");if(!U(b[0])||!U(b[1]))throw new Error("coordinates must contain numbers");var D={type:"Point",coordinates:b};return i(D,C,w)}e.point=a;function s(b,C,w){return w===void 0&&(w={}),m(b.map(function(D){return a(D,C)}),w)}e.points=s;function l(b,C,w){w===void 0&&(w={});for(var D=0,T=b;D<T.length;D++){var F=T[D];if(F.length<4)throw new Error("Each LinearRing of a Polygon must have 4 or more Positions.");for(var G=0;G<F[F.length-1].length;G++)if(F[F.length-1][G]!==F[0][G])throw new Error("First and last Position are not equivalent.")}var N={type:"Polygon",coordinates:b};return i(N,C,w)}e.polygon=l;function h(b,C,w){return w===void 0&&(w={}),m(b.map(function(D){return l(D,C)}),w)}e.polygons=h;function d(b,C,w){if(w===void 0&&(w={}),b.length<2)throw new Error("coordinates must be an array of two or more positions");var D={type:"LineString",coordinates:b};return i(D,C,w)}e.lineString=d;function f(b,C,w){return w===void 0&&(w={}),m(b.map(function(D){return d(D,C)}),w)}e.lineStrings=f;function m(b,C){C===void 0&&(C={});var w={type:"FeatureCollection"};return C.id&&(w.id=C.id),C.bbox&&(w.bbox=C.bbox),w.features=b,w}e.featureCollection=m;function E(b,C,w){w===void 0&&(w={});var D={type:"MultiLineString",coordinates:b};return i(D,C,w)}e.multiLineString=E;function P(b,C,w){w===void 0&&(w={});var D={type:"MultiPoint",coordinates:b};return i(D,C,w)}e.multiPoint=P;function j(b,C,w){w===void 0&&(w={});var D={type:"MultiPolygon",coordinates:b};return i(D,C,w)}e.multiPolygon=j;function R(b,C,w){w===void 0&&(w={});var D={type:"GeometryCollection",geometries:b};return i(D,C,w)}e.geometryCollection=R;function W(b,C){if(C===void 0&&(C=0),C&&!(C>=0))throw new Error("precision must be a positive number");var w=Math.pow(10,C||0);return Math.round(b*w)/w}e.round=W;function X(b,C){C===void 0&&(C="kilometers");var w=e.factors[C];if(!w)throw new Error(C+" units is invalid");return b*w}e.radiansToLength=X;function nt(b,C){C===void 0&&(C="kilometers");var w=e.factors[C];if(!w)throw new Error(C+" units is invalid");return b/w}e.lengthToRadians=nt;function vt(b,C){return S(nt(b,C))}e.lengthToDegrees=vt;function k(b){var C=b%360;return C<0&&(C+=360),C}e.bearingToAzimuth=k;function S(b){var C=b%(2*Math.PI);return C*180/Math.PI}e.radiansToDegrees=S;function O(b){var C=b%360;return C*Math.PI/180}e.degreesToRadians=O;function K(b,C,w){if(C===void 0&&(C="kilometers"),w===void 0&&(w="kilometers"),!(b>=0))throw new Error("length must be a positive number");return X(nt(b,C),w)}e.convertLength=K;function I(b,C,w){if(C===void 0&&(C="meters"),w===void 0&&(w="kilometers"),!(b>=0))throw new Error("area must be a positive number");var D=e.areaFactors[C];if(!D)throw new Error("invalid original units");var T=e.areaFactors[w];if(!T)throw new Error("invalid final units");return b/D*T}e.convertArea=I;function U(b){return!isNaN(b)&&b!==null&&!Array.isArray(b)}e.isNumber=U;function p(b){return!!b&&b.constructor===Object}e.isObject=p;function y(b){if(!b)throw new Error("bbox is required");if(!Array.isArray(b))throw new Error("bbox must be an Array");if(b.length!==4&&b.length!==6)throw new Error("bbox must be an Array of 4 or 6 numbers");b.forEach(function(C){if(!U(C))throw new Error("bbox must only contain numbers")})}e.validateBBox=y;function x(b){if(!b)throw new Error("id is required");if(["string","number"].indexOf(typeof b)===-1)throw new Error("id must be a number or a string")}e.validateId=x}),Ze=M(e=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=Ge();function r(S,O,K){if(S!==null)for(var I,U,p,y,x,b,C,w=0,D=0,T,F=S.type,G=F==="FeatureCollection",N=F==="Feature",J=G?S.features.length:1,it=0;it<J;it++){C=G?S.features[it].geometry:N?S.geometry:S,T=C?C.type==="GeometryCollection":!1,x=T?C.geometries.length:1;for(var rt=0;rt<x;rt++){var st=0,wt=0;if(y=T?C.geometries[rt]:C,y!==null){b=y.coordinates;var Bt=y.type;switch(w=K&&(Bt==="Polygon"||Bt==="MultiPolygon")?1:0,Bt){case null:break;case"Point":if(O(b,D,it,st,wt)===!1)return!1;D++,st++;break;case"LineString":case"MultiPoint":for(I=0;I<b.length;I++){if(O(b[I],D,it,st,wt)===!1)return!1;D++,Bt==="MultiPoint"&&st++}Bt==="LineString"&&st++;break;case"Polygon":case"MultiLineString":for(I=0;I<b.length;I++){for(U=0;U<b[I].length-w;U++){if(O(b[I][U],D,it,st,wt)===!1)return!1;D++}Bt==="MultiLineString"&&st++,Bt==="Polygon"&&wt++}Bt==="Polygon"&&st++;break;case"MultiPolygon":for(I=0;I<b.length;I++){for(wt=0,U=0;U<b[I].length;U++){for(p=0;p<b[I][U].length-w;p++){if(O(b[I][U][p],D,it,st,wt)===!1)return!1;D++}wt++}st++}break;case"GeometryCollection":for(I=0;I<y.geometries.length;I++)if(r(y.geometries[I],O,K)===!1)return!1;break;default:throw new Error("Unknown Geometry Type")}}}}}function a(S,O,K,I){var U=K;return r(S,function(p,y,x,b,C){y===0&&K===void 0?U=p:U=O(U,p,y,x,b,C)},I),U}function s(S,O){var K;switch(S.type){case"FeatureCollection":for(K=0;K<S.features.length&&O(S.features[K].properties,K)!==!1;K++);break;case"Feature":O(S.properties,0);break}}function l(S,O,K){var I=K;return s(S,function(U,p){p===0&&K===void 0?I=U:I=O(I,U,p)}),I}function h(S,O){if(S.type==="Feature")O(S,0);else if(S.type==="FeatureCollection")for(var K=0;K<S.features.length&&O(S.features[K],K)!==!1;K++);}function d(S,O,K){var I=K;return h(S,function(U,p){p===0&&K===void 0?I=U:I=O(I,U,p)}),I}function f(S){var O=[];return r(S,function(K){O.push(K)}),O}function m(S,O){var K,I,U,p,y,x,b,C,w,D,T=0,F=S.type==="FeatureCollection",G=S.type==="Feature",N=F?S.features.length:1;for(K=0;K<N;K++){for(x=F?S.features[K].geometry:G?S.geometry:S,C=F?S.features[K].properties:G?S.properties:{},w=F?S.features[K].bbox:G?S.bbox:void 0,D=F?S.features[K].id:G?S.id:void 0,b=x?x.type==="GeometryCollection":!1,y=b?x.geometries.length:1,U=0;U<y;U++){if(p=b?x.geometries[U]:x,p===null){if(O(null,T,C,w,D)===!1)return!1;continue}switch(p.type){case"Point":case"LineString":case"MultiPoint":case"Polygon":case"MultiLineString":case"MultiPolygon":{if(O(p,T,C,w,D)===!1)return!1;break}case"GeometryCollection":{for(I=0;I<p.geometries.length;I++)if(O(p.geometries[I],T,C,w,D)===!1)return!1;break}default:throw new Error("Unknown Geometry Type")}}T++}}function E(S,O,K){var I=K;return m(S,function(U,p,y,x,b){p===0&&K===void 0?I=U:I=O(I,U,p,y,x,b)}),I}function P(S,O){m(S,function(K,I,U,p,y){var x=K===null?null:K.type;switch(x){case null:case"Point":case"LineString":case"Polygon":return O(i.feature(K,U,{bbox:p,id:y}),I,0)===!1?!1:void 0}var b;switch(x){case"MultiPoint":b="Point";break;case"MultiLineString":b="LineString";break;case"MultiPolygon":b="Polygon";break}for(var C=0;C<K.coordinates.length;C++){var w=K.coordinates[C],D={type:b,coordinates:w};if(O(i.feature(D,U),I,C)===!1)return!1}})}function j(S,O,K){var I=K;return P(S,function(U,p,y){p===0&&y===0&&K===void 0?I=U:I=O(I,U,p,y)}),I}function R(S,O){P(S,function(K,I,U){var p=0;if(K.geometry){var y=K.geometry.type;if(!(y==="Point"||y==="MultiPoint")){var x,b=0,C=0,w=0;if(r(K,function(D,T,F,G,N){if(x===void 0||I>b||G>C||N>w){x=D,b=I,C=G,w=N,p=0;return}var J=i.lineString([x,D],K.properties);if(O(J,I,U,N,p)===!1)return!1;p++,x=D})===!1)return!1}}})}function W(S,O,K){var I=K,U=!1;return R(S,function(p,y,x,b,C){U===!1&&K===void 0?I=p:I=O(I,p,y,x,b,C),U=!0}),I}function X(S,O){if(!S)throw new Error("geojson is required");P(S,function(K,I,U){if(K.geometry!==null){var p=K.geometry.type,y=K.geometry.coordinates;switch(p){case"LineString":if(O(K,I,U,0,0)===!1)return!1;break;case"Polygon":for(var x=0;x<y.length;x++)if(O(i.lineString(y[x],K.properties),I,U,x)===!1)return!1;break}}})}function nt(S,O,K){var I=K;return X(S,function(U,p,y,x){p===0&&K===void 0?I=U:I=O(I,U,p,y,x)}),I}function vt(S,O){if(O=O||{},!i.isObject(O))throw new Error("options is invalid");var K=O.featureIndex||0,I=O.multiFeatureIndex||0,U=O.geometryIndex||0,p=O.segmentIndex||0,y=O.properties,x;switch(S.type){case"FeatureCollection":K<0&&(K=S.features.length+K),y=y||S.features[K].properties,x=S.features[K].geometry;break;case"Feature":y=y||S.properties,x=S.geometry;break;case"Point":case"MultiPoint":return null;case"LineString":case"Polygon":case"MultiLineString":case"MultiPolygon":x=S;break;default:throw new Error("geojson is invalid")}if(x===null)return null;var b=x.coordinates;switch(x.type){case"Point":case"MultiPoint":return null;case"LineString":return p<0&&(p=b.length+p-1),i.lineString([b[p],b[p+1]],y,O);case"Polygon":return U<0&&(U=b.length+U),p<0&&(p=b[U].length+p-1),i.lineString([b[U][p],b[U][p+1]],y,O);case"MultiLineString":return I<0&&(I=b.length+I),p<0&&(p=b[I].length+p-1),i.lineString([b[I][p],b[I][p+1]],y,O);case"MultiPolygon":return I<0&&(I=b.length+I),U<0&&(U=b[I].length+U),p<0&&(p=b[I][U].length-p-1),i.lineString([b[I][U][p],b[I][U][p+1]],y,O)}throw new Error("geojson is invalid")}function k(S,O){if(O=O||{},!i.isObject(O))throw new Error("options is invalid");var K=O.featureIndex||0,I=O.multiFeatureIndex||0,U=O.geometryIndex||0,p=O.coordIndex||0,y=O.properties,x;switch(S.type){case"FeatureCollection":K<0&&(K=S.features.length+K),y=y||S.features[K].properties,x=S.features[K].geometry;break;case"Feature":y=y||S.properties,x=S.geometry;break;case"Point":case"MultiPoint":return null;case"LineString":case"Polygon":case"MultiLineString":case"MultiPolygon":x=S;break;default:throw new Error("geojson is invalid")}if(x===null)return null;var b=x.coordinates;switch(x.type){case"Point":return i.point(b,y,O);case"MultiPoint":return I<0&&(I=b.length+I),i.point(b[I],y,O);case"LineString":return p<0&&(p=b.length+p),i.point(b[p],y,O);case"Polygon":return U<0&&(U=b.length+U),p<0&&(p=b[U].length+p),i.point(b[U][p],y,O);case"MultiLineString":return I<0&&(I=b.length+I),p<0&&(p=b[I].length+p),i.point(b[I][p],y,O);case"MultiPolygon":return I<0&&(I=b.length+I),U<0&&(U=b[I].length+U),p<0&&(p=b[I][U].length-p),i.point(b[I][U][p],y,O)}throw new Error("geojson is invalid")}e.coordAll=f,e.coordEach=r,e.coordReduce=a,e.featureEach=h,e.featureReduce=d,e.findPoint=k,e.findSegment=vt,e.flattenEach=P,e.flattenReduce=j,e.geomEach=m,e.geomReduce=E,e.lineEach=X,e.lineReduce=nt,e.propEach=s,e.propReduce=l,e.segmentEach=R,e.segmentReduce=W}),wn=M(e=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=Ze();function r(a){var s=[1/0,1/0,-1/0,-1/0];return i.coordEach(a,function(l){s[0]>l[0]&&(s[0]=l[0]),s[1]>l[1]&&(s[1]=l[1]),s[2]<l[0]&&(s[2]=l[0]),s[3]<l[1]&&(s[3]=l[1])}),s}r.default=r,e.default=r}),ft=M((e,i)=>{var r=Fi(),a=Ge(),s=Ze(),l=wn().default,h=s.featureEach,d=s.coordEach,f=a.polygon,m=a.featureCollection;function E(P){var j=new r(P);return j.insert=function(R){if(R.type!=="Feature")throw new Error("invalid feature");return R.bbox=R.bbox?R.bbox:l(R),r.prototype.insert.call(this,R)},j.load=function(R){var W=[];return Array.isArray(R)?R.forEach(function(X){if(X.type!=="Feature")throw new Error("invalid features");X.bbox=X.bbox?X.bbox:l(X),W.push(X)}):h(R,function(X){if(X.type!=="Feature")throw new Error("invalid features");X.bbox=X.bbox?X.bbox:l(X),W.push(X)}),r.prototype.load.call(this,W)},j.remove=function(R,W){if(R.type!=="Feature")throw new Error("invalid feature");return R.bbox=R.bbox?R.bbox:l(R),r.prototype.remove.call(this,R,W)},j.clear=function(){return r.prototype.clear.call(this)},j.search=function(R){var W=r.prototype.search.call(this,this.toBBox(R));return m(W)},j.collides=function(R){return r.prototype.collides.call(this,this.toBBox(R))},j.all=function(){var R=r.prototype.all.call(this);return m(R)},j.toJSON=function(){return r.prototype.toJSON.call(this)},j.fromJSON=function(R){return r.prototype.fromJSON.call(this,R)},j.toBBox=function(R){var W;if(R.bbox)W=R.bbox;else if(Array.isArray(R)&&R.length===4)W=R;else if(Array.isArray(R)&&R.length===6)W=[R[0],R[1],R[3],R[4]];else if(R.type==="Feature")W=l(R);else if(R.type==="FeatureCollection")W=l(R);else throw new Error("invalid geojson");return{minX:W[0],minY:W[1],maxX:W[2],maxY:W[3]}},j}i.exports=E,i.exports.default=E});Array.prototype.findIndex=Array.prototype.findIndex||function(e){if(this===null)throw new TypeError("Array.prototype.findIndex called on null or undefined");if(typeof e!="function")throw new TypeError("callback must be a function");for(var i=Object(this),r=i.length>>>0,a=arguments[1],s=0;s<r;s++)if(e.call(a,i[s],s,i))return s;return-1},Array.prototype.find=Array.prototype.find||function(e){if(this===null)throw new TypeError("Array.prototype.find called on null or undefined");if(typeof e!="function")throw new TypeError("callback must be a function");for(var i=Object(this),r=i.length>>>0,a=arguments[1],s=0;s<r;s++){var l=i[s];if(e.call(a,l,s,i))return l}},typeof Object.assign!="function"&&(Object.assign=function(e){"use strict";if(e==null)throw new TypeError("Cannot convert undefined or null to object");e=Object(e);for(var i=1;i<arguments.length;i++){var r=arguments[i];if(r!=null)for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e}),function(e){e.forEach(function(i){i.hasOwnProperty("remove")||Object.defineProperty(i,"remove",{configurable:!0,enumerable:!0,writable:!0,value:function(){this.parentNode.removeChild(this)}})})}([Element.prototype,CharacterData.prototype,DocumentType.prototype]),Array.prototype.includes||Object.defineProperty(Array.prototype,"includes",{value:function(e,i){if(this==null)throw new TypeError('"this" is null or not defined');var r=Object(this),a=r.length>>>0;if(a===0)return!1;var s=i|0,l=Math.max(s>=0?s:a-Math.abs(s),0);function h(d,f){return d===f||typeof d=="number"&&typeof f=="number"&&isNaN(d)&&isNaN(f)}for(;l<a;){if(h(r[l],e))return!0;l++}return!1}});var At={name:"@geoman-io/leaflet-geoman-free",version:"2.18.3",description:"A Leaflet Plugin For Editing Geometry Layers in Leaflet 1.0",keywords:["leaflet","geoman","polygon management","geometry editing","map data","map overlay","polygon","geojson","leaflet-draw","data-field-geojson","ui-leaflet-draw"],files:["dist"],main:"dist/leaflet-geoman.js",types:"dist/leaflet-geoman.d.ts",dependencies:{"@turf/boolean-contains":"^6.5.0","@turf/kinks":"^6.5.0","@turf/line-intersect":"^6.5.0","@turf/line-split":"^6.5.0",lodash:"4.17.21","polyclip-ts":"^0.16.5"},devDependencies:{"@types/leaflet":"^1.9.12","cross-env":"^7.0.3",cypress:"^13.11.0",esbuild:"^0.20.2",eslint:"8.56.0","eslint-config-airbnb-base":"15.0.0","eslint-config-prettier":"9.1.0","eslint-plugin-cypress":"2.15.1","eslint-plugin-import":"2.29.1",husky:"^9.0.11",leaflet:"1.9.3","lint-staged":"^15.2.5",prettier:"3.2.4","prosthetic-hand":"1.3.1","ts-node":"^10.9.2"},peerDependencies:{leaflet:"^1.2.0"},scripts:{start:"pnpm run dev",dev:"cross-env DEV=true ts-node bundle.mjs",build:"ts-node bundle.mjs",test:"cypress run --browser chrome",cypress:"cypress open",prepare:"pnpm run build && husky","eslint-check":"eslint --print-config . | eslint-config-prettier-check",eslint:'eslint "{src,demo}/**/*.js" --fix ',prettier:'prettier --write "{src,demo}/**/*.{js,css}" --log-level=warn',lint:"pnpm run eslint && pnpm run prettier"},repository:{type:"git",url:"git://github.com/geoman-io/leaflet-geoman.git"},author:{name:"Geoman.io",email:"<EMAIL>",url:"http://geoman.io"},license:"MIT",bugs:{url:"https://github.com/geoman-io/leaflet-geoman/issues"},homepage:"https://geoman.io",prettier:{trailingComma:"es5",tabWidth:2,semi:!0,singleQuote:!0},"lint-staged":{"*.js":'eslint "{src,demo}/**/*.js" --fix',"*.{js,css,md}":'prettier --write "{src,demo}/**/*.{js,css}"'}},Oi=$($e()),Ri={tooltips:{placeMarker:"Click to place marker",firstVertex:"Click to place first vertex",continueLine:"Click to continue drawing",finishLine:"Click any existing marker to finish",finishPoly:"Click first marker to finish",finishRect:"Click to finish",startCircle:"Click to place circle center",finishCircle:"Click to finish circle",placeCircleMarker:"Click to place circle marker",placeText:"Click to place text",selectFirstLayerFor:"Select first layer for {action}",selectSecondLayerFor:"Select second layer for {action}"},actions:{finish:"Finish",cancel:"Cancel",removeLastVertex:"Remove Last Vertex"},buttonTitles:{drawMarkerButton:"Draw Marker",drawPolyButton:"Draw Polygons",drawLineButton:"Draw Polyline",drawCircleButton:"Draw Circle",drawRectButton:"Draw Rectangle",editButton:"Edit Layers",dragButton:"Drag Layers",cutButton:"Cut Layers",deleteButton:"Remove Layers",drawCircleMarkerButton:"Draw Circle Marker",snappingButton:"Snap dragged marker to other layers and vertices",pinningButton:"Pin shared vertices together",rotateButton:"Rotate Layers",drawTextButton:"Draw Text",scaleButton:"Scale Layers",autoTracingButton:"Auto trace Line",snapGuidesButton:"Show SnapGuides",unionButton:"Union layers",differenceButton:"Subtract layers"},measurements:{totalLength:"Length",segmentLength:"Segment length",area:"Area",radius:"Radius",perimeter:"Perimeter",height:"Height",width:"Width",coordinates:"Position",coordinatesMarker:"Position Marker"}},oe={tooltips:{placeMarker:"Platziere den Marker mit Klick",firstVertex:"Platziere den ersten Marker mit Klick",continueLine:"Klicke, um weiter zu zeichnen",finishLine:"Beende mit Klick auf existierenden Marker",finishPoly:"Beende mit Klick auf ersten Marker",finishRect:"Beende mit Klick",startCircle:"Platziere das Kreiszentrum mit Klick",finishCircle:"Beende den Kreis mit Klick",placeCircleMarker:"Platziere den Kreismarker mit Klick",placeText:"Platziere den Text mit Klick"},actions:{finish:"Beenden",cancel:"Abbrechen",removeLastVertex:"Letzten Vertex l\xF6schen"},buttonTitles:{drawMarkerButton:"Marker zeichnen",drawPolyButton:"Polygon zeichnen",drawLineButton:"Polyline zeichnen",drawCircleButton:"Kreis zeichnen",drawRectButton:"Rechteck zeichnen",editButton:"Layer editieren",dragButton:"Layer bewegen",cutButton:"Layer schneiden",deleteButton:"Layer l\xF6schen",drawCircleMarkerButton:"Kreismarker zeichnen",snappingButton:"Bewegter Layer an andere Layer oder Vertexe einhacken",pinningButton:"Vertexe an der gleichen Position verkn\xFCpfen",rotateButton:"Layer drehen",drawTextButton:"Text zeichnen",scaleButton:"Layer skalieren",autoTracingButton:"Linie automatisch nachzeichen"},measurements:{totalLength:"L\xE4nge",segmentLength:"Segment L\xE4nge",area:"Fl\xE4che",radius:"Radius",perimeter:"Umfang",height:"H\xF6he",width:"Breite",coordinates:"Position",coordinatesMarker:"Position Marker"}},Ga={tooltips:{placeMarker:"Clicca per posizionare un Marker",firstVertex:"Clicca per posizionare il primo vertice",continueLine:"Clicca per continuare a disegnare",finishLine:"Clicca qualsiasi marker esistente per terminare",finishPoly:"Clicca il primo marker per terminare",finishRect:"Clicca per terminare",startCircle:"Clicca per posizionare il punto centrale del cerchio",finishCircle:"Clicca per terminare il cerchio",placeCircleMarker:"Clicca per posizionare un Marker del cherchio"},actions:{finish:"Termina",cancel:"Annulla",removeLastVertex:"Rimuovi l'ultimo vertice"},buttonTitles:{drawMarkerButton:"Disegna Marker",drawPolyButton:"Disegna Poligoni",drawLineButton:"Disegna Polilinea",drawCircleButton:"Disegna Cerchio",drawRectButton:"Disegna Rettangolo",editButton:"Modifica Livelli",dragButton:"Sposta Livelli",cutButton:"Ritaglia Livelli",deleteButton:"Elimina Livelli",drawCircleMarkerButton:"Disegna Marker del Cerchio",snappingButton:"Snap ha trascinato il pennarello su altri strati e vertici",pinningButton:"Pin condiviso vertici insieme",rotateButton:"Ruota livello"}},Ii={tooltips:{placeMarker:"Klik untuk menempatkan marker",firstVertex:"Klik untuk menempatkan vertex pertama",continueLine:"Klik untuk meneruskan digitasi",finishLine:"Klik pada sembarang marker yang ada untuk mengakhiri",finishPoly:"Klik marker pertama untuk mengakhiri",finishRect:"Klik untuk mengakhiri",startCircle:"Klik untuk menempatkan titik pusat lingkaran",finishCircle:"Klik untuk mengakhiri lingkaran",placeCircleMarker:"Klik untuk menempatkan penanda lingkarann"},actions:{finish:"Selesai",cancel:"Batal",removeLastVertex:"Hilangkan Vertex Terakhir"},buttonTitles:{drawMarkerButton:"Digitasi Marker",drawPolyButton:"Digitasi Polygon",drawLineButton:"Digitasi Polyline",drawCircleButton:"Digitasi Lingkaran",drawRectButton:"Digitasi Segi Empat",editButton:"Edit Layer",dragButton:"Geser Layer",cutButton:"Potong Layer",deleteButton:"Hilangkan Layer",drawCircleMarkerButton:"Digitasi Penanda Lingkaran",snappingButton:"Jepretkan penanda yang ditarik ke lapisan dan simpul lain",pinningButton:"Sematkan simpul bersama bersama",rotateButton:"Putar lapisan"}},je={tooltips:{placeMarker:"Adaug\u0103 un punct",firstVertex:"Apas\u0103 aici pentru a ad\u0103uga primul Vertex",continueLine:"Apas\u0103 aici pentru a continua desenul",finishLine:"Apas\u0103 pe orice obiect pentru a finisa desenul",finishPoly:"Apas\u0103 pe primul obiect pentru a finisa",finishRect:"Apas\u0103 pentru a finisa",startCircle:"Apas\u0103 pentru a desena un cerc",finishCircle:"Apas\u0103 pentru a finisa un cerc",placeCircleMarker:"Adaug\u0103 un punct"},actions:{finish:"Termin\u0103",cancel:"Anuleaz\u0103",removeLastVertex:"\u0218terge ultimul Vertex"},buttonTitles:{drawMarkerButton:"Adaug\u0103 o bulin\u0103",drawPolyButton:"Deseneaz\u0103 un poligon",drawLineButton:"Deseneaz\u0103 o linie",drawCircleButton:"Deseneaz\u0103 un cerc",drawRectButton:"Deseneaz\u0103 un dreptunghi",editButton:"Editeaz\u0103 straturile",dragButton:"Mut\u0103 straturile",cutButton:"Taie straturile",deleteButton:"\u0218terge straturile",drawCircleMarkerButton:"Deseneaz\u0103 marcatorul cercului",snappingButton:"Fixa\u021Bi marcatorul glisat pe alte straturi \u0219i v\xE2rfuri",pinningButton:"Fixa\u021Bi v\xE2rfurile partajate \xEEmpreun\u0103",rotateButton:"Roti\u021Bi stratul"}},Ot={tooltips:{placeMarker:"\u041D\u0430\u0436\u043C\u0438\u0442\u0435, \u0447\u0442\u043E\u0431\u044B \u043D\u0430\u043D\u0435\u0441\u0442\u0438 \u043C\u0430\u0440\u043A\u0435\u0440",firstVertex:"\u041D\u0430\u0436\u043C\u0438\u0442\u0435, \u0447\u0442\u043E\u0431\u044B \u043D\u0430\u043D\u0435\u0441\u0442\u0438 \u043F\u0435\u0440\u0432\u044B\u0439 \u043E\u0431\u044A\u0435\u043A\u0442",continueLine:"\u041D\u0430\u0436\u043C\u0438\u0442\u0435, \u0447\u0442\u043E\u0431\u044B \u043F\u0440\u043E\u0434\u043E\u043B\u0436\u0438\u0442\u044C \u0440\u0438\u0441\u043E\u0432\u0430\u043D\u0438\u0435",finishLine:"\u041D\u0430\u0436\u043C\u0438\u0442\u0435 \u043B\u044E\u0431\u043E\u0439 \u0441\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u044E\u0449\u0438\u0439 \u043C\u0430\u0440\u043A\u0435\u0440 \u0434\u043B\u044F \u0437\u0430\u0432\u0435\u0440\u0448\u0435\u043D\u0438\u044F",finishPoly:"\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u043F\u0435\u0440\u0432\u0443\u044E \u0442\u043E\u0447\u043A\u0443, \u0447\u0442\u043E\u0431\u044B \u0437\u0430\u043A\u043E\u043D\u0447\u0438\u0442\u044C",finishRect:"\u041D\u0430\u0436\u043C\u0438\u0442\u0435, \u0447\u0442\u043E\u0431\u044B \u0437\u0430\u043A\u043E\u043D\u0447\u0438\u0442\u044C",startCircle:"\u041D\u0430\u0436\u043C\u0438\u0442\u0435, \u0447\u0442\u043E\u0431\u044B \u0434\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0446\u0435\u043D\u0442\u0440 \u043A\u0440\u0443\u0433\u0430",finishCircle:"\u041D\u0430\u0436\u043C\u0438\u0442\u0435, \u0447\u0442\u043E\u0431\u044B \u0437\u0430\u0434\u0430\u0442\u044C \u0440\u0430\u0434\u0438\u0443\u0441",placeCircleMarker:"\u041D\u0430\u0436\u043C\u0438\u0442\u0435, \u0447\u0442\u043E\u0431\u044B \u043D\u0430\u043D\u0435\u0441\u0442\u0438 \u043A\u0440\u0443\u0433\u043E\u0432\u043E\u0439 \u043C\u0430\u0440\u043A\u0435\u0440"},actions:{finish:"\u0417\u0430\u0432\u0435\u0440\u0448\u0438\u0442\u044C",cancel:"\u041E\u0442\u043C\u0435\u043D\u0438\u0442\u044C",removeLastVertex:"\u041E\u0442\u043C\u0435\u043D\u0438\u0442\u044C \u043F\u043E\u0441\u043B\u0435\u0434\u043D\u0435\u0435 \u0434\u0435\u0439\u0441\u0442\u0432\u0438\u0435"},buttonTitles:{drawMarkerButton:"\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043C\u0430\u0440\u043A\u0435\u0440",drawPolyButton:"\u0420\u0438\u0441\u043E\u0432\u0430\u0442\u044C \u043F\u043E\u043B\u0438\u0433\u043E\u043D",drawLineButton:"\u0420\u0438\u0441\u043E\u0432\u0430\u0442\u044C \u043A\u0440\u0438\u0432\u0443\u044E",drawCircleButton:"\u0420\u0438\u0441\u043E\u0432\u0430\u0442\u044C \u043A\u0440\u0443\u0433",drawRectButton:"\u0420\u0438\u0441\u043E\u0432\u0430\u0442\u044C \u043F\u0440\u044F\u043C\u043E\u0443\u0433\u043E\u043B\u044C\u043D\u0438\u043A",editButton:"\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0441\u043B\u043E\u0439",dragButton:"\u041F\u0435\u0440\u0435\u043D\u0435\u0441\u0442\u0438 \u0441\u043B\u043E\u0439",cutButton:"\u0412\u044B\u0440\u0435\u0437\u0430\u0442\u044C \u0441\u043B\u043E\u0439",deleteButton:"\u0423\u0434\u0430\u043B\u0438\u0442\u044C \u0441\u043B\u043E\u0439",drawCircleMarkerButton:"\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043A\u0440\u0443\u0433\u043E\u0432\u043E\u0439 \u043C\u0430\u0440\u043A\u0435\u0440",snappingButton:"\u041F\u0440\u0438\u0432\u044F\u0437\u0430\u0442\u044C \u043F\u0435\u0440\u0435\u0442\u0430\u0441\u043A\u0438\u0432\u0430\u0435\u043C\u044B\u0439 \u043C\u0430\u0440\u043A\u0435\u0440 \u043A \u0434\u0440\u0443\u0433\u0438\u043C \u0441\u043B\u043E\u044F\u043C \u0438 \u0432\u0435\u0440\u0448\u0438\u043D\u0430\u043C",pinningButton:"\u0421\u0432\u044F\u0437\u0430\u0442\u044C \u043E\u0431\u0449\u0438\u0435 \u0442\u043E\u0447\u043A\u0438 \u0432\u043C\u0435\u0441\u0442\u0435",rotateButton:"\u041F\u043E\u0432\u043E\u0440\u043E\u0442 \u0441\u043B\u043E\u044F"}},Ue={tooltips:{placeMarker:"Presiona para colocar un marcador",firstVertex:"Presiona para colocar el primer v\xE9rtice",continueLine:"Presiona para continuar dibujando",finishLine:"Presiona cualquier marcador existente para finalizar",finishPoly:"Presiona el primer marcador para finalizar",finishRect:"Presiona para finalizar",startCircle:"Presiona para colocar el centro del c\xEDrculo",finishCircle:"Presiona para finalizar el c\xEDrculo",placeCircleMarker:"Presiona para colocar un marcador de c\xEDrculo"},actions:{finish:"Finalizar",cancel:"Cancelar",removeLastVertex:"Eliminar \xFAltimo v\xE9rtice"},buttonTitles:{drawMarkerButton:"Dibujar Marcador",drawPolyButton:"Dibujar Pol\xEDgono",drawLineButton:"Dibujar L\xEDnea",drawCircleButton:"Dibujar C\xEDrculo",drawRectButton:"Dibujar Rect\xE1ngulo",editButton:"Editar Capas",dragButton:"Arrastrar Capas",cutButton:"Cortar Capas",deleteButton:"Eliminar Capas",drawCircleMarkerButton:"Dibujar Marcador de C\xEDrculo",snappingButton:"El marcador de Snap arrastrado a otras capas y v\xE9rtices",pinningButton:"Fijar juntos los v\xE9rtices compartidos",rotateButton:"Rotar capa"}},mi={tooltips:{placeMarker:"Klik om een marker te plaatsen",firstVertex:"Klik om het eerste punt te plaatsen",continueLine:"Klik om te blijven tekenen",finishLine:"Klik op een bestaand punt om te be\xEBindigen",finishPoly:"Klik op het eerst punt om te be\xEBindigen",finishRect:"Klik om te be\xEBindigen",startCircle:"Klik om het middelpunt te plaatsen",finishCircle:"Klik om de cirkel te be\xEBindigen",placeCircleMarker:"Klik om een marker te plaatsen"},actions:{finish:"Bewaar",cancel:"Annuleer",removeLastVertex:"Verwijder laatste punt"},buttonTitles:{drawMarkerButton:"Plaats Marker",drawPolyButton:"Teken een vlak",drawLineButton:"Teken een lijn",drawCircleButton:"Teken een cirkel",drawRectButton:"Teken een vierkant",editButton:"Bewerk",dragButton:"Verplaats",cutButton:"Knip",deleteButton:"Verwijder",drawCircleMarkerButton:"Plaats Marker",snappingButton:"Snap gesleepte marker naar andere lagen en hoekpunten",pinningButton:"Speld gedeelde hoekpunten samen",rotateButton:"Laag roteren"}},gi={tooltips:{placeMarker:"Cliquez pour placer un marqueur",firstVertex:"Cliquez pour placer le premier sommet",continueLine:"Cliquez pour continuer \xE0 dessiner",finishLine:"Cliquez sur n'importe quel marqueur pour terminer",finishPoly:"Cliquez sur le premier marqueur pour terminer",finishRect:"Cliquez pour terminer",startCircle:"Cliquez pour placer le centre du cercle",finishCircle:"Cliquez pour finir le cercle",placeCircleMarker:"Cliquez pour placer le marqueur circulaire"},actions:{finish:"Terminer",cancel:"Annuler",removeLastVertex:"Retirer le dernier sommet"},buttonTitles:{drawMarkerButton:"Placer des marqueurs",drawPolyButton:"Dessiner des polygones",drawLineButton:"Dessiner des polylignes",drawCircleButton:"Dessiner un cercle",drawRectButton:"Dessiner un rectangle",editButton:"\xC9diter des calques",dragButton:"D\xE9placer des calques",cutButton:"Couper des calques",deleteButton:"Supprimer des calques",drawCircleMarkerButton:"Dessiner un marqueur circulaire",snappingButton:"Glisser le marqueur vers d'autres couches et sommets",pinningButton:"\xC9pingler ensemble les sommets partag\xE9s",rotateButton:"Tourner des calques"}},kn={tooltips:{placeMarker:"\u5355\u51FB\u653E\u7F6E\u6807\u8BB0",firstVertex:"\u5355\u51FB\u653E\u7F6E\u9996\u4E2A\u9876\u70B9",continueLine:"\u5355\u51FB\u7EE7\u7EED\u7ED8\u5236",finishLine:"\u5355\u51FB\u4EFB\u4F55\u5B58\u5728\u7684\u6807\u8BB0\u4EE5\u5B8C\u6210",finishPoly:"\u5355\u51FB\u7B2C\u4E00\u4E2A\u6807\u8BB0\u4EE5\u5B8C\u6210",finishRect:"\u5355\u51FB\u5B8C\u6210",startCircle:"\u5355\u51FB\u653E\u7F6E\u5706\u5FC3",finishCircle:"\u5355\u51FB\u5B8C\u6210\u5706\u5F62",placeCircleMarker:"\u70B9\u51FB\u653E\u7F6E\u5706\u5F62\u6807\u8BB0"},actions:{finish:"\u5B8C\u6210",cancel:"\u53D6\u6D88",removeLastVertex:"\u79FB\u9664\u6700\u540E\u7684\u9876\u70B9"},buttonTitles:{drawMarkerButton:"\u7ED8\u5236\u6807\u8BB0",drawPolyButton:"\u7ED8\u5236\u591A\u8FB9\u5F62",drawLineButton:"\u7ED8\u5236\u7EBF\u6BB5",drawCircleButton:"\u7ED8\u5236\u5706\u5F62",drawRectButton:"\u7ED8\u5236\u957F\u65B9\u5F62",editButton:"\u7F16\u8F91\u56FE\u5C42",dragButton:"\u62D6\u62FD\u56FE\u5C42",cutButton:"\u526A\u5207\u56FE\u5C42",deleteButton:"\u5220\u9664\u56FE\u5C42",drawCircleMarkerButton:"\u753B\u5706\u5708\u6807\u8BB0",snappingButton:"\u5C06\u62D6\u52A8\u7684\u6807\u8BB0\u6355\u6349\u5230\u5176\u4ED6\u56FE\u5C42\u548C\u9876\u70B9",pinningButton:"\u5C06\u5171\u4EAB\u9876\u70B9\u56FA\u5B9A\u5728\u4E00\u8D77",rotateButton:"\u65CB\u8F6C\u56FE\u5C42"}},yi={tooltips:{placeMarker:"\u55AE\u64CA\u653E\u7F6E\u6A19\u8A18",firstVertex:"\u55AE\u64CA\u653E\u7F6E\u7B2C\u4E00\u500B\u9802\u9EDE",continueLine:"\u55AE\u64CA\u7E7C\u7E8C\u7E6A\u88FD",finishLine:"\u55AE\u64CA\u4EFB\u4F55\u5B58\u5728\u7684\u6A19\u8A18\u4EE5\u5B8C\u6210",finishPoly:"\u55AE\u64CA\u7B2C\u4E00\u500B\u6A19\u8A18\u4EE5\u5B8C\u6210",finishRect:"\u55AE\u64CA\u5B8C\u6210",startCircle:"\u55AE\u64CA\u653E\u7F6E\u5713\u5FC3",finishCircle:"\u55AE\u64CA\u5B8C\u6210\u5713\u5F62",placeCircleMarker:"\u9EDE\u64CA\u653E\u7F6E\u5713\u5F62\u6A19\u8A18"},actions:{finish:"\u5B8C\u6210",cancel:"\u53D6\u6D88",removeLastVertex:"\u79FB\u9664\u6700\u5F8C\u4E00\u500B\u9802\u9EDE"},buttonTitles:{drawMarkerButton:"\u653E\u7F6E\u6A19\u8A18",drawPolyButton:"\u7E6A\u88FD\u591A\u908A\u5F62",drawLineButton:"\u7E6A\u88FD\u7DDA\u6BB5",drawCircleButton:"\u7E6A\u88FD\u5713\u5F62",drawRectButton:"\u7E6A\u88FD\u65B9\u5F62",editButton:"\u7DE8\u8F2F\u5716\u5F62",dragButton:"\u79FB\u52D5\u5716\u5F62",cutButton:"\u88C1\u5207\u5716\u5F62",deleteButton:"\u522A\u9664\u5716\u5F62",drawCircleMarkerButton:"\u756B\u5713\u5708\u6A19\u8A18",snappingButton:"\u5C07\u62D6\u52D5\u7684\u6A19\u8A18\u5C0D\u9F4A\u5230\u5176\u4ED6\u5716\u5C64\u548C\u9802\u9EDE",pinningButton:"\u5C07\u5171\u4EAB\u9802\u9EDE\u56FA\u5B9A\u5728\u4E00\u8D77",rotateButton:"\u65CB\u8F49\u5716\u5F62"}},Mn={tooltips:{placeMarker:"Clique para posicionar o marcador",firstVertex:"Clique para posicionar o primeiro v\xE9rtice",continueLine:"Clique para continuar desenhando",finishLine:"Clique em qualquer marcador existente para finalizar",finishPoly:"Clique no primeiro marcador para finalizar",finishRect:"Clique para finalizar",startCircle:"Clique para posicionar o centro do c\xEDrculo",finishCircle:"Clique para finalizar o c\xEDrculo",placeCircleMarker:"Clique para posicionar o marcador circular",placeText:"Clique para inserir texto"},actions:{finish:"Finalizar",cancel:"Cancelar",removeLastVertex:"Remover \xFAltimo v\xE9rtice"},buttonTitles:{drawMarkerButton:"Desenhar Marcador",drawPolyButton:"Desenhar Pol\xEDgonos",drawLineButton:"Desenhar Linha Poligonal",drawCircleButton:"Desenhar C\xEDrculo",drawRectButton:"Desenhar Ret\xE2ngulo",editButton:"Editar Camadas",dragButton:"Arrastar Camadas",cutButton:"Recortar Camadas",deleteButton:"Remover Camadas",drawCircleMarkerButton:"Desenhar Marcador de C\xEDrculo",snappingButton:"Ajustar marcador arrastado a outras camadas e v\xE9rtices",pinningButton:"Unir v\xE9rtices compartilhados",rotateButton:"Rotacionar Camadas",drawTextButton:"Desenhar Texto",scaleButton:"Redimensionar Camadas",autoTracingButton:"Tra\xE7ado Autom\xE1tico de Linha"},measurements:{totalLength:"Comprimento",segmentLength:"Comprimento do Segmento",area:"\xC1rea",radius:"Raio",perimeter:"Per\xEDmetro",height:"Altura",width:"Largura",coordinates:"Posi\xE7\xE3o",coordinatesMarker:"Marcador de Posi\xE7\xE3o"}},zi={tooltips:{placeMarker:"Clique para colocar marcador",firstVertex:"Clique para colocar primeiro v\xE9rtice",continueLine:"Clique para continuar a desenhar",finishLine:"Clique num marcador existente para terminar",finishPoly:"Clique no primeiro marcador para terminar",finishRect:"Clique para terminar",startCircle:"Clique para colocar o centro do c\xEDrculo",finishCircle:"Clique para terminar o c\xEDrculo",placeCircleMarker:"Clique para colocar marcador de c\xEDrculo",placeText:"Clique para colocar texto"},actions:{finish:"Terminar",cancel:"Cancelar",removeLastVertex:"Remover \xDAltimo V\xE9rtice"},buttonTitles:{drawMarkerButton:"Desenhar Marcador",drawPolyButton:"Desenhar Pol\xEDgonos",drawLineButton:"Desenhar Polilinha",drawCircleButton:"Desenhar C\xEDrculo",drawRectButton:"Desenhar Ret\xE2ngulo",editButton:"Editar Camadas",dragButton:"Arrastar Camadas",cutButton:"Cortar Camadas",deleteButton:"Remover Camadas",drawCircleMarkerButton:"Desenhar Marcador de C\xEDrculo",snappingButton:"Ajustar marcador arrastado a outras camadas e v\xE9rtices",pinningButton:"Unir v\xE9rtices partilhados",rotateButton:"Rodar Camadas",drawTextButton:"Desenhar Texto",scaleButton:"Escalar Camadas",autoTracingButton:"Tra\xE7ado Autom\xE1tico de Linha"},measurements:{totalLength:"Comprimento",segmentLength:"Comprimento do Segmento",area:"\xC1rea",radius:"Raio",perimeter:"Per\xEDmetro",height:"Altura",width:"Largura",coordinates:"Posi\xE7\xE3o",coordinatesMarker:"Marcador de Posi\xE7\xE3o"}},Ni={tooltips:{placeMarker:"Kliknij, aby umie\u015Bci\u0107 znacznik",firstVertex:"Kliknij, aby umie\u015Bci\u0107 pierwszy wierzcho\u0142ek",continueLine:"Kliknij, aby kontynuowa\u0107 rysowanie",finishLine:"Kliknij dowolny istniej\u0105cy znacznik, aby zako\u0144czy\u0107",finishPoly:"Kliknij pierwszy znacznik, aby zako\u0144czy\u0107",finishRect:"Kliknij, aby zako\u0144czy\u0107",startCircle:"Kliknij, aby umie\u015Bci\u0107 \u015Brodek okr\u0119gu",finishCircle:"Kliknij, aby zako\u0144czy\u0107 okr\u0105g",placeCircleMarker:"Kliknij, aby umie\u015Bci\u0107 znacznik okr\u0119gu",placeText:"Kliknij, aby umie\u015Bci\u0107 tekst"},actions:{finish:"Zako\u0144cz",cancel:"Anuluj",removeLastVertex:"Usu\u0144 ostatni wierzcho\u0142ek"},buttonTitles:{drawMarkerButton:"Rysuj znacznik",drawPolyButton:"Rysuj wielok\u0105t",drawLineButton:"Rysuj lini\u0119",drawCircleButton:"Rysuj okr\u0105g",drawRectButton:"Rysuj prostok\u0105t",editButton:"Edytuj warstwy",dragButton:"Przeci\u0105gnij warstwy",cutButton:"Wytnij warstwy",deleteButton:"Usu\u0144 warstwy",drawCircleMarkerButton:"Rysuj znacznik okr\u0105g\u0142y",snappingButton:"Przyci\u0105gnij przenoszony znacznik do innych warstw i wierzcho\u0142k\xF3w",pinningButton:"Przypnij wsp\xF3lne wierzcho\u0142ki razem",rotateButton:"Obr\xF3\u0107 warstwy",drawTextButton:"Rysuj tekst",scaleButton:"Skaluj warstwy",autoTracingButton:"Automatyczne \u015Bledzenie linii"},measurements:{totalLength:"D\u0142ugo\u015B\u0107",segmentLength:"D\u0142ugo\u015B\u0107 odcinka",area:"Obszar",radius:"Promie\u0144",perimeter:"Obw\xF3d",height:"Wysoko\u015B\u0107",width:"Szeroko\u015B\u0107",coordinates:"Pozycja",coordinatesMarker:"Znacznik pozycji"}},En={tooltips:{placeMarker:"Klicka f\xF6r att placera mark\xF6r",firstVertex:"Klicka f\xF6r att placera f\xF6rsta h\xF6rnet",continueLine:"Klicka f\xF6r att forts\xE4tta rita",finishLine:"Klicka p\xE5 en existerande punkt f\xF6r att slutf\xF6ra",finishPoly:"Klicka p\xE5 den f\xF6rsta punkten f\xF6r att slutf\xF6ra",finishRect:"Klicka f\xF6r att slutf\xF6ra",startCircle:"Klicka f\xF6r att placera cirkelns centrum",finishCircle:"Klicka f\xF6r att slutf\xF6ra cirkeln",placeCircleMarker:"Klicka f\xF6r att placera cirkelmark\xF6r"},actions:{finish:"Slutf\xF6r",cancel:"Avbryt",removeLastVertex:"Ta bort sista h\xF6rnet"},buttonTitles:{drawMarkerButton:"Rita Mark\xF6r",drawPolyButton:"Rita Polygoner",drawLineButton:"Rita Linje",drawCircleButton:"Rita Cirkel",drawRectButton:"Rita Rektangel",editButton:"Redigera Lager",dragButton:"Dra Lager",cutButton:"Klipp i Lager",deleteButton:"Ta bort Lager",drawCircleMarkerButton:"Rita Cirkelmark\xF6r",snappingButton:"Sn\xE4pp dra mark\xF6ren till andra lager och h\xF6rn",pinningButton:"F\xE4st delade h\xF6rn tillsammans",rotateButton:"Rotera lagret"}},Bn={tooltips:{placeMarker:"\u039A\u03AC\u03BD\u03C4\u03B5 \u03BA\u03BB\u03B9\u03BA \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C4\u03BF\u03C0\u03BF\u03B8\u03B5\u03C4\u03AE\u03C3\u03B5\u03C4\u03B5 \u0394\u03B5\u03AF\u03BA\u03C4\u03B7",firstVertex:"\u039A\u03AC\u03BD\u03C4\u03B5 \u03BA\u03BB\u03B9\u03BA \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C4\u03BF\u03C0\u03BF\u03B8\u03B5\u03C4\u03AE\u03C3\u03B5\u03C4\u03B5 \u03C4\u03BF \u03C0\u03C1\u03CE\u03C4\u03BF \u03C3\u03B7\u03BC\u03B5\u03AF\u03BF",continueLine:"\u039A\u03AC\u03BD\u03C4\u03B5 \u03BA\u03BB\u03B9\u03BA \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B5\u03C7\u03AF\u03C3\u03B5\u03C4\u03B5 \u03BD\u03B1 \u03C3\u03C7\u03B5\u03B4\u03B9\u03AC\u03B6\u03B5\u03C4\u03B5",finishLine:"\u039A\u03AC\u03BD\u03C4\u03B5 \u03BA\u03BB\u03B9\u03BA \u03C3\u03B5 \u03BF\u03C0\u03BF\u03B9\u03BF\u03BD\u03B4\u03AE\u03C0\u03BF\u03C4\u03B5 \u03C5\u03C0\u03AC\u03C1\u03C7\u03BF\u03BD \u03C3\u03B7\u03BC\u03B5\u03AF\u03BF \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03BF\u03BB\u03BF\u03BA\u03BB\u03B7\u03C1\u03C9\u03B8\u03B5\u03AF",finishPoly:"\u039A\u03AC\u03BD\u03C4\u03B5 \u03BA\u03BB\u03B9\u03BA \u03C3\u03C4\u03BF \u03C0\u03C1\u03CE\u03C4\u03BF \u03C3\u03B7\u03BC\u03B5\u03AF\u03BF \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C4\u03B5\u03BB\u03B5\u03B9\u03CE\u03C3\u03B5\u03C4\u03B5",finishRect:"\u039A\u03AC\u03BD\u03C4\u03B5 \u03BA\u03BB\u03B9\u03BA \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C4\u03B5\u03BB\u03B5\u03B9\u03CE\u03C3\u03B5\u03C4\u03B5",startCircle:"\u039A\u03AC\u03BD\u03C4\u03B5 \u03BA\u03BB\u03B9\u03BA \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C4\u03BF\u03C0\u03BF\u03B8\u03B5\u03C4\u03AE\u03C3\u03B5\u03C4\u03B5 \u03BA\u03AD\u03BD\u03C4\u03C1\u03BF \u039A\u03CD\u03BA\u03BB\u03BF\u03C5",finishCircle:"\u039A\u03AC\u03BD\u03C4\u03B5 \u03BA\u03BB\u03B9\u03BA \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03BF\u03BB\u03BF\u03BA\u03BB\u03B7\u03C1\u03CE\u03C3\u03B5\u03C4\u03B5 \u03C4\u03BF\u03BD \u039A\u03CD\u03BA\u03BB\u03BF",placeCircleMarker:"\u039A\u03AC\u03BD\u03C4\u03B5 \u03BA\u03BB\u03B9\u03BA \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C4\u03BF\u03C0\u03BF\u03B8\u03B5\u03C4\u03AE\u03C3\u03B5\u03C4\u03B5 \u039A\u03C5\u03BA\u03BB\u03B9\u03BA\u03CC \u0394\u03B5\u03AF\u03BA\u03C4\u03B7"},actions:{finish:"\u03A4\u03AD\u03BB\u03BF\u03C2",cancel:"\u0391\u03BA\u03CD\u03C1\u03C9\u03C3\u03B7",removeLastVertex:"\u039A\u03B1\u03C4\u03AC\u03C1\u03B3\u03B7\u03C3\u03B7 \u03C4\u03B5\u03BB\u03B5\u03C5\u03C4\u03B1\u03AF\u03BF\u03C5 \u03C3\u03B7\u03BC\u03B5\u03AF\u03BF\u03C5"},buttonTitles:{drawMarkerButton:"\u03A3\u03C7\u03B5\u03B4\u03AF\u03B1\u03C3\u03B7 \u0394\u03B5\u03AF\u03BA\u03C4\u03B7",drawPolyButton:"\u03A3\u03C7\u03B5\u03B4\u03AF\u03B1\u03C3\u03B7 \u03A0\u03BF\u03BB\u03C5\u03B3\u03CE\u03BD\u03BF\u03C5",drawLineButton:"\u03A3\u03C7\u03B5\u03B4\u03AF\u03B1\u03C3\u03B7 \u0393\u03C1\u03B1\u03BC\u03BC\u03AE\u03C2",drawCircleButton:"\u03A3\u03C7\u03B5\u03B4\u03AF\u03B1\u03C3\u03B7 \u039A\u03CD\u03BA\u03BB\u03BF\u03C5",drawRectButton:"\u03A3\u03C7\u03B5\u03B4\u03AF\u03B1\u03C3\u03B7 \u039F\u03C1\u03B8\u03BF\u03B3\u03C9\u03BD\u03AF\u03BF\u03C5",editButton:"\u0395\u03C0\u03B5\u03BE\u03B5\u03C1\u03B3\u03B1\u03C3\u03AF\u03B1 \u0395\u03C0\u03B9\u03C0\u03AD\u03B4\u03C9\u03BD",dragButton:"\u039C\u03B5\u03C4\u03B1\u03C6\u03BF\u03C1\u03AC \u0395\u03C0\u03B9\u03C0\u03AD\u03B4\u03C9\u03BD",cutButton:"\u0391\u03C0\u03BF\u03BA\u03BF\u03C0\u03AE \u0395\u03C0\u03B9\u03C0\u03AD\u03B4\u03C9\u03BD",deleteButton:"\u039A\u03B1\u03C4\u03AC\u03C1\u03B3\u03B7\u03C3\u03B7 \u0395\u03C0\u03B9\u03C0\u03AD\u03B4\u03C9\u03BD",drawCircleMarkerButton:"\u03A3\u03C7\u03B5\u03B4\u03AF\u03B1\u03C3\u03B7 \u039A\u03C5\u03BA\u03BB\u03B9\u03BA\u03BF\u03CD \u0394\u03B5\u03AF\u03BA\u03C4\u03B7",snappingButton:"\u03A0\u03C1\u03BF\u03C3\u03BA\u03CC\u03BB\u03BB\u03B7\u03C3\u03B7 \u03C4\u03BF\u03C5 \u0394\u03B5\u03AF\u03BA\u03C4\u03B7 \u03BC\u03B5\u03C4\u03B1\u03C6\u03BF\u03C1\u03AC\u03C2 \u03C3\u03B5 \u03AC\u03BB\u03BB\u03B1 \u0395\u03C0\u03AF\u03C0\u03B5\u03B4\u03B1 \u03BA\u03B1\u03B9 \u039A\u03BF\u03C1\u03C5\u03C6\u03AD\u03C2",pinningButton:"\u03A0\u03B5\u03C1\u03B9\u03BA\u03BF\u03C0\u03AE \u03BA\u03BF\u03B9\u03BD\u03CE\u03BD \u03BA\u03BF\u03C1\u03C5\u03C6\u03CE\u03BD \u03BC\u03B1\u03B6\u03AF",rotateButton:"\u03A0\u03B5\u03C1\u03B9\u03C3\u03C4\u03C1\u03AD\u03C8\u03C4\u03B5 \u03C4\u03BF \u03C3\u03C4\u03C1\u03CE\u03BC\u03B1"}},Gi={tooltips:{placeMarker:"Kattintson a jel\xF6l\u0151 elhelyez\xE9s\xE9hez",firstVertex:"Kattintson az els\u0151 pont elhelyez\xE9s\xE9hez",continueLine:"Kattintson a k\xF6vetkez\u0151 pont elhelyez\xE9s\xE9hez",finishLine:"A befejez\xE9shez kattintson egy megl\xE9v\u0151 pontra",finishPoly:"A befejez\xE9shez kattintson az els\u0151 pontra",finishRect:"Kattintson a befejez\xE9shez",startCircle:"Kattintson a k\xF6r k\xF6z\xE9ppontj\xE1nak elhelyez\xE9s\xE9hez",finishCircle:"Kattintson a k\xF6r befejez\xE9s\xE9hez",placeCircleMarker:"Kattintson a k\xF6rjel\xF6l\u0151 elhelyez\xE9s\xE9hez"},actions:{finish:"Befejez\xE9s",cancel:"M\xE9gse",removeLastVertex:"Utols\xF3 pont elt\xE1vol\xEDt\xE1sa"},buttonTitles:{drawMarkerButton:"Jel\xF6l\u0151 rajzol\xE1sa",drawPolyButton:"Poligon rajzol\xE1sa",drawLineButton:"Vonal rajzol\xE1sa",drawCircleButton:"K\xF6r rajzol\xE1sa",drawRectButton:"N\xE9gyzet rajzol\xE1sa",editButton:"Elemek szerkeszt\xE9se",dragButton:"Elemek mozgat\xE1sa",cutButton:"Elemek v\xE1g\xE1sa",deleteButton:"Elemek t\xF6rl\xE9se",drawCircleMarkerButton:"K\xF6r jel\xF6l\u0151 rajzol\xE1sa",snappingButton:"Kapcsolja a jel\xF6lt\u0151t m\xE1sik elemhez vagy ponthoz",pinningButton:"K\xF6z\xF6s pontok \xF6sszek\xF6t\xE9se",rotateButton:"F\xF3lia elforgat\xE1sa"}},Or={tooltips:{placeMarker:"Tryk for at placere en mark\xF8r",firstVertex:"Tryk for at placere det f\xF8rste punkt",continueLine:"Tryk for at forts\xE6tte linjen",finishLine:"Tryk p\xE5 et eksisterende punkt for at afslutte",finishPoly:"Tryk p\xE5 det f\xF8rste punkt for at afslutte",finishRect:"Tryk for at afslutte",startCircle:"Tryk for at placere cirklens center",finishCircle:"Tryk for at afslutte cirklen",placeCircleMarker:"Tryk for at placere en cirkelmark\xF8r"},actions:{finish:"Afslut",cancel:"Afbryd",removeLastVertex:"Fjern sidste punkt"},buttonTitles:{drawMarkerButton:"Placer mark\xF8r",drawPolyButton:"Tegn polygon",drawLineButton:"Tegn linje",drawCircleButton:"Tegn cirkel",drawRectButton:"Tegn firkant",editButton:"Rediger",dragButton:"Tr\xE6k",cutButton:"Klip",deleteButton:"Fjern",drawCircleMarkerButton:"Tegn cirkelmark\xF8r",snappingButton:"Fastg\xF8r trukket mark\xF8r til andre elementer",pinningButton:"Sammenl\xE6g delte elementer",rotateButton:"Roter laget"}},Pn={tooltips:{placeMarker:"Klikk for \xE5 plassere punkt",firstVertex:"Klikk for \xE5 plassere f\xF8rste punkt",continueLine:"Klikk for \xE5 tegne videre",finishLine:"Klikk p\xE5 et eksisterende punkt for \xE5 fullf\xF8re",finishPoly:"Klikk f\xF8rste punkt for \xE5 fullf\xF8re",finishRect:"Klikk for \xE5 fullf\xF8re",startCircle:"Klikk for \xE5 sette sirkel midtpunkt",finishCircle:"Klikk for \xE5 fullf\xF8re sirkel",placeCircleMarker:"Klikk for \xE5 plassere sirkel",placeText:"Klikk for \xE5 plassere tekst"},actions:{finish:"Fullf\xF8r",cancel:"Kanseller",removeLastVertex:"Fjern forrige punkt"},buttonTitles:{drawMarkerButton:"Tegn punkt",drawPolyButton:"Tegn flate",drawLineButton:"Tegn linje",drawCircleButton:"Tegn sirkel",drawRectButton:"Tegn rektangel",editButton:"Rediger objekter",dragButton:"Dra objekter",cutButton:"Kutt objekter",deleteButton:"Fjern objekter",drawCircleMarkerButton:"Tegn sirkel-punkt",snappingButton:"Fest dratt punkt til andre objekter og punkt",pinningButton:"Pin delte punkter sammen",rotateButton:"Rot\xE9r objekter",drawTextButton:"Tegn tekst",scaleButton:"Skal\xE9r objekter",autoTracingButton:"Automatisk sporing av linje"},measurements:{totalLength:"Lengde",segmentLength:"Segmentlengde",area:"Omr\xE5de",radius:"Radius",perimeter:"Omriss",height:"H\xF8yde",width:"Bredde",coordinates:"Posisjon",coordinatesMarker:"Posisjonsmark\xF8r"}},Za={tooltips:{placeMarker:"\u06A9\u0644\u06CC\u06A9 \u0628\u0631\u0627\u06CC \u062C\u0627\u0646\u0645\u0627\u06CC\u06CC \u0646\u0634\u0627\u0646",firstVertex:"\u06A9\u0644\u06CC\u06A9 \u0628\u0631\u0627\u06CC \u0631\u0633\u0645 \u0627\u0648\u0644\u06CC\u0646 \u0631\u0623\u0633",continueLine:"\u06A9\u0644\u06CC\u06A9 \u0628\u0631\u0627\u06CC \u0627\u062F\u0627\u0645\u0647 \u0631\u0633\u0645",finishLine:"\u06A9\u0644\u06CC\u06A9 \u0631\u0648\u06CC \u0647\u0631 \u0646\u0634\u0627\u0646 \u0645\u0648\u062C\u0648\u062F \u0628\u0631\u0627\u06CC \u067E\u0627\u06CC\u0627\u0646",finishPoly:"\u06A9\u0644\u06CC\u06A9 \u0631\u0648\u06CC \u0627\u0648\u0644\u06CC\u0646 \u0646\u0634\u0627\u0646 \u0628\u0631\u0627\u06CC \u067E\u0627\u06CC\u0627\u0646",finishRect:"\u06A9\u0644\u06CC\u06A9 \u0628\u0631\u0627\u06CC \u067E\u0627\u06CC\u0627\u0646",startCircle:"\u06A9\u0644\u06CC\u06A9 \u0628\u0631\u0627\u06CC \u0631\u0633\u0645 \u0645\u0631\u06A9\u0632 \u062F\u0627\u06CC\u0631\u0647",finishCircle:"\u06A9\u0644\u06CC\u06A9 \u0628\u0631\u0627\u06CC \u067E\u0627\u06CC\u0627\u0646 \u0631\u0633\u0645 \u062F\u0627\u06CC\u0631\u0647",placeCircleMarker:"\u06A9\u0644\u06CC\u06A9 \u0628\u0631\u0627\u06CC \u0631\u0633\u0645 \u0646\u0634\u0627\u0646 \u062F\u0627\u06CC\u0631\u0647",placeText:"\u06A9\u0644\u06CC\u06A9 \u0628\u0631\u0627\u06CC \u0646\u0648\u0634\u062A\u0646 \u0645\u062A\u0646"},actions:{finish:"\u067E\u0627\u06CC\u0627\u0646",cancel:"\u0644\u0641\u0648",removeLastVertex:"\u062D\u0630\u0641 \u0622\u062E\u0631\u06CC\u0646 \u0631\u0623\u0633"},buttonTitles:{drawMarkerButton:"\u062F\u0631\u062C \u0646\u0634\u0627\u0646",drawPolyButton:"\u0631\u0633\u0645 \u0686\u0646\u062F\u0636\u0644\u0639\u06CC",drawLineButton:"\u0631\u0633\u0645 \u062E\u0637",drawCircleButton:"\u0631\u0633\u0645 \u062F\u0627\u06CC\u0631\u0647",drawRectButton:"\u0631\u0633\u0645 \u0686\u0647\u0627\u0631\u0636\u0644\u0639\u06CC",editButton:"\u0648\u06CC\u0631\u0627\u06CC\u0634 \u0644\u0627\u06CC\u0647\u200C\u0647\u0627",dragButton:"\u062C\u0627\u0628\u062C\u0627\u06CC\u06CC \u0644\u0627\u06CC\u0647\u200C\u0647\u0627",cutButton:"\u0628\u0631\u0634 \u0644\u0627\u06CC\u0647\u200C\u0647\u0627",deleteButton:"\u062D\u0630\u0641 \u0644\u0627\u06CC\u0647\u200C\u0647\u0627",drawCircleMarkerButton:"\u0631\u0633\u0645 \u0646\u0634\u0627\u0646 \u062F\u0627\u06CC\u0631\u0647",snappingButton:"\u0646\u0634\u0627\u0646\u06AF\u0631 \u0631\u0627 \u0628\u0647 \u0644\u0627\u06CC\u0647\u200C\u0647\u0627 \u0648 \u0631\u0626\u0648\u0633 \u062F\u06CC\u06AF\u0631 \u0628\u06A9\u0634\u06CC\u062F",pinningButton:"\u0631\u0626\u0648\u0633 \u0645\u0634\u062A\u0631\u06A9 \u0631\u0627 \u0628\u0627 \u0647\u0645 \u067E\u06CC\u0646 \u06A9\u0646\u06CC\u062F",rotateButton:"\u0686\u0631\u062E\u0634 \u0644\u0627\u06CC\u0647",drawTextButton:"\u0631\u0633\u0645 \u0645\u062A\u0646",scaleButton:"\u0645\u0642\u06CC\u0627\u0633\u200C\u06AF\u0630\u0627\u0631\u06CC",autoTracingButton:"\u0631\u062F\u06CC\u0627\u0628 \u062E\u0648\u062F\u06A9\u0627\u0631"},measurements:{totalLength:"\u0637\u0648\u0644",segmentLength:"\u0637\u0648\u0644 \u0628\u062E\u0634",area:"\u0646\u0627\u062D\u06CC\u0647",radius:"\u0634\u0639\u0627\u0639",perimeter:"\u0645\u062D\u06CC\u0637",height:"\u0627\u0631\u062A\u0641\u0627\u0639",width:"\u0639\u0631\u0636",coordinates:"\u0645\u0648\u0642\u0639\u06CC\u062A",coordinatesMarker:"\u0645\u0648\u0642\u0639\u06CC\u062A \u0646\u0634\u0627\u0646"}},pt={tooltips:{placeMarker:"\u041D\u0430\u0442\u0438\u0441\u043D\u0456\u0442\u044C, \u0449\u043E\u0431 \u043D\u0430\u043D\u0435\u0441\u0442\u0438 \u043C\u0430\u0440\u043A\u0435\u0440",firstVertex:"\u041D\u0430\u0442\u0438\u0441\u043D\u0456\u0442\u044C, \u0449\u043E\u0431 \u043D\u0430\u043D\u0435\u0441\u0442\u0438 \u043F\u0435\u0440\u0448\u0443 \u0432\u0435\u0440\u0448\u0438\u043D\u0443",continueLine:"\u041D\u0430\u0442\u0438\u0441\u043D\u0456\u0442\u044C, \u0449\u043E\u0431 \u043F\u0440\u043E\u0434\u043E\u0432\u0436\u0438\u0442\u0438 \u043C\u0430\u043B\u044E\u0432\u0430\u0442\u0438",finishLine:"\u041D\u0430\u0442\u0438\u0441\u043D\u0456\u0442\u044C \u0431\u0443\u0434\u044C-\u044F\u043A\u0438\u0439 \u0456\u0441\u043D\u0443\u044E\u0447\u0438\u0439 \u043C\u0430\u0440\u043A\u0435\u0440 \u0434\u043B\u044F \u0437\u0430\u0432\u0435\u0440\u0448\u0435\u043D\u043D\u044F",finishPoly:"\u0412\u0438\u0431\u0435\u0440\u0456\u0442\u044C \u043F\u0435\u0440\u0448\u0438\u0439 \u043C\u0430\u0440\u043A\u0435\u0440, \u0449\u043E\u0431 \u0437\u0430\u0432\u0435\u0440\u0448\u0438\u0442\u0438",finishRect:"\u041D\u0430\u0442\u0438\u0441\u043D\u0456\u0442\u044C, \u0449\u043E\u0431 \u0437\u0430\u0432\u0435\u0440\u0448\u0438\u0442\u0438",startCircle:"\u041D\u0430\u0442\u0438\u0441\u043D\u0456\u0442\u044C, \u0449\u043E\u0431 \u0434\u043E\u0434\u0430\u0442\u0438 \u0446\u0435\u043D\u0442\u0440 \u043A\u043E\u043B\u0430",finishCircle:"\u041D\u0430\u0442\u0438\u0441\u043D\u0456\u0442\u044C, \u0449\u043E\u0431 \u0437\u0430\u0432\u0435\u0440\u0448\u0438\u0442\u0438 \u043A\u043E\u043B\u043E",placeCircleMarker:"\u041D\u0430\u0442\u0438\u0441\u043D\u0456\u0442\u044C, \u0449\u043E\u0431 \u043D\u0430\u043D\u0435\u0441\u0442\u0438 \u043A\u0440\u0443\u0433\u043E\u0432\u0438\u0439 \u043C\u0430\u0440\u043A\u0435\u0440"},actions:{finish:"\u0417\u0430\u0432\u0435\u0440\u0448\u0438\u0442\u0438",cancel:"\u0412\u0456\u0434\u043C\u0456\u043D\u0438\u0442\u0438",removeLastVertex:"\u0412\u0438\u0434\u0430\u043B\u0438\u0442\u0438 \u043F\u043E\u043F\u0435\u0440\u0435\u0434\u043D\u044E \u0432\u0435\u0440\u0448\u0438\u043D\u0443"},buttonTitles:{drawMarkerButton:"\u041C\u0430\u043B\u044E\u0432\u0430\u0442\u0438 \u043C\u0430\u0440\u043A\u0435\u0440",drawPolyButton:"\u041C\u0430\u043B\u044E\u0432\u0430\u0442\u0438 \u043F\u043E\u043B\u0456\u0433\u043E\u043D",drawLineButton:"\u041C\u0430\u043B\u044E\u0432\u0430\u0442\u0438 \u043A\u0440\u0438\u0432\u0443",drawCircleButton:"\u041C\u0430\u043B\u044E\u0432\u0430\u0442\u0438 \u043A\u043E\u043B\u043E",drawRectButton:"\u041C\u0430\u043B\u044E\u0432\u0430\u0442\u0438 \u043F\u0440\u044F\u043C\u043E\u043A\u0443\u0442\u043D\u0438\u043A",editButton:"\u0420\u0435\u0434\u0430\u0433\u0443\u0432\u0430\u0442\u0438 \u0448\u0430\u0440\u0438",dragButton:"\u041F\u0435\u0440\u0435\u043D\u0435\u0441\u0442\u0438 \u0448\u0430\u0440\u0438",cutButton:"\u0412\u0438\u0440\u0456\u0437\u0430\u0442\u0438 \u0448\u0430\u0440\u0438",deleteButton:"\u0412\u0438\u0434\u0430\u043B\u0438\u0442\u0438 \u0448\u0430\u0440\u0438",drawCircleMarkerButton:"\u041C\u0430\u043B\u044E\u0432\u0430\u0442\u0438 \u043A\u0440\u0443\u0433\u043E\u0432\u0438\u0439 \u043C\u0430\u0440\u043A\u0435\u0440",snappingButton:"\u041F\u0440\u0438\u0432\u2019\u044F\u0437\u0430\u0442\u0438 \u043F\u0435\u0440\u0435\u0442\u044F\u0433\u043D\u0443\u0442\u0438\u0439 \u043C\u0430\u0440\u043A\u0435\u0440 \u0434\u043E \u0456\u043D\u0448\u0438\u0445 \u0448\u0430\u0440\u0456\u0432 \u0442\u0430 \u0432\u0435\u0440\u0448\u0438\u043D",pinningButton:"\u0417\u0432'\u044F\u0437\u0430\u0442\u0438 \u0441\u043F\u0456\u043B\u044C\u043D\u0456 \u0432\u0435\u0440\u0448\u0438\u043D\u0438 \u0440\u0430\u0437\u043E\u043C",rotateButton:"\u041F\u043E\u0432\u0435\u0440\u043D\u0443\u0442\u0438 \u0448\u0430\u0440"}},Le={tooltips:{placeMarker:"\u0130\u015Faret\xE7i yerle\u015Ftirmek i\xE7in t\u0131klay\u0131n",firstVertex:"\u0130lk tepe noktas\u0131n\u0131 yerle\u015Ftirmek i\xE7in t\u0131klay\u0131n",continueLine:"\xC7izime devam etmek i\xE7in t\u0131klay\u0131n",finishLine:"Bitirmek i\xE7in mevcut herhangi bir i\u015Faret\xE7iyi t\u0131klay\u0131n",finishPoly:"Bitirmek i\xE7in ilk i\u015Faret\xE7iyi t\u0131klay\u0131n",finishRect:"Bitirmek i\xE7in t\u0131klay\u0131n",startCircle:"Daire merkezine yerle\u015Ftirmek i\xE7in t\u0131klay\u0131n",finishCircle:"Daireyi bitirmek i\xE7in t\u0131klay\u0131n",placeCircleMarker:"Daire i\u015Faret\xE7isi yerle\u015Ftirmek i\xE7in t\u0131klay\u0131n"},actions:{finish:"Bitir",cancel:"\u0130ptal",removeLastVertex:"Son k\xF6\u015Feyi kald\u0131r"},buttonTitles:{drawMarkerButton:"\xC7izim \u0130\u015Faret\xE7isi",drawPolyButton:"\xC7okgenler \xE7iz",drawLineButton:"\xC7oklu \xE7izgi \xE7iz",drawCircleButton:"\xC7ember \xE7iz",drawRectButton:"Dikd\xF6rtgen \xE7iz",editButton:"Katmanlar\u0131 d\xFCzenle",dragButton:"Katmanlar\u0131 s\xFCr\xFCkle",cutButton:"Katmanlar\u0131 kes",deleteButton:"Katmanlar\u0131 kald\u0131r",drawCircleMarkerButton:"Daire i\u015Faret\xE7isi \xE7iz",snappingButton:"S\xFCr\xFCklenen i\u015Faret\xE7iyi di\u011Fer katmanlara ve k\xF6\u015Felere yap\u0131\u015Ft\u0131r",pinningButton:"Payla\u015F\u0131lan k\xF6\u015Feleri birbirine sabitle",rotateButton:"Katman\u0131 d\xF6nd\xFCr"}},Et={tooltips:{placeMarker:"Kliknut\xEDm vytvo\u0159\xEDte zna\u010Dku",firstVertex:"Kliknut\xEDm vytvo\u0159\xEDte prvn\xED objekt",continueLine:"Kliknut\xEDm pokra\u010Dujte v kreslen\xED",finishLine:"Kliknut\xED na libovolnou existuj\xEDc\xED zna\u010Dku pro dokon\u010Den\xED",finishPoly:"Vyberte prvn\xED bod pro dokon\u010Den\xED",finishRect:"Klikn\u011Bte pro dokon\u010Den\xED",startCircle:"Kliknut\xEDm p\u0159idejte st\u0159ed kruhu",finishCircle:"\u041D\u0430\u0436\u043C\u0438\u0442\u0435, \u0447\u0442\u043E\u0431\u044B \u0437\u0430\u0434\u0430\u0442\u044C \u0440\u0430\u0434\u0438\u0443\u0441",placeCircleMarker:"Kliknut\xEDm nastavte polom\u011Br"},actions:{finish:"Dokon\u010Dit",cancel:"Zru\u0161it",removeLastVertex:"Zru\u0161it posledn\xED akci"},buttonTitles:{drawMarkerButton:"P\u0159idat zna\u010Dku",drawPolyButton:"Nakreslit polygon",drawLineButton:"Nakreslit k\u0159ivku",drawCircleButton:"Nakreslit kruh",drawRectButton:"Nakreslit obd\xE9ln\xEDk",editButton:"Upravit vrstvu",dragButton:"P\u0159eneste vrstvu",cutButton:"Vyjmout vrstvu",deleteButton:"Smazat vrstvu",drawCircleMarkerButton:"P\u0159idat kruhovou zna\u010Dku",snappingButton:"Nav\xE1zat ta\u017Enou zna\u010Dku k dal\u0161\xEDm vrstv\xE1m a vrchol\u016Fm",pinningButton:"Spojit spole\u010Dn\xE9 body dohromady",rotateButton:"Oto\u010Dte vrstvu"}},Rr={tooltips:{placeMarker:"\u30AF\u30EA\u30C3\u30AF\u3057\u3066\u30DE\u30FC\u30AB\u30FC\u3092\u914D\u7F6E",firstVertex:"\u30AF\u30EA\u30C3\u30AF\u3057\u3066\u6700\u521D\u306E\u9802\u70B9\u3092\u914D\u7F6E",continueLine:"\u30AF\u30EA\u30C3\u30AF\u3057\u3066\u63CF\u753B\u3092\u7D9A\u3051\u308B",finishLine:"\u4EFB\u610F\u306E\u30DE\u30FC\u30AB\u30FC\u3092\u30AF\u30EA\u30C3\u30AF\u3057\u3066\u7D42\u4E86",finishPoly:"\u6700\u521D\u306E\u30DE\u30FC\u30AB\u30FC\u3092\u30AF\u30EA\u30C3\u30AF\u3057\u3066\u7D42\u4E86",finishRect:"\u30AF\u30EA\u30C3\u30AF\u3057\u3066\u7D42\u4E86",startCircle:"\u30AF\u30EA\u30C3\u30AF\u3057\u3066\u5186\u306E\u4E2D\u5FC3\u3092\u914D\u7F6E",finishCircle:"\u30AF\u30EA\u30C3\u30AF\u3057\u3066\u5186\u306E\u63CF\u753B\u3092\u7D42\u4E86",placeCircleMarker:"\u30AF\u30EA\u30C3\u30AF\u3057\u3066\u5186\u30DE\u30FC\u30AB\u30FC\u3092\u914D\u7F6E",placeText:"\u30AF\u30EA\u30C3\u30AF\u3057\u3066\u30C6\u30AD\u30B9\u30C8\u3092\u914D\u7F6E"},actions:{finish:"\u7D42\u4E86",cancel:"\u30AD\u30E3\u30F3\u30BB\u30EB",removeLastVertex:"\u6700\u5F8C\u306E\u9802\u70B9\u3092\u524A\u9664"},buttonTitles:{drawMarkerButton:"\u30DE\u30FC\u30AB\u30FC\u3092\u63CF\u753B",drawPolyButton:"\u30DD\u30EA\u30B4\u30F3\u3092\u63CF\u753B",drawLineButton:"\u6298\u308C\u7DDA\u3092\u63CF\u753B",drawCircleButton:"\u5186\u3092\u63CF\u753B",drawRectButton:"\u77E9\u5F62\u3092\u63CF\u753B",editButton:"\u30EC\u30A4\u30E4\u30FC\u3092\u7DE8\u96C6",dragButton:"\u30EC\u30A4\u30E4\u30FC\u3092\u30C9\u30E9\u30C3\u30B0",cutButton:"\u30EC\u30A4\u30E4\u30FC\u3092\u5207\u308A\u53D6\u308A",deleteButton:"\u30EC\u30A4\u30E4\u30FC\u3092\u524A\u9664",drawCircleMarkerButton:"\u5186\u30DE\u30FC\u30AB\u30FC\u3092\u63CF\u753B",snappingButton:"\u30C9\u30E9\u30C3\u30B0\u3057\u305F\u30DE\u30FC\u30AB\u30FC\u3092\u4ED6\u306E\u30EC\u30A4\u30E4\u30FC\u3084\u9802\u70B9\u306B\u30B9\u30CA\u30C3\u30D7\u3059\u308B",pinningButton:"\u5171\u6709\u3059\u308B\u9802\u70B9\u3092\u540C\u6642\u306B\u52D5\u304B\u3059",rotateButton:"\u30EC\u30A4\u30E4\u30FC\u3092\u56DE\u8EE2",drawTextButton:"\u30C6\u30AD\u30B9\u30C8\u3092\u63CF\u753B"}},Sn={tooltips:{placeMarker:"Klikkaa asettaaksesi merkin",firstVertex:"Klikkaa asettaakseni ensimm\xE4isen osuuden",continueLine:"Klikkaa jatkaaksesi piirt\xE4mist\xE4",finishLine:"Klikkaa olemassa olevaa merkki\xE4 lopettaaksesi",finishPoly:"Klikkaa ensimm\xE4ist\xE4 merkki\xE4 lopettaaksesi",finishRect:"Klikkaa lopettaaksesi",startCircle:"Klikkaa asettaaksesi ympyr\xE4n keskipisteen",finishCircle:"Klikkaa lopettaaksesi ympyr\xE4n",placeCircleMarker:"Klikkaa asettaaksesi ympyr\xE4merkin",placeText:"Klikkaa asettaaksesi tekstin"},actions:{finish:"Valmis",cancel:"Peruuta",removeLastVertex:"Poista viimeinen osuus"},buttonTitles:{drawMarkerButton:"Piirr\xE4 merkkej\xE4",drawPolyButton:"Piirr\xE4 monikulmioita",drawLineButton:"Piirr\xE4 viivoja",drawCircleButton:"Piirr\xE4 ympyr\xE4",drawRectButton:"Piirr\xE4 neliskulmioita",editButton:"Muokkaa",dragButton:"Siirr\xE4",cutButton:"Leikkaa",deleteButton:"Poista",drawCircleMarkerButton:"Piirr\xE4 ympyr\xE4merkki",snappingButton:"Kiinnit\xE4 siirrett\xE4v\xE4 merkki toisiin muotoihin",pinningButton:"Kiinnit\xE4 jaetut muodot yhteen",rotateButton:"K\xE4\xE4nn\xE4",drawTextButton:"Piirr\xE4 teksti\xE4"}},Tn={tooltips:{placeMarker:"\uB9C8\uCEE4 \uC704\uCE58\uB97C \uD074\uB9AD\uD558\uC138\uC694",firstVertex:"\uCCAB\uBC88\uC9F8 \uAF2D\uC9C0\uC810 \uC704\uCE58\uC744 \uD074\uB9AD\uD558\uC138\uC694",continueLine:"\uACC4\uC18D \uADF8\uB9AC\uB824\uBA74 \uD074\uB9AD\uD558\uC138\uC694",finishLine:"\uB05D\uB0B4\uB824\uBA74 \uAE30\uC874 \uB9C8\uCEE4\uB97C \uD074\uB9AD\uD558\uC138\uC694",finishPoly:"\uB05D\uB0B4\uB824\uBA74 \uCC98\uC74C \uB9C8\uCEE4\uB97C \uD074\uB9AD\uD558\uC138\uC694",finishRect:"\uB05D\uB0B4\uB824\uBA74 \uD074\uB9AD\uD558\uC138\uC694",startCircle:"\uC6D0\uC758 \uC911\uC2EC\uC774 \uB420 \uC704\uCE58\uB97C \uD074\uB9AD\uD558\uC138\uC694",finishCircle:"\uC6D0\uC744 \uB05D\uB0B4\uB824\uBA74 \uD074\uB9AD\uD558\uC138\uC694",placeCircleMarker:"\uC6D0 \uB9C8\uCEE4 \uC704\uCE58\uB97C \uD074\uB9AD\uD558\uC138\uC694",placeText:"\uD14D\uC2A4\uD2B8 \uC704\uCE58\uB97C \uD074\uB9AD\uD558\uC138\uC694"},actions:{finish:"\uB05D\uB0B4\uAE30",cancel:"\uCDE8\uC18C",removeLastVertex:"\uB9C8\uC9C0\uB9C9 \uAF2D\uC9C0\uC810 \uC81C\uAC70"},buttonTitles:{drawMarkerButton:"\uB9C8\uCEE4 \uADF8\uB9AC\uAE30",drawPolyButton:"\uB2E4\uAC01\uD615 \uADF8\uB9AC\uAE30",drawLineButton:"\uB2E4\uAC01\uC120 \uADF8\uB9AC\uAE30",drawCircleButton:"\uC6D0 \uADF8\uB9AC\uAE30",drawRectButton:"\uC9C1\uC0AC\uAC01\uD615 \uADF8\uB9AC\uAE30",editButton:"\uB808\uC774\uC5B4 \uD3B8\uC9D1\uD558\uAE30",dragButton:"\uB808\uC774\uC5B4 \uB04C\uAE30",cutButton:"\uB808\uC774\uC5B4 \uC790\uB974\uAE30",deleteButton:"\uB808\uC774\uC5B4 \uC81C\uAC70\uD558\uAE30",drawCircleMarkerButton:"\uC6D0 \uB9C8\uCEE4 \uADF8\uB9AC\uAE30",snappingButton:"\uC7A1\uC544\uB048 \uB9C8\uCEE4\uB97C \uB2E4\uB978 \uB808\uC774\uC5B4 \uBC0F \uAF2D\uC9C0\uC810\uC5D0 \uB4E4\uB7EC\uBD99\uAC8C \uD558\uAE30",pinningButton:"\uACF5\uC720 \uAF2D\uC9C0\uC810\uC744 \uD568\uAED8 \uCC0D\uAE30",rotateButton:"\uB808\uC774\uC5B4 \uD68C\uC804\uD558\uAE30",drawTextButton:"\uD14D\uC2A4\uD2B8 \uADF8\uB9AC\uAE30"}},Dn={tooltips:{placeMarker:"\u041C\u0430\u0440\u043A\u0435\u0440\u0434\u0438 \u0436\u0430\u0439\u0433\u0430\u0448\u0442\u044B\u0440\u0443\u0443 \u04AF\u0447\u04AF\u043D \u0431\u0430\u0441\u044B\u04A3\u044B\u0437",firstVertex:"\u0411\u0438\u0440\u0438\u043D\u0447\u0438 \u0447\u043E\u043A\u0443\u043D\u0443 \u0436\u0430\u0439\u0433\u0430\u0448\u0442\u044B\u0440\u0443\u0443\u043D\u0443 \u04AF\u0447\u04AF\u043D \u0431\u0430\u0441\u044B\u04A3\u044B\u0437",continueLine:"\u0421\u04AF\u0440\u04E9\u0442 \u0442\u0430\u0440\u0442\u0443\u0443\u043D\u0443 \u0443\u043B\u0430\u043D\u0442\u0443\u0443 \u04AF\u0447\u04AF\u043D \u0431\u0430\u0441\u044B\u04A3\u044B\u0437",finishLine:"\u0410\u044F\u043A\u0442\u043E\u043E \u04AF\u0447\u04AF\u043D \u0443\u0447\u0443\u0440\u0434\u0430\u0433\u044B \u043C\u0430\u0440\u043A\u0435\u0440\u0434\u0438 \u0431\u0430\u0441\u044B\u04A3\u044B\u0437",finishPoly:"\u0411\u04AF\u0442\u04AF\u0440\u04AF\u04AF \u04AF\u0447\u04AF\u043D \u0431\u0438\u0440\u0438\u043D\u0447\u0438 \u043C\u0430\u0440\u043A\u0435\u0440\u0434\u0438 \u0431\u0430\u0441\u044B\u04A3\u044B\u0437",finishRect:"\u0411\u04AF\u0442\u04AF\u0440\u04AF\u04AF \u04AF\u0447\u04AF\u043D \u0431\u0430\u0441\u044B\u04A3\u044B\u0437",startCircle:"\u0410\u0439\u043B\u0430\u043D\u0430\u043D\u044B\u043D \u0431\u043E\u0440\u0431\u043E\u0440\u0443\u043D \u0436\u0430\u0439\u0433\u0430\u0448\u0442\u044B\u0440\u0443\u0443\u043D\u0443 \u04AF\u0447\u04AF\u043D \u0431\u0430\u0441\u044B\u04A3\u044B\u0437",finishCircle:"\u0410\u0439\u043B\u0430\u043D\u0430\u043D\u044B \u0431\u04AF\u0442\u04AF\u0440\u04AF\u04AF \u04AF\u0447\u04AF\u043D \u0431\u0430\u0441\u044B\u04A3\u044B\u0437",placeCircleMarker:"\u0422\u0435\u0433\u0435\u0440\u0435\u043A \u043C\u0430\u0440\u043A\u0435\u0440\u0434\u0438 \u0436\u0430\u0439\u0433\u0430\u0448\u0442\u044B\u0440\u0443\u0443 \u04AF\u0447\u04AF\u043D \u0431\u0430\u0441\u044B\u04A3\u044B\u0437",placeText:"\u0422\u0435\u043A\u0441\u0442\u0442\u0438 \u0436\u0430\u0439\u0433\u0430\u0448\u0442\u044B\u0440\u0443\u0443 \u04AF\u0447\u04AF\u043D \u0431\u0430\u0441\u044B\u04A3\u044B\u0437"},actions:{finish:"\u0410\u044F\u0433\u044B",cancel:"\u0416\u043E\u043A \u043A\u044B\u043B\u0443\u0443",removeLastVertex:"\u0410\u043A\u044B\u0440\u043A\u044B \u0447\u043E\u043A\u0443\u043D\u0443 \u04E9\u0447\u04AF\u0440\u04AF\u04AF"},buttonTitles:{drawMarkerButton:"\u041C\u0430\u0440\u043A\u0435\u0440\u0434\u0438 \u0447\u0438\u0437\u0443\u0443",drawPolyButton:"\u041F\u043E\u043B\u0438\u0433\u043E\u043D \u0447\u0438\u0437\u0443\u0443",drawLineButton:"\u041F\u043E\u043B\u0438\u043B\u0438\u043D\u0438\u044F \u0447\u0438\u0437\u0443\u0443",drawCircleButton:"\u0414\u0430\u0439\u044B\u043D\u0434\u044B \u0447\u0438\u0437\u0443\u0443",drawRectButton:"\u041F\u0440\u044F\u043C\u043E\u0443\u0433\u043E\u043B\u044C\u043D\u0438\u043A \u0447\u0438\u0437\u0443\u0443",editButton:"\u0421\u043B\u043E\u043E\u043F\u0442\u0443 \u0442\u04AF\u0437\u04E9\u0442\u04AF\u04AF",dragButton:"\u0421\u043B\u043E\u043E\u043F\u0442\u0443 \u043A\u0430\u0440\u0430\u043F \u0441\u04AF\u0439\u043B\u04E9\u04AF",cutButton:"\u0421\u043B\u043E\u043E\u043F\u0442\u0443\u043D \u0431\u0430\u0448\u044B\u043D \u043A\u0435\u0441\u04AF\u04AF",deleteButton:"\u0421\u043B\u043E\u043E\u043F\u0442\u0443\u043D \u04E9\u0447\u04AF\u0440\u04AF\u04AF",drawCircleMarkerButton:"\u0414\u0430\u0439\u044B\u043D\u0434\u044B \u043C\u0430\u0440\u043A\u0435\u0440\u0434\u0438 \u0447\u0438\u0437\u0443\u0443",snappingButton:"\u0411\u0430\u0448\u043A\u0430 \u0441\u043B\u043E\u043E\u043F\u0442\u043E\u0440\u0434\u0443\u043D \u0436\u0430\u043D\u0430 \u0432\u0435\u0440\u0442\u0435\u043A\u0441\u0442\u0435\u0440\u0434\u0438\u043D \u0430\u0440\u0430\u0441\u044B\u043D\u0430 \u0447\u0435\u043A\u0438\u043B\u0434\u04E9\u04E9",pinningButton:"\u0411\u04E9\u043B\u04AF\u0448\u043A\u04E9\u043D \u0432\u0435\u0440\u0442\u0435\u043A\u0441\u0442\u0435\u0440\u0434\u0438 \u0431\u0438\u0440\u0433\u0435 \u0442\u0443\u0442\u0443\u0448\u0442\u0443\u0440\u0443\u0443",rotateButton:"\u0421\u043B\u043E\u043E\u043F\u0442\u0443\u043D \u04E9\u0437\u0433\u04E9\u0440\u0442\u04AF\u04AF",drawTextButton:"\u0422\u0435\u043A\u0441\u0442 \u0447\u0438\u0437\u0443\u0443",scaleButton:"\u0421\u043B\u043E\u043E\u043F\u0442\u0443\u043D \u04E9\u043B\u0447\u04E9\u043C\u04AF\u043D \u04E9\u0437\u0433\u04E9\u0440\u0442\u04AF\u04AF",autoTracingButton:"\u0410\u0432\u0442\u043E\u043C\u0430\u0442\u0442\u044B\u043A \u0442\u0438\u0437\u043C\u0435\u0433\u0438 \u0447\u0438\u0437\u0443\u0443"},measurements:{totalLength:"\u0423\u0437\u0443\u043D\u0434\u0443\u043A",segmentLength:"\u0421\u0435\u0433\u043C\u0435\u043D\u0442 \u0443\u0437\u0443\u043D\u0434\u0443\u0433\u0443",area:"\u0410\u0439\u043C\u0430\u043A",radius:"\u0420\u0430\u0434\u0438\u0443\u0441",perimeter:"\u041F\u0435\u0440\u0438\u043C\u0435\u0442\u0440",height:"\u0414\u0438\u0430\u043C\u0435\u0442\u0440",width:"\u041A\u0435\u043D\u0447\u0438\u043B\u0438\u043A",coordinates:"\u041A\u043E\u043E\u0440\u0434\u0438\u043D\u0430\u0442\u0442\u0430\u0440",coordinatesMarker:"\u041C\u0430\u0440\u043A\u0435\u0440\u0434\u0438\u043D \u043A\u043E\u043E\u0440\u0434\u0438\u043D\u0430\u0442\u0442\u0430\u0440\u044B"}},Ve=zi,Oe={en:Ri,de:oe,it:Ga,id:Ii,ro:je,ru:Ot,es:Ue,nl:mi,fr:gi,pt:Ve,pt_br:Mn,pt_pt:zi,zh:kn,zh_tw:yi,pl:Ni,sv:En,el:Bn,hu:Gi,da:Or,no:Pn,fa:Za,ua:pt,tr:Le,cz:Et,ja:Rr,fi:Sn,ko:Tn,ky:Dn},vi={_globalEditModeEnabled:!1,enableGlobalEditMode(e){let i={...e};this._globalEditModeEnabled=!0,this.Toolbar.toggleButton("editMode",this.globalEditModeEnabled()),L.PM.Utils.findLayers(this.map).forEach(r=>{this._isRelevantForEdit(r)&&r.pm.enable(i)}),this.throttledReInitEdit||(this.throttledReInitEdit=L.Util.throttle(this.handleLayerAdditionInGlobalEditMode,100,this)),this._addedLayersEdit={},this.map.on("layeradd",this._layerAddedEdit,this),this.map.on("layeradd",this.throttledReInitEdit,this),this._fireGlobalEditModeToggled(!0)},disableGlobalEditMode(){this._globalEditModeEnabled=!1,L.PM.Utils.findLayers(this.map).forEach(e=>{e.pm.disable()}),this.map.off("layeradd",this._layerAddedEdit,this),this.map.off("layeradd",this.throttledReInitEdit,this),this.Toolbar.toggleButton("editMode",this.globalEditModeEnabled()),this._fireGlobalEditModeToggled(!1)},globalEditEnabled(){return this.globalEditModeEnabled()},globalEditModeEnabled(){return this._globalEditModeEnabled},toggleGlobalEditMode(e=this.globalOptions){this.globalEditModeEnabled()?this.disableGlobalEditMode():this.enableGlobalEditMode(e)},handleLayerAdditionInGlobalEditMode(){let e=this._addedLayersEdit;if(this._addedLayersEdit={},this.globalEditModeEnabled())for(let i in e){let r=e[i];this._isRelevantForEdit(r)&&r.pm.enable({...this.globalOptions})}},_layerAddedEdit({layer:e}){this._addedLayersEdit[L.stamp(e)]=e},_isRelevantForEdit(e){return e.pm&&!(e instanceof L.LayerGroup)&&(!L.PM.optIn&&!e.options.pmIgnore||L.PM.optIn&&e.options.pmIgnore===!1)&&!e._pmTempLayer&&e.pm.options.allowEditing}},Zt=vi,He={_globalDragModeEnabled:!1,enableGlobalDragMode(){let e=L.PM.Utils.findLayers(this.map);this._globalDragModeEnabled=!0,this._addedLayersDrag={},e.forEach(i=>{this._isRelevantForDrag(i)&&i.pm.enableLayerDrag()}),this.throttledReInitDrag||(this.throttledReInitDrag=L.Util.throttle(this.reinitGlobalDragMode,100,this)),this.map.on("layeradd",this._layerAddedDrag,this),this.map.on("layeradd",this.throttledReInitDrag,this),this.Toolbar.toggleButton("dragMode",this.globalDragModeEnabled()),this._fireGlobalDragModeToggled(!0)},disableGlobalDragMode(){let e=L.PM.Utils.findLayers(this.map);this._globalDragModeEnabled=!1,e.forEach(i=>{i.pm.disableLayerDrag()}),this.map.off("layeradd",this._layerAddedDrag,this),this.map.off("layeradd",this.throttledReInitDrag,this),this.Toolbar.toggleButton("dragMode",this.globalDragModeEnabled()),this._fireGlobalDragModeToggled(!1)},globalDragModeEnabled(){return!!this._globalDragModeEnabled},toggleGlobalDragMode(){this.globalDragModeEnabled()?this.disableGlobalDragMode():this.enableGlobalDragMode()},reinitGlobalDragMode(){let e=this._addedLayersDrag;if(this._addedLayersDrag={},this.globalDragModeEnabled())for(let i in e){let r=e[i];this._isRelevantForDrag(r)&&r.pm.enableLayerDrag()}},_layerAddedDrag({layer:e}){this._addedLayersDrag[L.stamp(e)]=e},_isRelevantForDrag(e){return e.pm&&!(e instanceof L.LayerGroup)&&(!L.PM.optIn&&!e.options.pmIgnore||L.PM.optIn&&e.options.pmIgnore===!1)&&!e._pmTempLayer&&e.pm.options.draggable}},Ir=He,zr={_globalRemovalModeEnabled:!1,enableGlobalRemovalMode(){this._globalRemovalModeEnabled=!0,this.map.eachLayer(e=>{this._isRelevantForRemoval(e)&&(e.pm.enabled()&&e.pm.disable(),e.on("click",this.removeLayer,this))}),this.throttledReInitRemoval||(this.throttledReInitRemoval=L.Util.throttle(this.handleLayerAdditionInGlobalRemovalMode,100,this)),this._addedLayersRemoval={},this.map.on("layeradd",this._layerAddedRemoval,this),this.map.on("layeradd",this.throttledReInitRemoval,this),this.Toolbar.toggleButton("removalMode",this.globalRemovalModeEnabled()),this._fireGlobalRemovalModeToggled(!0)},disableGlobalRemovalMode(){this._globalRemovalModeEnabled=!1,this.map.eachLayer(e=>{e.off("click",this.removeLayer,this)}),this.map.off("layeradd",this._layerAddedRemoval,this),this.map.off("layeradd",this.throttledReInitRemoval,this),this.Toolbar.toggleButton("removalMode",this.globalRemovalModeEnabled()),this._fireGlobalRemovalModeToggled(!1)},globalRemovalEnabled(){return this.globalRemovalModeEnabled()},globalRemovalModeEnabled(){return!!this._globalRemovalModeEnabled},toggleGlobalRemovalMode(){this.globalRemovalModeEnabled()?this.disableGlobalRemovalMode():this.enableGlobalRemovalMode()},removeLayer(e){let i=e.target;this._isRelevantForRemoval(i)&&!i.pm.dragging()&&(i.removeFrom(this.map.pm._getContainingLayer()),i.remove(),i instanceof L.LayerGroup?(this._fireRemoveLayerGroup(i),this._fireRemoveLayerGroup(this.map,i)):(i.pm._fireRemove(i),i.pm._fireRemove(this.map,i)))},_isRelevantForRemoval(e){return e.pm&&!(e instanceof L.LayerGroup)&&(!L.PM.optIn&&!e.options.pmIgnore||L.PM.optIn&&e.options.pmIgnore===!1)&&!e._pmTempLayer&&e.pm.options.allowRemoval},handleLayerAdditionInGlobalRemovalMode(){let e=this._addedLayersRemoval;if(this._addedLayersRemoval={},this.globalRemovalModeEnabled())for(let i in e){let r=e[i];this._isRelevantForRemoval(r)&&(r.pm.enabled()&&r.pm.disable(),r.on("click",this.removeLayer,this))}},_layerAddedRemoval({layer:e}){this._addedLayersRemoval[L.stamp(e)]=e}},ja=zr,Nr={_globalRotateModeEnabled:!1,enableGlobalRotateMode(){this._globalRotateModeEnabled=!0,L.PM.Utils.findLayers(this.map).filter(e=>e instanceof L.Polyline).forEach(e=>{this._isRelevantForRotate(e)&&e.pm.enableRotate()}),this.throttledReInitRotate||(this.throttledReInitRotate=L.Util.throttle(this.handleLayerAdditionInGlobalRotateMode,100,this)),this._addedLayersRotate={},this.map.on("layeradd",this._layerAddedRotate,this),this.map.on("layeradd",this.throttledReInitRotate,this),this.Toolbar.toggleButton("rotateMode",this.globalRotateModeEnabled()),this._fireGlobalRotateModeToggled()},disableGlobalRotateMode(){this._globalRotateModeEnabled=!1,L.PM.Utils.findLayers(this.map).filter(e=>e instanceof L.Polyline).forEach(e=>{e.pm.disableRotate()}),this.map.off("layeradd",this._layerAddedRotate,this),this.map.off("layeradd",this.throttledReInitRotate,this),this.Toolbar.toggleButton("rotateMode",this.globalRotateModeEnabled()),this._fireGlobalRotateModeToggled()},globalRotateModeEnabled(){return!!this._globalRotateModeEnabled},toggleGlobalRotateMode(){this.globalRotateModeEnabled()?this.disableGlobalRotateMode():this.enableGlobalRotateMode()},_isRelevantForRotate(e){return e.pm&&e instanceof L.Polyline&&!(e instanceof L.LayerGroup)&&(!L.PM.optIn&&!e.options.pmIgnore||L.PM.optIn&&e.options.pmIgnore===!1)&&!e._pmTempLayer&&e.pm.options.allowRotation},handleLayerAdditionInGlobalRotateMode(){let e=this._addedLayersRotate;if(this._addedLayersRotate={},this.globalRotateModeEnabled())for(let i in e){let r=e[i];this._isRelevantForRemoval(r)&&r.pm.enableRotate()}},_layerAddedRotate({layer:e}){this._addedLayersRotate[L.stamp(e)]=e}},An=Nr,Ua=$($e()),Gr={_fireDrawStart(e="Draw",i={}){this.__fire(this._map,"pm:drawstart",{shape:this._shape,workingLayer:this._layer},e,i)},_fireDrawEnd(e="Draw",i={}){this.__fire(this._map,"pm:drawend",{shape:this._shape},e,i)},_fireCreate(e,i="Draw",r={}){this.__fire(this._map,"pm:create",{shape:this._shape,marker:e,layer:e},i,r)},_fireCenterPlaced(e="Draw",i={}){let r=e==="Draw"?this._layer:void 0,a=e!=="Draw"?this._layer:void 0;this.__fire(this._layer,"pm:centerplaced",{shape:this._shape,workingLayer:r,layer:a,latlng:this._layer.getLatLng()},e,i)},_fireCut(e,i,r,a="Draw",s={}){this.__fire(e,"pm:cut",{shape:this._shape,layer:i,originalLayer:r},a,s)},_fireEdit(e=this._layer,i="Edit",r={}){this.__fire(e,"pm:edit",{layer:this._layer,shape:this.getShape()},i,r)},_fireEnable(e="Edit",i={}){this.__fire(this._layer,"pm:enable",{layer:this._layer,shape:this.getShape()},e,i)},_fireDisable(e="Edit",i={}){this.__fire(this._layer,"pm:disable",{layer:this._layer,shape:this.getShape()},e,i)},_fireUpdate(e="Edit",i={}){this.__fire(this._layer,"pm:update",{layer:this._layer,shape:this.getShape()},e,i)},_fireMarkerDragStart(e,i=void 0,r="Edit",a={}){this.__fire(this._layer,"pm:markerdragstart",{layer:this._layer,markerEvent:e,shape:this.getShape(),indexPath:i},r,a)},_fireMarkerDrag(e,i=void 0,r="Edit",a={}){this.__fire(this._layer,"pm:markerdrag",{layer:this._layer,markerEvent:e,shape:this.getShape(),indexPath:i},r,a)},_fireMarkerDragEnd(e,i=void 0,r=void 0,a="Edit",s={}){this.__fire(this._layer,"pm:markerdragend",{layer:this._layer,markerEvent:e,shape:this.getShape(),indexPath:i,intersectionReset:r},a,s)},_fireDragStart(e="Edit",i={}){this.__fire(this._layer,"pm:dragstart",{layer:this._layer,shape:this.getShape()},e,i)},_fireDrag(e,i="Edit",r={}){this.__fire(this._layer,"pm:drag",{...e,shape:this.getShape()},i,r)},_fireDragEnd(e="Edit",i={}){this.__fire(this._layer,"pm:dragend",{layer:this._layer,shape:this.getShape()},e,i)},_fireDragEnable(e="Edit",i={}){this.__fire(this._layer,"pm:dragenable",{layer:this._layer,shape:this.getShape()},e,i)},_fireDragDisable(e="Edit",i={}){this.__fire(this._layer,"pm:dragdisable",{layer:this._layer,shape:this.getShape()},e,i)},_fireRemove(e,i=e,r="Edit",a={}){this.__fire(e,"pm:remove",{layer:i,shape:this.getShape()},r,a)},_fireVertexAdded(e,i,r,a="Edit",s={}){this.__fire(this._layer,"pm:vertexadded",{layer:this._layer,workingLayer:this._layer,marker:e,indexPath:i,latlng:r,shape:this.getShape()},a,s)},_fireVertexRemoved(e,i,r="Edit",a={}){this.__fire(this._layer,"pm:vertexremoved",{layer:this._layer,marker:e,indexPath:i,shape:this.getShape()},r,a)},_fireVertexClick(e,i,r="Edit",a={}){this.__fire(this._layer,"pm:vertexclick",{layer:this._layer,markerEvent:e,indexPath:i,shape:this.getShape()},r,a)},_fireIntersect(e,i=this._layer,r="Edit",a={}){this.__fire(i,"pm:intersect",{layer:this._layer,intersection:e,shape:this.getShape()},r,a)},_fireLayerReset(e,i,r="Edit",a={}){this.__fire(this._layer,"pm:layerreset",{layer:this._layer,markerEvent:e,indexPath:i,shape:this.getShape()},r,a)},_fireChange(e,i="Edit",r={}){this.__fire(this._layer,"pm:change",{layer:this._layer,latlngs:e,shape:this.getShape()},i,r)},_fireTextChange(e,i="Edit",r={}){this.__fire(this._layer,"pm:textchange",{layer:this._layer,text:e,shape:this.getShape()},i,r)},_fireTextFocus(e="Edit",i={}){this.__fire(this._layer,"pm:textfocus",{layer:this._layer,shape:this.getShape()},e,i)},_fireTextBlur(e="Edit",i={}){this.__fire(this._layer,"pm:textblur",{layer:this._layer,shape:this.getShape()},e,i)},_fireSnapDrag(e,i,r="Snapping",a={}){this.__fire(e,"pm:snapdrag",i,r,a)},_fireSnap(e,i,r="Snapping",a={}){this.__fire(e,"pm:snap",i,r,a)},_fireUnsnap(e,i,r="Snapping",a={}){this.__fire(e,"pm:unsnap",i,r,a)},_fireRotationEnable(e,i,r="Rotation",a={}){this.__fire(e,"pm:rotateenable",{layer:this._layer,helpLayer:this._rotatePoly,shape:this.getShape()},r,a)},_fireRotationDisable(e,i="Rotation",r={}){this.__fire(e,"pm:rotatedisable",{layer:this._layer,shape:this.getShape()},i,r)},_fireRotationStart(e,i,r="Rotation",a={}){this.__fire(e,"pm:rotatestart",{layer:this._rotationLayer,helpLayer:this._layer,startAngle:this._startAngle,originLatLngs:i},r,a)},_fireRotation(e,i,r,a=this._rotationLayer,s="Rotation",l={}){this.__fire(e,"pm:rotate",{layer:a,helpLayer:this._layer,startAngle:this._startAngle,angle:a.pm.getAngle(),angleDiff:i,oldLatLngs:r,newLatLngs:a.getLatLngs()},s,l)},_fireRotationEnd(e,i,r,a="Rotation",s={}){this.__fire(e,"pm:rotateend",{layer:this._rotationLayer,helpLayer:this._layer,startAngle:i,angle:this._rotationLayer.pm.getAngle(),originLatLngs:r,newLatLngs:this._rotationLayer.getLatLngs()},a,s)},_fireActionClick(e,i,r,a="Toolbar",s={}){this.__fire(this._map,"pm:actionclick",{text:e.text,action:e,btnName:i,button:r},a,s)},_fireButtonClick(e,i,r="Toolbar",a={}){this.__fire(this._map,"pm:buttonclick",{btnName:e,button:i},r,a)},_fireLangChange(e,i,r,a,s="Global",l={}){this.__fire(this.map,"pm:langchange",{oldLang:e,activeLang:i,fallback:r,translations:a},s,l)},_fireGlobalDragModeToggled(e,i="Global",r={}){this.__fire(this.map,"pm:globaldragmodetoggled",{enabled:e,map:this.map},i,r)},_fireGlobalEditModeToggled(e,i="Global",r={}){this.__fire(this.map,"pm:globaleditmodetoggled",{enabled:e,map:this.map},i,r)},_fireGlobalRemovalModeToggled(e,i="Global",r={}){this.__fire(this.map,"pm:globalremovalmodetoggled",{enabled:e,map:this.map},i,r)},_fireGlobalCutModeToggled(e="Global",i={}){this.__fire(this._map,"pm:globalcutmodetoggled",{enabled:!!this._enabled,map:this._map},e,i)},_fireGlobalDrawModeToggled(e="Global",i={}){this.__fire(this._map,"pm:globaldrawmodetoggled",{enabled:this._enabled,shape:this._shape,map:this._map},e,i)},_fireGlobalRotateModeToggled(e="Global",i={}){this.__fire(this.map,"pm:globalrotatemodetoggled",{enabled:this.globalRotateModeEnabled(),map:this.map},e,i)},_fireRemoveLayerGroup(e,i=e,r="Edit",a={}){this.__fire(e,"pm:remove",{layer:i,shape:void 0},r,a)},_fireKeyeventEvent(e,i,r,a="Global",s={}){this.__fire(this.map,"pm:keyevent",{event:e,eventType:i,focusOn:r},a,s)},__fire(e,i,r,a,s={}){r=(0,Ua.default)(r,s,{source:a}),L.PM.Utils._fireEvent(e,i,r)}},mt=Gr,Va=()=>({_lastEvents:{keydown:void 0,keyup:void 0,current:void 0},_initKeyListener(e){this.map=e,L.DomEvent.on(document,"keydown keyup",this._onKeyListener,this),L.DomEvent.on(window,"blur",this._onBlur,this),e.once("unload",this._unbindKeyListenerEvents,this)},_unbindKeyListenerEvents(){L.DomEvent.off(document,"keydown keyup",this._onKeyListener,this),L.DomEvent.off(window,"blur",this._onBlur,this)},_onKeyListener(e){let i="document";this.map.getContainer().contains(e.target)&&(i="map");let r={event:e,eventType:e.type,focusOn:i};this._lastEvents[e.type]=r,this._lastEvents.current=r,this.map.pm._fireKeyeventEvent(e,e.type,i)},_onBlur(e){e.altKey=!1;let i={event:e,eventType:e.type,focusOn:"document"};this._lastEvents[e.type]=i,this._lastEvents.current=i},getLastKeyEvent(e="current"){return this._lastEvents[e]},isShiftKeyPressed(){return this._lastEvents.current?.event.shiftKey},isAltKeyPressed(){return this._lastEvents.current?.event.altKey},isCtrlKeyPressed(){return this._lastEvents.current?.event.ctrlKey},isMetaKeyPressed(){return this._lastEvents.current?.event.metaKey},getPressedKey(){return this._lastEvents.current?.event.key}}),fe=Va,Xe=$(Mt());function yt(e){let i=L.PM.activeLang;return(0,Xe.default)(Oe[i],e)||(0,Xe.default)(Oe.en,e)||e}function Fn(e){for(let i=0;i<e.length;i+=1){let r=e[i];if(Array.isArray(r)){if(Fn(r))return!0}else if(r!=null&&r!=="")return!0}return!1}function Qe(e){return e.reduce((i,r)=>{if(r.length!==0){let a=Array.isArray(r)?Qe(r):r;Array.isArray(a)?a.length!==0&&i.push(a):i.push(a)}return i},[])}function Ha(e,i,r){let a={a:L.CRS.Earth.R,b:63567523142e-4,f:.0033528106647474805},{a:s,b:l,f:h}=a,d=e.lng,f=e.lat,m=r,E=Math.PI,P=i*E/180,j=Math.sin(P),R=Math.cos(P),W=(1-h)*Math.tan(f*E/180),X=1/Math.sqrt(1+W*W),nt=W*X,vt=Math.atan2(W,R),k=X*j,S=1-k*k,O=S*(s*s-l*l)/(l*l),K=1+O/16384*(4096+O*(-768+O*(320-175*O))),I=O/1024*(256+O*(-128+O*(74-47*O))),U=m/(l*K),p=2*Math.PI,y,x,b;for(;Math.abs(U-p)>1e-12;){y=Math.cos(2*vt+U),x=Math.sin(U),b=Math.cos(U);let J=I*x*(y+I/4*(b*(-1+2*y*y)-I/6*y*(-3+4*x*x)*(-3+4*y*y)));p=U,U=m/(l*K)+J}let C=nt*x-X*b*R,w=Math.atan2(nt*b+X*x*R,(1-h)*Math.sqrt(k*k+C*C)),D=Math.atan2(x*j,X*b-nt*x*R),T=h/16*S*(4+h*(4-3*S)),F=D-(1-T)*h*k*(U+T*x*(y+T*b*(-1+2*y*y))),G=d+F*180/E,N=w*180/E;return L.latLng(G,N)}function On(e,i,r,a,s=!0){let l,h,d,f=[];for(let m=0;m<r;m+=1){if(s)l=m*360/r+a,h=Ha(e,l,i),d=L.latLng(h.lng,h.lat);else{let E=e.lat+Math.cos(2*m*Math.PI/r)*i,P=e.lng+Math.sin(2*m*Math.PI/r)*i;d=L.latLng(E,P)}f.push(d)}return f}function Ka(e,i,r){i=(i+360)%360;let a=Math.PI/180,s=180/Math.PI,{R:l}=L.CRS.Earth,h=e.lng*a,d=e.lat*a,f=i*a,m=Math.sin(d),E=Math.cos(d),P=Math.cos(r/l),j=Math.sin(r/l),R=Math.asin(m*P+E*j*Math.cos(f)),W=h+Math.atan2(Math.sin(f)*j*E,P-m*Math.sin(R));W*=s;let X=W-360,nt=W<-180?W+360:W;return W=W>180?X:nt,L.latLng([R*s,W])}function Rn(e,i,r){let a=e.latLngToContainerPoint(i),s=e.latLngToContainerPoint(r),l=Math.atan2(s.y-a.y,s.x-a.x)*180/Math.PI+90;return l+=l<0?360:0,l}function ti(e,i,r,a){let s=Rn(e,i,r);return Ka(i,s,a)}function qa(e,i,r="asc"){if(!i||Object.keys(i).length===0)return(f,m)=>f-m;let a=Object.keys(i),s,l=a.length-1,h={};for(;l>=0;)s=a[l],h[s.toLowerCase()]=i[s],l-=1;function d(f){if(f instanceof L.Marker)return"Marker";if(f instanceof L.Circle)return"Circle";if(f instanceof L.CircleMarker)return"CircleMarker";if(f instanceof L.Rectangle)return"Rectangle";if(f instanceof L.Polygon)return"Polygon";if(f instanceof L.Polyline)return"Line"}return(f,m)=>{let E,P;if(e==="instanceofShape"){if(E=d(f.layer).toLowerCase(),P=d(m.layer).toLowerCase(),!E||!P)return 0}else{if(!f.hasOwnProperty(e)||!m.hasOwnProperty(e))return 0;E=f[e].toLowerCase(),P=m[e].toLowerCase()}let j=E in h?h[E]:Number.MAX_SAFE_INTEGER,R=P in h?h[P]:Number.MAX_SAFE_INTEGER,W=0;return j<R?W=-1:j>R&&(W=1),r==="desc"?W*-1:W}}function jt(e,i=e.getLatLngs()){return e instanceof L.Polygon?L.polygon(i).getLatLngs():L.polyline(i).getLatLngs()}function Zr(e,i){if(i.options.crs?.projection?.MAX_LATITUDE){let r=i.options.crs?.projection?.MAX_LATITUDE;e.lat=Math.max(Math.min(r,e.lat),-r)}return e}function Ke(e){return e.options.renderer||e._map&&(e._map._getPaneRenderer(e.options.pane)||e._map.options.renderer||e._map._renderer)||e._renderer}var Re=L.Class.extend({includes:[Zt,Ir,ja,An,mt],initialize(e){this.map=e,this.Draw=new L.PM.Draw(e),this.Toolbar=new L.PM.Toolbar(e),this.Keyboard=fe(),this.globalOptions={snappable:!0,layerGroup:void 0,snappingOrder:["Marker","CircleMarker","Circle","Line","Polygon","Rectangle"],panes:{vertexPane:"markerPane",layerPane:"overlayPane",markerPane:"markerPane"},draggable:!0},this.Keyboard._initKeyListener(e)},setLang(e="en",i,r="en"){if(e=e.trim().toLowerCase(),!/^[a-z]{2}$/.test(e)){let s=e.replace(/[-_\s]/g,"-").replace(/^(\w{2})$/,"$1-").match(/([a-z]{2})-?([a-z]{2})?/);if(s){let l=[`${s[1]}_${s[2]}`,`${s[1]}`];for(let h of l)if(Oe[h]){e=h;break}}}let a=L.PM.activeLang;i&&(Oe[e]=(0,Oi.default)(Oe[r],i)),L.PM.activeLang=e,this.map.pm.Toolbar.reinit(),this._fireLangChange(a,e,r,Oe[e])},addControls(e){this.Toolbar.addControls(e)},removeControls(){this.Toolbar.removeControls()},toggleControls(){this.Toolbar.toggleControls()},controlsVisible(){return this.Toolbar.isVisible},enableDraw(e="Polygon",i){e==="Poly"&&(e="Polygon"),this.Draw.enable(e,i)},disableDraw(e="Polygon"){e==="Poly"&&(e="Polygon"),this.Draw.disable(e)},setPathOptions(e,i={}){let r=i.ignoreShapes||[],a=i.merge||!1;this.map.pm.Draw.shapes.forEach(s=>{r.indexOf(s)===-1&&this.map.pm.Draw[s].setPathOptions(e,a)})},getGlobalOptions(){return this.globalOptions},setGlobalOptions(e){let i=(0,Oi.default)(this.globalOptions,e);i.editable&&(i.resizeableCircleMarker=i.editable,delete i.editable);let r=!1;this.map.pm.Draw.CircleMarker.enabled()&&!!this.map.pm.Draw.CircleMarker.options.resizeableCircleMarker!=!!i.resizeableCircleMarker&&(this.map.pm.Draw.CircleMarker.disable(),r=!0);let a=!1;this.map.pm.Draw.Circle.enabled()&&!!this.map.pm.Draw.Circle.options.resizeableCircle!=!!i.resizeableCircle&&(this.map.pm.Draw.Circle.disable(),a=!0),this.map.pm.Draw.shapes.forEach(s=>{this.map.pm.Draw[s].setOptions(i)}),r&&this.map.pm.Draw.CircleMarker.enable(),a&&this.map.pm.Draw.Circle.enable(),L.PM.Utils.findLayers(this.map).forEach(s=>{s.pm.setOptions(i)}),this.map.fire("pm:globaloptionschanged"),this.globalOptions=i,this.applyGlobalOptions()},applyGlobalOptions(){L.PM.Utils.findLayers(this.map).forEach(e=>{e.pm.enabled()&&e.pm.applyOptions()})},globalDrawModeEnabled(){return!!this.Draw.getActiveShape()},globalCutModeEnabled(){return!!this.Draw.Cut.enabled()},enableGlobalCutMode(e){return this.Draw.Cut.enable(e)},toggleGlobalCutMode(e){return this.Draw.Cut.toggle(e)},disableGlobalCutMode(){return this.Draw.Cut.disable()},getGeomanLayers(e=!1){let i=L.PM.Utils.findLayers(this.map);if(!e)return i;let r=L.featureGroup();return r._pmTempLayer=!0,i.forEach(a=>{r.addLayer(a)}),r},getGeomanDrawLayers(e=!1){let i=L.PM.Utils.findLayers(this.map).filter(a=>a._drawnByGeoman===!0);if(!e)return i;let r=L.featureGroup();return r._pmTempLayer=!0,i.forEach(a=>{r.addLayer(a)}),r},_getContainingLayer(){return this.globalOptions.layerGroup&&this.globalOptions.layerGroup instanceof L.LayerGroup?this.globalOptions.layerGroup:this.map},_isCRSSimple(){return this.map.options.crs===L.CRS.Simple},_touchEventCounter:0,_addTouchEvents(e){this._touchEventCounter===0&&(L.DomEvent.on(e,"touchmove",this._canvasTouchMove,this),L.DomEvent.on(e,"touchstart touchend touchcancel",this._canvasTouchClick,this)),this._touchEventCounter+=1},_removeTouchEvents(e){this._touchEventCounter===1&&(L.DomEvent.off(e,"touchmove",this._canvasTouchMove,this),L.DomEvent.off(e,"touchstart touchend touchcancel",this._canvasTouchClick,this)),this._touchEventCounter=this._touchEventCounter<=1?0:this._touchEventCounter-1},_canvasTouchMove(e){Ke(this.map)._onMouseMove(this._createMouseEvent("mousemove",e))},_canvasTouchClick(e){let i="";e.type==="touchstart"||e.type==="pointerdown"?i="mousedown":(e.type==="touchend"||e.type==="pointerup"||e.type==="touchcancel"||e.type==="pointercancel")&&(i="mouseup"),i&&Ke(this.map)._onClick(this._createMouseEvent(i,e))},_createMouseEvent(e,i){let r,a=i.touches[0]||i.changedTouches[0];try{r=new MouseEvent(e,{bubbles:i.bubbles,cancelable:i.cancelable,view:i.view,detail:a.detail,screenX:a.screenX,screenY:a.screenY,clientX:a.clientX,clientY:a.clientY,ctrlKey:i.ctrlKey,altKey:i.altKey,shiftKey:i.shiftKey,metaKey:i.metaKey,button:i.button,relatedTarget:i.relatedTarget})}catch{r=document.createEvent("MouseEvents"),r.initMouseEvent(e,i.bubbles,i.cancelable,i.view,a.detail,a.screenX,a.screenY,a.clientX,a.clientY,i.ctrlKey,i.altKey,i.shiftKey,i.metaKey,i.button,i.relatedTarget)}return r}}),jr=Re,Ur=L.Control.extend({includes:[mt],options:{position:"topleft",disableByOtherButtons:!0},initialize(e){this._button=L.Util.extend({},this.options,e)},onAdd(e){return this._map=e,this._map.pm.Toolbar.options.oneBlock?this._container=this._map.pm.Toolbar._createContainer(this.options.position):this._button.tool==="edit"?this._container=this._map.pm.Toolbar.editContainer:this._button.tool==="options"?this._container=this._map.pm.Toolbar.optionsContainer:this._button.tool==="custom"?this._container=this._map.pm.Toolbar.customContainer:this._container=this._map.pm.Toolbar.drawContainer,this._renderButton(),this._container},_renderButton(){let e=this.buttonsDomNode;this.buttonsDomNode=this._makeButton(this._button),e?e.replaceWith(this.buttonsDomNode):this._container.appendChild(this.buttonsDomNode)},onRemove(){return this.buttonsDomNode.remove(),this._container},getText(){return this._button.text},getIconUrl(){return this._button.iconUrl},destroy(){this._button={},this._update()},toggle(e){return typeof e=="boolean"?this._button.toggleStatus=e:this._button.toggleStatus=!this._button.toggleStatus,this._applyStyleClasses(),this._updateActiveAction(this._button),this._button.toggleStatus},toggled(){return this._button.toggleStatus},onCreate(){this.toggle(!1)},disable(){this.toggle(!1),this._button.disabled=!0,this._updateDisabled()},enable(){this._button.disabled=!1,this._updateDisabled(),this._updateActiveAction(this._button)},_triggerClick(e){e&&e.preventDefault(),!this._button.disabled&&(this._button.onClick(e,{button:this,event:e}),this._clicked(e),this._button.afterClick(e,{button:this,event:e}))},_makeButton(e){let i=this.options.position.indexOf("right")>-1?"pos-right":"",r=L.DomUtil.create("div",`button-container  ${i}`,this._container);e.title&&r.setAttribute("title",e.title);let a=L.DomUtil.create("a","leaflet-buttons-control-button",r);a.setAttribute("role","button"),a.setAttribute("tabindex","0"),a.href="#";let s=L.DomUtil.create("div",`leaflet-pm-actions-container ${i}`,r),l=e.actions,h={cancel:{text:yt("actions.cancel"),title:yt("actions.cancel"),onClick(){this._triggerClick()}},finishMode:{text:yt("actions.finish"),title:yt("actions.finish"),onClick(){this._triggerClick()}},removeLastVertex:{text:yt("actions.removeLastVertex"),title:yt("actions.removeLastVertex"),onClick(){this._map.pm.Draw[e.jsClass]._removeLastVertex()}},finish:{text:yt("actions.finish"),title:yt("actions.finish"),onClick(f){this._map.pm.Draw[e.jsClass]._finishShape(f)}}};e._preparedActions=l.map(f=>{let m=typeof f=="string"?f:f.name,E;if(h[m])E=h[m];else if(f.text)E=f;else return E;let P=L.DomUtil.create("a",`leaflet-pm-action ${i} action-${m}`,s);if(P.setAttribute("role","button"),P.setAttribute("tabindex","0"),P.href="#",E.title&&(P.title=E.title),P.innerHTML=E.text,L.DomEvent.disableClickPropagation(P),L.DomEvent.on(P,"click",L.DomEvent.stop),E._node=P,!e.disabled&&E.onClick){let j=R=>{R.preventDefault();let W="",{buttons:X}=this._map.pm.Toolbar;for(let nt in X)if(X[nt]._button===e){W=nt;break}this._fireActionClick(E,W,e)};L.DomEvent.addListener(P,"click",j,this),L.DomEvent.addListener(P,"click",E.onClick,this),L.DomEvent.addListener(P,"click",()=>this._updateActiveAction(e))}return E}),this._updateActiveAction(e),e.toggleStatus&&L.DomUtil.addClass(r,"active");let d=L.DomUtil.create("div","control-icon",a);return e.iconUrl&&d.setAttribute("src",e.iconUrl),e.className&&L.DomUtil.addClass(d,e.className),L.DomEvent.disableClickPropagation(a),L.DomEvent.on(a,"click",L.DomEvent.stop),e.disabled||(L.DomEvent.addListener(a,"click",this._onBtnClick,this),L.DomEvent.addListener(a,"click",this._triggerClick,this)),e.disabled&&(L.DomUtil.addClass(a,"pm-disabled"),a.setAttribute("aria-disabled","true")),r},_applyStyleClasses(){this._container&&(!this._button.toggleStatus||this._button.cssToggle===!1?(L.DomUtil.removeClass(this.buttonsDomNode,"active"),L.DomUtil.removeClass(this._container,"activeChild")):(L.DomUtil.addClass(this.buttonsDomNode,"active"),L.DomUtil.addClass(this._container,"activeChild")))},_onBtnClick(){if(this._button.disabled)return;this._button.disableOtherButtons&&this._map.pm.Toolbar.triggerClickOnToggledButtons(this);let e="",{buttons:i}=this._map.pm.Toolbar;for(let r in i)if(i[r]._button===this._button){e=r;break}this._fireButtonClick(e,this._button)},_clicked(){this._button.doToggle&&this.toggle()},_updateDisabled(){if(!this._container)return;let e="pm-disabled",i=this.buttonsDomNode.children[0];this._button.disabled?(L.DomUtil.addClass(i,e),i.setAttribute("aria-disabled","true")):(L.DomUtil.removeClass(i,e),i.setAttribute("aria-disabled","false"))},_updateActiveAction(e){e._preparedActions?.forEach(i=>{i?._node&&(i.isActive&&i.isActive.call(this)?L.DomUtil.addClass(i._node,"active-action"):L.DomUtil.removeClass(i._node,"active-action"))})}}),In=Ur;L.Control.PMButton=In;var Wa=L.Class.extend({options:{drawMarker:!0,drawRectangle:!0,drawPolyline:!0,drawPolygon:!0,drawCircle:!0,drawCircleMarker:!0,drawText:!0,editMode:!0,dragMode:!0,cutPolygon:!0,removalMode:!0,rotateMode:!0,snappingOption:!0,drawControls:!0,editControls:!0,optionsControls:!0,customControls:!0,oneBlock:!1,position:"topleft",positions:{draw:"",edit:"",options:"",custom:""}},customButtons:[],initialize(e){this.customButtons=[],this.options.positions={draw:"",edit:"",options:"",custom:""},this.init(e)},reinit(){let e=this.isVisible;this.removeControls(),this._defineButtons(),e&&this.addControls()},init(e){this.map=e,this.buttons={},this.isVisible=!1,this.drawContainer=L.DomUtil.create("div","leaflet-pm-toolbar leaflet-pm-draw leaflet-bar leaflet-control"),this.editContainer=L.DomUtil.create("div","leaflet-pm-toolbar leaflet-pm-edit leaflet-bar leaflet-control"),this.optionsContainer=L.DomUtil.create("div","leaflet-pm-toolbar leaflet-pm-options leaflet-bar leaflet-control"),this.customContainer=L.DomUtil.create("div","leaflet-pm-toolbar leaflet-pm-custom leaflet-bar leaflet-control"),this._defineButtons()},_createContainer(e){let i=`${e}Container`;return this[i]||(this[i]=L.DomUtil.create("div",`leaflet-pm-toolbar leaflet-pm-${e} leaflet-bar leaflet-control`)),this[i]},getButtons(){return this.buttons},addControls(e=this.options){typeof e.editPolygon<"u"&&(e.editMode=e.editPolygon),typeof e.deleteLayer<"u"&&(e.removalMode=e.deleteLayer),L.Util.setOptions(this,e),this.applyIconStyle(),this.isVisible=!0,this._showHideButtons()},applyIconStyle(){let e=this.getButtons(),i={geomanIcons:{drawMarker:"control-icon leaflet-pm-icon-marker",drawPolyline:"control-icon leaflet-pm-icon-polyline",drawRectangle:"control-icon leaflet-pm-icon-rectangle",drawPolygon:"control-icon leaflet-pm-icon-polygon",drawCircle:"control-icon leaflet-pm-icon-circle",drawCircleMarker:"control-icon leaflet-pm-icon-circle-marker",editMode:"control-icon leaflet-pm-icon-edit",dragMode:"control-icon leaflet-pm-icon-drag",cutPolygon:"control-icon leaflet-pm-icon-cut",removalMode:"control-icon leaflet-pm-icon-delete",drawText:"control-icon leaflet-pm-icon-text"}};for(let r in e){let a=e[r];L.Util.setOptions(a,{className:i.geomanIcons[r]})}},removeControls(){let e=this.getButtons();for(let i in e)e[i].remove();this.isVisible=!1},toggleControls(e=this.options){this.isVisible?this.removeControls():this.addControls(e)},_addButton(e,i){return this.buttons[e]=i,this.options[e]=!!this.options[e]||!1,this.buttons[e]},triggerClickOnToggledButtons(e){for(let i in this.buttons){let r=this.buttons[i];r._button.disableByOtherButtons&&r!==e&&r.toggled()&&r._triggerClick()}},toggleButton(e,i,r=!0){e==="editPolygon"&&(e="editMode"),e==="deleteLayer"&&(e="removalMode");let a=e;return r&&this.triggerClickOnToggledButtons(this.buttons[a]),this.buttons[a]?this.buttons[a].toggle(i):!1},_defineButtons(){let e={className:"control-icon leaflet-pm-icon-marker",title:yt("buttonTitles.drawMarkerButton"),jsClass:"Marker",onClick:()=>{},afterClick:(j,R)=>{this.map.pm.Draw[R.button._button.jsClass].toggle()},doToggle:!0,toggleStatus:!1,disableOtherButtons:!0,position:this.options.position,actions:["cancel"]},i={title:yt("buttonTitles.drawPolyButton"),className:"control-icon leaflet-pm-icon-polygon",jsClass:"Polygon",onClick:()=>{},afterClick:(j,R)=>{this.map.pm.Draw[R.button._button.jsClass].toggle()},doToggle:!0,toggleStatus:!1,disableOtherButtons:!0,position:this.options.position,actions:["finish","removeLastVertex","cancel"]},r={className:"control-icon leaflet-pm-icon-polyline",title:yt("buttonTitles.drawLineButton"),jsClass:"Line",onClick:()=>{},afterClick:(j,R)=>{this.map.pm.Draw[R.button._button.jsClass].toggle()},doToggle:!0,toggleStatus:!1,disableOtherButtons:!0,position:this.options.position,actions:["finish","removeLastVertex","cancel"]},a={title:yt("buttonTitles.drawCircleButton"),className:"control-icon leaflet-pm-icon-circle",jsClass:"Circle",onClick:()=>{},afterClick:(j,R)=>{this.map.pm.Draw[R.button._button.jsClass].toggle()},doToggle:!0,toggleStatus:!1,disableOtherButtons:!0,position:this.options.position,actions:["cancel"]},s={title:yt("buttonTitles.drawCircleMarkerButton"),className:"control-icon leaflet-pm-icon-circle-marker",jsClass:"CircleMarker",onClick:()=>{},afterClick:(j,R)=>{this.map.pm.Draw[R.button._button.jsClass].toggle()},doToggle:!0,toggleStatus:!1,disableOtherButtons:!0,position:this.options.position,actions:["cancel"]},l={title:yt("buttonTitles.drawRectButton"),className:"control-icon leaflet-pm-icon-rectangle",jsClass:"Rectangle",onClick:()=>{},afterClick:(j,R)=>{this.map.pm.Draw[R.button._button.jsClass].toggle()},doToggle:!0,toggleStatus:!1,disableOtherButtons:!0,position:this.options.position,actions:["cancel"]},h={title:yt("buttonTitles.editButton"),className:"control-icon leaflet-pm-icon-edit",onClick:()=>{},afterClick:()=>{this.map.pm.toggleGlobalEditMode()},doToggle:!0,toggleStatus:!1,disableOtherButtons:!0,position:this.options.position,tool:"edit",actions:["finishMode"]},d={title:yt("buttonTitles.dragButton"),className:"control-icon leaflet-pm-icon-drag",onClick:()=>{},afterClick:()=>{this.map.pm.toggleGlobalDragMode()},doToggle:!0,toggleStatus:!1,disableOtherButtons:!0,position:this.options.position,tool:"edit",actions:["finishMode"]},f={title:yt("buttonTitles.cutButton"),className:"control-icon leaflet-pm-icon-cut",jsClass:"Cut",onClick:()=>{},afterClick:(j,R)=>{this.map.pm.Draw[R.button._button.jsClass].toggle({snappable:!0,cursorMarker:!0,allowSelfIntersection:!1})},doToggle:!0,toggleStatus:!1,disableOtherButtons:!0,position:this.options.position,tool:"edit",actions:["finish","removeLastVertex","cancel"]},m={title:yt("buttonTitles.deleteButton"),className:"control-icon leaflet-pm-icon-delete",onClick:()=>{},afterClick:()=>{this.map.pm.toggleGlobalRemovalMode()},doToggle:!0,toggleStatus:!1,disableOtherButtons:!0,position:this.options.position,tool:"edit",actions:["finishMode"]},E={title:yt("buttonTitles.rotateButton"),className:"control-icon leaflet-pm-icon-rotate",onClick:()=>{},afterClick:()=>{this.map.pm.toggleGlobalRotateMode()},doToggle:!0,toggleStatus:!1,disableOtherButtons:!0,position:this.options.position,tool:"edit",actions:["finishMode"]},P={className:"control-icon leaflet-pm-icon-text",title:yt("buttonTitles.drawTextButton"),jsClass:"Text",onClick:()=>{},afterClick:(j,R)=>{this.map.pm.Draw[R.button._button.jsClass].toggle()},doToggle:!0,toggleStatus:!1,disableOtherButtons:!0,position:this.options.position,actions:["cancel"]};this._addButton("drawMarker",new L.Control.PMButton(e)),this._addButton("drawPolyline",new L.Control.PMButton(r)),this._addButton("drawRectangle",new L.Control.PMButton(l)),this._addButton("drawPolygon",new L.Control.PMButton(i)),this._addButton("drawCircle",new L.Control.PMButton(a)),this._addButton("drawCircleMarker",new L.Control.PMButton(s)),this._addButton("drawText",new L.Control.PMButton(P)),this._addButton("editMode",new L.Control.PMButton(h)),this._addButton("dragMode",new L.Control.PMButton(d)),this._addButton("cutPolygon",new L.Control.PMButton(f)),this._addButton("removalMode",new L.Control.PMButton(m)),this._addButton("rotateMode",new L.Control.PMButton(E))},_showHideButtons(){if(!this.isVisible)return;this.removeControls(),this.isVisible=!0;let e=this.getButtons(),i=[];this.options.drawControls===!1&&(i=i.concat(Object.keys(e).filter(r=>!e[r]._button.tool))),this.options.editControls===!1&&(i=i.concat(Object.keys(e).filter(r=>e[r]._button.tool==="edit"))),this.options.optionsControls===!1&&(i=i.concat(Object.keys(e).filter(r=>e[r]._button.tool==="options"))),this.options.customControls===!1&&(i=i.concat(Object.keys(e).filter(r=>e[r]._button.tool==="custom")));for(let r in e)if(this.options[r]&&i.indexOf(r)===-1){let a=e[r]._button.tool;a||(a="draw"),e[r].setPosition(this._getBtnPosition(a)),e[r].addTo(this.map)}},_getBtnPosition(e){return this.options.positions&&this.options.positions[e]?this.options.positions[e]:this.options.position},setBlockPosition(e,i){this.options.positions[e]=i,this._showHideButtons(),this.changeControlOrder()},getBlockPositions(){return this.options.positions},copyDrawControl(e,i){if(i)typeof i!="object"&&(i={name:i});else throw new TypeError("Button has no name");let r=this._btnNameMapping(e);if(!i.name)throw new TypeError("Button has no name");if(this.buttons[i.name])throw new TypeError("Button with this name already exists");let a=this.map.pm.Draw.createNewDrawInstance(i.name,r);i={...this.buttons[r]._button,...i};let s=this.createCustomControl(i);return{drawInstance:a,control:s}},createCustomControl(e){if(!e.name)throw new TypeError("Button has no name");if(this.buttons[e.name])throw new TypeError("Button with this name already exists");e.onClick||(e.onClick=()=>{}),e.afterClick||(e.afterClick=()=>{}),e.toggle!==!1&&(e.toggle=!0),e.block&&(e.block=e.block.toLowerCase()),(!e.block||e.block==="draw")&&(e.block=""),e.className?e.className.indexOf("control-icon")===-1&&(e.className=`control-icon ${e.className}`):e.className="control-icon";let i={tool:e.block,className:e.className,title:e.title||"",jsClass:e.name,onClick:e.onClick,afterClick:e.afterClick,doToggle:e.toggle,toggleStatus:!1,disableOtherButtons:e.disableOtherButtons??!0,disableByOtherButtons:e.disableByOtherButtons??!0,cssToggle:e.toggle,position:this.options.position,actions:e.actions||[],disabled:!!e.disabled};this.options[e.name]!==!1&&(this.options[e.name]=!0);let r=this._addButton(e.name,new L.Control.PMButton(i));return this.changeControlOrder(),r},controlExists(e){return!!this.getButton(e)},getButton(e){return this.getButtons()[e]},getButtonsInBlock(e){let i={};if(e)for(let r in this.getButtons()){let a=this.getButtons()[r];(a._button.tool===e||e==="draw"&&!a._button.tool)&&(i[r]=a)}return i},changeControlOrder(e=[]){let i=this._shapeMapping(),r=[];e.forEach(l=>{i[l]?r.push(i[l]):r.push(l)});let a=this.getButtons(),s={};r.forEach(l=>{a[l]&&(s[l]=a[l])}),Object.keys(a).filter(l=>!a[l]._button.tool||a[l]._button.tool==="draw").forEach(l=>{r.indexOf(l)===-1&&(s[l]=a[l])}),Object.keys(a).filter(l=>a[l]._button.tool==="edit").forEach(l=>{r.indexOf(l)===-1&&(s[l]=a[l])}),Object.keys(a).filter(l=>a[l]._button.tool==="options").forEach(l=>{r.indexOf(l)===-1&&(s[l]=a[l])}),Object.keys(a).filter(l=>a[l]._button.tool==="custom").forEach(l=>{r.indexOf(l)===-1&&(s[l]=a[l])}),Object.keys(a).forEach(l=>{r.indexOf(l)===-1&&(s[l]=a[l])}),this.map.pm.Toolbar.buttons=s,this._showHideButtons()},getControlOrder(){let e=this.getButtons(),i=[];for(let r in e)i.push(r);return i},changeActionsOfControl(e,i){let r=this._btnNameMapping(e);if(!r)throw new TypeError("No name passed");if(!i)throw new TypeError("No actions passed");if(!this.buttons[r])throw new TypeError("Button with this name not exists");this.buttons[r]._button.actions=i,this.changeControlOrder()},setButtonDisabled(e,i){let r=this._btnNameMapping(e);i?this.buttons[r].disable():this.buttons[r].enable()},_shapeMapping(){return{Marker:"drawMarker",Circle:"drawCircle",Polygon:"drawPolygon",Rectangle:"drawRectangle",Polyline:"drawPolyline",Line:"drawPolyline",CircleMarker:"drawCircleMarker",Edit:"editMode",Drag:"dragMode",Cut:"cutPolygon",Removal:"removalMode",Rotate:"rotateMode",Text:"drawText"}},_btnNameMapping(e){let i=this._shapeMapping();return i[e]?i[e]:e}}),Vr=Wa,Hr=$($e()),Ja={_initSnappableMarkers(){this.options.snapDistance=this.options.snapDistance||30,this.options.snapSegment=this.options.snapSegment===void 0?!0:this.options.snapSegment,this._assignEvents(this._markers),this._layer.off("pm:dragstart",this._unsnap,this),this._layer.on("pm:dragstart",this._unsnap,this)},_disableSnapping(){this._layer.off("pm:dragstart",this._unsnap,this)},_assignEvents(e){e.forEach(i=>{if(Array.isArray(i)){this._assignEvents(i);return}i.off("drag",this._handleSnapping,this),i.on("drag",this._handleSnapping,this),i.off("dragend",this._cleanupSnapping,this),i.on("dragend",this._cleanupSnapping,this)})},_cleanupSnapping(e){if(e){let i=e.target;i._snapped=!1}delete this._snapList,this.throttledList&&(this._map.off("layeradd",this.throttledList,this),this.throttledList=void 0),this._map.off("layerremove",this._handleSnapLayerRemoval,this),this.debugIndicatorLines&&this.debugIndicatorLines.forEach(i=>{i.remove()})},_handleThrottleSnapping(){this.throttledList&&this._createSnapList()},_handleSnapping(e){let i=e.target;if(i._snapped=!1,this.throttledList||(this.throttledList=L.Util.throttle(this._handleThrottleSnapping,100,this)),e?.originalEvent?.altKey||this._map?.pm?.Keyboard.isAltKeyPressed()||(this._snapList===void 0&&(this._createSnapList(),this._map.off("layeradd",this.throttledList,this),this._map.on("layeradd",this.throttledList,this)),this._snapList.length<=0))return!1;let r=this._calcClosestLayer(i.getLatLng(),this._snapList);if(Object.keys(r).length===0)return!1;let a=r.layer instanceof L.Marker||r.layer instanceof L.CircleMarker||!this.options.snapSegment,s;a?s=r.latlng:s=this._checkPrioritiySnapping(r);let l=this.options.snapDistance,h={marker:i,shape:this._shape,snapLatLng:s,segment:r.segment,layer:this._layer,workingLayer:this._layer,layerInteractedWith:r.layer,distance:r.distance};if(this._fireSnapDrag(h.marker,h),this._fireSnapDrag(this._layer,h),r.distance<l){i._orgLatLng=i.getLatLng(),i.setLatLng(s),i._snapped=!0,i._snapInfo=h;let d=()=>{this._snapLatLng=s,this._fireSnap(i,h),this._fireSnap(this._layer,h)},f=this._snapLatLng||{},m=s||{};(f.lat!==m.lat||f.lng!==m.lng)&&d()}else this._snapLatLng&&(this._unsnap(h),i._snapped=!1,i._snapInfo=void 0,this._fireUnsnap(h.marker,h),this._fireUnsnap(this._layer,h));return!0},_createSnapList(){let e=[],i=[],r=this._map;r.off("layerremove",this._handleSnapLayerRemoval,this),r.on("layerremove",this._handleSnapLayerRemoval,this),r.eachLayer(a=>{if((a instanceof L.Polyline||a instanceof L.Marker||a instanceof L.CircleMarker||a instanceof L.ImageOverlay)&&a.options.snapIgnore!==!0){if(a.options.snapIgnore===void 0&&(!L.PM.optIn&&a.options.pmIgnore===!0||L.PM.optIn&&a.options.pmIgnore!==!1))return;(a instanceof L.Circle||a instanceof L.CircleMarker)&&a.pm&&a.pm._hiddenPolyCircle?e.push(a.pm._hiddenPolyCircle):a instanceof L.ImageOverlay&&(a=L.rectangle(a.getBounds())),e.push(a);let s=L.polyline([],{color:"red",pmIgnore:!0});s._pmTempLayer=!0,i.push(s),(a instanceof L.Circle||a instanceof L.CircleMarker)&&i.push(s)}}),e=e.filter(a=>this._layer!==a),e=e.filter(a=>a._latlng||a._latlngs&&Fn(a._latlngs)),e=e.filter(a=>!a._pmTempLayer),this._otherSnapLayers?(this._otherSnapLayers.forEach(()=>{let a=L.polyline([],{color:"red",pmIgnore:!0});a._pmTempLayer=!0,i.push(a)}),this._snapList=e.concat(this._otherSnapLayers)):this._snapList=e,this.debugIndicatorLines=i},_handleSnapLayerRemoval({layer:e}){if(!e._leaflet_id)return;let i=this._snapList.findIndex(r=>r._leaflet_id===e._leaflet_id);i>-1&&this._snapList.splice(i,1)},_calcClosestLayer(e,i){return this._calcClosestLayers(e,i,1)[0]},_calcClosestLayers(e,i,r=1){let a=[],s={};i.forEach((h,d)=>{if(h._parentCopy&&h._parentCopy===this._layer||h.getLatLngs?.().flat(5).length<2)return;let f=this._calcLayerDistances(e,h);if(f.distance=Math.floor(f.distance),this.debugIndicatorLines){if(!this.debugIndicatorLines[d]){let m=L.polyline([],{color:"red",pmIgnore:!0});m._pmTempLayer=!0,this.debugIndicatorLines[d]=m}this.debugIndicatorLines[d].setLatLngs([e,f.latlng])}r===1&&(s.distance===void 0||f.distance-5<=s.distance)?(f.distance+5<s.distance&&(a=[]),s=f,s.layer=h,a.push(s)):r!==1&&(s={},s=f,s.layer=h,a.push(s))}),r!==1&&(a=a.sort((h,d)=>h.distance-d.distance)),r===-1&&(r=a.length);let l=this._getClosestLayerByPriority(a,r);return L.Util.isArray(l)?l:[l]},_calcLayerDistances(e,i){let r=this._map,a=i instanceof L.Marker||i instanceof L.CircleMarker,s=i instanceof L.Polygon,l=e;if(a){let h=i.getLatLng();return{latlng:{...h},distance:this._getDistance(r,h,l)}}return this._calcLatLngDistances(l,i.getLatLngs(),r,s)},_calcLatLngDistances(e,i,r,a=!1){let s,l,h,d=f=>{f.forEach((m,E)=>{if(Array.isArray(m)){d(m);return}if(this.options.snapSegment){let P=m,j;a?j=E+1===f.length?0:E+1:j=E+1===f.length?void 0:E+1;let R=f[j];if(R){let W=this._getDistanceToSegment(r,e,P,R);(l===void 0||W<l)&&(l=W,h=[P,R])}}else{let P=this._getDistance(r,e,m);(l===void 0||P<l)&&(l=P,s=m)}})};return d(i),this.options.snapSegment?{latlng:{...this._getClosestPointOnSegment(r,e,h[0],h[1])},segment:h,distance:l}:{latlng:s,distance:l}},_getClosestLayerByPriority(e,i=1){e=e.sort((h,d)=>h._leaflet_id-d._leaflet_id);let r=["Marker","CircleMarker","Circle","Line","Polygon","Rectangle"],a=this._map.pm.globalOptions.snappingOrder||[],s=0,l={};return a.concat(r).forEach(h=>{l[h]||(s+=1,l[h]=s)}),e.sort(qa("instanceofShape",l)),i===1?e[0]||{}:e.slice(0,i)},_checkPrioritiySnapping(e){let i=this._map,r=e.segment[0],a=e.segment[1],s=e.latlng,l=s;if(this.options.snapVertex){let h=this._getDistance(i,r,s),d=this._getDistance(i,a,s),f=h<d?r:a,m=h<d?h:d;if(this.options.snapMiddle){let P=L.PM.Utils.calcMiddleLatLng(i,r,a),j=this._getDistance(i,P,s);j<h&&j<d&&(f=P,m=j)}let E=this.options.snapDistance;m<E&&(l=f)}return{...l}},_unsnap(){delete this._snapLatLng},_getClosestPointOnSegment(e,i,r,a){let s=e.getMaxZoom();s===1/0&&(s=e.getZoom());let l=e.project(i,s),h=e.project(r,s),d=e.project(a,s),f=L.LineUtil.closestPointOnSegment(l,h,d);return e.unproject(f,s)},_getDistanceToSegment(e,i,r,a){let s=e.latLngToContainerPoint(i),l=e.latLngToContainerPoint(r),h=e.latLngToContainerPoint(a);return L.LineUtil.pointToSegmentDistance(s,l,h)},_getDistance(e,i,r){return e.latLngToContainerPoint(i).distanceTo(e.latLngToContainerPoint(r))}},Kr=Ja,zn=L.Class.extend({includes:[Kr,mt],options:{snappable:!0,snapDistance:20,snapMiddle:!1,allowSelfIntersection:!0,tooltips:!0,templineStyle:{},hintlineStyle:{color:"#3388ff",dashArray:"5,5"},pathOptions:null,cursorMarker:!0,finishOn:null,markerStyle:{draggable:!0,icon:L.icon()},hideMiddleMarkers:!1,minRadiusCircle:null,maxRadiusCircle:null,minRadiusCircleMarker:null,maxRadiusCircleMarker:null,resizeableCircleMarker:!1,resizeableCircle:!0,markerEditable:!0,continueDrawing:!1,snapSegment:!0,requireSnapToFinish:!1,rectangleAngle:0,textOptions:{text:null,focusAfterDraw:null,removeIfEmpty:null,className:null},snapVertex:!0},setOptions(e){L.Util.setOptions(this,e),this.setStyle(this.options)},setStyle(){},getOptions(){return this.options},initialize(e){let i=new L.Icon.Default;i.options.tooltipAnchor=[0,0],this.options.markerStyle.icon=i,this._map=e,this.shapes=["Marker","CircleMarker","Line","Polygon","Rectangle","Circle","Cut","Text"],this.shapes.forEach(r=>{this[r]=new L.PM.Draw[r](this._map)}),this.Marker.setOptions({continueDrawing:!0}),this.CircleMarker.setOptions({continueDrawing:!0})},setPathOptions(e,i=!1){i?this.options.pathOptions=(0,Hr.default)(this.options.pathOptions,e):this.options.pathOptions=e},getShapes(){return this.shapes},getShape(){return this._shape},enable(e,i){if(!e)throw new Error(`Error: Please pass a shape as a parameter. Possible shapes are: ${this.getShapes().join(",")}`);this.disable(),this[e].enable(i)},disable(){this.shapes.forEach(e=>{this[e].disable()})},addControls(){this.shapes.forEach(e=>{this[e].addButton()})},getActiveShape(){let e;return this.shapes.forEach(i=>{this[i]._enabled&&(e=i)}),e},_setGlobalDrawMode(){this._shape==="Cut"?this._fireGlobalCutModeToggled():this._fireGlobalDrawModeToggled();let e=[];this._map.eachLayer(i=>{(i instanceof L.Polyline||i instanceof L.Marker||i instanceof L.Circle||i instanceof L.CircleMarker||i instanceof L.ImageOverlay)&&(i._pmTempLayer||e.push(i))}),this._enabled?e.forEach(i=>{L.PM.Utils.disablePopup(i)}):e.forEach(i=>{L.PM.Utils.enablePopup(i)})},createNewDrawInstance(e,i){let r=this._getShapeFromBtnName(i);if(this[e])throw new TypeError("Draw Type already exists");if(!L.PM.Draw[r])throw new TypeError(`There is no class L.PM.Draw.${r}`);return this[e]=new L.PM.Draw[r](this._map),this[e].toolbarButtonName=e,this[e]._shape=e,this.shapes.push(e),this[i]&&this[e].setOptions(this[i].options),this[e].setOptions(this[e].options),this[e]},_getShapeFromBtnName(e){let i={drawMarker:"Marker",drawCircle:"Circle",drawPolygon:"Polygon",drawPolyline:"Line",drawRectangle:"Rectangle",drawCircleMarker:"CircleMarker",editMode:"Edit",dragMode:"Drag",cutPolygon:"Cut",removalMode:"Removal",rotateMode:"Rotate",drawText:"Text"};return i[e]?i[e]:this[e]?this[e]._shape:e},_finishLayer(e){e.pm&&(e.pm.setOptions(this.options),e.pm._shape=this._shape,e.pm._map=this._map),this._addDrawnLayerProp(e)},_addDrawnLayerProp(e){e._drawnByGeoman=!0},_setPane(e,i){i==="layerPane"?e.options.pane=this._map.pm.globalOptions.panes&&this._map.pm.globalOptions.panes.layerPane||"overlayPane":i==="vertexPane"?e.options.pane=this._map.pm.globalOptions.panes&&this._map.pm.globalOptions.panes.vertexPane||"markerPane":i==="markerPane"&&(e.options.pane=this._map.pm.globalOptions.panes&&this._map.pm.globalOptions.panes.markerPane||"markerPane")},_isFirstLayer(){return(this._map||this._layer._map).pm.getGeomanLayers().length===0}}),Ut=zn;Ut.Marker=Ut.extend({initialize(e){this._map=e,this._shape="Marker",this.toolbarButtonName="drawMarker"},enable(e){L.Util.setOptions(this,e),this._enabled=!0,this._map.getContainer().classList.add("geoman-draw-cursor"),this._map.on("click",this._createMarker,this),this._map.pm.Toolbar.toggleButton(this.toolbarButtonName,!0),this._hintMarker=L.marker(this._map.getCenter(),this.options.markerStyle),this._setPane(this._hintMarker,"markerPane"),this._hintMarker._pmTempLayer=!0,this._hintMarker.addTo(this._map),this.options.tooltips&&this._hintMarker.bindTooltip(yt("tooltips.placeMarker"),{permanent:!0,offset:L.point(0,10),direction:"bottom",opacity:.8}).openTooltip(),this._layer=this._hintMarker,this._map.on("mousemove",this._syncHintMarker,this),this.options.markerEditable&&this._map.eachLayer(i=>{this.isRelevantMarker(i)&&i.pm.enable()}),this._fireDrawStart(),this._setGlobalDrawMode()},disable(){this._enabled&&(this._enabled=!1,this._map.getContainer().classList.remove("geoman-draw-cursor"),this._map.off("click",this._createMarker,this),this._hintMarker.remove(),this._map.off("mousemove",this._syncHintMarker,this),this._map.eachLayer(e=>{this.isRelevantMarker(e)&&e.pm.disable()}),this._map.pm.Toolbar.toggleButton(this.toolbarButtonName,!1),this.options.snappable&&this._cleanupSnapping(),this._fireDrawEnd(),this._setGlobalDrawMode())},enabled(){return this._enabled},toggle(e){this.enabled()?this.disable():this.enable(e)},isRelevantMarker(e){return e instanceof L.Marker&&e.pm&&!e._pmTempLayer&&!e.pm._initTextMarker},_syncHintMarker(e){if(this._hintMarker.setLatLng(e.latlng),this.options.snappable){let i=e;i.target=this._hintMarker,this._handleSnapping(i)}this._fireChange(this._hintMarker.getLatLng(),"Draw")},_createMarker(e){if(!e.latlng||this.options.requireSnapToFinish&&!this._hintMarker._snapped&&!this._isFirstLayer())return;this._hintMarker._snapped||this._hintMarker.setLatLng(e.latlng);let i=this._hintMarker.getLatLng(),r=new L.Marker(i,this.options.markerStyle);this._setPane(r,"markerPane"),this._finishLayer(r),r.pm||(r.options.draggable=!1),r.addTo(this._map.pm._getContainingLayer()),r.pm&&this.options.markerEditable?r.pm.enable():r.dragging&&r.dragging.disable(),this._fireCreate(r),this._cleanupSnapping(),this.options.continueDrawing||this.disable()},setStyle(){this.options.markerStyle?.icon&&this._hintMarker?.setIcon(this.options.markerStyle.icon)}});var Ht=63710088e-1,Nn={centimeters:Ht*100,centimetres:Ht*100,degrees:Ht/111325,feet:Ht*3.28084,inches:Ht*39.37,kilometers:Ht/1e3,kilometres:Ht/1e3,meters:Ht,metres:Ht,miles:Ht/1609.344,millimeters:Ht*1e3,millimetres:Ht*1e3,nauticalmiles:Ht/1852,radians:1,yards:Ht*1.0936},Gn={centimeters:100,centimetres:100,degrees:1/111325,feet:3.28084,inches:39.37,kilometers:1/1e3,kilometres:1/1e3,meters:1,metres:1,miles:1/1609.344,millimeters:1e3,millimetres:1e3,nauticalmiles:1/1852,radians:1/Ht,yards:1.0936133};function se(e,i,r){r===void 0&&(r={});var a={type:"Feature"};return(r.id===0||r.id)&&(a.id=r.id),r.bbox&&(a.bbox=r.bbox),a.properties=i||{},a.geometry=e,a}function ei(e,i,r){if(r===void 0&&(r={}),!e)throw new Error("coordinates is required");if(!Array.isArray(e))throw new Error("coordinates must be an Array");if(e.length<2)throw new Error("coordinates must be at least 2 numbers long");if(!Zi(e[0])||!Zi(e[1]))throw new Error("coordinates must contain numbers");var a={type:"Point",coordinates:e};return se(a,i,r)}function Be(e,i,r){if(r===void 0&&(r={}),e.length<2)throw new Error("coordinates must be an array of two or more positions");var a={type:"LineString",coordinates:e};return se(a,i,r)}function It(e,i){i===void 0&&(i={});var r={type:"FeatureCollection"};return i.id&&(r.id=i.id),i.bbox&&(r.bbox=i.bbox),r.features=e,r}function qr(e,i){i===void 0&&(i="kilometers");var r=Nn[i];if(!r)throw new Error(i+" units is invalid");return e*r}function Wr(e,i){i===void 0&&(i="kilometers");var r=Nn[i];if(!r)throw new Error(i+" units is invalid");return e/r}function Zn(e){var i=e%(2*Math.PI);return i*180/Math.PI}function ne(e){var i=e%360;return i*Math.PI/180}function Zi(e){return!isNaN(e)&&e!==null&&!Array.isArray(e)}function ji(e){var i,r,a={type:"FeatureCollection",features:[]};if(e.type==="Feature"?r=e.geometry:r=e,r.type==="LineString")i=[r.coordinates];else if(r.type==="MultiLineString")i=r.coordinates;else if(r.type==="MultiPolygon")i=[].concat.apply([],r.coordinates);else if(r.type==="Polygon")i=r.coordinates;else throw new Error("Input must be a LineString, MultiLineString, Polygon, or MultiPolygon Feature or Geometry");return i.forEach(function(s){i.forEach(function(l){for(var h=0;h<s.length-1;h++)for(var d=h;d<l.length-1;d++)if(!(s===l&&(Math.abs(h-d)===1||h===0&&d===s.length-2&&s[h][0]===s[s.length-1][0]&&s[h][1]===s[s.length-1][1]))){var f=Ya(s[h][0],s[h][1],s[h+1][0],s[h+1][1],l[d][0],l[d][1],l[d+1][0],l[d+1][1]);f&&a.features.push(ei([f[0],f[1]]))}})}),a}function Ya(e,i,r,a,s,l,h,d){var f,m,E,P,j,R={x:null,y:null,onLine1:!1,onLine2:!1};return f=(d-l)*(r-e)-(h-s)*(a-i),f===0?R.x!==null&&R.y!==null?R:!1:(m=i-l,E=e-s,P=(h-s)*m-(d-l)*E,j=(r-e)*m-(a-i)*E,m=P/f,E=j/f,R.x=e+m*(r-e),R.y=i+m*(a-i),m>=0&&m<=1&&(R.onLine1=!0),E>=0&&E<=1&&(R.onLine2=!0),R.onLine1&&R.onLine2?[R.x,R.y]:!1)}Ut.Line=Ut.extend({initialize(e){this._map=e,this._shape="Line",this.toolbarButtonName="drawPolyline",this._doesSelfIntersect=!1},enable(e){L.Util.setOptions(this,e),this._enabled=!0,this._markers=[],this._layerGroup=new L.FeatureGroup,this._layerGroup._pmTempLayer=!0,this._layerGroup.addTo(this._map),this._layer=L.polyline([],{...this.options.templineStyle,pmIgnore:!1}),this._setPane(this._layer,"layerPane"),this._layer._pmTempLayer=!0,this._layerGroup.addLayer(this._layer),this._hintline=L.polyline([],this.options.hintlineStyle),this._setPane(this._hintline,"layerPane"),this._hintline._pmTempLayer=!0,this._layerGroup.addLayer(this._hintline),this._hintMarker=L.marker(this._map.getCenter(),{interactive:!1,zIndexOffset:100,icon:L.divIcon({className:"marker-icon cursor-marker"})}),this._setPane(this._hintMarker,"vertexPane"),this._hintMarker._pmTempLayer=!0,this._layerGroup.addLayer(this._hintMarker),this.options.cursorMarker&&L.DomUtil.addClass(this._hintMarker._icon,"visible"),this.options.tooltips&&this._hintMarker.bindTooltip(yt("tooltips.firstVertex"),{permanent:!0,offset:L.point(0,10),direction:"bottom",opacity:.8}).openTooltip(),this._map.getContainer().classList.add("geoman-draw-cursor"),this._map.on("click",this._createVertex,this),this.options.finishOn&&this.options.finishOn!=="snap"&&this._map.on(this.options.finishOn,this._finishShape,this),this.options.finishOn==="dblclick"&&(this.tempMapDoubleClickZoomState=this._map.doubleClickZoom._enabled,this.tempMapDoubleClickZoomState&&this._map.doubleClickZoom.disable()),this._map.on("mousemove",this._syncHintMarker,this),this._hintMarker.on("move",this._syncHintLine,this),this._map.pm.Toolbar.toggleButton(this.toolbarButtonName,!0),this._otherSnapLayers=[],this.isRed=!1,this._fireDrawStart(),this._setGlobalDrawMode()},disable(){this._enabled&&(this._enabled=!1,this._map.getContainer().classList.remove("geoman-draw-cursor"),this._map.off("click",this._createVertex,this),this._map.off("mousemove",this._syncHintMarker,this),this.options.finishOn&&this.options.finishOn!=="snap"&&this._map.off(this.options.finishOn,this._finishShape,this),this.tempMapDoubleClickZoomState&&this._map.doubleClickZoom.enable(),this._map.removeLayer(this._layerGroup),this._map.pm.Toolbar.toggleButton(this.toolbarButtonName,!1),this.options.snappable&&this._cleanupSnapping(),this._fireDrawEnd(),this._setGlobalDrawMode())},enabled(){return this._enabled},toggle(e){this.enabled()?this.disable():this.enable(e)},_syncHintLine(){let e=this._layer.getLatLngs();if(e.length>0){let i=e[e.length-1];this._hintline.setLatLngs([i,this._hintMarker.getLatLng()])}},_syncHintMarker(e){if(this._hintMarker.setLatLng(e.latlng),this.options.snappable){let r=e;r.target=this._hintMarker,this._handleSnapping(r)}this.options.allowSelfIntersection||this._handleSelfIntersection(!0,this._hintMarker.getLatLng());let i=this._layer._defaultShape().slice();i.push(this._hintMarker.getLatLng()),this._change(i)},hasSelfIntersection(){return ji(this._layer.toGeoJSON(15)).features.length>0},_handleSelfIntersection(e,i){let r=L.polyline(this._layer.getLatLngs());e&&(i||(i=this._hintMarker.getLatLng()),r.addLatLng(i));let a=ji(r.toGeoJSON(15));this._doesSelfIntersect=a.features.length>0,this._doesSelfIntersect?this.isRed||(this.isRed=!0,this._hintline.setStyle({color:"#f00000ff"}),this._fireIntersect(a,this._map,"Draw")):this._hintline.isEmpty()||(this.isRed=!1,this._hintline.setStyle(this.options.hintlineStyle))},_createVertex(e){if(!this.options.allowSelfIntersection&&(this._handleSelfIntersection(!0,e.latlng),this._doesSelfIntersect))return;this._hintMarker._snapped||this._hintMarker.setLatLng(e.latlng);let i=this._hintMarker.getLatLng(),r=this._layer.getLatLngs(),a=r[r.length-1];if(i.equals(r[0])||r.length>0&&i.equals(a)){this._finishShape();return}this._layer._latlngInfo=this._layer._latlngInfo||[],this._layer._latlngInfo.push({latlng:i,snapInfo:this._hintMarker._snapInfo}),this._layer.addLatLng(i);let s=this._createMarker(i);this._setTooltipText(),this._setHintLineAfterNewVertex(i),this._fireVertexAdded(s,void 0,i,"Draw"),this._change(this._layer.getLatLngs()),this.options.finishOn==="snap"&&this._hintMarker._snapped&&this._finishShape(e)},_setHintLineAfterNewVertex(e){this._hintline.setLatLngs([e,e])},_removeLastVertex(){let e=this._markers;if(e.length<=1){this.disable();return}let i=this._layer.getLatLngs(),r=e[e.length-1],{indexPath:a}=L.PM.Utils.findDeepMarkerIndex(e,r);e.pop(),this._layerGroup.removeLayer(r);let s=e[e.length-1],l=i.indexOf(s.getLatLng());i=i.slice(0,l+1),this._layer.setLatLngs(i),this._layer._latlngInfo.pop(),this._syncHintLine(),this._setTooltipText(),this._fireVertexRemoved(r,a,"Draw"),this._change(this._layer.getLatLngs())},_finishShape(){if(!this.options.allowSelfIntersection&&(this._handleSelfIntersection(!1),this._doesSelfIntersect)||this.options.requireSnapToFinish&&!this._hintMarker._snapped&&!this._isFirstLayer())return;let e=this._layer.getLatLngs();if(e.length<=1)return;let i=L.polyline(e,this.options.pathOptions);this._setPane(i,"layerPane"),this._finishLayer(i),i.addTo(this._map.pm._getContainingLayer()),this._fireCreate(i),this.options.snappable&&this._cleanupSnapping();let r=this._hintMarker.getLatLng();this.disable(),this.options.continueDrawing&&(this.enable(),this._hintMarker.setLatLng(r))},_createMarker(e){let i=new L.Marker(e,{draggable:!1,icon:L.divIcon({className:"marker-icon"})});return this._setPane(i,"vertexPane"),i._pmTempLayer=!0,this._layerGroup.addLayer(i),this._markers.push(i),i.on("click",this._finishShape,this),i},_setTooltipText(){let{length:e}=this._layer.getLatLngs().flat(),i="";e<=1?i=yt("tooltips.continueLine"):i=yt("tooltips.finishLine"),this._hintMarker.setTooltipContent(i)},_change(e){this._fireChange(e,"Draw")},setStyle(){this._layer?.setStyle(this.options.templineStyle),this._hintline?.setStyle(this.options.hintlineStyle)}}),Ut.Polygon=Ut.Line.extend({initialize(e){this._map=e,this._shape="Polygon",this.toolbarButtonName="drawPolygon"},enable(e){L.PM.Draw.Line.prototype.enable.call(this,e),this._layer.pm._shape="Polygon"},_createMarker(e){let i=new L.Marker(e,{draggable:!1,icon:L.divIcon({className:"marker-icon"})});return this._setPane(i,"vertexPane"),i._pmTempLayer=!0,this._layerGroup.addLayer(i),this._markers.push(i),this._layer.getLatLngs().flat().length===1?(i.on("click",this._finishShape,this),this._tempSnapLayerIndex=this._otherSnapLayers.push(i)-1,this.options.snappable&&this._cleanupSnapping()):i.on("click",()=>1),i},_setTooltipText(){let{length:e}=this._layer.getLatLngs().flat(),i="";e<=2?i=yt("tooltips.continueLine"):i=yt("tooltips.finishPoly"),this._hintMarker.setTooltipContent(i)},_finishShape(){if(!this.options.allowSelfIntersection&&(this._handleSelfIntersection(!0,this._layer.getLatLngs()[0]),this._doesSelfIntersect)||this.options.requireSnapToFinish&&!this._hintMarker._snapped&&!this._isFirstLayer())return;let e=this._layer.getLatLngs();if(e.length<=2)return;let i=L.polygon(e,this.options.pathOptions);this._setPane(i,"layerPane"),this._finishLayer(i),i.addTo(this._map.pm._getContainingLayer()),this._fireCreate(i),this._cleanupSnapping(),this._otherSnapLayers.splice(this._tempSnapLayerIndex,1),delete this._tempSnapLayerIndex;let r=this._hintMarker.getLatLng();this.disable(),this.options.continueDrawing&&(this.enable(),this._hintMarker.setLatLng(r))}}),Ut.Rectangle=Ut.extend({initialize(e){this._map=e,this._shape="Rectangle",this.toolbarButtonName="drawRectangle"},enable(e){if(L.Util.setOptions(this,e),this._enabled=!0,this._layerGroup=new L.FeatureGroup,this._layerGroup._pmTempLayer=!0,this._layerGroup.addTo(this._map),this._layer=L.rectangle([[0,0],[0,0]],this.options.pathOptions),this._setPane(this._layer,"layerPane"),this._layer._pmTempLayer=!0,this._startMarker=L.marker(this._map.getCenter(),{icon:L.divIcon({className:"marker-icon rect-start-marker"}),draggable:!1,zIndexOffset:-100,opacity:this.options.cursorMarker?1:0}),this._setPane(this._startMarker,"vertexPane"),this._startMarker._pmTempLayer=!0,this._layerGroup.addLayer(this._startMarker),this._hintMarker=L.marker(this._map.getCenter(),{zIndexOffset:150,icon:L.divIcon({className:"marker-icon cursor-marker"})}),this._setPane(this._hintMarker,"vertexPane"),this._hintMarker._pmTempLayer=!0,this._layerGroup.addLayer(this._hintMarker),this.options.cursorMarker&&L.DomUtil.addClass(this._hintMarker._icon,"visible"),this.options.tooltips&&this._hintMarker.bindTooltip(yt("tooltips.firstVertex"),{permanent:!0,offset:L.point(0,10),direction:"bottom",opacity:.8}).openTooltip(),this.options.cursorMarker){this._styleMarkers=[];for(let i=0;i<2;i+=1){let r=L.marker(this._map.getCenter(),{icon:L.divIcon({className:"marker-icon rect-style-marker"}),draggable:!1,zIndexOffset:100});this._setPane(r,"vertexPane"),r._pmTempLayer=!0,this._layerGroup.addLayer(r),this._styleMarkers.push(r)}}this._map.getContainer().classList.add("geoman-draw-cursor"),this._map.on("click",this._placeStartingMarkers,this),this._map.on("mousemove",this._syncHintMarker,this),this._map.pm.Toolbar.toggleButton(this.toolbarButtonName,!0),this._otherSnapLayers=[],this._fireDrawStart(),this._setGlobalDrawMode()},disable(){this._enabled&&(this._enabled=!1,this._map.getContainer().classList.remove("geoman-draw-cursor"),this._map.off("click",this._finishShape,this),this._map.off("click",this._placeStartingMarkers,this),this._map.off("mousemove",this._syncHintMarker,this),this._map.removeLayer(this._layerGroup),this._map.pm.Toolbar.toggleButton(this.toolbarButtonName,!1),this.options.snappable&&this._cleanupSnapping(),this._fireDrawEnd(),this._setGlobalDrawMode())},enabled(){return this._enabled},toggle(e){this.enabled()?this.disable():this.enable(e)},_placeStartingMarkers(e){this._hintMarker._snapped||this._hintMarker.setLatLng(e.latlng);let i=this._hintMarker.getLatLng();L.DomUtil.addClass(this._startMarker._icon,"visible"),this._startMarker.setLatLng(i),this.options.cursorMarker&&this._styleMarkers&&this._styleMarkers.forEach(r=>{L.DomUtil.addClass(r._icon,"visible"),r.setLatLng(i)}),this._map.off("click",this._placeStartingMarkers,this),this._map.on("click",this._finishShape,this),this._hintMarker.setTooltipContent(yt("tooltips.finishRect")),this._setRectangleOrigin()},_setRectangleOrigin(){let e=this._startMarker.getLatLng();e&&(this._layerGroup.addLayer(this._layer),this._layer.setLatLngs([e,e]),this._hintMarker.on("move",this._syncRectangleSize,this))},_syncHintMarker(e){if(this._hintMarker.setLatLng(e.latlng),this.options.snappable){let r=e;r.target=this._hintMarker,this._handleSnapping(r)}let i=this._layerGroup&&this._layerGroup.hasLayer(this._layer)?this._layer.getLatLngs():[this._hintMarker.getLatLng()];this._fireChange(i,"Draw")},_syncRectangleSize(){let e=Zr(this._startMarker.getLatLng(),this._map),i=Zr(this._hintMarker.getLatLng(),this._map),r=L.PM.Utils._getRotatedRectangle(e,i,this.options.rectangleAngle||0,this._map);if(this._layer.setLatLngs(r),this.options.cursorMarker&&this._styleMarkers){let a=[];r.forEach(s=>{!s.equals(e,1e-8)&&!s.equals(i,1e-8)&&a.push(s)}),a.forEach((s,l)=>{try{this._styleMarkers[l].setLatLng(s)}catch{}})}},_findCorners(){let e=this._layer.getLatLngs()[0];return L.PM.Utils._getRotatedRectangle(e[0],e[2],this.options.rectangleAngle||0,this._map)},_finishShape(e){this._hintMarker._snapped||this._hintMarker.setLatLng(e.latlng);let i=this._hintMarker.getLatLng(),r=this._startMarker.getLatLng();if(this.options.requireSnapToFinish&&!this._hintMarker._snapped&&!this._isFirstLayer()||r.equals(i))return;let a=L.rectangle([r,i],this.options.pathOptions);if(this.options.rectangleAngle){let l=L.PM.Utils._getRotatedRectangle(r,i,this.options.rectangleAngle||0,this._map);a.setLatLngs(l),a.pm&&a.pm._setAngle(this.options.rectangleAngle||0)}this._setPane(a,"layerPane"),this._finishLayer(a),a.addTo(this._map.pm._getContainingLayer()),this._fireCreate(a);let s=this._hintMarker.getLatLng();this.disable(),this.options.continueDrawing&&(this.enable(),this._hintMarker.setLatLng(s))},setStyle(){this._layer?.setStyle(this.options.pathOptions)}}),Ut.CircleMarker=Ut.extend({initialize(e){this._map=e,this._shape="CircleMarker",this.toolbarButtonName="drawCircleMarker",this._layerIsDragging=!1,this._BaseCircleClass=L.CircleMarker,this._minRadiusOption="minRadiusCircleMarker",this._maxRadiusOption="maxRadiusCircleMarker",this._editableOption="resizeableCircleMarker",this._defaultRadius=10},enable(e){if(L.Util.setOptions(this,e),this.options.editable&&(this.options.resizeableCircleMarker=this.options.editable,delete this.options.editable),this._enabled=!0,this._map.pm.Toolbar.toggleButton(this.toolbarButtonName,!0),this._map.getContainer().classList.add("geoman-draw-cursor"),this.options[this._editableOption]){let i={};L.extend(i,this.options.templineStyle),i.radius=0,this._layerGroup=new L.FeatureGroup,this._layerGroup._pmTempLayer=!0,this._layerGroup.addTo(this._map),this._layer=new this._BaseCircleClass(this._map.getCenter(),i),this._setPane(this._layer,"layerPane"),this._layer._pmTempLayer=!0,this._centerMarker=L.marker(this._map.getCenter(),{icon:L.divIcon({className:"marker-icon"}),draggable:!1,zIndexOffset:100}),this._setPane(this._centerMarker,"vertexPane"),this._centerMarker._pmTempLayer=!0,this._hintMarker=L.marker(this._map.getCenter(),{zIndexOffset:110,icon:L.divIcon({className:"marker-icon cursor-marker"})}),this._setPane(this._hintMarker,"vertexPane"),this._hintMarker._pmTempLayer=!0,this._layerGroup.addLayer(this._hintMarker),this.options.cursorMarker&&L.DomUtil.addClass(this._hintMarker._icon,"visible"),this.options.tooltips&&this._hintMarker.bindTooltip(yt("tooltips.startCircle"),{permanent:!0,offset:L.point(0,10),direction:"bottom",opacity:.8}).openTooltip(),this._hintline=L.polyline([],this.options.hintlineStyle),this._setPane(this._hintline,"layerPane"),this._hintline._pmTempLayer=!0,this._layerGroup.addLayer(this._hintline),this._map.on("click",this._placeCenterMarker,this)}else this._map.on("click",this._createMarker,this),this._hintMarker=new this._BaseCircleClass(this._map.getCenter(),{radius:this._defaultRadius,...this.options.templineStyle}),this._setPane(this._hintMarker,"layerPane"),this._hintMarker._pmTempLayer=!0,this._hintMarker.addTo(this._map),this._layer=this._hintMarker,this.options.tooltips&&this._hintMarker.bindTooltip(yt("tooltips.placeCircleMarker"),{permanent:!0,offset:L.point(0,10),direction:"bottom",opacity:.8}).openTooltip();this._map.on("mousemove",this._syncHintMarker,this),this._extendingEnable(),this._otherSnapLayers=[],this._fireDrawStart(),this._setGlobalDrawMode()},_extendingEnable(){!this.options[this._editableOption]&&this.options.markerEditable&&this._map.eachLayer(e=>{this.isRelevantMarker(e)&&e.pm.enable()}),this._layer.bringToBack()},disable(){this._enabled&&(this._enabled=!1,this._map.getContainer().classList.remove("geoman-draw-cursor"),this.options[this._editableOption]?(this._map.off("click",this._finishShape,this),this._map.off("click",this._placeCenterMarker,this),this._map.removeLayer(this._layerGroup)):(this._map.off("click",this._createMarker,this),this._extendingDisable(),this._hintMarker.remove()),this._map.off("mousemove",this._syncHintMarker,this),this._map.pm.Toolbar.toggleButton(this.toolbarButtonName,!1),this.options.snappable&&this._cleanupSnapping(),this._fireDrawEnd(),this._setGlobalDrawMode())},_extendingDisable(){this._map.eachLayer(e=>{this.isRelevantMarker(e)&&e.pm.disable()})},enabled(){return this._enabled},toggle(e){this.enabled()?this.disable():this.enable(e)},_placeCenterMarker(e){this._hintMarker._snapped||this._hintMarker.setLatLng(e.latlng),this._layerGroup.addLayer(this._layer),this._layerGroup.addLayer(this._centerMarker);let i=this._hintMarker.getLatLng();this._centerMarker.setLatLng(i),this._map.off("click",this._placeCenterMarker,this),this._map.on("click",this._finishShape,this),this._placeCircleCenter()},_placeCircleCenter(){let e=this._centerMarker.getLatLng();e&&(this._layer.setLatLng(e),this._hintMarker.on("move",this._syncHintLine,this),this._hintMarker.on("move",this._syncCircleRadius,this),this._hintMarker.setTooltipContent(yt("tooltips.finishCircle")),this._fireCenterPlaced(),this._fireChange(this._layer.getLatLng(),"Draw"))},_syncHintLine(){let e=this._centerMarker.getLatLng(),i=this._getNewDestinationOfHintMarker();this._hintline.setLatLngs([e,i])},_syncCircleRadius(){let e=this._centerMarker.getLatLng(),i=this._hintMarker.getLatLng(),r=this._distanceCalculation(e,i);this.options[this._minRadiusOption]&&r<this.options[this._minRadiusOption]?this._layer.setRadius(this.options[this._minRadiusOption]):this.options[this._maxRadiusOption]&&r>this.options[this._maxRadiusOption]?this._layer.setRadius(this.options[this._maxRadiusOption]):this._layer.setRadius(r)},_syncHintMarker(e){if(this._hintMarker.setLatLng(e.latlng),this._hintMarker.setLatLng(this._getNewDestinationOfHintMarker()),this.options.snappable){let r=e;r.target=this._hintMarker,this._handleSnapping(r)}this._handleHintMarkerSnapping();let i=this._layerGroup&&this._layerGroup.hasLayer(this._centerMarker)?this._centerMarker.getLatLng():this._hintMarker.getLatLng();this._fireChange(i,"Draw")},isRelevantMarker(e){return e instanceof L.CircleMarker&&!(e instanceof L.Circle)&&e.pm&&!e._pmTempLayer},_createMarker(e){if(this.options.requireSnapToFinish&&!this._hintMarker._snapped&&!this._isFirstLayer()||!e.latlng||this._layerIsDragging)return;this._hintMarker._snapped||this._hintMarker.setLatLng(e.latlng);let i=this._hintMarker.getLatLng(),r=new this._BaseCircleClass(i,{radius:this._defaultRadius,...this.options.pathOptions});this._setPane(r,"layerPane"),this._finishLayer(r),r.addTo(this._map.pm._getContainingLayer()),this._extendingCreateMarker(r),this._fireCreate(r),this._cleanupSnapping(),this.options.continueDrawing||this.disable()},_extendingCreateMarker(e){e.pm&&this.options.markerEditable&&e.pm.enable()},_finishShape(e){if(this.options.requireSnapToFinish&&!this._hintMarker._snapped&&!this._isFirstLayer())return;this._hintMarker._snapped||this._hintMarker.setLatLng(e.latlng);let i=this._centerMarker.getLatLng(),r=this._defaultRadius;if(this.options[this._editableOption]){let h=this._hintMarker.getLatLng();r=this._distanceCalculation(i,h),this.options[this._minRadiusOption]&&r<this.options[this._minRadiusOption]?r=this.options[this._minRadiusOption]:this.options[this._maxRadiusOption]&&r>this.options[this._maxRadiusOption]&&(r=this.options[this._maxRadiusOption])}let a={...this.options.pathOptions,radius:r},s=new this._BaseCircleClass(i,a);this._setPane(s,"layerPane"),this._finishLayer(s),s.addTo(this._map.pm._getContainingLayer()),s.pm&&s.pm._updateHiddenPolyCircle(),this._fireCreate(s);let l=this._hintMarker.getLatLng();this.disable(),this.options.continueDrawing&&(this.enable(),this._hintMarker.setLatLng(l))},_getNewDestinationOfHintMarker(){let e=this._hintMarker.getLatLng();if(this.options[this._editableOption]){if(!this._layerGroup.hasLayer(this._centerMarker))return e;let i=this._centerMarker.getLatLng(),r=this._distanceCalculation(i,e);this.options[this._minRadiusOption]&&r<this.options[this._minRadiusOption]?e=ti(this._map,i,e,this._getMinDistanceInMeter()):this.options[this._maxRadiusOption]&&r>this.options[this._maxRadiusOption]&&(e=ti(this._map,i,e,this._getMaxDistanceInMeter()))}return e},_getMinDistanceInMeter(){return L.PM.Utils.pxRadiusToMeterRadius(this.options[this._minRadiusOption],this._map,this._centerMarker.getLatLng())},_getMaxDistanceInMeter(){return L.PM.Utils.pxRadiusToMeterRadius(this.options[this._maxRadiusOption],this._map,this._centerMarker.getLatLng())},_handleHintMarkerSnapping(){if(this.options[this._editableOption]){if(this._hintMarker._snapped){let e=this._centerMarker.getLatLng(),i=this._hintMarker.getLatLng(),r=this._distanceCalculation(e,i);this._layerGroup.hasLayer(this._centerMarker)&&(this.options[this._minRadiusOption]&&r<this.options[this._minRadiusOption]?this._hintMarker.setLatLng(this._hintMarker._orgLatLng):this.options[this._maxRadiusOption]&&r>this.options[this._maxRadiusOption]&&this._hintMarker.setLatLng(this._hintMarker._orgLatLng))}this._hintMarker.setLatLng(this._getNewDestinationOfHintMarker())}},setStyle(){let e={};L.extend(e,this.options.templineStyle),this.options[this._editableOption]&&(e.radius=0),this._layer?.setStyle(e),this._hintline?.setStyle(this.options.hintlineStyle)},_distanceCalculation(e,i){return this._map.project(e).distanceTo(this._map.project(i))}}),Ut.Circle=Ut.CircleMarker.extend({initialize(e){this._map=e,this._shape="Circle",this.toolbarButtonName="drawCircle",this._BaseCircleClass=L.Circle,this._minRadiusOption="minRadiusCircle",this._maxRadiusOption="maxRadiusCircle",this._editableOption="resizeableCircle",this._defaultRadius=100},_extendingEnable(){},_extendingDisable(){},_extendingCreateMarker(){},isRelevantMarker(){},_getMinDistanceInMeter(){return this.options[this._minRadiusOption]},_getMaxDistanceInMeter(){return this.options[this._maxRadiusOption]},_distanceCalculation(e,i){return this._map.distance(e,i)}});function _e(e){if(!e)throw new Error("coord is required");if(!Array.isArray(e)){if(e.type==="Feature"&&e.geometry!==null&&e.geometry.type==="Point")return e.geometry.coordinates;if(e.type==="Point")return e.coordinates}if(Array.isArray(e)&&e.length>=2&&!Array.isArray(e[0])&&!Array.isArray(e[1]))return e;throw new Error("coord must be GeoJSON Point or an Array of numbers")}function be(e){if(Array.isArray(e))return e;if(e.type==="Feature"){if(e.geometry!==null)return e.geometry.coordinates}else if(e.coordinates)return e.coordinates;throw new Error("coords must be GeoJSON Feature, Geometry Object or an Array")}function $t(e){return e.type==="Feature"?e.geometry:e}function qe(e,i){return e.type==="FeatureCollection"?"FeatureCollection":e.type==="GeometryCollection"?"GeometryCollection":e.type==="Feature"&&e.geometry!==null?e.geometry.type:e.type}function jn(e,i,r){if(e!==null)for(var a,s,l,h,d,f,m,E=0,P=0,j,R=e.type,W=R==="FeatureCollection",X=R==="Feature",nt=W?e.features.length:1,vt=0;vt<nt;vt++){m=W?e.features[vt].geometry:X?e.geometry:e,j=m?m.type==="GeometryCollection":!1,d=j?m.geometries.length:1;for(var k=0;k<d;k++){var S=0,O=0;if(h=j?m.geometries[k]:m,h!==null){f=h.coordinates;var K=h.type;switch(E=r&&(K==="Polygon"||K==="MultiPolygon")?1:0,K){case null:break;case"Point":if(i(f,P,vt,S,O)===!1)return!1;P++,S++;break;case"LineString":case"MultiPoint":for(a=0;a<f.length;a++){if(i(f[a],P,vt,S,O)===!1)return!1;P++,K==="MultiPoint"&&S++}K==="LineString"&&S++;break;case"Polygon":case"MultiLineString":for(a=0;a<f.length;a++){for(s=0;s<f[a].length-E;s++){if(i(f[a][s],P,vt,S,O)===!1)return!1;P++}K==="MultiLineString"&&S++,K==="Polygon"&&O++}K==="Polygon"&&S++;break;case"MultiPolygon":for(a=0;a<f.length;a++){for(O=0,s=0;s<f[a].length;s++){for(l=0;l<f[a][s].length-E;l++){if(i(f[a][s][l],P,vt,S,O)===!1)return!1;P++}O++}S++}break;case"GeometryCollection":for(a=0;a<h.geometries.length;a++)if(jn(h.geometries[a],i,r)===!1)return!1;break;default:throw new Error("Unknown Geometry Type")}}}}}function re(e,i){if(e.type==="Feature")i(e,0);else if(e.type==="FeatureCollection")for(var r=0;r<e.features.length&&i(e.features[r],r)!==!1;r++);}function $a(e,i,r){var a=r;return re(e,function(s,l){l===0&&r===void 0?a=s:a=i(a,s,l)}),a}function ii(e,i){var r,a,s,l,h,d,f,m,E,P,j=0,R=e.type==="FeatureCollection",W=e.type==="Feature",X=R?e.features.length:1;for(r=0;r<X;r++){for(d=R?e.features[r].geometry:W?e.geometry:e,m=R?e.features[r].properties:W?e.properties:{},E=R?e.features[r].bbox:W?e.bbox:void 0,P=R?e.features[r].id:W?e.id:void 0,f=d?d.type==="GeometryCollection":!1,h=f?d.geometries.length:1,s=0;s<h;s++){if(l=f?d.geometries[s]:d,l===null){if(i(null,j,m,E,P)===!1)return!1;continue}switch(l.type){case"Point":case"LineString":case"MultiPoint":case"Polygon":case"MultiLineString":case"MultiPolygon":{if(i(l,j,m,E,P)===!1)return!1;break}case"GeometryCollection":{for(a=0;a<l.geometries.length;a++)if(i(l.geometries[a],j,m,E,P)===!1)return!1;break}default:throw new Error("Unknown Geometry Type")}}j++}}function Un(e,i){ii(e,function(r,a,s,l,h){var d=r===null?null:r.type;switch(d){case null:case"Point":case"LineString":case"Polygon":return i(se(r,s,{bbox:l,id:h}),a,0)===!1?!1:void 0}var f;switch(d){case"MultiPoint":f="Point";break;case"MultiLineString":f="LineString";break;case"MultiPolygon":f="Polygon";break}for(var m=0;m<r.coordinates.length;m++){var E=r.coordinates[m],P={type:f,coordinates:E};if(i(se(P,s),a,m)===!1)return!1}})}function Li(e){if(!e)throw new Error("geojson is required");var i=[];return Un(e,function(r){Jr(r,i)}),It(i)}function Jr(e,i){var r=[],a=e.geometry;if(a!==null){switch(a.type){case"Polygon":r=be(a);break;case"LineString":r=[be(a)]}r.forEach(function(s){var l=Ui(s,e.properties);l.forEach(function(h){h.id=i.length,i.push(h)})})}}function Ui(e,i){var r=[];return e.reduce(function(a,s){var l=Be([a,s],i);return l.bbox=Xa(a,s),r.push(l),s}),r}function Xa(e,i){var r=e[0],a=e[1],s=i[0],l=i[1],h=r<s?r:s,d=a<l?a:l,f=r>s?r:s,m=a>l?a:l;return[h,d,f,m]}var xe=Li,Vi=$(ft(),1);function Qa(e,i){var r={},a=[];if(e.type==="LineString"&&(e=se(e)),i.type==="LineString"&&(i=se(i)),e.type==="Feature"&&i.type==="Feature"&&e.geometry!==null&&i.geometry!==null&&e.geometry.type==="LineString"&&i.geometry.type==="LineString"&&e.geometry.coordinates.length===2&&i.geometry.coordinates.length===2){var s=Hi(e,i);return s&&a.push(s),It(a)}var l=(0,Vi.default)();return l.load(xe(i)),re(xe(e),function(h){re(l.search(h),function(d){var f=Hi(h,d);if(f){var m=be(f).join(",");r[m]||(r[m]=!0,a.push(f))}})}),It(a)}function Hi(e,i){var r=be(e),a=be(i);if(r.length!==2)throw new Error("<intersects> line1 must only contain 2 coordinates");if(a.length!==2)throw new Error("<intersects> line2 must only contain 2 coordinates");var s=r[0][0],l=r[0][1],h=r[1][0],d=r[1][1],f=a[0][0],m=a[0][1],E=a[1][0],P=a[1][1],j=(P-m)*(h-s)-(E-f)*(d-l),R=(E-f)*(l-m)-(P-m)*(s-f),W=(h-s)*(l-m)-(d-l)*(s-f);if(j===0)return null;var X=R/j,nt=W/j;if(X>=0&&X<=1&&nt>=0&&nt<=1){var vt=s+X*(h-s),k=l+X*(d-l);return ei([vt,k])}return null}var We=Qa,Ce=$(ft(),1);function to(e,i,r){r===void 0&&(r={});var a=_e(e),s=_e(i),l=ne(s[1]-a[1]),h=ne(s[0]-a[0]),d=ne(a[1]),f=ne(s[1]),m=Math.pow(Math.sin(l/2),2)+Math.pow(Math.sin(h/2),2)*Math.cos(d)*Math.cos(f);return qr(2*Math.atan2(Math.sqrt(m),Math.sqrt(1-m)),r.units)}var ue=to;function eo(e){var i=e[0],r=e[1],a=e[2],s=e[3],l=ue(e.slice(0,2),[a,r]),h=ue(e.slice(0,2),[i,s]);if(l>=h){var d=(r+s)/2;return[i,d-(a-i)/2,a,d+(a-i)/2]}else{var f=(i+a)/2;return[f-(s-r)/2,r,f+(s-r)/2,s]}}var Pe=eo;function ni(e){var i=[1/0,1/0,-1/0,-1/0];return jn(e,function(r){i[0]>r[0]&&(i[0]=r[0]),i[1]>r[1]&&(i[1]=r[1]),i[2]<r[0]&&(i[2]=r[0]),i[3]<r[1]&&(i[3]=r[1])}),i}ni.default=ni;var ri=ni;function Vn(e,i){i===void 0&&(i={});var r=i.precision,a=i.coordinates,s=i.mutate;if(r=r==null||isNaN(r)?6:r,a=a==null||isNaN(a)?3:a,!e)throw new Error("<geojson> is required");if(typeof r!="number")throw new Error("<precision> must be a number");if(typeof a!="number")throw new Error("<coordinates> must be a number");(s===!1||s===void 0)&&(e=JSON.parse(JSON.stringify(e)));var l=Math.pow(10,r);return jn(e,function(h){Ki(h,l,a)}),e}function Ki(e,i,r){e.length>r&&e.splice(r,e.length);for(var a=0;a<e.length;a++)e[a]=Math.round(e[a]*i)/i;return e}var Hn=Vn;function bi(e,i,r){if(r===void 0&&(r={}),r.final===!0)return ai(e,i);var a=_e(e),s=_e(i),l=ne(a[0]),h=ne(s[0]),d=ne(a[1]),f=ne(s[1]),m=Math.sin(h-l)*Math.cos(f),E=Math.cos(d)*Math.sin(f)-Math.sin(d)*Math.cos(f)*Math.cos(h-l);return Zn(Math.atan2(m,E))}function ai(e,i){var r=bi(i,e);return r=(r+180)%360,r}function xi(e,i,r,a){a===void 0&&(a={});var s=_e(e),l=ne(s[0]),h=ne(s[1]),d=ne(r),f=Wr(i,a.units),m=Math.asin(Math.sin(h)*Math.cos(f)+Math.cos(h)*Math.sin(f)*Math.cos(d)),E=l+Math.atan2(Math.sin(d)*Math.sin(f)*Math.cos(h),Math.cos(f)-Math.sin(h)*Math.sin(m)),P=Zn(E),j=Zn(m);return ei([P,j],a.properties)}function Kn(e,i,r){r===void 0&&(r={});var a=ei([1/0,1/0],{dist:1/0}),s=0;return Un(e,function(l){for(var h=be(l),d=0;d<h.length-1;d++){var f=ei(h[d]);f.properties.dist=ue(i,f,r);var m=ei(h[d+1]);m.properties.dist=ue(i,m,r);var E=ue(f,m,r),P=Math.max(f.properties.dist,m.properties.dist),j=bi(f,m),R=xi(i,P,j+90,r),W=xi(i,P,j-90,r),X=We(Be([R.geometry.coordinates,W.geometry.coordinates]),Be([f.geometry.coordinates,m.geometry.coordinates])),nt=null;X.features.length>0&&(nt=X.features[0],nt.properties.dist=ue(i,nt,r),nt.properties.location=s+ue(f,nt,r)),f.properties.dist<a.properties.dist&&(a=f,a.properties.index=d,a.properties.location=s),m.properties.dist<a.properties.dist&&(a=m,a.properties.index=d+1,a.properties.location=s+E),nt&&nt.properties.dist<a.properties.dist&&(a=nt,a.properties.index=d),s+=E}}),a}var Yr=Kn;function io(e,i){if(!e)throw new Error("line is required");if(!i)throw new Error("splitter is required");var r=qe(e),a=qe(i);if(r!=="LineString")throw new Error("line must be LineString");if(a==="FeatureCollection")throw new Error("splitter cannot be a FeatureCollection");if(a==="GeometryCollection")throw new Error("splitter cannot be a GeometryCollection");var s=Hn(i,{precision:7});switch(a){case"Point":return qn(e,s);case"MultiPoint":return Ci(e,s);case"LineString":case"MultiLineString":case"Polygon":case"MultiPolygon":return Ci(e,We(e,s))}}function Ci(e,i){var r=[],a=(0,Ce.default)();return Un(i,function(s){if(r.forEach(function(d,f){d.id=f}),!r.length)r=qn(e,s).features,r.forEach(function(d){d.bbox||(d.bbox=Pe(ri(d)))}),a.load(It(r));else{var l=a.search(s);if(l.features.length){var h=Wn(s,l);r=r.filter(function(d){return d.id!==h.id}),a.remove(h),re(qn(h,s),function(d){r.push(d),a.insert(d)})}}}),It(r)}function qn(e,i){var r=[],a=be(e)[0],s=be(e)[e.geometry.coordinates.length-1];if(Jn(a,_e(i))||Jn(s,_e(i)))return It([e]);var l=(0,Ce.default)(),h=xe(e);l.load(h);var d=l.search(i);if(!d.features.length)return It([e]);var f=Wn(i,d),m=[a],E=$a(h,function(P,j,R){var W=be(j)[1],X=_e(i);return R===f.id?(P.push(X),r.push(Be(P)),Jn(X,W)?[X]:[X,W]):(P.push(W),P)},m);return E.length>1&&r.push(Be(E)),It(r)}function Wn(e,i){if(!i.features.length)throw new Error("lines must contain features");if(i.features.length===1)return i.features[0];var r,a=1/0;return re(i,function(s){var l=Yr(s,e),h=l.properties.dist;h<a&&(r=s,a=h)}),r}function Jn(e,i){return e[0]===i[0]&&e[1]===i[1]}var $r=io;function qi(e,i,r){if(r===void 0&&(r={}),!e)throw new Error("point is required");if(!i)throw new Error("polygon is required");var a=_e(e),s=$t(i),l=s.type,h=i.bbox,d=s.coordinates;if(h&&Wi(a,h)===!1)return!1;l==="Polygon"&&(d=[d]);for(var f=!1,m=0;m<d.length&&!f;m++)if(me(a,d[m][0],r.ignoreBoundary)){for(var E=!1,P=1;P<d[m].length&&!E;)me(a,d[m][P],!r.ignoreBoundary)&&(E=!0),P++;E||(f=!0)}return f}function me(e,i,r){var a=!1;i[0][0]===i[i.length-1][0]&&i[0][1]===i[i.length-1][1]&&(i=i.slice(0,i.length-1));for(var s=0,l=i.length-1;s<i.length;l=s++){var h=i[s][0],d=i[s][1],f=i[l][0],m=i[l][1],E=e[1]*(h-f)+d*(f-e[0])+m*(e[0]-h)===0&&(h-e[0])*(f-e[0])<=0&&(d-e[1])*(m-e[1])<=0;if(E)return!r;var P=d>e[1]!=m>e[1]&&e[0]<(f-h)*(e[1]-d)/(m-d)+h;P&&(a=!a)}return a}function Wi(e,i){return i[0]<=e[0]&&i[1]<=e[1]&&i[2]>=e[0]&&i[3]>=e[1]}function no(e,i,r){r===void 0&&(r={});for(var a=_e(e),s=be(i),l=0;l<s.length-1;l++){var h=!1;if(r.ignoreEndVertices&&(l===0&&(h="start"),l===s.length-2&&(h="end"),l===0&&l+1===s.length-1&&(h="both")),Ji(s[l],s[l+1],a,h,typeof r.epsilon>"u"?null:r.epsilon))return!0}return!1}function Ji(e,i,r,a,s){var l=r[0],h=r[1],d=e[0],f=e[1],m=i[0],E=i[1],P=r[0]-d,j=r[1]-f,R=m-d,W=E-f,X=P*W-j*R;if(s!==null){if(Math.abs(X)>s)return!1}else if(X!==0)return!1;if(a){if(a==="start")return Math.abs(R)>=Math.abs(W)?R>0?d<l&&l<=m:m<=l&&l<d:W>0?f<h&&h<=E:E<=h&&h<f;if(a==="end")return Math.abs(R)>=Math.abs(W)?R>0?d<=l&&l<m:m<l&&l<=d:W>0?f<=h&&h<E:E<h&&h<=f;if(a==="both")return Math.abs(R)>=Math.abs(W)?R>0?d<l&&l<m:m<l&&l<d:W>0?f<h&&h<E:E<h&&h<f}else return Math.abs(R)>=Math.abs(W)?R>0?d<=l&&l<=m:m<=l&&l<=d:W>0?f<=h&&h<=E:E<=h&&h<=f;return!1}var wi=no;function Xr(e,i){var r=$t(e),a=$t(i),s=r.type,l=a.type,h=r.coordinates,d=a.coordinates;switch(s){case"Point":switch(l){case"Point":return Yi(h,d);default:throw new Error("feature2 "+l+" geometry not supported")}case"MultiPoint":switch(l){case"Point":return ro(r,a);case"MultiPoint":return ki(r,a);default:throw new Error("feature2 "+l+" geometry not supported")}case"LineString":switch(l){case"Point":return wi(a,r,{ignoreEndVertices:!0});case"LineString":return Qr(r,a);case"MultiPoint":return ao(r,a);default:throw new Error("feature2 "+l+" geometry not supported")}case"Polygon":switch(l){case"Point":return qi(a,r,{ignoreBoundary:!0});case"LineString":return ta(r,a);case"Polygon":return oo(r,a);case"MultiPoint":return oi(r,a);default:throw new Error("feature2 "+l+" geometry not supported")}default:throw new Error("feature1 "+s+" geometry not supported")}}function ro(e,i){var r,a=!1;for(r=0;r<e.coordinates.length;r++)if(Yi(e.coordinates[r],i.coordinates)){a=!0;break}return a}function ki(e,i){for(var r=0,a=i.coordinates;r<a.length;r++){for(var s=a[r],l=!1,h=0,d=e.coordinates;h<d.length;h++){var f=d[h];if(Yi(s,f)){l=!0;break}}if(!l)return!1}return!0}function ao(e,i){for(var r=!1,a=0,s=i.coordinates;a<s.length;a++){var l=s[a];if(wi(l,e,{ignoreEndVertices:!0})&&(r=!0),!wi(l,e))return!1}return!!r}function oi(e,i){for(var r=0,a=i.coordinates;r<a.length;r++){var s=a[r];if(!qi(s,e,{ignoreBoundary:!0}))return!1}return!0}function Qr(e,i){for(var r=!1,a=0,s=i.coordinates;a<s.length;a++){var l=s[a];if(wi({type:"Point",coordinates:l},e,{ignoreEndVertices:!0})&&(r=!0),!wi({type:"Point",coordinates:l},e,{ignoreEndVertices:!1}))return!1}return r}function ta(e,i){var r=!1,a=0,s=ri(e),l=ri(i);if(!we(s,l))return!1;for(a;a<i.coordinates.length-1;a++){var h=ea(i.coordinates[a],i.coordinates[a+1]);if(qi({type:"Point",coordinates:h},e,{ignoreBoundary:!0})){r=!0;break}}return r}function oo(e,i){if(e.type==="Feature"&&e.geometry===null||i.type==="Feature"&&i.geometry===null)return!1;var r=ri(e),a=ri(i);if(!we(r,a))return!1;for(var s=$t(i).coordinates,l=0,h=s;l<h.length;l++)for(var d=h[l],f=0,m=d;f<m.length;f++){var E=m[f];if(!qi(E,e))return!1}return!0}function we(e,i){return!(e[0]>i[0]||e[2]<i[2]||e[1]>i[1]||e[3]<i[3])}function Yi(e,i){return e[0]===i[0]&&e[1]===i[1]}function ea(e,i){return[(e[0]+i[0])/2,(e[1]+i[1])/2]}var Mi=$(Mt()),ia=e=>()=>e,si=e=>{let i=e?(r,a)=>a.minus(r).abs().isLessThanOrEqualTo(e):ia(!1);return(r,a)=>i(r,a)?0:r.comparedTo(a)};function Ei(e){let i=e?(r,a,s,l,h)=>r.exponentiatedBy(2).isLessThanOrEqualTo(l.minus(a).exponentiatedBy(2).plus(h.minus(s).exponentiatedBy(2)).times(e)):ia(!1);return(r,a,s)=>{let l=r.x,h=r.y,d=s.x,f=s.y,m=h.minus(f).times(a.x.minus(d)).minus(l.minus(d).times(a.y.minus(f)));return i(m,l,h,d,f)?0:m.comparedTo(0)}}var na=/^-?(?:\d+(?:\.\d*)?|\.\d+)(?:e[+-]?\d+)?$/i,$i=Math.ceil,le=Math.floor,Wt="[BigNumber Error] ",Yn=Wt+"Number primitive has more than 15 significant digits: ",he=1e14,ct=14,Xi=9007199254740991,$n=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8,1e9,1e10,1e11,1e12,1e13],Se=1e7,zt=1e9;function ra(e){var i,r,a,s=k.prototype={constructor:k,toString:null,valueOf:null},l=new k(1),h=20,d=4,f=-7,m=21,E=-1e7,P=1e7,j=!1,R=1,W=0,X={prefix:"",groupSize:3,secondaryGroupSize:0,groupSeparator:",",decimalSeparator:".",fractionGroupSize:0,fractionGroupSeparator:"\xA0",suffix:""},nt="0123456789abcdefghijklmnopqrstuvwxyz",vt=!0;function k(p,y){var x,b,C,w,D,T,F,G,N=this;if(!(N instanceof k))return new k(p,y);if(y==null){if(p&&p._isBigNumber===!0){N.s=p.s,!p.c||p.e>P?N.c=N.e=null:p.e<E?N.c=[N.e=0]:(N.e=p.e,N.c=p.c.slice());return}if((T=typeof p=="number")&&p*0==0){if(N.s=1/p<0?(p=-p,-1):1,p===~~p){for(w=0,D=p;D>=10;D/=10,w++);w>P?N.c=N.e=null:(N.e=w,N.c=[p]);return}G=String(p)}else{if(!na.test(G=String(p)))return a(N,G,T);N.s=G.charCodeAt(0)==45?(G=G.slice(1),-1):1}(w=G.indexOf("."))>-1&&(G=G.replace(".","")),(D=G.search(/e/i))>0?(w<0&&(w=D),w+=+G.slice(D+1),G=G.substring(0,D)):w<0&&(w=G.length)}else{if(u(y,2,nt.length,"Base"),y==10&&vt)return N=new k(p),I(N,h+N.e+1,d);if(G=String(p),T=typeof p=="number"){if(p*0!=0)return a(N,G,T,y);if(N.s=1/p<0?(G=G.slice(1),-1):1,k.DEBUG&&G.replace(/^0\.0*|\./,"").length>15)throw Error(Yn+p)}else N.s=G.charCodeAt(0)===45?(G=G.slice(1),-1):1;for(x=nt.slice(0,y),w=D=0,F=G.length;D<F;D++)if(x.indexOf(b=G.charAt(D))<0){if(b=="."){if(D>w){w=F;continue}}else if(!C&&(G==G.toUpperCase()&&(G=G.toLowerCase())||G==G.toLowerCase()&&(G=G.toUpperCase()))){C=!0,D=-1,w=0;continue}return a(N,String(p),T,y)}T=!1,G=r(G,y,10,N.s),(w=G.indexOf("."))>-1?G=G.replace(".",""):w=G.length}for(D=0;G.charCodeAt(D)===48;D++);for(F=G.length;G.charCodeAt(--F)===48;);if(G=G.slice(D,++F)){if(F-=D,T&&k.DEBUG&&F>15&&(p>Xi||p!==le(p)))throw Error(Yn+N.s*p);if((w=w-D-1)>P)N.c=N.e=null;else if(w<E)N.c=[N.e=0];else{if(N.e=w,N.c=[],D=(w+1)%ct,w<0&&(D+=ct),D<F){for(D&&N.c.push(+G.slice(0,D)),F-=ct;D<F;)N.c.push(+G.slice(D,D+=ct));D=ct-(G=G.slice(D)).length}else D-=F;for(;D--;G+="0");N.c.push(+G)}}else N.c=[N.e=0]}k.clone=ra,k.ROUND_UP=0,k.ROUND_DOWN=1,k.ROUND_CEIL=2,k.ROUND_FLOOR=3,k.ROUND_HALF_UP=4,k.ROUND_HALF_DOWN=5,k.ROUND_HALF_EVEN=6,k.ROUND_HALF_CEIL=7,k.ROUND_HALF_FLOOR=8,k.EUCLID=9,k.config=k.set=function(p){var y,x;if(p!=null)if(typeof p=="object"){if(p.hasOwnProperty(y="DECIMAL_PLACES")&&(x=p[y],u(x,0,zt,y),h=x),p.hasOwnProperty(y="ROUNDING_MODE")&&(x=p[y],u(x,0,8,y),d=x),p.hasOwnProperty(y="EXPONENTIAL_AT")&&(x=p[y],x&&x.pop?(u(x[0],-zt,0,y),u(x[1],0,zt,y),f=x[0],m=x[1]):(u(x,-zt,zt,y),f=-(m=x<0?-x:x))),p.hasOwnProperty(y="RANGE"))if(x=p[y],x&&x.pop)u(x[0],-zt,-1,y),u(x[1],1,zt,y),E=x[0],P=x[1];else if(u(x,-zt,zt,y),x)E=-(P=x<0?-x:x);else throw Error(Wt+y+" cannot be zero: "+x);if(p.hasOwnProperty(y="CRYPTO"))if(x=p[y],x===!!x)if(x)if(typeof crypto<"u"&&crypto&&(crypto.getRandomValues||crypto.randomBytes))j=x;else throw j=!x,Error(Wt+"crypto unavailable");else j=x;else throw Error(Wt+y+" not true or false: "+x);if(p.hasOwnProperty(y="MODULO_MODE")&&(x=p[y],u(x,0,9,y),R=x),p.hasOwnProperty(y="POW_PRECISION")&&(x=p[y],u(x,0,zt,y),W=x),p.hasOwnProperty(y="FORMAT"))if(x=p[y],typeof x=="object")X=x;else throw Error(Wt+y+" not an object: "+x);if(p.hasOwnProperty(y="ALPHABET"))if(x=p[y],typeof x=="string"&&!/^.?$|[+\-.\s]|(.).*\1/.test(x))vt=x.slice(0,10)=="0123456789",nt=x;else throw Error(Wt+y+" invalid: "+x)}else throw Error(Wt+"Object expected: "+p);return{DECIMAL_PLACES:h,ROUNDING_MODE:d,EXPONENTIAL_AT:[f,m],RANGE:[E,P],CRYPTO:j,MODULO_MODE:R,POW_PRECISION:W,FORMAT:X,ALPHABET:nt}},k.isBigNumber=function(p){if(!p||p._isBigNumber!==!0)return!1;if(!k.DEBUG)return!0;var y,x,b=p.c,C=p.e,w=p.s;t:if({}.toString.call(b)=="[object Array]"){if((w===1||w===-1)&&C>=-zt&&C<=zt&&C===le(C)){if(b[0]===0){if(C===0&&b.length===1)return!0;break t}if(y=(C+1)%ct,y<1&&(y+=ct),String(b[0]).length==y){for(y=0;y<b.length;y++)if(x=b[y],x<0||x>=he||x!==le(x))break t;if(x!==0)return!0}}}else if(b===null&&C===null&&(w===null||w===1||w===-1))return!0;throw Error(Wt+"Invalid BigNumber: "+p)},k.maximum=k.max=function(){return O(arguments,-1)},k.minimum=k.min=function(){return O(arguments,1)},k.random=function(){var p=9007199254740992,y=Math.random()*p&2097151?function(){return le(Math.random()*p)}:function(){return(Math.random()*1073741824|0)*8388608+(Math.random()*8388608|0)};return function(x){var b,C,w,D,T,F=0,G=[],N=new k(l);if(x==null?x=h:u(x,0,zt),D=$i(x/ct),j)if(crypto.getRandomValues){for(b=crypto.getRandomValues(new Uint32Array(D*=2));F<D;)T=b[F]*131072+(b[F+1]>>>11),T>=9e15?(C=crypto.getRandomValues(new Uint32Array(2)),b[F]=C[0],b[F+1]=C[1]):(G.push(T%1e14),F+=2);F=D/2}else if(crypto.randomBytes){for(b=crypto.randomBytes(D*=7);F<D;)T=(b[F]&31)*281474976710656+b[F+1]*1099511627776+b[F+2]*4294967296+b[F+3]*16777216+(b[F+4]<<16)+(b[F+5]<<8)+b[F+6],T>=9e15?crypto.randomBytes(7).copy(b,F):(G.push(T%1e14),F+=7);F=D/7}else throw j=!1,Error(Wt+"crypto unavailable");if(!j)for(;F<D;)T=y(),T<9e15&&(G[F++]=T%1e14);for(D=G[--F],x%=ct,D&&x&&(T=$n[ct-x],G[F]=le(D/T)*T);G[F]===0;G.pop(),F--);if(F<0)G=[w=0];else{for(w=-1;G[0]===0;G.splice(0,1),w-=ct);for(F=1,T=G[0];T>=10;T/=10,F++);F<ct&&(w-=ct-F)}return N.e=w,N.c=G,N}}(),k.sum=function(){for(var p=1,y=arguments,x=new k(y[0]);p<y.length;)x=x.plus(y[p++]);return x},r=function(){var p="0123456789";function y(x,b,C,w){for(var D,T=[0],F,G=0,N=x.length;G<N;){for(F=T.length;F--;T[F]*=b);for(T[0]+=w.indexOf(x.charAt(G++)),D=0;D<T.length;D++)T[D]>C-1&&(T[D+1]==null&&(T[D+1]=0),T[D+1]+=T[D]/C|0,T[D]%=C)}return T.reverse()}return function(x,b,C,w,D){var T,F,G,N,J,it,rt,st,wt=x.indexOf("."),Bt=h,xt=d;for(wt>=0&&(N=W,W=0,x=x.replace(".",""),st=new k(b),it=st.pow(x.length-wt),W=N,st.c=y(B(n(it.c),it.e,"0"),10,C,p),st.e=st.c.length),rt=y(x,b,C,D?(T=nt,p):(T=p,nt)),G=N=rt.length;rt[--N]==0;rt.pop());if(!rt[0])return T.charAt(0);if(wt<0?--G:(it.c=rt,it.e=G,it.s=w,it=i(it,st,Bt,xt,C),rt=it.c,J=it.r,G=it.e),F=G+Bt+1,wt=rt[F],N=C/2,J=J||F<0||rt[F+1]!=null,J=xt<4?(wt!=null||J)&&(xt==0||xt==(it.s<0?3:2)):wt>N||wt==N&&(xt==4||J||xt==6&&rt[F-1]&1||xt==(it.s<0?8:7)),F<1||!rt[0])x=J?B(T.charAt(1),-Bt,T.charAt(0)):T.charAt(0);else{if(rt.length=F,J)for(--C;++rt[--F]>C;)rt[F]=0,F||(++G,rt=[1].concat(rt));for(N=rt.length;!rt[--N];);for(wt=0,x="";wt<=N;x+=T.charAt(rt[wt++]));x=B(x,G,T.charAt(0))}return x}}(),i=function(){function p(b,C,w){var D,T,F,G,N=0,J=b.length,it=C%Se,rt=C/Se|0;for(b=b.slice();J--;)F=b[J]%Se,G=b[J]/Se|0,D=rt*F+G*it,T=it*F+D%Se*Se+N,N=(T/w|0)+(D/Se|0)+rt*G,b[J]=T%w;return N&&(b=[N].concat(b)),b}function y(b,C,w,D){var T,F;if(w!=D)F=w>D?1:-1;else for(T=F=0;T<w;T++)if(b[T]!=C[T]){F=b[T]>C[T]?1:-1;break}return F}function x(b,C,w,D){for(var T=0;w--;)b[w]-=T,T=b[w]<C[w]?1:0,b[w]=T*D+b[w]-C[w];for(;!b[0]&&b.length>1;b.splice(0,1));}return function(b,C,w,D,T){var F,G,N,J,it,rt,st,wt,Bt,xt,Pt,Qt,da,ho,co,Ie,ir,ke=b.s==C.s?1:-1,ae=b.c,Rt=C.c;if(!ae||!ae[0]||!Rt||!Rt[0])return new k(!b.s||!C.s||(ae?Rt&&ae[0]==Rt[0]:!Rt)?NaN:ae&&ae[0]==0||!Rt?ke*0:ke/0);for(wt=new k(ke),Bt=wt.c=[],G=b.e-C.e,ke=w+G+1,T||(T=he,G=t(b.e/ct)-t(C.e/ct),ke=ke/ct|0),N=0;Rt[N]==(ae[N]||0);N++);if(Rt[N]>(ae[N]||0)&&G--,ke<0)Bt.push(1),J=!0;else{for(ho=ae.length,Ie=Rt.length,N=0,ke+=2,it=le(T/(Rt[0]+1)),it>1&&(Rt=p(Rt,it,T),ae=p(ae,it,T),Ie=Rt.length,ho=ae.length),da=Ie,xt=ae.slice(0,Ie),Pt=xt.length;Pt<Ie;xt[Pt++]=0);ir=Rt.slice(),ir=[0].concat(ir),co=Rt[0],Rt[1]>=T/2&&co++;do{if(it=0,F=y(Rt,xt,Ie,Pt),F<0){if(Qt=xt[0],Ie!=Pt&&(Qt=Qt*T+(xt[1]||0)),it=le(Qt/co),it>1)for(it>=T&&(it=T-1),rt=p(Rt,it,T),st=rt.length,Pt=xt.length;y(rt,xt,st,Pt)==1;)it--,x(rt,Ie<st?ir:Rt,st,T),st=rt.length,F=1;else it==0&&(F=it=1),rt=Rt.slice(),st=rt.length;if(st<Pt&&(rt=[0].concat(rt)),x(xt,rt,Pt,T),Pt=xt.length,F==-1)for(;y(Rt,xt,Ie,Pt)<1;)it++,x(xt,Ie<Pt?ir:Rt,Pt,T),Pt=xt.length}else F===0&&(it++,xt=[0]);Bt[N++]=it,xt[0]?xt[Pt++]=ae[da]||0:(xt=[ae[da]],Pt=1)}while((da++<ho||xt[0]!=null)&&ke--);J=xt[0]!=null,Bt[0]||Bt.splice(0,1)}if(T==he){for(N=1,ke=Bt[0];ke>=10;ke/=10,N++);I(wt,w+(wt.e=N+G*ct-1)+1,D,J)}else wt.e=G,wt.r=+J;return wt}}();function S(p,y,x,b){var C,w,D,T,F;if(x==null?x=d:u(x,0,8),!p.c)return p.toString();if(C=p.c[0],D=p.e,y==null)F=n(p.c),F=b==1||b==2&&(D<=f||D>=m)?g(F,D):B(F,D,"0");else if(p=I(new k(p),y,x),w=p.e,F=n(p.c),T=F.length,b==1||b==2&&(y<=w||w<=f)){for(;T<y;F+="0",T++);F=g(F,w)}else if(y-=D,F=B(F,w,"0"),w+1>T){if(--y>0)for(F+=".";y--;F+="0");}else if(y+=w-T,y>0)for(w+1==T&&(F+=".");y--;F+="0");return p.s<0&&C?"-"+F:F}function O(p,y){for(var x,b,C=1,w=new k(p[0]);C<p.length;C++)b=new k(p[C]),(!b.s||(x=o(w,b))===y||x===0&&w.s===y)&&(w=b);return w}function K(p,y,x){for(var b=1,C=y.length;!y[--C];y.pop());for(C=y[0];C>=10;C/=10,b++);return(x=b+x*ct-1)>P?p.c=p.e=null:x<E?p.c=[p.e=0]:(p.e=x,p.c=y),p}a=function(){var p=/^(-?)0([xbo])(?=\w[\w.]*$)/i,y=/^([^.]+)\.$/,x=/^\.([^.]+)$/,b=/^-?(Infinity|NaN)$/,C=/^\s*\+(?=[\w.])|^\s+|\s+$/g;return function(w,D,T,F){var G,N=T?D:D.replace(C,"");if(b.test(N))w.s=isNaN(N)?null:N<0?-1:1;else{if(!T&&(N=N.replace(p,function(J,it,rt){return G=(rt=rt.toLowerCase())=="x"?16:rt=="b"?2:8,!F||F==G?it:J}),F&&(G=F,N=N.replace(y,"$1").replace(x,"0.$1")),D!=N))return new k(N,G);if(k.DEBUG)throw Error(Wt+"Not a"+(F?" base "+F:"")+" number: "+D);w.s=null}w.c=w.e=null}}();function I(p,y,x,b){var C,w,D,T,F,G,N,J=p.c,it=$n;if(J){t:{for(C=1,T=J[0];T>=10;T/=10,C++);if(w=y-C,w<0)w+=ct,D=y,F=J[G=0],N=le(F/it[C-D-1]%10);else if(G=$i((w+1)/ct),G>=J.length)if(b){for(;J.length<=G;J.push(0));F=N=0,C=1,w%=ct,D=w-ct+1}else break t;else{for(F=T=J[G],C=1;T>=10;T/=10,C++);w%=ct,D=w-ct+C,N=D<0?0:le(F/it[C-D-1]%10)}if(b=b||y<0||J[G+1]!=null||(D<0?F:F%it[C-D-1]),b=x<4?(N||b)&&(x==0||x==(p.s<0?3:2)):N>5||N==5&&(x==4||b||x==6&&(w>0?D>0?F/it[C-D]:0:J[G-1])%10&1||x==(p.s<0?8:7)),y<1||!J[0])return J.length=0,b?(y-=p.e+1,J[0]=it[(ct-y%ct)%ct],p.e=-y||0):J[0]=p.e=0,p;if(w==0?(J.length=G,T=1,G--):(J.length=G+1,T=it[ct-w],J[G]=D>0?le(F/it[C-D]%it[D])*T:0),b)for(;;)if(G==0){for(w=1,D=J[0];D>=10;D/=10,w++);for(D=J[0]+=T,T=1;D>=10;D/=10,T++);w!=T&&(p.e++,J[0]==he&&(J[0]=1));break}else{if(J[G]+=T,J[G]!=he)break;J[G--]=0,T=1}for(w=J.length;J[--w]===0;J.pop());}p.e>P?p.c=p.e=null:p.e<E&&(p.c=[p.e=0])}return p}function U(p){var y,x=p.e;return x===null?p.toString():(y=n(p.c),y=x<=f||x>=m?g(y,x):B(y,x,"0"),p.s<0?"-"+y:y)}return s.absoluteValue=s.abs=function(){var p=new k(this);return p.s<0&&(p.s=1),p},s.comparedTo=function(p,y){return o(this,new k(p,y))},s.decimalPlaces=s.dp=function(p,y){var x,b,C,w=this;if(p!=null)return u(p,0,zt),y==null?y=d:u(y,0,8),I(new k(w),p+w.e+1,y);if(!(x=w.c))return null;if(b=((C=x.length-1)-t(this.e/ct))*ct,C=x[C])for(;C%10==0;C/=10,b--);return b<0&&(b=0),b},s.dividedBy=s.div=function(p,y){return i(this,new k(p,y),h,d)},s.dividedToIntegerBy=s.idiv=function(p,y){return i(this,new k(p,y),0,1)},s.exponentiatedBy=s.pow=function(p,y){var x,b,C,w,D,T,F,G,N,J=this;if(p=new k(p),p.c&&!p.isInteger())throw Error(Wt+"Exponent not an integer: "+U(p));if(y!=null&&(y=new k(y)),T=p.e>14,!J.c||!J.c[0]||J.c[0]==1&&!J.e&&J.c.length==1||!p.c||!p.c[0])return N=new k(Math.pow(+U(J),T?p.s*(2-c(p)):+U(p))),y?N.mod(y):N;if(F=p.s<0,y){if(y.c?!y.c[0]:!y.s)return new k(NaN);b=!F&&J.isInteger()&&y.isInteger(),b&&(J=J.mod(y))}else{if(p.e>9&&(J.e>0||J.e<-1||(J.e==0?J.c[0]>1||T&&J.c[1]>=24e7:J.c[0]<8e13||T&&J.c[0]<=9999975e7)))return w=J.s<0&&c(p)?-0:0,J.e>-1&&(w=1/w),new k(F?1/w:w);W&&(w=$i(W/ct+2))}for(T?(x=new k(.5),F&&(p.s=1),G=c(p)):(C=Math.abs(+U(p)),G=C%2),N=new k(l);;){if(G){if(N=N.times(J),!N.c)break;w?N.c.length>w&&(N.c.length=w):b&&(N=N.mod(y))}if(C){if(C=le(C/2),C===0)break;G=C%2}else if(p=p.times(x),I(p,p.e+1,1),p.e>14)G=c(p);else{if(C=+U(p),C===0)break;G=C%2}J=J.times(J),w?J.c&&J.c.length>w&&(J.c.length=w):b&&(J=J.mod(y))}return b?N:(F&&(N=l.div(N)),y?N.mod(y):w?I(N,W,d,D):N)},s.integerValue=function(p){var y=new k(this);return p==null?p=d:u(p,0,8),I(y,y.e+1,p)},s.isEqualTo=s.eq=function(p,y){return o(this,new k(p,y))===0},s.isFinite=function(){return!!this.c},s.isGreaterThan=s.gt=function(p,y){return o(this,new k(p,y))>0},s.isGreaterThanOrEqualTo=s.gte=function(p,y){return(y=o(this,new k(p,y)))===1||y===0},s.isInteger=function(){return!!this.c&&t(this.e/ct)>this.c.length-2},s.isLessThan=s.lt=function(p,y){return o(this,new k(p,y))<0},s.isLessThanOrEqualTo=s.lte=function(p,y){return(y=o(this,new k(p,y)))===-1||y===0},s.isNaN=function(){return!this.s},s.isNegative=function(){return this.s<0},s.isPositive=function(){return this.s>0},s.isZero=function(){return!!this.c&&this.c[0]==0},s.minus=function(p,y){var x,b,C,w,D=this,T=D.s;if(p=new k(p,y),y=p.s,!T||!y)return new k(NaN);if(T!=y)return p.s=-y,D.plus(p);var F=D.e/ct,G=p.e/ct,N=D.c,J=p.c;if(!F||!G){if(!N||!J)return N?(p.s=-y,p):new k(J?D:NaN);if(!N[0]||!J[0])return J[0]?(p.s=-y,p):new k(N[0]?D:d==3?-0:0)}if(F=t(F),G=t(G),N=N.slice(),T=F-G){for((w=T<0)?(T=-T,C=N):(G=F,C=J),C.reverse(),y=T;y--;C.push(0));C.reverse()}else for(b=(w=(T=N.length)<(y=J.length))?T:y,T=y=0;y<b;y++)if(N[y]!=J[y]){w=N[y]<J[y];break}if(w&&(C=N,N=J,J=C,p.s=-p.s),y=(b=J.length)-(x=N.length),y>0)for(;y--;N[x++]=0);for(y=he-1;b>T;){if(N[--b]<J[b]){for(x=b;x&&!N[--x];N[x]=y);--N[x],N[b]+=he}N[b]-=J[b]}for(;N[0]==0;N.splice(0,1),--G);return N[0]?K(p,N,G):(p.s=d==3?-1:1,p.c=[p.e=0],p)},s.modulo=s.mod=function(p,y){var x,b,C=this;return p=new k(p,y),!C.c||!p.s||p.c&&!p.c[0]?new k(NaN):!p.c||C.c&&!C.c[0]?new k(C):(R==9?(b=p.s,p.s=1,x=i(C,p,0,3),p.s=b,x.s*=b):x=i(C,p,0,R),p=C.minus(x.times(p)),!p.c[0]&&R==1&&(p.s=C.s),p)},s.multipliedBy=s.times=function(p,y){var x,b,C,w,D,T,F,G,N,J,it,rt,st,wt,Bt,xt=this,Pt=xt.c,Qt=(p=new k(p,y)).c;if(!Pt||!Qt||!Pt[0]||!Qt[0])return!xt.s||!p.s||Pt&&!Pt[0]&&!Qt||Qt&&!Qt[0]&&!Pt?p.c=p.e=p.s=null:(p.s*=xt.s,!Pt||!Qt?p.c=p.e=null:(p.c=[0],p.e=0)),p;for(b=t(xt.e/ct)+t(p.e/ct),p.s*=xt.s,F=Pt.length,J=Qt.length,F<J&&(st=Pt,Pt=Qt,Qt=st,C=F,F=J,J=C),C=F+J,st=[];C--;st.push(0));for(wt=he,Bt=Se,C=J;--C>=0;){for(x=0,it=Qt[C]%Bt,rt=Qt[C]/Bt|0,D=F,w=C+D;w>C;)G=Pt[--D]%Bt,N=Pt[D]/Bt|0,T=rt*G+N*it,G=it*G+T%Bt*Bt+st[w]+x,x=(G/wt|0)+(T/Bt|0)+rt*N,st[w--]=G%wt;st[w]=x}return x?++b:st.splice(0,1),K(p,st,b)},s.negated=function(){var p=new k(this);return p.s=-p.s||null,p},s.plus=function(p,y){var x,b=this,C=b.s;if(p=new k(p,y),y=p.s,!C||!y)return new k(NaN);if(C!=y)return p.s=-y,b.minus(p);var w=b.e/ct,D=p.e/ct,T=b.c,F=p.c;if(!w||!D){if(!T||!F)return new k(C/0);if(!T[0]||!F[0])return F[0]?p:new k(T[0]?b:C*0)}if(w=t(w),D=t(D),T=T.slice(),C=w-D){for(C>0?(D=w,x=F):(C=-C,x=T),x.reverse();C--;x.push(0));x.reverse()}for(C=T.length,y=F.length,C-y<0&&(x=F,F=T,T=x,y=C),C=0;y;)C=(T[--y]=T[y]+F[y]+C)/he|0,T[y]=he===T[y]?0:T[y]%he;return C&&(T=[C].concat(T),++D),K(p,T,D)},s.precision=s.sd=function(p,y){var x,b,C,w=this;if(p!=null&&p!==!!p)return u(p,1,zt),y==null?y=d:u(y,0,8),I(new k(w),p,y);if(!(x=w.c))return null;if(C=x.length-1,b=C*ct+1,C=x[C]){for(;C%10==0;C/=10,b--);for(C=x[0];C>=10;C/=10,b++);}return p&&w.e+1>b&&(b=w.e+1),b},s.shiftedBy=function(p){return u(p,-Xi,Xi),this.times("1e"+p)},s.squareRoot=s.sqrt=function(){var p,y,x,b,C,w=this,D=w.c,T=w.s,F=w.e,G=h+4,N=new k("0.5");if(T!==1||!D||!D[0])return new k(!T||T<0&&(!D||D[0])?NaN:D?w:1/0);if(T=Math.sqrt(+U(w)),T==0||T==1/0?(y=n(D),(y.length+F)%2==0&&(y+="0"),T=Math.sqrt(+y),F=t((F+1)/2)-(F<0||F%2),T==1/0?y="5e"+F:(y=T.toExponential(),y=y.slice(0,y.indexOf("e")+1)+F),x=new k(y)):x=new k(T+""),x.c[0]){for(F=x.e,T=F+G,T<3&&(T=0);;)if(C=x,x=N.times(C.plus(i(w,C,G,1))),n(C.c).slice(0,T)===(y=n(x.c)).slice(0,T))if(x.e<F&&--T,y=y.slice(T-3,T+1),y=="9999"||!b&&y=="4999"){if(!b&&(I(C,C.e+h+2,0),C.times(C).eq(w))){x=C;break}G+=4,T+=4,b=1}else{(!+y||!+y.slice(1)&&y.charAt(0)=="5")&&(I(x,x.e+h+2,1),p=!x.times(x).eq(w));break}}return I(x,x.e+h+1,d,p)},s.toExponential=function(p,y){return p!=null&&(u(p,0,zt),p++),S(this,p,y,1)},s.toFixed=function(p,y){return p!=null&&(u(p,0,zt),p=p+this.e+1),S(this,p,y)},s.toFormat=function(p,y,x){var b,C=this;if(x==null)p!=null&&y&&typeof y=="object"?(x=y,y=null):p&&typeof p=="object"?(x=p,p=y=null):x=X;else if(typeof x!="object")throw Error(Wt+"Argument not an object: "+x);if(b=C.toFixed(p,y),C.c){var w,D=b.split("."),T=+x.groupSize,F=+x.secondaryGroupSize,G=x.groupSeparator||"",N=D[0],J=D[1],it=C.s<0,rt=it?N.slice(1):N,st=rt.length;if(F&&(w=T,T=F,F=w,st-=w),T>0&&st>0){for(w=st%T||T,N=rt.substr(0,w);w<st;w+=T)N+=G+rt.substr(w,T);F>0&&(N+=G+rt.slice(w)),it&&(N="-"+N)}b=J?N+(x.decimalSeparator||"")+((F=+x.fractionGroupSize)?J.replace(new RegExp("\\d{"+F+"}\\B","g"),"$&"+(x.fractionGroupSeparator||"")):J):N}return(x.prefix||"")+b+(x.suffix||"")},s.toFraction=function(p){var y,x,b,C,w,D,T,F,G,N,J,it,rt=this,st=rt.c;if(p!=null&&(T=new k(p),!T.isInteger()&&(T.c||T.s!==1)||T.lt(l)))throw Error(Wt+"Argument "+(T.isInteger()?"out of range: ":"not an integer: ")+U(T));if(!st)return new k(rt);for(y=new k(l),G=x=new k(l),b=F=new k(l),it=n(st),w=y.e=it.length-rt.e-1,y.c[0]=$n[(D=w%ct)<0?ct+D:D],p=!p||T.comparedTo(y)>0?w>0?y:G:T,D=P,P=1/0,T=new k(it),F.c[0]=0;N=i(T,y,0,1),C=x.plus(N.times(b)),C.comparedTo(p)!=1;)x=b,b=C,G=F.plus(N.times(C=G)),F=C,y=T.minus(N.times(C=y)),T=C;return C=i(p.minus(x),b,0,1),F=F.plus(C.times(G)),x=x.plus(C.times(b)),F.s=G.s=rt.s,w=w*2,J=i(G,b,w,d).minus(rt).abs().comparedTo(i(F,x,w,d).minus(rt).abs())<1?[G,b]:[F,x],P=D,J},s.toNumber=function(){return+U(this)},s.toPrecision=function(p,y){return p!=null&&u(p,1,zt),S(this,p,y,2)},s.toString=function(p){var y,x=this,b=x.s,C=x.e;return C===null?b?(y="Infinity",b<0&&(y="-"+y)):y="NaN":(p==null?y=C<=f||C>=m?g(n(x.c),C):B(n(x.c),C,"0"):p===10&&vt?(x=I(new k(x),h+C+1,d),y=B(n(x.c),x.e,"0")):(u(p,2,nt.length,"Base"),y=r(B(n(x.c),C,"0"),10,p,b,!0)),b<0&&x.c[0]&&(y="-"+y)),y},s.valueOf=s.toJSON=function(){return U(this)},s._isBigNumber=!0,s[Symbol.toStringTag]="BigNumber",s[Symbol.for("nodejs.util.inspect.custom")]=s.valueOf,e!=null&&k.set(e),k}function t(e){var i=e|0;return e>0||e===i?i:i-1}function n(e){for(var i,r,a=1,s=e.length,l=e[0]+"";a<s;){for(i=e[a++]+"",r=ct-i.length;r--;i="0"+i);l+=i}for(s=l.length;l.charCodeAt(--s)===48;);return l.slice(0,s+1||1)}function o(e,i){var r,a,s=e.c,l=i.c,h=e.s,d=i.s,f=e.e,m=i.e;if(!h||!d)return null;if(r=s&&!s[0],a=l&&!l[0],r||a)return r?a?0:-d:h;if(h!=d)return h;if(r=h<0,a=f==m,!s||!l)return a?0:!s^r?1:-1;if(!a)return f>m^r?1:-1;for(d=(f=s.length)<(m=l.length)?f:m,h=0;h<d;h++)if(s[h]!=l[h])return s[h]>l[h]^r?1:-1;return f==m?0:f>m^r?1:-1}function u(e,i,r,a){if(e<i||e>r||e!==le(e))throw Error(Wt+(a||"Argument")+(typeof e=="number"?e<i||e>r?" out of range: ":" not an integer: ":" not a primitive number: ")+String(e))}function c(e){var i=e.c.length-1;return t(e.e/ct)==i&&e.c[i]%2!=0}function g(e,i){return(e.length>1?e.charAt(0)+"."+e.slice(1):e)+(i<0?"e":"e+")+i}function B(e,i,r){var a,s;if(i<0){for(s=r+".";++i;s+=r);e=s+e}else if(a=e.length,++i>a){for(s=r,i-=a;--i;s+=r);e+=s}else i<a&&(e=e.slice(0,i)+"."+e.slice(i));return e}var Z=ra(),H=Z,Y=class{constructor(e){ot(this,"key");ot(this,"left",null);ot(this,"right",null);this.key=e}},Q=class extends Y{constructor(e){super(e)}},ut=class{constructor(){ot(this,"size",0);ot(this,"modificationCount",0);ot(this,"splayCount",0)}splay(e){let i=this.root;if(i==null)return this.compare(e,e),-1;let r=null,a=null,s=null,l=null,h=i,d=this.compare,f;for(;;)if(f=d(h.key,e),f>0){let m=h.left;if(m==null||(f=d(m.key,e),f>0&&(h.left=m.right,m.right=h,h=m,m=h.left,m==null)))break;r==null?a=h:r.left=h,r=h,h=m}else if(f<0){let m=h.right;if(m==null||(f=d(m.key,e),f<0&&(h.right=m.left,m.left=h,h=m,m=h.right,m==null)))break;s==null?l=h:s.right=h,s=h,h=m}else break;return s!=null&&(s.right=h.left,h.left=l),r!=null&&(r.left=h.right,h.right=a),this.root!==h&&(this.root=h,this.splayCount++),f}splayMin(e){let i=e,r=i.left;for(;r!=null;){let a=r;i.left=a.right,a.right=i,i=a,r=i.left}return i}splayMax(e){let i=e,r=i.right;for(;r!=null;){let a=r;i.right=a.left,a.left=i,i=a,r=i.right}return i}_delete(e){if(this.root==null||this.splay(e)!=0)return null;let i=this.root,r=i,a=i.left;if(this.size--,a==null)this.root=i.right;else{let s=i.right;i=this.splayMax(a),i.right=s,this.root=i}return this.modificationCount++,r}addNewRoot(e,i){this.size++,this.modificationCount++;let r=this.root;if(r==null){this.root=e;return}i<0?(e.left=r,e.right=r.right,r.right=null):(e.right=r,e.left=r.left,r.left=null),this.root=e}_first(){let e=this.root;return e==null?null:(this.root=this.splayMin(e),this.root)}_last(){let e=this.root;return e==null?null:(this.root=this.splayMax(e),this.root)}clear(){this.root=null,this.size=0,this.modificationCount++}has(e){return this.validKey(e)&&this.splay(e)==0}defaultCompare(){return(e,i)=>e<i?-1:e>i?1:0}wrap(){return{getRoot:()=>this.root,setRoot:e=>{this.root=e},getSize:()=>this.size,getModificationCount:()=>this.modificationCount,getSplayCount:()=>this.splayCount,setSplayCount:e=>{this.splayCount=e},splay:e=>this.splay(e),has:e=>this.has(e)}}},_t=class nr extends ut{constructor(r,a){super();ot(this,"root",null);ot(this,"compare");ot(this,"validKey");ot(this,Po,"[object Set]");this.compare=r??this.defaultCompare(),this.validKey=a??(s=>s!=null&&s!=null)}delete(r){return this.validKey(r)?this._delete(r)!=null:!1}deleteAll(r){for(let a of r)this.delete(a)}forEach(r){let a=this[Symbol.iterator](),s;for(;s=a.next(),!s.done;)r(s.value,s.value,this)}add(r){let a=this.splay(r);return a!=0&&this.addNewRoot(new Q(r),a),this}addAndReturn(r){let a=this.splay(r);return a!=0&&this.addNewRoot(new Q(r),a),this.root.key}addAll(r){for(let a of r)this.add(a)}isEmpty(){return this.root==null}isNotEmpty(){return this.root!=null}single(){if(this.size==0)throw"Bad state: No element";if(this.size>1)throw"Bad state: Too many element";return this.root.key}first(){if(this.size==0)throw"Bad state: No element";return this._first().key}last(){if(this.size==0)throw"Bad state: No element";return this._last().key}lastBefore(r){if(r==null)throw"Invalid arguments(s)";if(this.root==null)return null;if(this.splay(r)<0)return this.root.key;let a=this.root.left;if(a==null)return null;let s=a.right;for(;s!=null;)a=s,s=a.right;return a.key}firstAfter(r){if(r==null)throw"Invalid arguments(s)";if(this.root==null)return null;if(this.splay(r)>0)return this.root.key;let a=this.root.right;if(a==null)return null;let s=a.left;for(;s!=null;)a=s,s=a.left;return a.key}retainAll(r){let a=new nr(this.compare,this.validKey),s=this.modificationCount;for(let l of r){if(s!=this.modificationCount)throw"Concurrent modification during iteration.";this.validKey(l)&&this.splay(l)==0&&a.add(this.root.key)}a.size!=this.size&&(this.root=a.root,this.size=a.size,this.modificationCount++)}lookup(r){return!this.validKey(r)||this.splay(r)!=0?null:this.root.key}intersection(r){let a=new nr(this.compare,this.validKey);for(let s of this)r.has(s)&&a.add(s);return a}difference(r){let a=new nr(this.compare,this.validKey);for(let s of this)r.has(s)||a.add(s);return a}union(r){let a=this.clone();return a.addAll(r),a}clone(){let r=new nr(this.compare,this.validKey);return r.size=this.size,r.root=this.copyNode(this.root),r}copyNode(r){if(r==null)return null;function a(l,h){let d,f;do{if(d=l.left,f=l.right,d!=null){let m=new Q(d.key);h.left=m,a(d,m)}if(f!=null){let m=new Q(f.key);h.right=m,l=f,h=m}}while(f!=null)}let s=new Q(r.key);return a(r,s),s}toSet(){return this.clone()}entries(){return new ce(this.wrap())}keys(){return this[Symbol.iterator]()}values(){return this[Symbol.iterator]()}[(So=Symbol.iterator,Po=Symbol.toStringTag,So)](){return new Nt(this.wrap())}},Kt=class{constructor(e){ot(this,"tree");ot(this,"path",new Array);ot(this,"modificationCount",null);ot(this,"splayCount");this.tree=e,this.splayCount=e.getSplayCount()}[Symbol.iterator](){return this}next(){return this.moveNext()?{done:!1,value:this.current()}:{done:!0,value:null}}current(){if(!this.path.length)return null;let e=this.path[this.path.length-1];return this.getValue(e)}rebuildPath(e){this.path.splice(0,this.path.length),this.tree.splay(e),this.path.push(this.tree.getRoot()),this.splayCount=this.tree.getSplayCount()}findLeftMostDescendent(e){for(;e!=null;)this.path.push(e),e=e.left}moveNext(){if(this.modificationCount!=this.tree.getModificationCount()){if(this.modificationCount==null){this.modificationCount=this.tree.getModificationCount();let r=this.tree.getRoot();for(;r!=null;)this.path.push(r),r=r.left;return this.path.length>0}throw"Concurrent modification during iteration."}if(!this.path.length)return!1;this.splayCount!=this.tree.getSplayCount()&&this.rebuildPath(this.path[this.path.length-1].key);let e=this.path[this.path.length-1],i=e.right;if(i!=null){for(;i!=null;)this.path.push(i),i=i.left;return!0}for(this.path.pop();this.path.length&&this.path[this.path.length-1].right===e;)e=this.path.pop();return this.path.length>0}},Nt=class extends Kt{getValue(e){return e.key}},ce=class extends Kt{getValue(e){return[e.key,e.key]}},Xt=e=>e,ui=e=>{if(e){let i=new _t(si(e)),r=new _t(si(e)),a=(l,h)=>h.addAndReturn(l),s=l=>({x:a(l.x,i),y:a(l.y,r)});return s({x:new H(0),y:new H(0)}),s}return Xt},Xn=e=>({set:i=>{Te=Xn(i)},reset:()=>Xn(e),compare:si(e),snap:ui(e),orient:Ei(e)}),Te=Xn(),Bi=(e,i)=>e.ll.x.isLessThanOrEqualTo(i.x)&&i.x.isLessThanOrEqualTo(e.ur.x)&&e.ll.y.isLessThanOrEqualTo(i.y)&&i.y.isLessThanOrEqualTo(e.ur.y),Qi=(e,i)=>{if(i.ur.x.isLessThan(e.ll.x)||e.ur.x.isLessThan(i.ll.x)||i.ur.y.isLessThan(e.ll.y)||e.ur.y.isLessThan(i.ll.y))return null;let r=e.ll.x.isLessThan(i.ll.x)?i.ll.x:e.ll.x,a=e.ur.x.isLessThan(i.ur.x)?e.ur.x:i.ur.x,s=e.ll.y.isLessThan(i.ll.y)?i.ll.y:e.ll.y,l=e.ur.y.isLessThan(i.ur.y)?e.ur.y:i.ur.y;return{ll:{x:r,y:s},ur:{x:a,y:l}}},tn=(e,i)=>e.x.times(i.y).minus(e.y.times(i.x)),Qn=(e,i)=>e.x.times(i.x).plus(e.y.times(i.y)),Dt=e=>Qn(e,e).sqrt(),en=(e,i,r)=>{let a={x:i.x.minus(e.x),y:i.y.minus(e.y)},s={x:r.x.minus(e.x),y:r.y.minus(e.y)};return tn(s,a).div(Dt(s)).div(Dt(a))},so=(e,i,r)=>{let a={x:i.x.minus(e.x),y:i.y.minus(e.y)},s={x:r.x.minus(e.x),y:r.y.minus(e.y)};return Qn(s,a).div(Dt(s)).div(Dt(a))},aa=(e,i,r)=>i.y.isZero()?null:{x:e.x.plus(i.x.div(i.y).times(r.minus(e.y))),y:r},oa=(e,i,r)=>i.x.isZero()?null:{x:r,y:e.y.plus(i.y.div(i.x).times(r.minus(e.x)))},tr=(e,i,r,a)=>{if(i.x.isZero())return oa(r,a,e.x);if(a.x.isZero())return oa(e,i,r.x);if(i.y.isZero())return aa(r,a,e.y);if(a.y.isZero())return aa(e,i,r.y);let s=tn(i,a);if(s.isZero())return null;let l={x:r.x.minus(e.x),y:r.y.minus(e.y)},h=tn(l,i).div(s),d=tn(l,a).div(s),f=e.x.plus(d.times(i.x)),m=r.x.plus(h.times(a.x)),E=e.y.plus(d.times(i.y)),P=r.y.plus(h.times(a.y)),j=f.plus(m).div(2),R=E.plus(P).div(2);return{x:j,y:R}},ge=class Ao{constructor(i,r){ot(this,"point");ot(this,"isLeft");ot(this,"segment");ot(this,"otherSE");ot(this,"consumedBy");i.events===void 0?i.events=[this]:i.events.push(this),this.point=i,this.isLeft=r}static compare(i,r){let a=Ao.comparePoints(i.point,r.point);return a!==0?a:(i.point!==r.point&&i.link(r),i.isLeft!==r.isLeft?i.isLeft?1:-1:sa.compare(i.segment,r.segment))}static comparePoints(i,r){return i.x.isLessThan(r.x)?-1:i.x.isGreaterThan(r.x)?1:i.y.isLessThan(r.y)?-1:i.y.isGreaterThan(r.y)?1:0}link(i){if(i.point===this.point)throw new Error("Tried to link already linked events");let r=i.point.events;for(let a=0,s=r.length;a<s;a++){let l=r[a];this.point.events.push(l),l.point=this.point}this.checkForConsuming()}checkForConsuming(){let i=this.point.events.length;for(let r=0;r<i;r++){let a=this.point.events[r];if(a.segment.consumedBy===void 0)for(let s=r+1;s<i;s++){let l=this.point.events[s];l.consumedBy===void 0&&a.otherSE.point.events===l.otherSE.point.events&&a.segment.consume(l.segment)}}}getAvailableLinkedEvents(){let i=[];for(let r=0,a=this.point.events.length;r<a;r++){let s=this.point.events[r];s!==this&&!s.segment.ringOut&&s.segment.isInResult()&&i.push(s)}return i}getLeftmostComparator(i){let r=new Map,a=s=>{let l=s.otherSE;r.set(s,{sine:en(this.point,i.point,l.point),cosine:so(this.point,i.point,l.point)})};return(s,l)=>{r.has(s)||a(s),r.has(l)||a(l);let{sine:h,cosine:d}=r.get(s),{sine:f,cosine:m}=r.get(l);return h.isGreaterThanOrEqualTo(0)&&f.isGreaterThanOrEqualTo(0)?d.isLessThan(m)?1:d.isGreaterThan(m)?-1:0:h.isLessThan(0)&&f.isLessThan(0)?d.isLessThan(m)?-1:d.isGreaterThan(m)?1:0:f.isLessThan(h)?-1:f.isGreaterThan(h)?1:0}}},uo=0,sa=class fa{constructor(i,r,a,s){ot(this,"id");ot(this,"leftSE");ot(this,"rightSE");ot(this,"rings");ot(this,"windings");ot(this,"ringOut");ot(this,"consumedBy");ot(this,"prev");ot(this,"_prevInResult");ot(this,"_beforeState");ot(this,"_afterState");ot(this,"_isInResult");this.id=++uo,this.leftSE=i,i.segment=this,i.otherSE=r,this.rightSE=r,r.segment=this,r.otherSE=i,this.rings=a,this.windings=s}static compare(i,r){let a=i.leftSE.point.x,s=r.leftSE.point.x,l=i.rightSE.point.x,h=r.rightSE.point.x;if(h.isLessThan(a))return 1;if(l.isLessThan(s))return-1;let d=i.leftSE.point.y,f=r.leftSE.point.y,m=i.rightSE.point.y,E=r.rightSE.point.y;if(a.isLessThan(s)){if(f.isLessThan(d)&&f.isLessThan(m))return 1;if(f.isGreaterThan(d)&&f.isGreaterThan(m))return-1;let P=i.comparePoint(r.leftSE.point);if(P<0)return 1;if(P>0)return-1;let j=r.comparePoint(i.rightSE.point);return j!==0?j:-1}if(a.isGreaterThan(s)){if(d.isLessThan(f)&&d.isLessThan(E))return-1;if(d.isGreaterThan(f)&&d.isGreaterThan(E))return 1;let P=r.comparePoint(i.leftSE.point);if(P!==0)return P;let j=i.comparePoint(r.rightSE.point);return j<0?1:j>0?-1:1}if(d.isLessThan(f))return-1;if(d.isGreaterThan(f))return 1;if(l.isLessThan(h)){let P=r.comparePoint(i.rightSE.point);if(P!==0)return P}if(l.isGreaterThan(h)){let P=i.comparePoint(r.rightSE.point);if(P<0)return 1;if(P>0)return-1}if(!l.eq(h)){let P=m.minus(d),j=l.minus(a),R=E.minus(f),W=h.minus(s);if(P.isGreaterThan(j)&&R.isLessThan(W))return 1;if(P.isLessThan(j)&&R.isGreaterThan(W))return-1}return l.isGreaterThan(h)?1:l.isLessThan(h)||m.isLessThan(E)?-1:m.isGreaterThan(E)?1:i.id<r.id?-1:i.id>r.id?1:0}static fromRing(i,r,a){let s,l,h,d=ge.comparePoints(i,r);if(d<0)s=i,l=r,h=1;else if(d>0)s=r,l=i,h=-1;else throw new Error(`Tried to create degenerate segment at [${i.x}, ${i.y}]`);let f=new ge(s,!0),m=new ge(l,!1);return new fa(f,m,[a],[h])}replaceRightSE(i){this.rightSE=i,this.rightSE.segment=this,this.rightSE.otherSE=this.leftSE,this.leftSE.otherSE=this.rightSE}bbox(){let i=this.leftSE.point.y,r=this.rightSE.point.y;return{ll:{x:this.leftSE.point.x,y:i.isLessThan(r)?i:r},ur:{x:this.rightSE.point.x,y:i.isGreaterThan(r)?i:r}}}vector(){return{x:this.rightSE.point.x.minus(this.leftSE.point.x),y:this.rightSE.point.y.minus(this.leftSE.point.y)}}isAnEndpoint(i){return i.x.eq(this.leftSE.point.x)&&i.y.eq(this.leftSE.point.y)||i.x.eq(this.rightSE.point.x)&&i.y.eq(this.rightSE.point.y)}comparePoint(i){return Te.orient(this.leftSE.point,i,this.rightSE.point)}getIntersection(i){let r=this.bbox(),a=i.bbox(),s=Qi(r,a);if(s===null)return null;let l=this.leftSE.point,h=this.rightSE.point,d=i.leftSE.point,f=i.rightSE.point,m=Bi(r,d)&&this.comparePoint(d)===0,E=Bi(a,l)&&i.comparePoint(l)===0,P=Bi(r,f)&&this.comparePoint(f)===0,j=Bi(a,h)&&i.comparePoint(h)===0;if(E&&m)return j&&!P?h:!j&&P?f:null;if(E)return P&&l.x.eq(f.x)&&l.y.eq(f.y)?null:l;if(m)return j&&h.x.eq(d.x)&&h.y.eq(d.y)?null:d;if(j&&P)return null;if(j)return h;if(P)return f;let R=tr(l,this.vector(),d,i.vector());return R===null||!Bi(s,R)?null:Te.snap(R)}split(i){let r=[],a=i.events!==void 0,s=new ge(i,!0),l=new ge(i,!1),h=this.rightSE;this.replaceRightSE(l),r.push(l),r.push(s);let d=new fa(s,h,this.rings.slice(),this.windings.slice());return ge.comparePoints(d.leftSE.point,d.rightSE.point)>0&&d.swapEvents(),ge.comparePoints(this.leftSE.point,this.rightSE.point)>0&&this.swapEvents(),a&&(s.checkForConsuming(),l.checkForConsuming()),r}swapEvents(){let i=this.rightSE;this.rightSE=this.leftSE,this.leftSE=i,this.leftSE.isLeft=!0,this.rightSE.isLeft=!1;for(let r=0,a=this.windings.length;r<a;r++)this.windings[r]*=-1}consume(i){let r=this,a=i;for(;r.consumedBy;)r=r.consumedBy;for(;a.consumedBy;)a=a.consumedBy;let s=fa.compare(r,a);if(s!==0){if(s>0){let l=r;r=a,a=l}if(r.prev===a){let l=r;r=a,a=l}for(let l=0,h=a.rings.length;l<h;l++){let d=a.rings[l],f=a.windings[l],m=r.rings.indexOf(d);m===-1?(r.rings.push(d),r.windings.push(f)):r.windings[m]+=f}a.rings=null,a.windings=null,a.consumedBy=r,a.leftSE.consumedBy=r.leftSE,a.rightSE.consumedBy=r.rightSE}}prevInResult(){return this._prevInResult!==void 0?this._prevInResult:(this.prev?this.prev.isInResult()?this._prevInResult=this.prev:this._prevInResult=this.prev.prevInResult():this._prevInResult=null,this._prevInResult)}beforeState(){if(this._beforeState!==void 0)return this._beforeState;if(!this.prev)this._beforeState={rings:[],windings:[],multiPolys:[]};else{let i=this.prev.consumedBy||this.prev;this._beforeState=i.afterState()}return this._beforeState}afterState(){if(this._afterState!==void 0)return this._afterState;let i=this.beforeState();this._afterState={rings:i.rings.slice(0),windings:i.windings.slice(0),multiPolys:[]};let r=this._afterState.rings,a=this._afterState.windings,s=this._afterState.multiPolys;for(let d=0,f=this.rings.length;d<f;d++){let m=this.rings[d],E=this.windings[d],P=r.indexOf(m);P===-1?(r.push(m),a.push(E)):a[P]+=E}let l=[],h=[];for(let d=0,f=r.length;d<f;d++){if(a[d]===0)continue;let m=r[d],E=m.poly;if(h.indexOf(E)===-1)if(m.isExterior)l.push(E);else{h.indexOf(E)===-1&&h.push(E);let P=l.indexOf(m.poly);P!==-1&&l.splice(P,1)}}for(let d=0,f=l.length;d<f;d++){let m=l[d].multiPoly;s.indexOf(m)===-1&&s.push(m)}return this._afterState}isInResult(){if(this.consumedBy)return!1;if(this._isInResult!==void 0)return this._isInResult;let i=this.beforeState().multiPolys,r=this.afterState().multiPolys;switch(ua.type){case"union":{let a=i.length===0,s=r.length===0;this._isInResult=a!==s;break}case"intersection":{let a,s;i.length<r.length?(a=i.length,s=r.length):(a=r.length,s=i.length),this._isInResult=s===ua.numMultiPolys&&a<s;break}case"xor":{let a=Math.abs(i.length-r.length);this._isInResult=a%2===1;break}case"difference":{let a=s=>s.length===1&&s[0].isSubject;this._isInResult=a(i)!==a(r);break}}return this._isInResult}},bo=class{constructor(e,i,r){ot(this,"poly");ot(this,"isExterior");ot(this,"segments");ot(this,"bbox");if(!Array.isArray(e)||e.length===0)throw new Error("Input geometry is not a valid Polygon or MultiPolygon");if(this.poly=i,this.isExterior=r,this.segments=[],typeof e[0][0]!="number"||typeof e[0][1]!="number")throw new Error("Input geometry is not a valid Polygon or MultiPolygon");let a=Te.snap({x:new H(e[0][0]),y:new H(e[0][1])});this.bbox={ll:{x:a.x,y:a.y},ur:{x:a.x,y:a.y}};let s=a;for(let l=1,h=e.length;l<h;l++){if(typeof e[l][0]!="number"||typeof e[l][1]!="number")throw new Error("Input geometry is not a valid Polygon or MultiPolygon");let d=Te.snap({x:new H(e[l][0]),y:new H(e[l][1])});d.x.eq(s.x)&&d.y.eq(s.y)||(this.segments.push(sa.fromRing(s,d,this)),d.x.isLessThan(this.bbox.ll.x)&&(this.bbox.ll.x=d.x),d.y.isLessThan(this.bbox.ll.y)&&(this.bbox.ll.y=d.y),d.x.isGreaterThan(this.bbox.ur.x)&&(this.bbox.ur.x=d.x),d.y.isGreaterThan(this.bbox.ur.y)&&(this.bbox.ur.y=d.y),s=d)}(!a.x.eq(s.x)||!a.y.eq(s.y))&&this.segments.push(sa.fromRing(s,a,this))}getSweepEvents(){let e=[];for(let i=0,r=this.segments.length;i<r;i++){let a=this.segments[i];e.push(a.leftSE),e.push(a.rightSE)}return e}},es=class{constructor(e,i){ot(this,"multiPoly");ot(this,"exteriorRing");ot(this,"interiorRings");ot(this,"bbox");if(!Array.isArray(e))throw new Error("Input geometry is not a valid Polygon or MultiPolygon");this.exteriorRing=new bo(e[0],this,!0),this.bbox={ll:{x:this.exteriorRing.bbox.ll.x,y:this.exteriorRing.bbox.ll.y},ur:{x:this.exteriorRing.bbox.ur.x,y:this.exteriorRing.bbox.ur.y}},this.interiorRings=[];for(let r=1,a=e.length;r<a;r++){let s=new bo(e[r],this,!1);s.bbox.ll.x.isLessThan(this.bbox.ll.x)&&(this.bbox.ll.x=s.bbox.ll.x),s.bbox.ll.y.isLessThan(this.bbox.ll.y)&&(this.bbox.ll.y=s.bbox.ll.y),s.bbox.ur.x.isGreaterThan(this.bbox.ur.x)&&(this.bbox.ur.x=s.bbox.ur.x),s.bbox.ur.y.isGreaterThan(this.bbox.ur.y)&&(this.bbox.ur.y=s.bbox.ur.y),this.interiorRings.push(s)}this.multiPoly=i}getSweepEvents(){let e=this.exteriorRing.getSweepEvents();for(let i=0,r=this.interiorRings.length;i<r;i++){let a=this.interiorRings[i].getSweepEvents();for(let s=0,l=a.length;s<l;s++)e.push(a[s])}return e}},xo=class{constructor(e,i){ot(this,"isSubject");ot(this,"polys");ot(this,"bbox");if(!Array.isArray(e))throw new Error("Input geometry is not a valid Polygon or MultiPolygon");try{typeof e[0][0][0]=="number"&&(e=[e])}catch{}this.polys=[],this.bbox={ll:{x:new H(Number.POSITIVE_INFINITY),y:new H(Number.POSITIVE_INFINITY)},ur:{x:new H(Number.NEGATIVE_INFINITY),y:new H(Number.NEGATIVE_INFINITY)}};for(let r=0,a=e.length;r<a;r++){let s=new es(e[r],this);s.bbox.ll.x.isLessThan(this.bbox.ll.x)&&(this.bbox.ll.x=s.bbox.ll.x),s.bbox.ll.y.isLessThan(this.bbox.ll.y)&&(this.bbox.ll.y=s.bbox.ll.y),s.bbox.ur.x.isGreaterThan(this.bbox.ur.x)&&(this.bbox.ur.x=s.bbox.ur.x),s.bbox.ur.y.isGreaterThan(this.bbox.ur.y)&&(this.bbox.ur.y=s.bbox.ur.y),this.polys.push(s)}this.isSubject=i}getSweepEvents(){let e=[];for(let i=0,r=this.polys.length;i<r;i++){let a=this.polys[i].getSweepEvents();for(let s=0,l=a.length;s<l;s++)e.push(a[s])}return e}},is=class fo{constructor(i){ot(this,"events");ot(this,"poly");ot(this,"_isExteriorRing");ot(this,"_enclosingRing");this.events=i;for(let r=0,a=i.length;r<a;r++)i[r].segment.ringOut=this;this.poly=null}static factory(i){let r=[];for(let a=0,s=i.length;a<s;a++){let l=i[a];if(!l.isInResult()||l.ringOut)continue;let h=null,d=l.leftSE,f=l.rightSE,m=[d],E=d.point,P=[];for(;h=d,d=f,m.push(d),d.point!==E;)for(;;){let j=d.getAvailableLinkedEvents();if(j.length===0){let X=m[0].point,nt=m[m.length-1].point;throw new Error(`Unable to complete output ring starting at [${X.x}, ${X.y}]. Last matching segment found ends at [${nt.x}, ${nt.y}].`)}if(j.length===1){f=j[0].otherSE;break}let R=null;for(let X=0,nt=P.length;X<nt;X++)if(P[X].point===d.point){R=X;break}if(R!==null){let X=P.splice(R)[0],nt=m.splice(X.index);nt.unshift(nt[0].otherSE),r.push(new fo(nt.reverse()));continue}P.push({index:m.length,point:d.point});let W=d.getLeftmostComparator(h);f=j.sort(W)[0].otherSE;break}r.push(new fo(m))}return r}getGeom(){let i=this.events[0].point,r=[i];for(let m=1,E=this.events.length-1;m<E;m++){let P=this.events[m].point,j=this.events[m+1].point;Te.orient(P,i,j)!==0&&(r.push(P),i=P)}if(r.length===1)return null;let a=r[0],s=r[1];Te.orient(a,i,s)===0&&r.shift(),r.push(r[0]);let l=this.isExteriorRing()?1:-1,h=this.isExteriorRing()?0:r.length-1,d=this.isExteriorRing()?r.length:-1,f=[];for(let m=h;m!=d;m+=l)f.push([r[m].x.toNumber(),r[m].y.toNumber()]);return f}isExteriorRing(){if(this._isExteriorRing===void 0){let i=this.enclosingRing();this._isExteriorRing=i?!i.isExteriorRing():!0}return this._isExteriorRing}enclosingRing(){return this._enclosingRing===void 0&&(this._enclosingRing=this._calcEnclosingRing()),this._enclosingRing}_calcEnclosingRing(){let i=this.events[0];for(let s=1,l=this.events.length;s<l;s++){let h=this.events[s];ge.compare(i,h)>0&&(i=h)}let r=i.segment.prevInResult(),a=r?r.prevInResult():null;for(;;){if(!r)return null;if(!a)return r.ringOut;if(a.ringOut!==r.ringOut)return a.ringOut?.enclosingRing()!==r.ringOut?r.ringOut:r.ringOut?.enclosingRing();r=a.prevInResult(),a=r?r.prevInResult():null}}},Co=class{constructor(e){ot(this,"exteriorRing");ot(this,"interiorRings");this.exteriorRing=e,e.poly=this,this.interiorRings=[]}addInterior(e){this.interiorRings.push(e),e.poly=this}getGeom(){let e=this.exteriorRing.getGeom();if(e===null)return null;let i=[e];for(let r=0,a=this.interiorRings.length;r<a;r++){let s=this.interiorRings[r].getGeom();s!==null&&i.push(s)}return i}},ns=class{constructor(e){ot(this,"rings");ot(this,"polys");this.rings=e,this.polys=this._composePolys(e)}getGeom(){let e=[];for(let i=0,r=this.polys.length;i<r;i++){let a=this.polys[i].getGeom();a!==null&&e.push(a)}return e}_composePolys(e){let i=[];for(let r=0,a=e.length;r<a;r++){let s=e[r];if(!s.poly)if(s.isExteriorRing())i.push(new Co(s));else{let l=s.enclosingRing();l?.poly||i.push(new Co(l)),l?.poly?.addInterior(s)}}return i}},rs=class{constructor(e,i=sa.compare){ot(this,"queue");ot(this,"tree");ot(this,"segments");this.queue=e,this.tree=new _t(i),this.segments=[]}process(e){let i=e.segment,r=[];if(e.consumedBy)return e.isLeft?this.queue.delete(e.otherSE):this.tree.delete(i),r;e.isLeft&&this.tree.add(i);let a=i,s=i;do a=this.tree.lastBefore(a);while(a!=null&&a.consumedBy!=null);do s=this.tree.firstAfter(s);while(s!=null&&s.consumedBy!=null);if(e.isLeft){let l=null;if(a){let d=a.getIntersection(i);if(d!==null&&(i.isAnEndpoint(d)||(l=d),!a.isAnEndpoint(d))){let f=this._splitSafely(a,d);for(let m=0,E=f.length;m<E;m++)r.push(f[m])}}let h=null;if(s){let d=s.getIntersection(i);if(d!==null&&(i.isAnEndpoint(d)||(h=d),!s.isAnEndpoint(d))){let f=this._splitSafely(s,d);for(let m=0,E=f.length;m<E;m++)r.push(f[m])}}if(l!==null||h!==null){let d=null;l===null?d=h:h===null?d=l:d=ge.comparePoints(l,h)<=0?l:h,this.queue.delete(i.rightSE),r.push(i.rightSE);let f=i.split(d);for(let m=0,E=f.length;m<E;m++)r.push(f[m])}r.length>0?(this.tree.delete(i),r.push(e)):(this.segments.push(i),i.prev=a)}else{if(a&&s){let l=a.getIntersection(s);if(l!==null){if(!a.isAnEndpoint(l)){let h=this._splitSafely(a,l);for(let d=0,f=h.length;d<f;d++)r.push(h[d])}if(!s.isAnEndpoint(l)){let h=this._splitSafely(s,l);for(let d=0,f=h.length;d<f;d++)r.push(h[d])}}}this.tree.delete(i)}return r}_splitSafely(e,i){this.tree.delete(e);let r=e.rightSE;this.queue.delete(r);let a=e.split(i);return a.push(r),e.consumedBy===void 0&&this.tree.add(e),a}},as=class{constructor(){ot(this,"type");ot(this,"numMultiPolys")}run(e,i,r){er.type=e;let a=[new xo(i,!0)];for(let f=0,m=r.length;f<m;f++)a.push(new xo(r[f],!1));if(er.numMultiPolys=a.length,er.type==="difference"){let f=a[0],m=1;for(;m<a.length;)Qi(a[m].bbox,f.bbox)!==null?m++:a.splice(m,1)}if(er.type==="intersection")for(let f=0,m=a.length;f<m;f++){let E=a[f];for(let P=f+1,j=a.length;P<j;P++)if(Qi(E.bbox,a[P].bbox)===null)return[]}let s=new _t(ge.compare);for(let f=0,m=a.length;f<m;f++){let E=a[f].getSweepEvents();for(let P=0,j=E.length;P<j;P++)s.add(E[P])}let l=new rs(s),h=null;for(s.size!=0&&(h=s.first(),s.delete(h));h;){let f=l.process(h);for(let m=0,E=f.length;m<E;m++){let P=f[m];P.consumedBy===void 0&&s.add(P)}s.size!=0?(h=s.first(),s.delete(h)):h=null}Te.reset();let d=is.factory(l.segments);return new ns(d).getGeom()}},er=new as,ua=er,os=(e,...i)=>ua.run("intersection",e,i),ss=(e,...i)=>ua.run("difference",e,i),gu=Te.set;function la(e){let i={type:"Feature"};return i.geometry=e,i}function ha(e){return e.type==="Feature"?e.geometry:e}function wo(e){return e&&e.geometry&&e.geometry.coordinates?e.geometry.coordinates:e}function us(e){return la({type:"LineString",coordinates:e})}function ls(e){return la({type:"MultiLineString",coordinates:e})}function ko(e){return la({type:"Polygon",coordinates:e})}function Mo(e){return la({type:"MultiPolygon",coordinates:e})}function hs(e,i){let r=ha(e),a=ha(i),s=os(r.coordinates,a.coordinates);return s.length===0?null:s.length===1?ko(s[0]):Mo(s)}function cs(e,i){let r=ha(e),a=ha(i),s=ss(r.coordinates,a.coordinates);return s.length===0?null:s.length===1?ko(s[0]):Mo(s)}function Eo(e){return Array.isArray(e)?1+Eo(e[0]):-1}function ds(e){e instanceof L.Polyline&&(e=e.toGeoJSON(15));let i=wo(e),r=Eo(i),a=[];return r>1?i.forEach(s=>{a.push(us(s))}):a.push(e),a}function ps(e){let i=[];return e.eachLayer(r=>{i.push(wo(r.toGeoJSON(15)))}),ls(i)}Ut.Cut=Ut.Polygon.extend({initialize(e){this._map=e,this._shape="Cut",this.toolbarButtonName="cutPolygon"},_finishShape(){if(this._editedLayers=[],!this.options.allowSelfIntersection&&(this._handleSelfIntersection(!0,this._layer.getLatLngs()[0]),this._doesSelfIntersect)||this.options.requireSnapToFinish&&!this._hintMarker._snapped&&!this._isFirstLayer())return;let e=this._layer.getLatLngs();if(e.length<=2)return;let i=L.polygon(e,this.options.pathOptions);i._latlngInfos=this._layer._latlngInfo,this.cut(i),this._cleanupSnapping(),this._otherSnapLayers.splice(this._tempSnapLayerIndex,1),delete this._tempSnapLayerIndex,this._editedLayers.forEach(({layer:a,originalLayer:s})=>{this._fireCut(s,a,s),this._fireCut(this._map,a,s),s.pm._fireEdit()}),this._editedLayers=[];let r=this._hintMarker.getLatLng();this.disable(),this.options.continueDrawing&&(this.enable(),this._hintMarker.setLatLng(r))},cut(e){let i=this._map._layers,r=e._latlngInfos||[];Object.keys(i).map(a=>i[a]).filter(a=>a.pm).filter(a=>!a._pmTempLayer).filter(a=>!L.PM.optIn&&!a.options.pmIgnore||L.PM.optIn&&a.options.pmIgnore===!1).filter(a=>a instanceof L.Polyline).filter(a=>a!==e).filter(a=>a.pm.options.allowCutting).filter(a=>this.options.layersToCut&&L.Util.isArray(this.options.layersToCut)&&this.options.layersToCut.length>0?this.options.layersToCut.indexOf(a)>-1:!0).filter(a=>!this._layerGroup.hasLayer(a)).filter(a=>{try{let s=!!We(e.toGeoJSON(15),a.toGeoJSON(15)).features.length>0;return s||a instanceof L.Polyline&&!(a instanceof L.Polygon)?s:!!hs(e.toGeoJSON(15),a.toGeoJSON(15))}catch{return a instanceof L.Polygon&&console.error("You can't cut polygons with self-intersections"),!1}}).forEach(a=>{let s;if(a instanceof L.Polygon){s=L.polygon(a.getLatLngs());let f=s.getLatLngs();r.forEach(m=>{if(m&&m.snapInfo){let{latlng:E}=m,P=this._calcClosestLayer(E,[s]);if(P&&P.segment&&P.distance<this.options.snapDistance){let{segment:j}=P;if(j&&j.length===2){let{indexPath:R,parentPath:W,newIndex:X}=L.PM.Utils._getIndexFromSegment(f,j);(R.length>1?(0,Mi.default)(f,W):f).splice(X,0,E)}}}})}else s=a;let l=this._cutLayer(e,s),h=L.geoJSON(l,a.options);h.getLayers().length===1&&([h]=h.getLayers()),this._setPane(h,"layerPane");let d=h.addTo(this._map.pm._getContainingLayer());if(d.pm.enable(a.pm.options),d.pm.disable(),a._pmTempLayer=!0,e._pmTempLayer=!0,a.remove(),a.removeFrom(this._map.pm._getContainingLayer()),e.remove(),e.removeFrom(this._map.pm._getContainingLayer()),d.getLayers&&d.getLayers().length===0&&this._map.pm.removeLayer({target:d}),d instanceof L.LayerGroup?(d.eachLayer(f=>{this._addDrawnLayerProp(f)}),this._addDrawnLayerProp(d)):this._addDrawnLayerProp(d),this.options.layersToCut&&L.Util.isArray(this.options.layersToCut)&&this.options.layersToCut.length>0){let f=this.options.layersToCut.indexOf(a);f>-1&&this.options.layersToCut.splice(f,1)}this._editedLayers.push({layer:d,originalLayer:a})})},_cutLayer(e,i){let r=L.geoJSON(),a;if(i instanceof L.Polygon)a=cs(i.toGeoJSON(15),e.toGeoJSON(15));else{let s=ds(i);s.forEach(l=>{let h=$r(l,e.toGeoJSON(15)),d;h&&h.features.length>0?d=L.geoJSON(h):d=L.geoJSON(l),d.getLayers().forEach(f=>{Xr(e.toGeoJSON(15),f.toGeoJSON(15))||f.addTo(r)})}),s.length>1?a=ps(r):a=r.toGeoJSON(15)}return a},_change:L.Util.falseFn}),Ut.Text=Ut.extend({initialize(e){this._map=e,this._shape="Text",this.toolbarButtonName="drawText"},enable(e){L.Util.setOptions(this,e),this._enabled=!0,this._map.on("click",this._createMarker,this),this._map.pm.Toolbar.toggleButton(this.toolbarButtonName,!0),this._hintMarker=L.marker(this._map.getCenter(),{interactive:!1,zIndexOffset:100,icon:L.divIcon({className:"marker-icon cursor-marker"})}),this._setPane(this._hintMarker,"vertexPane"),this._hintMarker._pmTempLayer=!0,this._hintMarker.addTo(this._map),this.options.cursorMarker&&L.DomUtil.addClass(this._hintMarker._icon,"visible"),this.options.tooltips&&this._hintMarker.bindTooltip(yt("tooltips.placeText"),{permanent:!0,offset:L.point(0,10),direction:"bottom",opacity:.8}).openTooltip(),this._layer=this._hintMarker,this._map.on("mousemove",this._syncHintMarker,this),this._map.getContainer().classList.add("geoman-draw-cursor"),this._fireDrawStart(),this._setGlobalDrawMode()},disable(){this._enabled&&(this._enabled=!1,this._map.off("click",this._createMarker,this),this._hintMarker?.remove(),this._map.getContainer().classList.remove("geoman-draw-cursor"),this._map.off("mousemove",this._syncHintMarker,this),this._map.off("mousemove",this._showHintMarker,this),this._map.pm.Toolbar.toggleButton(this.toolbarButtonName,!1),this.options.snappable&&this._cleanupSnapping(),this._fireDrawEnd(),this._setGlobalDrawMode())},enabled(){return this._enabled},toggle(e){this.enabled()?this.disable():this.enable(e)},_syncHintMarker(e){if(this._hintMarker.setLatLng(e.latlng),this.options.snappable){let i=e;i.target=this._hintMarker,this._handleSnapping(i)}},_createMarker(e){if(!e.latlng||this.options.requireSnapToFinish&&!this._hintMarker._snapped&&!this._isFirstLayer())return;this._hintMarker._snapped||this._hintMarker.setLatLng(e.latlng);let i=this._hintMarker.getLatLng();if(this.textArea=this._createTextArea(),this.options.textOptions?.className){let s=this.options.textOptions.className.split(" ");this.textArea.classList.add(...s)}let r=this._createTextIcon(this.textArea),a=new L.Marker(i,{textMarker:!0,_textMarkerOverPM:!0,icon:r});if(this._setPane(a,"markerPane"),this._finishLayer(a),a.pm||(a.options.draggable=!1),a.addTo(this._map.pm._getContainingLayer()),a.pm){a.pm.textArea=this.textArea,L.setOptions(a.pm,{removeIfEmpty:this.options.textOptions?.removeIfEmpty??!0});let s=this.options.textOptions?.focusAfterDraw??!0;a.pm._createTextMarker(s),this.options.textOptions?.text&&a.pm.setText(this.options.textOptions.text)}this._fireCreate(a),this._cleanupSnapping(),this.disable(),this.options.continueDrawing&&this._map.once("mousemove",this._showHintMarkerAfterMoving,this)},_showHintMarkerAfterMoving(e){this.enable(),this._hintMarker.setLatLng(e.latlng)},_createTextArea(){let e=document.createElement("textarea");return e.readOnly=!0,e.classList.add("pm-textarea","pm-disabled"),e},_createTextIcon(e){return L.divIcon({className:"pm-text-marker",html:e})}});var fs={enableLayerDrag(){if(!this.options.draggable||!this._layer._map)return;this.disable(),this._layerDragEnabled=!0,this._map||(this._map=this._layer._map),(this._layer instanceof L.Marker||this._layer instanceof L.ImageOverlay)&&L.DomEvent.on(this._getDOMElem(),"dragstart",this._stopDOMImageDrag),this._layer.dragging&&this._layer.dragging.disable(),this._tempDragCoord=null,Ke(this._layer)instanceof L.Canvas?(this._layer.on("mouseout",this.removeDraggingClass,this),this._layer.on("mouseover",this.addDraggingClass,this)):this.addDraggingClass(),this._originalMapDragState=this._layer._map.dragging._enabled,this._safeToCacheDragState=!0;let e=this._getDOMElem();e&&(Ke(this._layer)instanceof L.Canvas?(this._layer.on("touchstart mousedown",this._dragMixinOnMouseDown,this),this._map.pm._addTouchEvents(e)):L.DomEvent.on(e,"touchstart mousedown",this._simulateMouseDownEvent,this)),this._fireDragEnable()},disableLayerDrag(){this._layerDragEnabled=!1,Ke(this._layer)instanceof L.Canvas?(this._layer.off("mouseout",this.removeDraggingClass,this),this._layer.off("mouseover",this.addDraggingClass,this)):this.removeDraggingClass(),this._originalMapDragState&&this._dragging&&this._map.dragging.enable(),this._safeToCacheDragState=!1,this._layer.dragging&&this._layer.dragging.disable();let e=this._getDOMElem();e&&(Ke(this._layer)instanceof L.Canvas?(this._layer.off("touchstart mousedown",this._dragMixinOnMouseDown,this),this._map.pm._removeTouchEvents(e)):L.DomEvent.off(e,"touchstart mousedown",this._simulateMouseDownEvent,this)),this._layerDragged&&this._fireUpdate(),this._layerDragged=!1,this._fireDragDisable()},dragging(){return this._dragging},layerDragEnabled(){return!!this._layerDragEnabled},_simulateMouseDownEvent(e){let i=e.touches?e.touches[0]:e,r={originalEvent:i,target:this._layer};return r.containerPoint=this._map.mouseEventToContainerPoint(i),r.latlng=this._map.containerPointToLatLng(r.containerPoint),this._dragMixinOnMouseDown(r),!1},_simulateMouseMoveEvent(e){let i=e.touches?e.touches[0]:e,r={originalEvent:i,target:this._layer};return r.containerPoint=this._map.mouseEventToContainerPoint(i),r.latlng=this._map.containerPointToLatLng(r.containerPoint),this._dragMixinOnMouseMove(r),!1},_simulateMouseUpEvent(e){let i={originalEvent:e.touches?e.touches[0]:e,target:this._layer};return e.type.indexOf("touch")===-1&&(i.containerPoint=this._map.mouseEventToContainerPoint(e),i.latlng=this._map.containerPointToLatLng(i.containerPoint)),this._dragMixinOnMouseUp(i),!1},_dragMixinOnMouseDown(e){if(e.originalEvent.button>0)return;this._overwriteEventIfItComesFromMarker(e);let i=e._fromLayerSync,r=this._syncLayers("_dragMixinOnMouseDown",e);if(this._layer instanceof L.Marker&&(this.options.snappable&&!i&&!r?this._initSnappableMarkers():this._disableSnapping()),this._layer instanceof L.CircleMarker){let a="resizeableCircleMarker";this._layer instanceof L.Circle&&(a="resizeableCircle"),this.options.snappable&&!i&&!r?this._layer.pm.options[a]||this._initSnappableMarkersDrag():this._layer.pm.options[a]?this._layer.pm._disableSnapping():this._layer.pm._disableSnappingDrag()}this._safeToCacheDragState&&(this._originalMapDragState=this._layer._map.dragging._enabled,this._safeToCacheDragState=!1),this._tempDragCoord=e.latlng,L.DomEvent.on(this._map.getContainer(),"touchend mouseup",this._simulateMouseUpEvent,this),L.DomEvent.on(this._map.getContainer(),"touchmove mousemove",this._simulateMouseMoveEvent,this)},_dragMixinOnMouseMove(e){this._overwriteEventIfItComesFromMarker(e);let i=this._getDOMElem();this._syncLayers("_dragMixinOnMouseMove",e),this._dragging||(this._dragging=!0,L.DomUtil.addClass(i,"leaflet-pm-dragging"),this._layer instanceof L.Marker||this._layer.bringToFront(),this._originalMapDragState&&this._map.dragging.disable(),this._fireDragStart()),this._tempDragCoord||(this._tempDragCoord=e.latlng),this._onLayerDrag(e),this._layer instanceof L.CircleMarker&&this._layer.pm._updateHiddenPolyCircle()},_dragMixinOnMouseUp(e){let i=this._getDOMElem();return this._syncLayers("_dragMixinOnMouseUp",e),this._originalMapDragState&&this._map.dragging.enable(),this._safeToCacheDragState=!0,L.DomEvent.off(this._map.getContainer(),"touchmove mousemove",this._simulateMouseMoveEvent,this),L.DomEvent.off(this._map.getContainer(),"touchend mouseup",this._simulateMouseUpEvent,this),this._dragging?(this._layer instanceof L.CircleMarker&&this._layer.pm._updateHiddenPolyCircle(),this._layerDragged=!0,window.setTimeout(()=>{this._dragging=!1,i&&L.DomUtil.removeClass(i,"leaflet-pm-dragging"),this._fireDragEnd(),this._fireEdit(),this._layerEdited=!0},10),!0):!1},_onLayerDrag(e){let{latlng:i}=e,r={lat:i.lat-this._tempDragCoord.lat,lng:i.lng-this._tempDragCoord.lng},a=s=>s.map(l=>{if(Array.isArray(l))return a(l);let h={lat:l.lat+r.lat,lng:l.lng+r.lng};return(l.alt||l.alt===0)&&(h.alt=l.alt),h});if(this._layer instanceof L.Circle&&this._layer.options.resizeableCircle||this._layer instanceof L.CircleMarker&&this._layer.options.resizeableCircleMarker){let s=a([this._layer.getLatLng()]);this._layer.setLatLng(s[0]),this._fireChange(this._layer.getLatLng(),"Edit")}else if(this._layer instanceof L.CircleMarker||this._layer instanceof L.Marker){let s=this._layer.getLatLng();this._layer._snapped&&(s=this._layer._orgLatLng);let l=a([s]);this._layer.setLatLng(l[0]),this._fireChange(this._layer.getLatLng(),"Edit")}else if(this._layer instanceof L.ImageOverlay){let s=a([this._layer.getBounds().getNorthWest(),this._layer.getBounds().getSouthEast()]);this._layer.setBounds(s),this._fireChange(this._layer.getBounds(),"Edit")}else{let s=a(this._layer.getLatLngs());this._layer.setLatLngs(s),this._fireChange(this._layer.getLatLngs(),"Edit")}this._tempDragCoord=i,e.layer=this._layer,this._fireDrag(e)},addDraggingClass(){let e=this._getDOMElem();e&&L.DomUtil.addClass(e,"leaflet-pm-draggable")},removeDraggingClass(){let e=this._getDOMElem();e&&L.DomUtil.removeClass(e,"leaflet-pm-draggable")},_getDOMElem(){let e=null;return this._layer._path?e=this._layer._path:this._layer._renderer&&this._layer._renderer._container?e=this._layer._renderer._container:this._layer._image?e=this._layer._image:this._layer._icon&&(e=this._layer._icon),e},_overwriteEventIfItComesFromMarker(e){e.target.getLatLng&&(!e.target._radius||e.target._radius<=10)&&(e.containerPoint=this._map.mouseEventToContainerPoint(e.originalEvent),e.latlng=this._map.containerPointToLatLng(e.containerPoint))},_syncLayers(e,i){if(this.enabled())return!1;if(!i._fromLayerSync&&this._layer===i.target&&this.options.syncLayersOnDrag){i._fromLayerSync=!0;let r=[];if(L.Util.isArray(this.options.syncLayersOnDrag))r=this.options.syncLayersOnDrag,this.options.syncLayersOnDrag.forEach(a=>{a instanceof L.LayerGroup&&(r=r.concat(a.pm.getLayers(!0)))});else if(this.options.syncLayersOnDrag===!0&&this._parentLayerGroup)for(let a in this._parentLayerGroup){let s=this._parentLayerGroup[a];s.pm&&(r=s.pm.getLayers(!0))}return L.Util.isArray(r)&&r.length>0&&(r=r.filter(a=>!!a.pm).filter(a=>!!a.pm.options.draggable),r.forEach(a=>{a!==this._layer&&a.pm[e]&&(a._snapped=!1,a.pm[e](i))})),r.length>0}return!1},_stopDOMImageDrag(e){return e.preventDefault(),!1}},_s=fs,ms=$(Mt());function gs(e,i,r,a){return r.unproject(i.transform(r.project(e,a)),a)}function Bo(e,i,r){let a=r.getMaxZoom();if(a===1/0&&(a=r.getZoom()),L.Util.isArray(e)){let s=[];return e.forEach(l=>{s.push(Bo(l,i,r))}),s}return e instanceof L.LatLng?gs(e,i,r,a):null}function nn(e,i){i instanceof L.Layer&&(i=i.getLatLng());let r=e.getMaxZoom();return r===1/0&&(r=e.getZoom()),e.project(i,r)}function ca(e,i){let r=e.getMaxZoom();return r===1/0&&(r=e.getZoom()),e.unproject(i,r)}var ys={_onRotateStart(e){this._preventRenderingMarkers(!0),this._rotationOriginLatLng=this._getRotationCenter().clone(),this._rotationOriginPoint=nn(this._map,this._rotationOriginLatLng),this._rotationStartPoint=nn(this._map,e.target.getLatLng()),this._initialRotateLatLng=jt(this._layer),this._startAngle=this.getAngle();let i=jt(this._rotationLayer,this._rotationLayer.pm._rotateOrgLatLng);this._fireRotationStart(this._rotationLayer,i),this._fireRotationStart(this._map,i)},_onRotate(e){let i=nn(this._map,e.target.getLatLng()),r=this._rotationStartPoint,a=this._rotationOriginPoint,s=Math.atan2(i.y-a.y,i.x-a.x)-Math.atan2(r.y-a.y,r.x-a.x);this._layer.setLatLngs(this._rotateLayer(s,this._initialRotateLatLng,this._rotationOriginLatLng,L.PM.Matrix.init(),this._map));let l=this;function h(E,P=[],j=-1){if(j>-1&&P.push(j),L.Util.isArray(E[0]))E.forEach((R,W)=>h(R,P.slice(),W));else{let R=P.length>0?(0,ms.default)(l._markers,P):l._markers[0];E.forEach((W,X)=>{R[X].setLatLng(W)})}}h(this._layer.getLatLngs());let d=jt(this._rotationLayer);this._rotationLayer.setLatLngs(this._rotateLayer(s,this._rotationLayer.pm._rotateOrgLatLng,this._rotationOriginLatLng,L.PM.Matrix.init(),this._map));let f=s*180/Math.PI;f=f<0?f+360:f;let m=f+this._startAngle;this._setAngle(m),this._rotationLayer.pm._setAngle(m),this._fireRotation(this._rotationLayer,f,d),this._fireRotation(this._map,f,d),this._rotationLayer.pm._fireChange(this._rotationLayer.getLatLngs(),"Rotation")},_onRotateEnd(){let e=this._startAngle;delete this._rotationOriginLatLng,delete this._rotationOriginPoint,delete this._rotationStartPoint,delete this._initialRotateLatLng,delete this._startAngle;let i=jt(this._rotationLayer,this._rotationLayer.pm._rotateOrgLatLng);this._rotationLayer.pm._rotateOrgLatLng=jt(this._rotationLayer),this._fireRotationEnd(this._rotationLayer,e,i),this._fireRotationEnd(this._map,e,i),this._rotationLayer.pm._fireEdit(this._rotationLayer,"Rotation"),this._preventRenderingMarkers(!1),this._layerRotated=!0},_rotateLayer(e,i,r,a,s){let l=nn(s,r);return this._matrix=a.clone().rotate(e,l).flip(),Bo(i,this._matrix,s)},_setAngle(e){e=e<0?e+360:e,this._angle=e%360},_getRotationCenter(){if(this._rotationCenter)return this._rotationCenter;let e=L.polygon(this._layer.getLatLngs(),{stroke:!1,fill:!1,pmIgnore:!0}).addTo(this._layer._map),i=e.getCenter();return e.removeFrom(this._layer._map),i},enableRotate(){if(!this.options.allowRotation){this.disableRotate();return}this.rotateEnabled()&&this.disableRotate(),this._layer instanceof L.Rectangle&&this._angle===void 0&&this.setInitAngle(Rn(this._layer._map,this._layer.getLatLngs()[0][0],this._layer.getLatLngs()[0][1])||0);let e={fill:!1,stroke:!1,pmIgnore:!1,snapIgnore:!0};this._rotatePoly=L.polygon(this._layer.getLatLngs(),e),this._rotatePoly._pmTempLayer=!0,this._rotatePoly.addTo(this._layer._map),this._rotatePoly.pm._setAngle(this.getAngle()),this._rotatePoly.pm.setRotationCenter(this.getRotationCenter()),this._rotatePoly.pm.setOptions(this._layer._map.pm.getGlobalOptions()),this._rotatePoly.pm.setOptions({rotate:!0,snappable:!1,hideMiddleMarkers:!0}),this._rotatePoly.pm._rotationLayer=this._layer,this._rotatePoly.pm.enable(),this._rotateOrgLatLng=jt(this._layer),this._rotateEnabled=!0,this._layer.on("remove",this.disableRotate,this),this._fireRotationEnable(this._layer),this._fireRotationEnable(this._layer._map)},disableRotate(){this.rotateEnabled()&&(this._rotatePoly.pm._layerRotated&&this._fireUpdate(),this._rotatePoly.pm._layerRotated=!1,this._rotatePoly.pm.disable(),this._rotatePoly.remove(),this._rotatePoly.pm.setOptions({rotate:!1}),this._rotatePoly=void 0,this._rotateOrgLatLng=void 0,this._layer.off("remove",this.disableRotate,this),this._rotateEnabled=!1,this._fireRotationDisable(this._layer),this._fireRotationDisable(this._layer._map))},rotateEnabled(){return!!this._rotateEnabled},rotateLayer(e){let i=this.getAngle(),r=this._layer.getLatLngs(),a=e*(Math.PI/180);this._layer.setLatLngs(this._rotateLayer(a,this._layer.getLatLngs(),this._getRotationCenter(),L.PM.Matrix.init(),this._layer._map)),this._rotateOrgLatLng=L.polygon(this._layer.getLatLngs()).getLatLngs(),this._setAngle(this.getAngle()+e),this.rotateEnabled()&&this._rotatePoly&&this._rotatePoly.pm.enabled()&&(this._rotatePoly.setLatLngs(this._rotateLayer(a,this._rotatePoly.getLatLngs(),this._getRotationCenter(),L.PM.Matrix.init(),this._rotatePoly._map)),this._rotatePoly.pm._initMarkers());let s=this.getAngle()-i;s=s<0?s+360:s,this._startAngle=i,this._fireRotation(this._layer,s,r,this._layer),this._fireRotation(this._map||this._layer._map,s,r,this._layer),delete this._startAngle,this._fireChange(this._layer.getLatLngs(),"Rotation")},rotateLayerToAngle(e){let i=e-this.getAngle();this.rotateLayer(i)},getAngle(){return this._angle||0},setInitAngle(e){this._setAngle(e)},getRotationCenter(){return this._getRotationCenter()},setRotationCenter(e){this._rotationCenter=e,this._rotatePoly&&this._rotatePoly.pm.setRotationCenter(e)}},vs=ys,Ls=L.Class.extend({includes:[_s,Kr,vs,mt],options:{snappable:!0,snapDistance:20,allowSelfIntersection:!0,allowSelfIntersectionEdit:!1,preventMarkerRemoval:!1,removeLayerBelowMinVertexCount:!0,limitMarkersToCount:-1,hideMiddleMarkers:!1,snapSegment:!0,syncLayersOnDrag:!1,draggable:!0,allowEditing:!0,allowRemoval:!0,allowCutting:!0,allowRotation:!0,addVertexOn:"click",removeVertexOn:"contextmenu",removeVertexValidation:void 0,addVertexValidation:void 0,moveVertexValidation:void 0,resizeableCircleMarker:!1,resizeableCircle:!0,snapMiddle:!1,snapVertex:!0},setOptions(e){L.Util.setOptions(this,e)},getOptions(){return this.options},applyOptions(){},isPolygon(){return this._layer instanceof L.Polygon},getShape(){return this._shape},_setPane(e,i){i==="layerPane"?e.options.pane=this._map.pm.globalOptions.panes&&this._map.pm.globalOptions.panes.layerPane||"overlayPane":i==="vertexPane"?e.options.pane=this._map.pm.globalOptions.panes&&this._map.pm.globalOptions.panes.vertexPane||"markerPane":i==="markerPane"&&(e.options.pane=this._map.pm.globalOptions.panes&&this._map.pm.globalOptions.panes.markerPane||"markerPane")},remove(){(this._map||this._layer._map).pm.removeLayer({target:this._layer})},_vertexValidation(e,i){let r=i.target,a={layer:this._layer,marker:r,event:i},s="";return e==="move"?s="moveVertexValidation":e==="add"?s="addVertexValidation":e==="remove"&&(s="removeVertexValidation"),this.options[s]&&typeof this.options[s]=="function"&&!this.options[s](a)?(e==="move"&&(r._cancelDragEventChain=r.getLatLng()),!1):(r._cancelDragEventChain=null,!0)},_vertexValidationDrag(e){return e._cancelDragEventChain?(e._latlng=e._cancelDragEventChain,e.update(),!1):!0},_vertexValidationDragEnd(e){return e._cancelDragEventChain?(e._cancelDragEventChain=null,!1):!0}}),qt=Ls;qt.LayerGroup=L.Class.extend({initialize(e){this._layerGroup=e,this._layers=this.getLayers(),this._getMap(),this._layers.forEach(a=>this._initLayer(a));let i=a=>{if(a.layer._pmTempLayer)return;this._layers=this.getLayers();let s=this._layers.filter(l=>!l.pm._parentLayerGroup||!(this._layerGroup._leaflet_id in l.pm._parentLayerGroup));s.forEach(l=>{this._initLayer(l)}),s.length>0&&this._getMap()&&this._getMap().pm.globalEditModeEnabled()&&this.enabled()&&this.enable(this.getOptions())};this._layerGroup.on("layeradd",L.Util.throttle(i,100,this),this),this._layerGroup.on("layerremove",a=>{this._removeLayerFromGroup(a.target)},this);let r=a=>{a.target._pmTempLayer||(this._layers=this.getLayers())};this._layerGroup.on("layerremove",L.Util.throttle(r,100,this),this)},enable(e,i=[]){i.length===0&&(this._layers=this.getLayers()),this._options=e,this._layers.forEach(r=>{r instanceof L.LayerGroup?i.indexOf(r._leaflet_id)===-1&&(i.push(r._leaflet_id),r.pm.enable(e,i)):r.pm.enable(e)})},disable(e=[]){e.length===0&&(this._layers=this.getLayers()),this._layers.forEach(i=>{i instanceof L.LayerGroup?e.indexOf(i._leaflet_id)===-1&&(e.push(i._leaflet_id),i.pm.disable(e)):i.pm.disable()})},enabled(e=[]){return e.length===0&&(this._layers=this.getLayers()),!!this._layers.find(i=>i instanceof L.LayerGroup?e.indexOf(i._leaflet_id)===-1?(e.push(i._leaflet_id),i.pm.enabled(e)):!1:i.pm.enabled())},toggleEdit(e,i=[]){i.length===0&&(this._layers=this.getLayers()),this._options=e,this._layers.forEach(r=>{r instanceof L.LayerGroup?i.indexOf(r._leaflet_id)===-1&&(i.push(r._leaflet_id),r.pm.toggleEdit(e,i)):r.pm.toggleEdit(e)})},_initLayer(e){let i=L.Util.stamp(this._layerGroup);e.pm._parentLayerGroup||(e.pm._parentLayerGroup={}),e.pm._parentLayerGroup[i]=this._layerGroup},_removeLayerFromGroup(e){if(e.pm&&e.pm._layerGroup){let i=L.Util.stamp(this._layerGroup);delete e.pm._layerGroup[i]}},dragging(){return this._layers=this.getLayers(),this._layers?!!this._layers.find(e=>e.pm.dragging()):!1},getOptions(){return this.options},_getMap(){return this._map||this._layers.find(e=>!!e._map)?._map||null},getLayers(e=!1,i=!0,r=!0,a=[]){let s=[];return e?this._layerGroup.getLayers().forEach(l=>{s.push(l),l instanceof L.LayerGroup&&a.indexOf(l._leaflet_id)===-1&&(a.push(l._leaflet_id),s=s.concat(l.pm.getLayers(!0,!0,!0,a)))}):s=this._layerGroup.getLayers(),r&&(s=s.filter(l=>!(l instanceof L.LayerGroup))),i&&(s=s.filter(l=>!!l.pm),s=s.filter(l=>!l._pmTempLayer),s=s.filter(l=>!L.PM.optIn&&!l.options.pmIgnore||L.PM.optIn&&l.options.pmIgnore===!1)),s},setOptions(e,i=[]){i.length===0&&(this._layers=this.getLayers()),this.options=e,this._layers.forEach(r=>{r.pm&&(r instanceof L.LayerGroup?i.indexOf(r._leaflet_id)===-1&&(i.push(r._leaflet_id),r.pm.setOptions(e,i)):r.pm.setOptions(e))})}}),qt.Marker=qt.extend({_shape:"Marker",initialize(e){this._layer=e,this._enabled=!1,this._layer.on("dragend",this._onDragEnd,this)},enable(e={draggable:!0}){if(L.Util.setOptions(this,e),!this.options.allowEditing||!this._layer._map){this.disable();return}this._map=this._layer._map,this.enabled()&&this.disable(),this.applyOptions(),this._layer.on("remove",this.disable,this),this._enabled=!0,this._fireEnable()},disable(){this.enabled()&&(this.disableLayerDrag(),this._layer.off("remove",this.disable,this),this._layer.off("contextmenu",this._removeMarker,this),this._layerEdited&&this._fireUpdate(),this._layerEdited=!1,this._fireDisable(),this._enabled=!1)},enabled(){return this._enabled},toggleEdit(e){this.enabled()?this.disable():this.enable(e)},applyOptions(){this.options.snappable?this._initSnappableMarkers():this._disableSnapping(),this.options.draggable?this.enableLayerDrag():this.disableLayerDrag(),this.options.preventMarkerRemoval||this._layer.on("contextmenu",this._removeMarker,this)},_removeMarker(e){let i=e.target;i.remove(),this._fireRemove(i),this._fireRemove(this._map,i)},_onDragEnd(){this._fireEdit(),this._layerEdited=!0},_initSnappableMarkers(){let e=this._layer;this.options.snapDistance=this.options.snapDistance||30,this.options.snapSegment=this.options.snapSegment===void 0?!0:this.options.snapSegment,e.off("pm:drag",this._handleSnapping,this),e.on("pm:drag",this._handleSnapping,this),e.off("pm:dragend",this._cleanupSnapping,this),e.on("pm:dragend",this._cleanupSnapping,this),e.off("pm:dragstart",this._unsnap,this),e.on("pm:dragstart",this._unsnap,this)},_disableSnapping(){let e=this._layer;e.off("pm:drag",this._handleSnapping,this),e.off("pm:dragend",this._cleanupSnapping,this),e.off("pm:dragstart",this._unsnap,this)}});var li=$(Mt()),bs={filterMarkerGroup(){this.markerCache=[],this.createCache(),this._layer.on("pm:edit",this.createCache,this),this.applyLimitFilters({}),this.throttledApplyLimitFilters||(this.throttledApplyLimitFilters=L.Util.throttle(this.applyLimitFilters,100,this)),this._layer.on("pm:disable",this._removeMarkerLimitEvents,this),this._layer.on("remove",this._removeMarkerLimitEvents,this),this.options.limitMarkersToCount>-1&&(this._layer.on("pm:vertexremoved",this._initMarkers,this),this._map.on("mousemove",this.throttledApplyLimitFilters,this))},_removeMarkerLimitEvents(){this._map.off("mousemove",this.throttledApplyLimitFilters,this),this._layer.off("pm:edit",this.createCache,this),this._layer.off("pm:disable",this._removeMarkerLimitEvents,this),this._layer.off("pm:vertexremoved",this._initMarkers,this)},createCache(){let e=[...this._markerGroup.getLayers(),...this.markerCache];this.markerCache=e.filter((i,r,a)=>a.indexOf(i)===r)},_removeFromCache(e){let i=this.markerCache.indexOf(e);i>-1&&this.markerCache.splice(i,1)},renderLimits(e){this.markerCache.forEach(i=>{e.includes(i)?this._markerGroup.addLayer(i):this._markerGroup.removeLayer(i)})},applyLimitFilters({latlng:e={lat:0,lng:0}}){if(this._preventRenderMarkers)return;let i=[...this._filterClosestMarkers(e)];this.renderLimits(i)},_filterClosestMarkers(e){let i=[...this.markerCache],r=this.options.limitMarkersToCount;return r===-1?i:(i.sort((a,s)=>{let l=a._latlng.distanceTo(e),h=s._latlng.distanceTo(e);return l-h}),i.filter((a,s)=>r>-1?s<r:!0))},_preventRenderMarkers:!1,_preventRenderingMarkers(e){this._preventRenderMarkers=!!e}},xs=bs;qt.Line=qt.extend({includes:[xs],_shape:"Line",initialize(e){this._layer=e,this._enabled=!1},enable(e){if(L.Util.setOptions(this,e),this._map=this._layer._map,!!this._map){if(!this.options.allowEditing){this.disable();return}this.enabled()&&this.disable(),this._enabled=!0,this._initMarkers(),this.applyOptions(),this._layer.on("remove",this.disable,this),this.options.allowSelfIntersection||this._layer.on("pm:vertexremoved",this._handleSelfIntersectionOnVertexRemoval,this),this.options.allowSelfIntersection?this.cachedColor=void 0:(this._layer.options.color!=="#f00000ff"?(this.cachedColor=this._layer.options.color,this.isRed=!1):this.isRed=!0,this._handleLayerStyle()),this._fireEnable()}},disable(){if(!this.enabled()||this._dragging)return;this._enabled=!1,this._markerGroup.clearLayers(),this._markerGroup.removeFrom(this._map),this._layer.off("remove",this.disable,this),this.options.allowSelfIntersection||this._layer.off("pm:vertexremoved",this._handleSelfIntersectionOnVertexRemoval,this);let e=this._layer._path?this._layer._path:this._layer._renderer._container;L.DomUtil.removeClass(e,"leaflet-pm-draggable"),this._layerEdited&&this._fireUpdate(),this._layerEdited=!1,this._fireDisable()},enabled(){return this._enabled},toggleEdit(e){return this.enabled()?this.disable():this.enable(e),this.enabled()},applyOptions(){this.options.snappable?this._initSnappableMarkers():this._disableSnapping()},_initMarkers(){let e=this._map,i=this._layer.getLatLngs();this._markerGroup&&(this._markerGroup.removeFrom(e),this._markerGroup.clearLayers()),this._markerGroup=new L.FeatureGroup,this._markerGroup._pmTempLayer=!0;let r=a=>{if(Array.isArray(a[0]))return a.map(r,this);let s=a.map(this._createMarker,this);return this.options.hideMiddleMarkers!==!0&&a.map((l,h)=>{let d=this.isPolygon()?(h+1)%a.length:h+1;return this._createMiddleMarker(s[h],s[d])}),s};this._markers=r(i),this.filterMarkerGroup(),e.addLayer(this._markerGroup)},_createMarker(e){let i=new L.Marker(e,{draggable:!0,icon:L.divIcon({className:"marker-icon"})});return this._setPane(i,"vertexPane"),i._pmTempLayer=!0,this.options.rotate?(i.on("dragstart",this._onRotateStart,this),i.on("drag",this._onRotate,this),i.on("dragend",this._onRotateEnd,this)):(i.on("click",this._onVertexClick,this),i.on("dragstart",this._onMarkerDragStart,this),i.on("move",this._onMarkerDrag,this),i.on("dragend",this._onMarkerDragEnd,this),this.options.preventMarkerRemoval||i.on(this.options.removeVertexOn,this._removeMarker,this)),this._markerGroup.addLayer(i),i},_createMiddleMarker(e,i){if(!e||!i)return!1;let r=L.PM.Utils.calcMiddleLatLng(this._map,e.getLatLng(),i.getLatLng()),a=this._createMarker(r),s=L.divIcon({className:"marker-icon marker-icon-middle"});return a.setIcon(s),a.leftM=e,a.rightM=i,e._middleMarkerNext=a,i._middleMarkerPrev=a,a.on(this.options.addVertexOn,this._onMiddleMarkerClick,this),a.on("movestart",this._onMiddleMarkerMoveStart,this),a},_onMiddleMarkerClick(e){let i=e.target;if(!this._vertexValidation("add",e))return;let r=L.divIcon({className:"marker-icon"});i.setIcon(r),this._addMarker(i,i.leftM,i.rightM)},_onMiddleMarkerMoveStart(e){let i=e.target;if(i.on("moveend",this._onMiddleMarkerMoveEnd,this),!this._vertexValidation("add",e)){i.on("move",this._onMiddleMarkerMovePrevent,this);return}i._dragging=!0,this._addMarker(i,i.leftM,i.rightM)},_onMiddleMarkerMovePrevent(e){let i=e.target;this._vertexValidationDrag(i)},_onMiddleMarkerMoveEnd(e){let i=e.target;if(i.off("move",this._onMiddleMarkerMovePrevent,this),i.off("moveend",this._onMiddleMarkerMoveEnd,this),!this._vertexValidationDragEnd(i))return;let r=L.divIcon({className:"marker-icon"});i.setIcon(r),setTimeout(()=>{delete i._dragging},100)},_addMarker(e,i,r){e.off("movestart",this._onMiddleMarkerMoveStart,this),e.off(this.options.addVertexOn,this._onMiddleMarkerClick,this);let a=e.getLatLng(),s=this._layer._latlngs;delete e.leftM,delete e.rightM;let{indexPath:l,index:h,parentPath:d}=L.PM.Utils.findDeepMarkerIndex(this._markers,i),f=l.length>1?(0,li.default)(s,d):s,m=l.length>1?(0,li.default)(this._markers,d):this._markers;f.splice(h+1,0,a),m.splice(h+1,0,e),this._layer.setLatLngs(s),this.options.hideMiddleMarkers!==!0&&(this._createMiddleMarker(i,e),this._createMiddleMarker(e,r)),this._fireEdit(),this._layerEdited=!0,this._fireChange(this._layer.getLatLngs(),"Edit"),this._fireVertexAdded(e,L.PM.Utils.findDeepMarkerIndex(this._markers,e).indexPath,a),this.options.snappable&&this._initSnappableMarkers()},hasSelfIntersection(){return ji(this._layer.toGeoJSON(15)).features.length>0},_handleSelfIntersectionOnVertexRemoval(){this._handleLayerStyle(!0)&&(this._layer.setLatLngs(this._coordsBeforeEdit),this._coordsBeforeEdit=null,this._initMarkers())},_handleLayerStyle(e){let i=this._layer,r,a;if(this.options.allowSelfIntersection?r=!1:(a=ji(this._layer.toGeoJSON(15)),r=a.features.length>0),r){if(!this.options.allowSelfIntersection&&this.options.allowSelfIntersectionEdit&&this._updateDisabledMarkerStyle(this._markers,!0),this.isRed)return r;e?this._flashLayer():(i.setStyle({color:"#f00000ff"}),this.isRed=!0),this._fireIntersect(a)}else i.setStyle({color:this.cachedColor}),this.isRed=!1,!this.options.allowSelfIntersection&&this.options.allowSelfIntersectionEdit&&this._updateDisabledMarkerStyle(this._markers,!1);return r},_flashLayer(){this.cachedColor||(this.cachedColor=this._layer.options.color),this._layer.setStyle({color:"#f00000ff"}),this.isRed=!0,window.setTimeout(()=>{this._layer.setStyle({color:this.cachedColor}),this.isRed=!1},200)},_updateDisabledMarkerStyle(e,i){e.forEach(r=>{Array.isArray(r)?this._updateDisabledMarkerStyle(r,i):r._icon&&(i&&!this._checkMarkerAllowedToDrag(r)?L.DomUtil.addClass(r._icon,"vertexmarker-disabled"):L.DomUtil.removeClass(r._icon,"vertexmarker-disabled"))})},_removeMarker(e){let i=e.target;if(!this._vertexValidation("remove",e))return;this.options.allowSelfIntersection||(this._coordsBeforeEdit=jt(this._layer,this._layer.getLatLngs()));let r=this._layer.getLatLngs(),{indexPath:a,index:s,parentPath:l}=L.PM.Utils.findDeepMarkerIndex(this._markers,i);if(!a)return;let h=a.length>1?(0,li.default)(r,l):r,d=a.length>1?(0,li.default)(this._markers,l):this._markers,f=l[l.length-1]>0&&this._layer instanceof L.Polygon;if(!this.options.removeLayerBelowMinVertexCount&&!f&&(h.length<=2||this.isPolygon()&&h.length<=3)){this._flashLayer();return}h.splice(s,1),this._layer.setLatLngs(r),this.isPolygon()&&h.length<=2&&h.splice(0,h.length);let m=!1;if(h.length<=1&&(h.splice(0,h.length),l.length>1&&a.length>1&&(r=Qe(r)),this._layer.setLatLngs(r),this._initMarkers(),m=!0),Fn(r)||this._layer.remove(),r=Qe(r),this._layer.setLatLngs(r),this._markers=Qe(this._markers),!m&&(d=a.length>1?(0,li.default)(this._markers,l):this._markers,i._middleMarkerPrev&&(this._markerGroup.removeLayer(i._middleMarkerPrev),this._removeFromCache(i._middleMarkerPrev)),i._middleMarkerNext&&(this._markerGroup.removeLayer(i._middleMarkerNext),this._removeFromCache(i._middleMarkerNext)),this._markerGroup.removeLayer(i),this._removeFromCache(i),d)){let E,P;if(this.isPolygon()?(E=(s+1)%d.length,P=(s+(d.length-1))%d.length):(P=s-1<0?void 0:s-1,E=s+1>=d.length?void 0:s+1),E!==P){let j=d[P],R=d[E];this.options.hideMiddleMarkers!==!0&&this._createMiddleMarker(j,R)}d.splice(s,1)}this._fireEdit(),this._layerEdited=!0,this._fireVertexRemoved(i,a),this._fireChange(this._layer.getLatLngs(),"Edit")},updatePolygonCoordsFromMarkerDrag(e){let i=this._layer.getLatLngs(),r=e.getLatLng(),{indexPath:a,index:s,parentPath:l}=L.PM.Utils.findDeepMarkerIndex(this._markers,e);(a.length>1?(0,li.default)(i,l):i).splice(s,1,r),this._layer.setLatLngs(i)},_getNeighborMarkers(e){let{indexPath:i,index:r,parentPath:a}=L.PM.Utils.findDeepMarkerIndex(this._markers,e),s=i.length>1?(0,li.default)(this._markers,a):this._markers,l=(r+1)%s.length,h=(r+(s.length-1))%s.length,d=s[h],f=s[l];return{prevMarker:d,nextMarker:f}},_checkMarkerAllowedToDrag(e){let{prevMarker:i,nextMarker:r}=this._getNeighborMarkers(e),a=L.polyline([i.getLatLng(),e.getLatLng()]),s=L.polyline([e.getLatLng(),r.getLatLng()]),l=We(this._layer.toGeoJSON(15),a.toGeoJSON(15)).features.length,h=We(this._layer.toGeoJSON(15),s.toGeoJSON(15)).features.length;return e.getLatLng()===this._markers[0][0].getLatLng()?h+=1:e.getLatLng()===this._markers[0][this._markers[0].length-1].getLatLng()&&(l+=1),!(l<=2&&h<=2)},_onMarkerDragStart(e){let i=e.target;if(this.cachedColor||(this.cachedColor=this._layer.options.color),!this._vertexValidation("move",e))return;let{indexPath:r}=L.PM.Utils.findDeepMarkerIndex(this._markers,i);this._fireMarkerDragStart(e,r),this.options.allowSelfIntersection||(this._coordsBeforeEdit=jt(this._layer,this._layer.getLatLngs())),!this.options.allowSelfIntersection&&this.options.allowSelfIntersectionEdit&&this.hasSelfIntersection()?this._markerAllowedToDrag=this._checkMarkerAllowedToDrag(i):this._markerAllowedToDrag=null},_onMarkerDrag(e){let i=e.target;if(!this._vertexValidationDrag(i))return;let{indexPath:r,index:a,parentPath:s}=L.PM.Utils.findDeepMarkerIndex(this._markers,i);if(!r)return;if(!this.options.allowSelfIntersection&&this.options.allowSelfIntersectionEdit&&this.hasSelfIntersection()&&this._markerAllowedToDrag===!1){this._layer.setLatLngs(this._coordsBeforeEdit),this._initMarkers(),this._handleLayerStyle();return}this.updatePolygonCoordsFromMarkerDrag(i);let l=r.length>1?(0,li.default)(this._markers,s):this._markers,h=(a+1)%l.length,d=(a+(l.length-1))%l.length,f=i.getLatLng(),m=l[d].getLatLng(),E=l[h].getLatLng();if(i._middleMarkerNext){let P=L.PM.Utils.calcMiddleLatLng(this._map,f,E);i._middleMarkerNext.setLatLng(P)}if(i._middleMarkerPrev){let P=L.PM.Utils.calcMiddleLatLng(this._map,f,m);i._middleMarkerPrev.setLatLng(P)}this.options.allowSelfIntersection||this._handleLayerStyle(),this._fireMarkerDrag(e,r),this._fireChange(this._layer.getLatLngs(),"Edit")},_onMarkerDragEnd(e){let i=e.target;if(!this._vertexValidationDragEnd(i))return;let{indexPath:r}=L.PM.Utils.findDeepMarkerIndex(this._markers,i),a=!this.options.allowSelfIntersection&&this.hasSelfIntersection();a&&this.options.allowSelfIntersectionEdit&&this._markerAllowedToDrag&&(a=!1);let s=!this.options.allowSelfIntersection&&a;if(this._fireMarkerDragEnd(e,r,s),s){this._layer.setLatLngs(this._coordsBeforeEdit),this._coordsBeforeEdit=null,this._initMarkers(),this.options.snappable&&this._initSnappableMarkers(),this._handleLayerStyle(),this._fireLayerReset(e,r);return}!this.options.allowSelfIntersection&&this.options.allowSelfIntersectionEdit&&this._handleLayerStyle(),this._fireEdit(),this._layerEdited=!0,this._fireChange(this._layer.getLatLngs(),"Edit")},_onVertexClick(e){let i=e.target;if(i._dragging)return;let{indexPath:r}=L.PM.Utils.findDeepMarkerIndex(this._markers,i);this._fireVertexClick(e,r)}}),qt.Polygon=qt.Line.extend({_shape:"Polygon",_checkMarkerAllowedToDrag(e){let{prevMarker:i,nextMarker:r}=this._getNeighborMarkers(e),a=L.polyline([i.getLatLng(),e.getLatLng()]),s=L.polyline([e.getLatLng(),r.getLatLng()]),l=We(this._layer.toGeoJSON(15),a.toGeoJSON(15)).features.length,h=We(this._layer.toGeoJSON(15),s.toGeoJSON(15)).features.length;return!(l<=2&&h<=2)}}),qt.Rectangle=qt.Polygon.extend({_shape:"Rectangle",_initMarkers(){let e=this._map,i=this._findCorners();this._markerGroup&&this._markerGroup.clearLayers(),this._markerGroup=new L.FeatureGroup,this._markerGroup._pmTempLayer=!0,e.addLayer(this._markerGroup),this._markers=[],this._markers[0]=i.map(this._createMarker,this),[this._cornerMarkers]=this._markers,this._layer.getLatLngs()[0].forEach((r,a)=>{let s=this._cornerMarkers.find(l=>l._index===a);s&&s.setLatLng(r)})},applyOptions(){this.options.snappable?this._initSnappableMarkers():this._disableSnapping(),this._addMarkerEvents()},_createMarker(e,i){let r=new L.Marker(e,{draggable:!0,icon:L.divIcon({className:"marker-icon"})});return this._setPane(r,"vertexPane"),r._origLatLng=e,r._index=i,r._pmTempLayer=!0,r.on("click",this._onVertexClick,this),this._markerGroup.addLayer(r),r},_addMarkerEvents(){this._markers[0].forEach(e=>{e.on("dragstart",this._onMarkerDragStart,this),e.on("drag",this._onMarkerDrag,this),e.on("dragend",this._onMarkerDragEnd,this),this.options.preventMarkerRemoval||e.on("contextmenu",this._removeMarker,this)})},_removeMarker(){return null},_onMarkerDragStart(e){if(!this._vertexValidation("move",e))return;let i=e.target,r=this._cornerMarkers;i._oppositeCornerLatLng=r.find(s=>s._index===(i._index+2)%4).getLatLng(),i._snapped=!1;let{indexPath:a}=L.PM.Utils.findDeepMarkerIndex(this._markers,i);this._fireMarkerDragStart(e,a)},_onMarkerDrag(e){let i=e.target;if(!this._vertexValidationDrag(i)||i._index===void 0)return;this._adjustRectangleForMarkerMove(i);let{indexPath:r}=L.PM.Utils.findDeepMarkerIndex(this._markers,i);this._fireMarkerDrag(e,r),this._fireChange(this._layer.getLatLngs(),"Edit")},_onMarkerDragEnd(e){let i=e.target;if(!this._vertexValidationDragEnd(i))return;this._cornerMarkers.forEach(a=>{delete a._oppositeCornerLatLng});let{indexPath:r}=L.PM.Utils.findDeepMarkerIndex(this._markers,i);this._fireMarkerDragEnd(e,r),this._fireEdit(),this._layerEdited=!0,this._fireChange(this._layer.getLatLngs(),"Edit")},_adjustRectangleForMarkerMove(e){L.extend(e._origLatLng,e._latlng);let i=L.PM.Utils._getRotatedRectangle(e.getLatLng(),e._oppositeCornerLatLng,this.getAngle(),this._map);this._layer.setLatLngs(i),this._adjustAllMarkers(e),this._layer.redraw()},_adjustAllMarkers(e){let i=this._layer.getLatLngs()[0];if(i&&i.length!==4&&i.length>0)i.forEach((r,a)=>{this._cornerMarkers[a].setLatLng(r)}),this._cornerMarkers.slice(i.length).forEach(r=>{r.setLatLng(i[0])});else if(!i||!i.length)console.error("The layer has no LatLngs");else{let r=i.findIndex(a=>e.getLatLng().equals(a));r>-1?(this._cornerMarkers[(e._index+1)%4].setLatLng(i[(r+1)%4]),this._cornerMarkers[(e._index+2)%4].setLatLng(i[(r+2)%4]),this._cornerMarkers[(e._index+3)%4].setLatLng(i[(r+3)%4])):this._cornerMarkers.forEach(a=>{a.setLatLng(i[a._index])})}},_findCorners(){this._angle===void 0&&this.setInitAngle(Rn(this._map,this._layer.getLatLngs()[0][0],this._layer.getLatLngs()[0][1])||0);let e=this._layer.getLatLngs()[0];return L.PM.Utils._getRotatedRectangle(e[0],e[2],this.getAngle(),this._map||this)}}),qt.CircleMarker=qt.extend({_shape:"CircleMarker",initialize(e){this._layer=e,this._enabled=!1,this._minRadiusOption="minRadiusCircleMarker",this._maxRadiusOption="maxRadiusCircleMarker",this._editableOption="resizeableCircleMarker",this._updateHiddenPolyCircle()},enable(e={draggable:!0,snappable:!0}){if(L.Util.setOptions(this,e),this.options.editable&&(this.options.resizeableCircleMarker=this.options.editable,delete this.options.editable),!this.options.allowEditing||!this._layer._map){this.disable();return}this._map=this._layer._map,this.enabled()&&this.disable(),this.applyOptions(),this._layer.on("remove",this.disable,this),this._enabled=!0,this._extendingEnable(),this._updateHiddenPolyCircle(),this._fireEnable()},_extendingEnable(){this._layer.on("pm:dragstart",this._onDragStart,this),this._layer.on("pm:drag",this._onMarkerDrag,this),this._layer.on("pm:dragend",this._onMarkerDragEnd,this)},disable(){this.dragging()||(this._map||(this._map=this._layer._map),this._map&&this.enabled()&&(this.layerDragEnabled()&&this.disableLayerDrag(),this._helperLayers&&(this._helperLayers.clearLayers(),this._helperLayers.removeFrom(this._map)),this.options[this._editableOption]?(this._map.off("move",this._syncMarkers,this),this._outerMarker.off("drag",this._handleOuterMarkerSnapping,this)):this._map.off("move",this._updateHiddenPolyCircle,this),this._extendingDisable(),this._layer.off("remove",this.disable,this),this._layerEdited&&this._fireUpdate(),this._layerEdited=!1,this._fireDisable(),this._enabled=!1))},_extendingDisable(){this._layer.off("contextmenu",this._removeMarker,this)},enabled(){return this._enabled},toggleEdit(e){this.enabled()?this.disable():this.enable(e)},applyOptions(){this.options[this._editableOption]?(this._initMarkers(),this._map.on("move",this._syncMarkers,this),this.options.snappable?(this._initSnappableMarkers(),this._outerMarker.on("drag",this._handleOuterMarkerSnapping,this),this._outerMarker.on("move",this._syncHintLine,this),this._outerMarker.on("move",this._syncCircleRadius,this)):this._disableSnapping()):(this.options.draggable&&this.enableLayerDrag(),this._map.on("move",this._updateHiddenPolyCircle,this),this.options.snappable?this._initSnappableMarkersDrag():this._disableSnappingDrag()),this._extendingApplyOptions()},_extendingApplyOptions(){this.options.preventMarkerRemoval||this._layer.on("contextmenu",this._removeMarker,this)},_initMarkers(){let e=this._map;this._helperLayers&&(this._helperLayers.removeFrom(e),this._helperLayers.clearLayers()),this._helperLayers=new L.FeatureGroup,this._helperLayers._pmTempLayer=!0,this._helperLayers.addTo(e);let i=this._layer.getLatLng(),r=this._layer._radius,a=this._getLatLngOnCircle(i,r);this._centerMarker=this._createCenterMarker(i),this._outerMarker=this._createOuterMarker(a),this._markers=[this._centerMarker,this._outerMarker],this._createHintLine(this._centerMarker,this._outerMarker)},_getLatLngOnCircle(e,i){let r=this._map.project(e),a=L.point(r.x+i,r.y);return this._map.unproject(a)},_createHintLine(e,i){let r=e.getLatLng(),a=i.getLatLng();this._hintline=L.polyline([r,a],this.options.hintlineStyle),this._setPane(this._hintline,"layerPane"),this._hintline._pmTempLayer=!0,this._helperLayers.addLayer(this._hintline)},_createCenterMarker(e){let i=this._createMarker(e);return this.options.draggable?(L.DomUtil.addClass(i._icon,"leaflet-pm-draggable"),i.on("move",this._moveCircle,this)):i.dragging.disable(),i},_createOuterMarker(e){let i=this._createMarker(e);return i.on("drag",this._resizeCircle,this),i},_createMarker(e){let i=new L.Marker(e,{draggable:!0,icon:L.divIcon({className:"marker-icon"})});return this._setPane(i,"vertexPane"),i._origLatLng=e,i._pmTempLayer=!0,i.on("dragstart",this._onMarkerDragStart,this),i.on("drag",this._onMarkerDrag,this),i.on("dragend",this._onMarkerDragEnd,this),i.on("click",this._onVertexClick,this),this._helperLayers.addLayer(i),i},_moveCircle(e){if(e.target._cancelDragEventChain)return;let i=this._centerMarker.getLatLng();this._layer.setLatLng(i);let r=this._layer._radius,a=this._getLatLngOnCircle(i,r);this._outerMarker._latlng=a,this._outerMarker.update(),this._syncHintLine(),this._updateHiddenPolyCircle(),this._fireCenterPlaced("Edit"),this._fireChange(this._layer.getLatLng(),"Edit")},_syncMarkers(){let e=this._layer.getLatLng(),i=this._layer._radius,r=this._getLatLngOnCircle(e,i);this._outerMarker.setLatLng(r),this._centerMarker.setLatLng(e),this._syncHintLine(),this._updateHiddenPolyCircle()},_resizeCircle(){this._outerMarker.setLatLng(this._getNewDestinationOfOuterMarker()),this._syncHintLine(),this._syncCircleRadius()},_syncCircleRadius(){let e=this._centerMarker.getLatLng(),i=this._outerMarker.getLatLng(),r=this._distanceCalculation(e,i);this.options[this._minRadiusOption]&&r<this.options[this._minRadiusOption]?this._layer.setRadius(this.options[this._minRadiusOption]):this.options[this._maxRadiusOption]&&r>this.options[this._maxRadiusOption]?this._layer.setRadius(this.options[this._maxRadiusOption]):this._layer.setRadius(r),this._updateHiddenPolyCircle(),this._fireChange(this._layer.getLatLng(),"Edit")},_syncHintLine(){let e=this._centerMarker.getLatLng(),i=this._outerMarker.getLatLng();this._hintline.setLatLngs([e,i])},_removeMarker(){this.options[this._editableOption]&&this.disable(),this._layer.remove(),this._fireRemove(this._layer),this._fireRemove(this._map,this._layer)},_onDragStart(){this._map.pm.Draw.CircleMarker._layerIsDragging=!0},_onMarkerDragStart(e){this._vertexValidation("move",e)&&this._fireMarkerDragStart(e)},_onMarkerDrag(e){let i=e.target;i instanceof L.Marker&&!this._vertexValidationDrag(i)||this._fireMarkerDrag(e)},_onMarkerDragEnd(e){this._extedingMarkerDragEnd();let i=e.target;this._vertexValidationDragEnd(i)&&(this.options[this._editableOption]&&(this._fireEdit(),this._layerEdited=!0),this._fireMarkerDragEnd(e))},_extedingMarkerDragEnd(){this._map.pm.Draw.CircleMarker._layerIsDragging=!1},_initSnappableMarkersDrag(){let e=this._layer;this.options.snapDistance=this.options.snapDistance||30,this.options.snapSegment=this.options.snapSegment===void 0?!0:this.options.snapSegment,e.off("pm:drag",this._handleSnapping,this),e.on("pm:drag",this._handleSnapping,this),e.off("pm:dragend",this._cleanupSnapping,this),e.on("pm:dragend",this._cleanupSnapping,this),e.off("pm:dragstart",this._unsnap,this),e.on("pm:dragstart",this._unsnap,this)},_disableSnappingDrag(){let e=this._layer;e.off("pm:drag",this._handleSnapping,this),e.off("pm:dragend",this._cleanupSnapping,this),e.off("pm:dragstart",this._unsnap,this)},_updateHiddenPolyCircle(){let e=this._layer._map||this._map;if(e){let i=L.PM.Utils.pxRadiusToMeterRadius(this._layer.getRadius(),e,this._layer.getLatLng()),r=L.circle(this._layer.getLatLng(),this._layer.options);r.setRadius(i);let a=e&&e.pm._isCRSSimple();this._hiddenPolyCircle?this._hiddenPolyCircle.setLatLngs(L.PM.Utils.circleToPolygon(r,200,!a).getLatLngs()):this._hiddenPolyCircle=L.PM.Utils.circleToPolygon(r,200,!a),this._hiddenPolyCircle._parentCopy||(this._hiddenPolyCircle._parentCopy=this._layer)}},_getNewDestinationOfOuterMarker(){let e=this._centerMarker.getLatLng(),i=this._outerMarker.getLatLng(),r=this._distanceCalculation(e,i);return this.options[this._minRadiusOption]&&r<this.options[this._minRadiusOption]?i=ti(this._map,e,i,this._getMinDistanceInMeter(e)):this.options[this._maxRadiusOption]&&r>this.options[this._maxRadiusOption]&&(i=ti(this._map,e,i,this._getMaxDistanceInMeter(e))),i},_handleOuterMarkerSnapping(){if(this._outerMarker._snapped){let e=this._centerMarker.getLatLng(),i=this._outerMarker.getLatLng(),r=this._distanceCalculation(e,i);this.options[this._minRadiusOption]&&r<this.options[this._minRadiusOption]?this._outerMarker.setLatLng(this._outerMarker._orgLatLng):this.options[this._maxRadiusOption]&&r>this.options[this._maxRadiusOption]&&this._outerMarker.setLatLng(this._outerMarker._orgLatLng)}this._outerMarker.setLatLng(this._getNewDestinationOfOuterMarker())},_distanceCalculation(e,i){return this._map.project(e).distanceTo(this._map.project(i))},_getMinDistanceInMeter(e){return L.PM.Utils.pxRadiusToMeterRadius(this.options[this._minRadiusOption],this._map,e)},_getMaxDistanceInMeter(e){return L.PM.Utils.pxRadiusToMeterRadius(this.options[this._maxRadiusOption],this._map,e)},_onVertexClick(e){e.target._dragging||this._fireVertexClick(e,void 0)}}),qt.Circle=qt.CircleMarker.extend({_shape:"Circle",initialize(e){this._layer=e,this._enabled=!1,this._minRadiusOption="minRadiusCircle",this._maxRadiusOption="maxRadiusCircle",this._editableOption="resizeableCircle",this._updateHiddenPolyCircle()},enable(e){L.PM.Edit.CircleMarker.prototype.enable.call(this,e||{})},_extendingEnable(){},_extendingDisable(){this._layer.off("remove",this.disable,this);let e=this._layer._path?this._layer._path:this._layer._renderer._container;L.DomUtil.removeClass(e,"leaflet-pm-draggable")},_extendingApplyOptions(){},_syncMarkers(){},_removeMarker(){},_onDragStart(){},_extedingMarkerDragEnd(){},_updateHiddenPolyCircle(){let e=this._map&&this._map.pm._isCRSSimple();this._hiddenPolyCircle?this._hiddenPolyCircle.setLatLngs(L.PM.Utils.circleToPolygon(this._layer,200,!e).getLatLngs()):this._hiddenPolyCircle=L.PM.Utils.circleToPolygon(this._layer,200,!e),this._hiddenPolyCircle._parentCopy||(this._hiddenPolyCircle._parentCopy=this._layer)},_distanceCalculation(e,i){return this._map.distance(e,i)},_getMinDistanceInMeter(){return this.options[this._minRadiusOption]},_getMaxDistanceInMeter(){return this.options[this._maxRadiusOption]},_onVertexClick(e){e.target._dragging||this._fireVertexClick(e,void 0)}}),qt.ImageOverlay=qt.extend({_shape:"ImageOverlay",initialize(e){this._layer=e,this._enabled=!1},toggleEdit(e){this.enabled()?this.disable():this.enable(e)},enabled(){return this._enabled},enable(e={draggable:!0,snappable:!0}){if(L.Util.setOptions(this,e),this._map=this._layer._map,!!this._map){if(!this.options.allowEditing){this.disable();return}this.enabled()||this.disable(),this.enableLayerDrag(),this._layer.on("remove",this.disable,this),this._enabled=!0,this._otherSnapLayers=this._findCorners(),this._fireEnable()}},disable(){this._dragging||(this._map||(this._map=this._layer._map),this.disableLayerDrag(),this._layer.off("remove",this.disable,this),this.enabled()||(this._layerEdited&&this._fireUpdate(),this._layerEdited=!1,this._fireDisable()),this._enabled=!1)},_findCorners(){let e=this._layer.getBounds(),i=e.getNorthWest(),r=e.getNorthEast(),a=e.getSouthEast(),s=e.getSouthWest();return[i,r,a,s]}}),qt.Text=qt.extend({_shape:"Text",initialize(e){this._layer=e,this._enabled=!1},enable(e){if(L.Util.setOptions(this,e),!!this.textArea){if(!this.options.allowEditing||!this._layer._map){this.disable();return}this._map=this._layer._map,this.enabled()&&this.disable(),this.applyOptions(),this._safeToCacheDragState=!0,this._focusChange(),this.textArea.readOnly=!1,this.textArea.classList.remove("pm-disabled"),this._layer.on("remove",this.disable,this),L.DomEvent.on(this.textArea,"input",this._autoResize,this),L.DomEvent.on(this.textArea,"focus",this._focusChange,this),L.DomEvent.on(this.textArea,"blur",this._focusChange,this),this._layer.on("dblclick",L.DomEvent.stop),L.DomEvent.off(this.textArea,"mousedown",this._preventTextSelection),this._enabled=!0,this._fireEnable()}},disable(){if(!this.enabled())return;this._layer.off("remove",this.disable,this),L.DomEvent.off(this.textArea,"input",this._autoResize,this),L.DomEvent.off(this.textArea,"focus",this._focusChange,this),L.DomEvent.off(this.textArea,"blur",this._focusChange,this),L.DomEvent.off(document,"click",this._documentClick,this),this._focusChange(),this.textArea.readOnly=!0,this.textArea.classList.add("pm-disabled");let e=document.activeElement;this.textArea.focus(),this.textArea.selectionStart=0,this.textArea.selectionEnd=0,L.DomEvent.on(this.textArea,"mousedown",this._preventTextSelection),e.focus(),this._disableOnBlurActive=!1,this._layerEdited&&this._fireUpdate(),this._layerEdited=!1,this._fireDisable(),this._enabled=!1},enabled(){return this._enabled},toggleEdit(e){this.enabled()?this.disable():this.enable(e)},applyOptions(){this.options.snappable?this._initSnappableMarkers():this._disableSnapping()},_initSnappableMarkers(){let e=this._layer;this.options.snapDistance=this.options.snapDistance||30,this.options.snapSegment=this.options.snapSegment===void 0?!0:this.options.snapSegment,e.off("pm:drag",this._handleSnapping,this),e.on("pm:drag",this._handleSnapping,this),e.off("pm:dragend",this._cleanupSnapping,this),e.on("pm:dragend",this._cleanupSnapping,this),e.off("pm:dragstart",this._unsnap,this),e.on("pm:dragstart",this._unsnap,this)},_disableSnapping(){let e=this._layer;e.off("pm:drag",this._handleSnapping,this),e.off("pm:dragend",this._cleanupSnapping,this),e.off("pm:dragstart",this._unsnap,this)},_autoResize(){this.textArea.style.height="1px",this.textArea.style.width="1px";let e=this.textArea.scrollHeight>21?this.textArea.scrollHeight:21,i=this.textArea.scrollWidth>16?this.textArea.scrollWidth:16;this.textArea.style.height=`${e}px`,this.textArea.style.width=`${i}px`,this._layer.options.text=this.getText(),this._fireTextChange(this.getText())},_disableOnBlur(){this._disableOnBlurActive=!0,setTimeout(()=>{this.enabled()&&L.DomEvent.on(document,"click",this._documentClick,this)},100)},_documentClick(e){e.target!==this.textArea&&(this.disable(),!this.getText()&&this.options.removeIfEmpty&&this.remove())},_focusChange(e={}){let i=this._hasFocus;this._hasFocus=e.type==="focus",!i!=!this._hasFocus&&(this._hasFocus?(this._applyFocus(),this._focusText=this.getText(),this._fireTextFocus()):(this._removeFocus(),this._fireTextBlur(),this._focusText!==this.getText()&&(this._fireEdit(),this._layerEdited=!0)))},_applyFocus(){this.textArea.classList.add("pm-hasfocus"),this._map.dragging&&(this._safeToCacheDragState&&(this._originalMapDragState=this._map.dragging._enabled,this._safeToCacheDragState=!1),this._map.dragging.disable())},_removeFocus(){this._map.dragging&&(this._originalMapDragState&&this._map.dragging.enable(),this._safeToCacheDragState=!0),this.textArea.classList.remove("pm-hasfocus")},focus(){if(!this.enabled())throw new TypeError("Layer is not enabled");this.textArea.focus()},blur(){if(!this.enabled())throw new TypeError("Layer is not enabled");this.textArea.blur(),this._disableOnBlurActive&&this.disable()},hasFocus(){return this._hasFocus},getElement(){return this.textArea},setText(e){e&&(this.textArea.value=e),this._autoResize()},getText(){return this.textArea.value},_initTextMarker(){if(this.textArea=L.PM.Draw.Text.prototype._createTextArea.call(this),this.options.className){let i=this.options.className.split(" ");this.textArea.classList.add(...i)}let e=L.PM.Draw.Text.prototype._createTextIcon.call(this,this.textArea);this._layer.setIcon(e),this._layer.once("add",this._createTextMarker,this)},_createTextMarker(e=!1){this._layer.off("add",this._createTextMarker,this),this._layer.getElement().tabIndex=-1,this.textArea.wrap="off",this.textArea.style.overflow="hidden",this.textArea.style.height=L.DomUtil.getStyle(this.textArea,"font-size"),this.textArea.style.width="1px",this._layer.options.text&&this.setText(this._layer.options.text),this._autoResize(),e===!0&&(this.enable(),this.focus(),this._disableOnBlur())},_preventTextSelection(e){e.preventDefault()}});var lo=function(e,i,r,a,s,l){this._matrix=[e,i,r,a,s,l]};lo.init=()=>new L.PM.Matrix(1,0,0,1,0,0),lo.prototype={transform(e){return this._transform(e.clone())},_transform(e){let i=this._matrix,{x:r,y:a}=e;return e.x=i[0]*r+i[1]*a+i[4],e.y=i[2]*r+i[3]*a+i[5],e},untransform(e){let i=this._matrix;return new L.Point((e.x/i[0]-i[4])/i[0],(e.y/i[2]-i[5])/i[2])},clone(){let e=this._matrix;return new L.PM.Matrix(e[0],e[1],e[2],e[3],e[4],e[5])},translate(e){if(e===void 0)return new L.Point(this._matrix[4],this._matrix[5]);let i,r;return typeof e=="number"?(i=e,r=e):(i=e.x,r=e.y),this._add(1,0,0,1,i,r)},scale(e,i){if(e===void 0)return new L.Point(this._matrix[0],this._matrix[3]);let r,a;return i=i||L.point(0,0),typeof e=="number"?(r=e,a=e):(r=e.x,a=e.y),this._add(r,0,0,a,i.x,i.y)._add(1,0,0,1,-i.x,-i.y)},rotate(e,i){let r=Math.cos(e),a=Math.sin(e);return i=i||new L.Point(0,0),this._add(r,a,-a,r,i.x,i.y)._add(1,0,0,1,-i.x,-i.y)},flip(){return this._matrix[1]*=-1,this._matrix[2]*=-1,this},_add(e,i,r,a,s,l){let h=[[],[],[]],d=this._matrix,f=[[d[0],d[2],d[4]],[d[1],d[3],d[5]],[0,0,1]],m=[[e,r,s],[i,a,l],[0,0,1]],E;e&&e instanceof L.PM.Matrix&&(d=e._matrix,m=[[d[0],d[2],d[4]],[d[1],d[3],d[5]],[0,0,1]]);for(let P=0;P<3;P+=1)for(let j=0;j<3;j+=1){E=0;for(let R=0;R<3;R+=1)E+=f[P][R]*m[R][j];h[P][j]=E}return this._matrix=[h[0][0],h[1][0],h[0][1],h[1][1],h[0][2],h[1][2]],this}};var Cs=lo,ws={calcMiddleLatLng(e,i,r){let a=e.project(i),s=e.project(r);return e.unproject(a._add(s)._divideBy(2))},findLayers(e){let i=[];return e.eachLayer(r=>{(r instanceof L.Polyline||r instanceof L.Marker||r instanceof L.Circle||r instanceof L.CircleMarker||r instanceof L.ImageOverlay)&&i.push(r)}),i=i.filter(r=>!!r.pm),i=i.filter(r=>!r._pmTempLayer),i=i.filter(r=>!L.PM.optIn&&!r.options.pmIgnore||L.PM.optIn&&r.options.pmIgnore===!1),i},circleToPolygon(e,i=60,r=!0){let a=e.getLatLng(),s=e.getRadius(),l=On(a,s,i,0,r),h=[];for(let d=0;d<l.length;d+=1){let f=[l[d].lat,l[d].lng];h.push(f)}return L.polygon(h,e.options)},disablePopup(e){e.getPopup()&&(e._tempPopupCopy=e.getPopup(),e.unbindPopup())},enablePopup(e){e._tempPopupCopy&&(e.bindPopup(e._tempPopupCopy),delete e._tempPopupCopy)},_fireEvent(e,i,r,a=!1){e.fire(i,r,a);let{groups:s}=this.getAllParentGroups(e);s.forEach(l=>{l.fire(i,r,a)})},getAllParentGroups(e){let i=[],r=[],a=s=>{for(let l in s._eventParents)if(i.indexOf(l)===-1){i.push(l);let h=s._eventParents[l];r.push(h),a(h)}};return!e._pmLastGroupFetch||!e._pmLastGroupFetch.time||new Date().getTime()-e._pmLastGroupFetch.time>1e3?(a(e),e._pmLastGroupFetch={time:new Date().getTime(),groups:r,groupIds:i},{groupIds:i,groups:r}):{groups:e._pmLastGroupFetch.groups,groupIds:e._pmLastGroupFetch.groupIds}},createGeodesicPolygon:On,getTranslation:yt,findDeepCoordIndex(e,i,r=!0){let a,s=h=>(d,f)=>{let m=h.concat(f);if(r){if(d.lat&&d.lat===i.lat&&d.lng===i.lng)return a=m,!0}else if(d.lat&&L.latLng(d).equals(i))return a=m,!0;return Array.isArray(d)&&d.some(s(m))};e.some(s([]));let l={};return a&&(l={indexPath:a,index:a[a.length-1],parentPath:a.slice(0,a.length-1)}),l},findDeepMarkerIndex(e,i){let r,a=l=>(h,d)=>{let f=l.concat(d);return h._leaflet_id===i._leaflet_id?(r=f,!0):Array.isArray(h)&&h.some(a(f))};e.some(a([]));let s={};return r&&(s={indexPath:r,index:r[r.length-1],parentPath:r.slice(0,r.length-1)}),s},_getIndexFromSegment(e,i){if(i&&i.length===2){let r=this.findDeepCoordIndex(e,i[0]),a=this.findDeepCoordIndex(e,i[1]),s=Math.max(r.index,a.index);return(r.index===0||a.index===0)&&s!==1&&(s+=1),{indexA:r,indexB:a,newIndex:s,indexPath:r.indexPath,parentPath:r.parentPath}}return null},_getRotatedRectangle(e,i,r,a){let s=nn(a,e),l=nn(a,i),h=r*Math.PI/180,d=Math.cos(h),f=Math.sin(h),m=(l.x-s.x)*d+(l.y-s.y)*f,E=(l.y-s.y)*d-(l.x-s.x)*f,P=m*d+s.x,j=m*f+s.y,R=-E*f+s.x,W=E*d+s.y,X=ca(a,s),nt=ca(a,{x:P,y:j}),vt=ca(a,l),k=ca(a,{x:R,y:W});return[X,nt,vt,k]},pxRadiusToMeterRadius(e,i,r){let a=i.project(r),s=L.point(a.x+e,a.y);return i.distance(i.unproject(s),r)}},ks=ws;L.PM=L.PM||{version:At.version,Map:jr,Toolbar:Vr,Draw:Ut,Edit:qt,Utils:ks,Matrix:Cs,activeLang:"en",optIn:!1,initialize(e){this.addInitHooks(e)},setOptIn(e){this.optIn=!!e},addInitHooks(){function e(){this.pm=void 0,L.PM.optIn?this.options.pmIgnore===!1&&(this.pm=new L.PM.Map(this)):this.options.pmIgnore||(this.pm=new L.PM.Map(this)),this.pm&&this.pm.setGlobalOptions({})}L.Map.addInitHook(e);function i(){this.pm=void 0,L.PM.optIn?this.options.pmIgnore===!1&&(this.pm=new L.PM.Edit.LayerGroup(this)):this.options.pmIgnore||(this.pm=new L.PM.Edit.LayerGroup(this))}L.LayerGroup.addInitHook(i);function r(){this.pm=void 0,L.PM.optIn?this.options.pmIgnore===!1&&(this.options.textMarker?(this.pm=new L.PM.Edit.Text(this),this.options._textMarkerOverPM||this.pm._initTextMarker(),delete this.options._textMarkerOverPM):this.pm=new L.PM.Edit.Marker(this)):this.options.pmIgnore||(this.options.textMarker?(this.pm=new L.PM.Edit.Text(this),this.options._textMarkerOverPM||this.pm._initTextMarker(),delete this.options._textMarkerOverPM):this.pm=new L.PM.Edit.Marker(this))}L.Marker.addInitHook(r);function a(){this.pm=void 0,L.PM.optIn?this.options.pmIgnore===!1&&(this.pm=new L.PM.Edit.CircleMarker(this)):this.options.pmIgnore||(this.pm=new L.PM.Edit.CircleMarker(this))}L.CircleMarker.addInitHook(a);function s(){this.pm=void 0,L.PM.optIn?this.options.pmIgnore===!1&&(this.pm=new L.PM.Edit.Line(this)):this.options.pmIgnore||(this.pm=new L.PM.Edit.Line(this))}L.Polyline.addInitHook(s);function l(){this.pm=void 0,L.PM.optIn?this.options.pmIgnore===!1&&(this.pm=new L.PM.Edit.Polygon(this)):this.options.pmIgnore||(this.pm=new L.PM.Edit.Polygon(this))}L.Polygon.addInitHook(l);function h(){this.pm=void 0,L.PM.optIn?this.options.pmIgnore===!1&&(this.pm=new L.PM.Edit.Rectangle(this)):this.options.pmIgnore||(this.pm=new L.PM.Edit.Rectangle(this))}L.Rectangle.addInitHook(h);function d(){this.pm=void 0,L.PM.optIn?this.options.pmIgnore===!1&&(this.pm=new L.PM.Edit.Circle(this)):this.options.pmIgnore||(this.pm=new L.PM.Edit.Circle(this))}L.Circle.addInitHook(d);function f(){this.pm=void 0,L.PM.optIn?this.options.pmIgnore===!1&&(this.pm=new L.PM.Edit.ImageOverlay(this)):this.options.pmIgnore||(this.pm=new L.PM.Edit.ImageOverlay(this))}L.ImageOverlay.addInitHook(f)},reInitLayer(e){e instanceof L.LayerGroup&&e.eachLayer(i=>{this.reInitLayer(i)}),e.pm||L.PM.optIn&&e.options.pmIgnore!==!1||e.options.pmIgnore||(e instanceof L.Map?e.pm=new L.PM.Map(e):e instanceof L.Marker?e.options.textMarker?(e.pm=new L.PM.Edit.Text(e),e.pm._initTextMarker(),e.pm._createTextMarker(!1)):e.pm=new L.PM.Edit.Marker(e):e instanceof L.Circle?e.pm=new L.PM.Edit.Circle(e):e instanceof L.CircleMarker?e.pm=new L.PM.Edit.CircleMarker(e):e instanceof L.Rectangle?e.pm=new L.PM.Edit.Rectangle(e):e instanceof L.Polygon?e.pm=new L.PM.Edit.Polygon(e):e instanceof L.Polyline?e.pm=new L.PM.Edit.Line(e):e instanceof L.LayerGroup?e.pm=new L.PM.Edit.LayerGroup(e):e instanceof L.ImageOverlay&&(e.pm=new L.PM.Edit.ImageOverlay(e)))}},L.version==="1.7.1"&&L.Canvas.include({_onClick(e){let i=this._map.mouseEventToLayerPoint(e),r,a;for(let s=this._drawFirst;s;s=s.next)r=s.layer,r.options.interactive&&r._containsPoint(i)&&(!(e.type==="click"||e.type==="preclick")||!this._map._draggableMoved(r))&&(a=r);a&&(L.DomEvent.fakeStop(e),this._fireEvent([a],e))}}),L.PM.initialize()})();function Fo(_,v){if(_.match(/^[a-z]+:\/\//i))return _;if(_.match(/^\/\//))return window.location.protocol+_;if(_.match(/^[a-z]+:/i))return _;let z=document.implementation.createHTMLDocument(),A=z.createElement("base"),V=z.createElement("a");return z.head.appendChild(A),z.body.appendChild(V),v&&(A.href=v),V.href=_,V.href}var Oo=(()=>{let _=0,v=()=>`0000${(Math.random()*36**4<<0).toString(36)}`.slice(-4);return()=>(_+=1,`u${v()}${_}`)})();function De(_){let v=[];for(let z=0,A=_.length;z<A;z++)v.push(_[z]);return v}var rn=null;function ma(_={}){return rn||(_.includeStyleProperties?(rn=_.includeStyleProperties,rn):(rn=De(window.getComputedStyle(document.documentElement)),rn))}function _a(_,v){let A=(_.ownerDocument.defaultView||window).getComputedStyle(_).getPropertyValue(v);return A?parseFloat(A.replace("px","")):0}function Os(_){let v=_a(_,"border-left-width"),z=_a(_,"border-right-width");return _.clientWidth+v+z}function Rs(_){let v=_a(_,"border-top-width"),z=_a(_,"border-bottom-width");return _.clientHeight+v+z}function _o(_,v={}){let z=v.width||Os(_),A=v.height||Rs(_);return{width:z,height:A}}function Ro(){let _,v;try{v=process}catch{}let z=v&&v.env?v.env.devicePixelRatio:null;return z&&(_=parseInt(z,10),Number.isNaN(_)&&(_=1)),_||window.devicePixelRatio||1}var ye=16384;function Io(_){(_.width>ye||_.height>ye)&&(_.width>ye&&_.height>ye?_.width>_.height?(_.height*=ye/_.width,_.width=ye):(_.width*=ye/_.height,_.height=ye):_.width>ye?(_.height*=ye/_.width,_.width=ye):(_.width*=ye/_.height,_.height=ye))}function zo(_,v={}){return _.toBlob?new Promise(z=>{_.toBlob(z,v.type?v.type:"image/png",v.quality?v.quality:1)}):new Promise(z=>{let A=window.atob(_.toDataURL(v.type?v.type:void 0,v.quality?v.quality:void 0).split(",")[1]),V=A.length,q=new Uint8Array(V);for(let M=0;M<V;M+=1)q[M]=A.charCodeAt(M);z(new Blob([q],{type:v.type?v.type:"image/png"}))})}function an(_){return new Promise((v,z)=>{let A=new Image;A.onload=()=>{A.decode().then(()=>{requestAnimationFrame(()=>v(A))})},A.onerror=z,A.crossOrigin="anonymous",A.decoding="async",A.src=_})}async function Is(_){return Promise.resolve().then(()=>new XMLSerializer().serializeToString(_)).then(encodeURIComponent).then(v=>`data:image/svg+xml;charset=utf-8,${v}`)}async function No(_,v,z){let A="http://www.w3.org/2000/svg",V=document.createElementNS(A,"svg"),q=document.createElementNS(A,"foreignObject");return V.setAttribute("width",`${v}`),V.setAttribute("height",`${z}`),V.setAttribute("viewBox",`0 0 ${v} ${z}`),q.setAttribute("width","100%"),q.setAttribute("height","100%"),q.setAttribute("x","0"),q.setAttribute("y","0"),q.setAttribute("externalResourcesRequired","true"),V.appendChild(q),q.appendChild(_),Is(V)}var te=(_,v)=>{if(_ instanceof v)return!0;let z=Object.getPrototypeOf(_);return z===null?!1:z.constructor.name===v.name||te(z,v)};function zs(_){let v=_.getPropertyValue("content");return`${_.cssText} content: '${v.replace(/'|"/g,"")}';`}function Ns(_,v){return ma(v).map(z=>{let A=_.getPropertyValue(z),V=_.getPropertyPriority(z);return`${z}: ${A}${V?" !important":""};`}).join(" ")}function Gs(_,v,z,A){let V=`.${_}:${v}`,q=z.cssText?zs(z):Ns(z,A);return document.createTextNode(`${V}{${q}}`)}function Go(_,v,z,A){let V=window.getComputedStyle(_,z),q=V.getPropertyValue("content");if(q===""||q==="none")return;let M=Oo();try{v.className=`${v.className} ${M}`}catch{return}let tt=document.createElement("style");tt.appendChild(Gs(M,z,V,A)),v.appendChild(tt)}function Zo(_,v,z){Go(_,v,":before",z),Go(_,v,":after",z)}var jo="application/font-woff",Uo="image/jpeg",Zs={woff:jo,woff2:jo,ttf:"application/font-truetype",eot:"application/vnd.ms-fontobject",png:"image/png",jpg:Uo,jpeg:Uo,gif:"image/gif",tiff:"image/tiff",svg:"image/svg+xml",webp:"image/webp"};function js(_){let v=/\.([^./]*?)$/g.exec(_);return v?v[1]:""}function on(_){let v=js(_).toLowerCase();return Zs[v]||""}function Us(_){return _.split(/,/)[1]}function rr(_){return _.search(/^(data:)/)!==-1}function go(_,v){return`data:${v};base64,${_}`}async function yo(_,v,z){let A=await fetch(_,v);if(A.status===404)throw new Error(`Resource "${A.url}" not found`);let V=await A.blob();return new Promise((q,M)=>{let tt=new FileReader;tt.onerror=M,tt.onloadend=()=>{try{q(z({res:A,result:tt.result}))}catch($){M($)}},tt.readAsDataURL(V)})}var mo={};function Vs(_,v,z){let A=_.replace(/\?.*/,"");return z&&(A=_),/ttf|otf|eot|woff2?/i.test(A)&&(A=A.replace(/.*\//,"")),v?`[${v}]${A}`:A}async function sn(_,v,z){let A=Vs(_,v,z.includeQueryParams);if(mo[A]!=null)return mo[A];z.cacheBust&&(_+=(/\?/.test(_)?"&":"?")+new Date().getTime());let V;try{let q=await yo(_,z.fetchRequestInit,({res:M,result:tt})=>(v||(v=M.headers.get("Content-Type")||""),Us(tt)));V=go(q,v)}catch(q){V=z.imagePlaceholder||"";let M=`Failed to fetch resource: ${_}`;q&&(M=typeof q=="string"?q:q.message),M&&console.warn(M)}return mo[A]=V,V}async function Hs(_){let v=_.toDataURL();return v==="data:,"?_.cloneNode(!1):an(v)}async function Ks(_,v){if(_.currentSrc){let q=document.createElement("canvas"),M=q.getContext("2d");q.width=_.clientWidth,q.height=_.clientHeight,M?.drawImage(_,0,0,q.width,q.height);let tt=q.toDataURL();return an(tt)}let z=_.poster,A=on(z),V=await sn(z,A,v);return an(V)}async function qs(_,v){var z;try{if(!((z=_?.contentDocument)===null||z===void 0)&&z.body)return await ar(_.contentDocument.body,v,!0)}catch{}return _.cloneNode(!1)}async function Ws(_,v){return te(_,HTMLCanvasElement)?Hs(_):te(_,HTMLVideoElement)?Ks(_,v):te(_,HTMLIFrameElement)?qs(_,v):_.cloneNode(Vo(_))}var Js=_=>_.tagName!=null&&_.tagName.toUpperCase()==="SLOT",Vo=_=>_.tagName!=null&&_.tagName.toUpperCase()==="SVG";async function Ys(_,v,z){var A,V;if(Vo(v))return v;let q=[];return Js(_)&&_.assignedNodes?q=De(_.assignedNodes()):te(_,HTMLIFrameElement)&&(!((A=_.contentDocument)===null||A===void 0)&&A.body)?q=De(_.contentDocument.body.childNodes):q=De(((V=_.shadowRoot)!==null&&V!==void 0?V:_).childNodes),q.length===0||te(_,HTMLVideoElement)||await q.reduce((M,tt)=>M.then(()=>ar(tt,z)).then($=>{$&&v.appendChild($)}),Promise.resolve()),v}function $s(_,v,z){let A=v.style;if(!A)return;let V=window.getComputedStyle(_);V.cssText?(A.cssText=V.cssText,A.transformOrigin=V.transformOrigin):ma(z).forEach(q=>{let M=V.getPropertyValue(q);q==="font-size"&&M.endsWith("px")&&(M=`${Math.floor(parseFloat(M.substring(0,M.length-2)))-.1}px`),te(_,HTMLIFrameElement)&&q==="display"&&M==="inline"&&(M="block"),q==="d"&&v.getAttribute("d")&&(M=`path(${v.getAttribute("d")})`),A.setProperty(q,M,V.getPropertyPriority(q))})}function Xs(_,v){te(_,HTMLTextAreaElement)&&(v.innerHTML=_.value),te(_,HTMLInputElement)&&v.setAttribute("value",_.value)}function Qs(_,v){if(te(_,HTMLSelectElement)){let z=v,A=Array.from(z.children).find(V=>_.value===V.getAttribute("value"));A&&A.setAttribute("selected","")}}function tu(_,v,z){return te(v,Element)&&($s(_,v,z),Zo(_,v,z),Xs(_,v),Qs(_,v)),v}async function eu(_,v){let z=_.querySelectorAll?_.querySelectorAll("use"):[];if(z.length===0)return _;let A={};for(let q=0;q<z.length;q++){let tt=z[q].getAttribute("xlink:href");if(tt){let $=_.querySelector(tt),et=document.querySelector(tt);!$&&et&&!A[tt]&&(A[tt]=await ar(et,v,!0))}}let V=Object.values(A);if(V.length){let q="http://www.w3.org/1999/xhtml",M=document.createElementNS(q,"svg");M.setAttribute("xmlns",q),M.style.position="absolute",M.style.width="0",M.style.height="0",M.style.overflow="hidden",M.style.display="none";let tt=document.createElementNS(q,"defs");M.appendChild(tt);for(let $=0;$<V.length;$++)tt.appendChild(V[$]);_.appendChild(M)}return _}async function ar(_,v,z){return!z&&v.filter&&!v.filter(_)?null:Promise.resolve(_).then(A=>Ws(A,v)).then(A=>Ys(_,A,v)).then(A=>tu(_,A,v)).then(A=>eu(A,v))}var Ho=/url\((['"]?)([^'"]+?)\1\)/g,iu=/url\([^)]+\)\s*format\((["']?)([^"']+)\1\)/g,nu=/src:\s*(?:url\([^)]+\)\s*format\([^)]+\)[,;]\s*)+/g;function ru(_){let v=_.replace(/([.*+?^${}()|\[\]\/\\])/g,"\\$1");return new RegExp(`(url\\(['"]?)(${v})(['"]?\\))`,"g")}function au(_){let v=[];return _.replace(Ho,(z,A,V)=>(v.push(V),z)),v.filter(z=>!rr(z))}async function ou(_,v,z,A,V){try{let q=z?Fo(v,z):v,M=on(v),tt;if(V){let $=await V(q);tt=go($,M)}else tt=await sn(q,M,A);return _.replace(ru(v),`$1${tt}$3`)}catch{}return _}function su(_,{preferredFontFormat:v}){return v?_.replace(nu,z=>{for(;;){let[A,,V]=iu.exec(z)||[];if(!V)return"";if(V===v)return`src: ${A};`}}):_}function vo(_){return _.search(Ho)!==-1}async function ga(_,v,z){if(!vo(_))return _;let A=su(_,z);return au(A).reduce((q,M)=>q.then(tt=>ou(tt,M,v,z)),Promise.resolve(A))}async function un(_,v,z){var A;let V=(A=v.style)===null||A===void 0?void 0:A.getPropertyValue(_);if(V){let q=await ga(V,null,z);return v.style.setProperty(_,q,v.style.getPropertyPriority(_)),!0}return!1}async function uu(_,v){await un("background",_,v)||await un("background-image",_,v),await un("mask",_,v)||await un("-webkit-mask",_,v)||await un("mask-image",_,v)||await un("-webkit-mask-image",_,v)}async function lu(_,v){let z=te(_,HTMLImageElement);if(!(z&&!rr(_.src))&&!(te(_,SVGImageElement)&&!rr(_.href.baseVal)))return;let A=z?_.src:_.href.baseVal,V=await sn(A,on(A),v);await new Promise((q,M)=>{_.onload=q,_.onerror=v.onImageErrorHandler?(...$)=>{try{q(v.onImageErrorHandler(...$))}catch(et){M(et)}}:M;let tt=_;tt.decode&&(tt.decode=q),tt.loading==="lazy"&&(tt.loading="eager"),z?(_.srcset="",_.src=V):_.href.baseVal=V})}async function hu(_,v){let A=De(_.childNodes).map(V=>Lo(V,v));await Promise.all(A).then(()=>_)}async function Lo(_,v){te(_,Element)&&(await uu(_,v),await lu(_,v),await hu(_,v))}function Ko(_,v){let{style:z}=_;v.backgroundColor&&(z.backgroundColor=v.backgroundColor),v.width&&(z.width=`${v.width}px`),v.height&&(z.height=`${v.height}px`);let A=v.style;return A!=null&&Object.keys(A).forEach(V=>{z[V]=A[V]}),_}var qo={};async function Wo(_){let v=qo[_];if(v!=null)return v;let A=await(await fetch(_)).text();return v={url:_,cssText:A},qo[_]=v,v}async function Jo(_,v){let z=_.cssText,A=/url\(["']?([^"')]+)["']?\)/g,q=(z.match(/url\([^)]+\)/g)||[]).map(async M=>{let tt=M.replace(A,"$1");return tt.startsWith("https://")||(tt=new URL(tt,_.url).href),yo(tt,v.fetchRequestInit,({result:$})=>(z=z.replace(M,`url(${$})`),[M,$]))});return Promise.all(q).then(()=>z)}function Yo(_){if(_==null)return[];let v=[],z=/(\/\*[\s\S]*?\*\/)/gi,A=_.replace(z,""),V=new RegExp("((@.*?keyframes [\\s\\S]*?){([\\s\\S]*?}\\s*?)})","gi");for(;;){let $=V.exec(A);if($===null)break;v.push($[0])}A=A.replace(V,"");let q=/@import[\s\S]*?url\([^)]*\)[\s\S]*?;/gi,M="((\\s*?(?:\\/\\*[\\s\\S]*?\\*\\/)?\\s*?@media[\\s\\S]*?){([\\s\\S]*?)}\\s*?})|(([\\s\\S]*?){([\\s\\S]*?)})",tt=new RegExp(M,"gi");for(;;){let $=q.exec(A);if($===null){if($=tt.exec(A),$===null)break;q.lastIndex=tt.lastIndex}else tt.lastIndex=q.lastIndex;v.push($[0])}return v}async function cu(_,v){let z=[],A=[];return _.forEach(V=>{if("cssRules"in V)try{De(V.cssRules||[]).forEach((q,M)=>{if(q.type===CSSRule.IMPORT_RULE){let tt=M+1,$=q.href,et=Wo($).then(lt=>Jo(lt,v)).then(lt=>Yo(lt).forEach(St=>{try{V.insertRule(St,St.startsWith("@import")?tt+=1:V.cssRules.length)}catch(ee){console.error("Error inserting rule from remote css",{rule:St,error:ee})}})).catch(lt=>{console.error("Error loading remote css",lt.toString())});A.push(et)}})}catch(q){let M=_.find(tt=>tt.href==null)||document.styleSheets[0];V.href!=null&&A.push(Wo(V.href).then(tt=>Jo(tt,v)).then(tt=>Yo(tt).forEach($=>{M.insertRule($,M.cssRules.length)})).catch(tt=>{console.error("Error loading remote stylesheet",tt)})),console.error("Error inlining remote css file",q)}}),Promise.all(A).then(()=>(_.forEach(V=>{if("cssRules"in V)try{De(V.cssRules||[]).forEach(q=>{z.push(q)})}catch(q){console.error(`Error while reading CSS rules from ${V.href}`,q)}}),z))}function du(_){return _.filter(v=>v.type===CSSRule.FONT_FACE_RULE).filter(v=>vo(v.style.getPropertyValue("src")))}async function pu(_,v){if(_.ownerDocument==null)throw new Error("Provided element is not within a Document");let z=De(_.ownerDocument.styleSheets),A=await cu(z,v);return du(A)}function $o(_){return _.trim().replace(/["']/g,"")}function fu(_){let v=new Set;function z(A){(A.style.fontFamily||getComputedStyle(A).fontFamily).split(",").forEach(q=>{v.add($o(q))}),Array.from(A.children).forEach(q=>{q instanceof HTMLElement&&z(q)})}return z(_),v}async function Xo(_,v){let z=await pu(_,v),A=fu(_);return(await Promise.all(z.filter(q=>A.has($o(q.style.fontFamily))).map(q=>{let M=q.parentStyleSheet?q.parentStyleSheet.href:null;return ga(q.cssText,M,v)}))).join(`
`)}async function Qo(_,v){let z=v.fontEmbedCSS!=null?v.fontEmbedCSS:v.skipFonts?null:await Xo(_,v);if(z){let A=document.createElement("style"),V=document.createTextNode(z);A.appendChild(V),_.firstChild?_.insertBefore(A,_.firstChild):_.appendChild(A)}}async function _u(_,v={}){let{width:z,height:A}=_o(_,v),V=await ar(_,v,!0);return await Qo(V,v),await Lo(V,v),Ko(V,v),await No(V,z,A)}async function mu(_,v={}){let{width:z,height:A}=_o(_,v),V=await _u(_,v),q=await an(V),M=document.createElement("canvas"),tt=M.getContext("2d"),$=v.pixelRatio||Ro(),et=v.canvasWidth||z,lt=v.canvasHeight||A;return M.width=et*$,M.height=lt*$,v.skipAutoScale||Io(M),M.style.width=`${et}`,M.style.height=`${lt}`,v.backgroundColor&&(tt.fillStyle=v.backgroundColor,tt.fillRect(0,0,M.width,M.height)),tt.drawImage(q,0,0,M.width,M.height),M}async function ts(_,v={}){let z=await mu(_,v);return await zo(z)}document.addEventListener("DOMContentLoaded",()=>{window.mapPicker=(_,v,z)=>({map:null,marker:null,rangeCircle:null,drawItems:null,rangeSelectField:null,markerShouldMoveWithMap:!1,baseLayers:{},overlayLayers:{},layersControl:null,isVariableValid:function(A){return typeof A>"u"||A===null?!1:typeof A=="string"?A.trim()!=="":Array.isArray(A)?A.length>0:typeof A=="object"?Object.keys(A).length>0:typeof A=="number"?!isNaN(A):typeof A=="boolean"},createMap:function(A){let V=this,q=document.getElementById("geomanbox"),M=v.controls.zoom||v.zoom()||13;if(this.map=Lt.map(A,v.controls),v.bounds){let $=Lt.latLng(v.bounds.sw.lat,v.bounds.sw.lng),et=Lt.latLng(v.bounds.ne.lat,v.bounds.ne.lng),lt=Lt.latLngBounds($,et);this.map.setMaxBounds(lt),this.map.fitBounds(lt),this.map.on("drag",function(){map.panInsideBounds(lt,{animate:!1})})}if(this.map.on("load",()=>{setTimeout(()=>this.map.invalidateSize(!0),0),v.showMarker&&this.marker.setLatLng(this.map.getCenter())}),v.draggable||this.map.dragging.disable(),v.clickable&&this.map.on("click",$=>{this.setCoordinates($.latlng)}),v.baseLayers&&Array.isArray(v.baseLayers)&&v.baseLayers.length>0){v.baseLayers.forEach(et=>{if(!et.url){console.error("Base layer configuration is missing 'url':",et),alert("Error: Base layer configuration is missing 'url'. See console for details.");return}et.name||console.warn("Base layer configuration is missing 'name':",et),this.baseLayers[et.name]=Lt.tileLayer(et.url,{attribution:et.attribution,minZoom:et.minZoom||1,maxZoom:et.maxZoom||28,tileSize:v.tileSize||256,zoomOffset:v.zoomOffset,detectRetina:v.detectRetina,opacity:et.opacity||1,crossOrigin:!0})});let $=v.defaultBaseLayer||v.baseLayers[0]?.name;this.baseLayers[$]?this.baseLayers[$].addTo(this.map):(console.warn(`Default base layer "${$}" not found.`),Object.values(this.baseLayers)[0]?.addTo(this.map))}else this.baseLayers.OpenStreetMap=Lt.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:""}).addTo(this.map);this.layersControl=Lt.control.layers(this.baseLayers,this.overlayLayers,{position:"bottomleft"}).addTo(this.map),v.overlayLayers&&Array.isArray(v.overlayLayers)&&v.overlayLayers.length>0&&v.overlayLayers.forEach($=>{if(!$.url&&$.type!=="api"){console.error("Overlay layer configuration is missing 'url':",$),alert("Error: Overlay layer configuration is missing 'url'. See console for details.");return}$.name||console.warn("Overlay layer configuration is missing 'name':",$);let et;if($.type==="api"){let lt=new Lt.FeatureGroup,St=$.data;console.log(St),!St||!Array.isArray(St.features)?console.warn("No valid features found in API data:",St):St.features.forEach((ee,kt)=>{ee.coordinates?Lt.geoJSON(ee.coordinates,{style:{color:"Red",fillColor:"Black",fillOpacity:0}}).addTo(lt):console.warn(`API feature at index ${kt} has no valid "coordinates" property.`,ee)}),this.overlayLayers[$.name]=lt,this.layersControl.addOverlay(lt,$.name),$.visibleByDefault&&lt.addTo(this.map)}else $.type==="geojson"?fetch($.url).then(lt=>{console.log("geojson",lt),et=Lt.geoJSON(lt,{style:$.style||{},onEachFeature:$.onEachFeature}),this.overlayLayers[$.name]=et,this.layersControl.addOverlay(et,$.name),$.visibleByDefault&&et.addTo(this.map)}).catch(lt=>{console.error("Error loading GeoJSON layer:",lt),alert("Error loading GeoJSON layer: "+lt.message)}):(et=Lt.tileLayer($.url,{attribution:$.attribution,minZoom:$.minZoom||1,maxZoom:$.maxZoom||28,tileSize:v.tileSize||256,zoomOffset:v.zoomOffset,detectRetina:v.detectRetina,opacity:$.opacity||1,crossOrigin:!0}),this.overlayLayers[$.name]=et,this.layersControl.addOverlay(et,$.name),$.visibleByDefault&&et.addTo(this.map))});let tt=z??this.getCoordinates();if(v.showMarker){let $=v.markerColor||"#3b82f6",et=Lt.divIcon({html:this.defaultIconMarker($),className:"",iconSize:[v.iconSize,v.iconSize]}),lt=[tt.lat||0,tt.lng||0];this.marker=Lt.marker(lt,{icon:et,draggable:!1,autoPan:!0}).addTo(this.map),this.setMarkerRange(),this.moveMarkerToCenter=()=>{this.markerShouldMoveWithMap&&this.marker.setLatLng(this.map.getCenter())},this.map.on("move",this.moveMarkerToCenter),this.markerShouldMoveWithMap=!tt.lat&&!tt.lng&&!v.clickable}if(this.map.on("moveend",()=>setTimeout(()=>this.updateLocation(),500)),this.map.on("locationfound",function(){V.map.setZoom(v.control.zoom)}),!tt.lat&&!tt.lng?this.map.locate({setView:!0,maxZoom:v.controls.maxZoom,enableHighAccuracy:!0,watch:!1}):this.map.setView(new Lt.LatLng(tt.lat,tt.lng),M),v.showMyLocationButton&&this.addLocationButton(),v.liveLocation.send&&v.liveLocation.realtime&&setInterval(()=>{this.fetchCurrentLocation()},v.liveLocation.milliseconds),v.geoManToolbar.show){this.map.pm.addControls(v.geoManToolbar);let $=new Lt.FeatureGroup().addTo(this.map);if(q)if(console.info("geomanbox field exists in DOM"),this.isVariableValid(q.value)){let et=JSON.parse(q.value);$=Lt.geoJSON(et,{style:{color:v.liveLocation?.color||"#FFFFFF",fillColor:"blue",fillOpacity:.5}}).addTo(this.map);let lt=$.getBounds();if(lt.isValid())this.map.fitBounds(lt,{maxZoom:M,padding:[20,20]});else{let St;et.type==="FeatureCollection"&&et.features.length>0?St=et.features[0].geometry.coordinates:et.type==="Feature"?St=et.geometry.coordinates:et.type==="Point"?St=et.coordinates:(console.error("Unsupported GeoJSON type or empty features"),St=[v.default.lng,v.default.lat]),this.map.setView([St[1],St[0]],M)}}else console.info("No Data to Edit"),this.map.setView([v.default.lat,v.default.lng],M);else console.warn("geomanbox field is not available");this.map.on("pm:create",function(et){if(et.layer&&et.layer.pm){let lt=et;lt.layer.pm.enable(),$.addLayer(lt.layer),q?(q.value=JSON.stringify($.toGeoJSON()),_.set(v.statePath,{geojson:q.value},!1),_.$refresh(),lt.layer.on("pm:edit",St=>{q.value=JSON.stringify($.toGeoJSON()),_.set(v.statePath,{geojson:q.value},!1),_.$refresh()})):(alert("This is just an alert to let you know the field 'geomanbox' was not found in the form to store geojson data"),console.warn("Field 'geomanbox' was not found in the structure"))}else console.log("Not a shape")}),$.on("pm:edit",et=>{q.value=JSON.stringify($.toGeoJSON()),_.set(v.statePath,{geojson:q.value},!1),_.$refresh()}),this.map.on("pm:remove",et=>{$.removeLayer(et.layer),q.value=JSON.stringify($.toGeoJSON()),_.set(v.statePath,{geojson:q.value},!1),_.$refresh()})}},updateLocation:function(){let A=this.getCoordinates(),V=this.map.getCenter();A.icon&&this.fetchInitialCategoryIcon(A.icon),this.markerShouldMoveWithMap&&(A.lng!==V.lng||A.lat!==V.lat)&&(_.set(v.statePath,V,!1),v.liveLocation.send&&_.$refresh())},removeMap:function(A){this.marker&&(this.marker.remove(),this.marker=null),this.tile.remove(),this.tile=null,this.map.off(),this.map.remove(),this.map=null},getCoordinates:function(){let A=_.get(v.statePath)??{};return A.hasOwnProperty("lat")&&A.hasOwnProperty("lng")&&A.lat!==null&&A.lng!==null||(A={lat:v.default.lat,lng:v.default.lng}),A},setCoordinates:function(A){return _.set(v.statePath,{..._.get(v.statePath),lat:A.lat,lng:A.lng},!1),v.liveLocation.send&&_.$refresh(),this.marker.setLatLng(A),this.markerShouldMoveWithMap=!1,this.updateMarker(),A},attach:function(A){this.createMap(A),new IntersectionObserver(q=>{q.forEach(M=>{M.intersectionRatio>0?this.map||this.createMap(A):this.removeMap(A)})},{root:null,rootMargin:"0px",threshold:1}).observe(A)},fetchCurrentLocation:function(){"geolocation"in navigator?navigator.geolocation.getCurrentPosition(async A=>{let V=new Lt.LatLng(A.coords.latitude,A.coords.longitude);await this.map.flyTo(V),this.updateLocation(),this.updateMarker()},A=>{console.error("Error fetching current location:",A)}):alert("Geolocation is not supported by this browser.")},addLocationButton:function(){let A=document.createElement("button");A.innerHTML=this.defaultIconMarker("currentColor"),A.type="button",A.classList.add("map-location-button"),A.onclick=()=>this.fetchCurrentLocation(),this.map.getContainer().appendChild(A)},setMarkerRange:function(){if(!this.rangeSelectField)return;let A=parseInt(this.rangeSelectField.value||0);this.rangeCircle?this.rangeCircle.setLatLng(this.getCoordinates()).setRadius(A):this.rangeCircle=Lt.circle(this.getCoordinates(),{color:"blue",fillColor:"#f03",fillOpacity:.5,radius:A}).addTo(this.map)},defaultIconMarker:function(A){return`<svg xmlns="http://www.w3.org/2000/svg" class="map-icon" fill="${A}" width="32" height="32" viewBox="0 0 24 24"><path d="M12 0c-4.198 0-8 3.403-8 7.602 0 4.198 3.469 9.21 8 16.398 4.531-7.188 8-12.2 8-16.398 0-4.199-3.801-7.602-8-7.602zm0 11c-1.657 0-3-1.343-3-3s1.343-3 3-3 3 1.343 3 3-1.343 3-3 3z"/></svg>`},updateMarker:function(){v.showMarker&&this.marker&&(this.marker.setLatLng(this.getCoordinates()),this.setMarkerRange(),this.updateLocation())},updateMapLocation:function(A,V,q){this.marker&&(this.marker.setLatLng([A,V]),this.markerShouldMoveWithMap=!q)},updateMarkerIcon:function(A){if(this.marker){let V;if(A[0].icon)V=Lt.icon({iconUrl:A[0].icon,iconSize:[v.iconSize,v.iconSize]});else{let q=this.config.markerColor||"#3b82f6";V=Lt.divIcon({html:this.defaultIconMarker(q),className:"",iconSize:[v.iconSize,v.iconSize]})}this.marker.setIcon(V)}},fetchInitialCategoryIcon:function(A){if(this.marker){let V;A&&(V=Lt.icon({iconUrl:A,iconSize:[v.iconSize,v.iconSize]}),this.marker.setIcon(V))}},captureAndUploadMapImage:function(){let A=this.map.getContainer();ts(A,{filter:V=>!(V.classList&&V.classList.contains("leaflet-control-container")),width:A.offsetWidth,height:A.offsetHeight}).then(V=>{if(!V||!(V instanceof Blob))throw new Error("Image snapshot did not return a valid blob object");let q=new FormData;q.append("map_image",V,"map.png"),fetch("/admin/upload-map-image",{method:"POST",headers:{"X-CSRF-TOKEN":document.querySelector('meta[name="csrf-token"]').getAttribute("content")},body:q}).then(M=>M.json()).then(M=>{M.success?(console.info("Snapshot successfully generated. Note: Remember to save your form data before leaving the page."),alert("Snapshot successfully generated. Note: Remember to save your form data before leaving the page.")):(console.error("Error uploading map image:",M.message),alert("Error uploading map image:"+M.message))}).catch(M=>{console.error("Error uploading map image:",M),alert("Error capturing map image:"+M)})}).catch(V=>{console.error("Error capturing map image:",V),alert("Error capturing map image:"+V)})},loadGeoJsonDataFromFile(A){if(this.isVariableValid(A)){let V=JSON.parse(A[0]);this.drawItems?this.drawItems.clearLayers():this.drawItems=new Lt.FeatureGroup().addTo(this.map);let M=Lt.geoJSON(V,{style:{color:"#FFFFFF",fillColor:"blue",fillOpacity:.5}}).addTo(this.drawItems).getBounds(),tt=this.config.controls.zoom||this.config.zoom()||13;M.isValid()?this.map.fitBounds(M,{maxZoom:tt,padding:[20,20]}):(console.warn("GeoJSON has invalid or single-point bounds; using defaults."),this.map.setView([this.config.default.lat,this.config.default.lng],tt))}},refreshMap:function(){this.map.flyTo(this.getCoordinates()),this.updateMarker()},init:function(){this.$wire=_,this.config=v,this.state=z,this.rangeSelectField=document.getElementById(v.rangeSelectField||"data.distance"),_.on("refreshMap",this.refreshMap.bind(this)),_.on("captureMapImage",()=>{this.captureAndUploadMapImage()}),_.on("updateMarkerIcon",A=>{this.updateMarkerIcon(A)}),_.on("loadGeoJsonDataFromFile",A=>{this.loadGeoJsonDataFromFile(A)}),_.on("updateMapLocation",A=>{let{lat:V,lng:q,fix:M}=A[0];this.updateMapLocation(V,q,M)})}}),window.dispatchEvent(new CustomEvent("map-script-loaded"))});
/*! Bundled license information:

leaflet/dist/leaflet-src.js:
  (* @preserve
   * Leaflet 1.9.4, a JS library for interactive maps. https://leafletjs.com
   * (c) 2010-2023 Vladimir Agafonkin, (c) 2010-2011 CloudMade
   *)
*/
