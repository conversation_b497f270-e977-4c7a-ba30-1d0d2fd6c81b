<?php

declare(strict_types=1);

return [

    /*
    |--------------------------------------------------------------------------
    | Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines contain the default error messages used by
    | the validator class. Some of these rules have multiple versions such
    | as the size rules. Feel free to tweak each of these messages here.
    |
    */

    'accepted' => 'يجب قبول :attribute.',
    'active_url' => ':attribute لا يُمثّل رابطا صحيحا.',
    'after' => 'يجب على :attribute أن يكون تاريخًا لاحقًا للتاريخ :date.',
    'after_or_equal' => ':attribute يجب أن يكون تاريخاً لاحقاً أو مطابقاً للتاريخ :date.',
    'alpha' => 'يجب أن لا يحتوي :attribute سوى على حروف.',
    'alpha_dash' => 'يجب أن لا يحتوي :attribute سوى على حروف، أرقام ومطّات.',
    'alpha_num' => 'يجب أن يحتوي :attribute على حروفٍ وأرقامٍ فقط.',
    'array' => 'يجب أن يكون :attribute ًمصفوفة.',
    'before' => 'يجب على :attribute أن يكون تاريخًا سابقًا للتاريخ :date.',
    'before_or_equal' => ':attribute يجب أن يكون تاريخا سابقا أو مطابقا للتاريخ :date.',
    'between' => [
        'numeric' => 'يجب أن تكون قيمة :attribute بين :min و :max.',
        'file' => 'يجب أن يكون حجم الملف :attribute بين :min و :max كيلوبايت.',
        'string' => 'يجب أن يكون عدد حروف النّص :attribute بين :min و :max.',
        'array' => 'يجب أن يحتوي :attribute على عدد من العناصر بين :min و :max.',
    ],
    'boolean' => 'يجب أن تكون قيمة :attribute إما true أو false .',
    'confirmed' => 'حقل التأكيد غير مُطابق للحقل :attribute.',
    'date' => ':attribute ليس تاريخًا صحيحًا.',
    'date_equals' => 'يجب أن يكون :attribute مطابقاً للتاريخ :date.',
    'date_format' => 'لا يتوافق :attribute مع الشكل :format.',
    'different' => 'يجب أن يكون الحقلان :attribute و :other مُختلفين.',
    'digits' => 'يجب أن يحتوي :attribute على :digits رقمًا/أرقام.',
    'digits_between' => 'يجب أن يحتوي :attribute بين :min و :max رقمًا/أرقام .',
    'dimensions' => 'الـ :attribute يحتوي على أبعاد صورة غير صالحة.',
    'distinct' => 'للحقل :attribute قيمة مُكرّرة.',
    'email' => 'يجب أن يكون :attribute عنوان بريد إلكتروني صحيح البُنية.',
    'exists' => 'القيمة المحددة :attribute غير موجودة.',
    'file' => 'الـ :attribute يجب أن يكون ملفا.',
    'filled' => ':attribute إجباري.',
    'gt' => [
        'numeric' => 'يجب أن تكون قيمة :attribute أكبر من :value.',
        'file' => 'يجب أن يكون حجم الملف :attribute أكبر من :value كيلوبايت.',
        'string' => 'يجب أن يكون طول النّص :attribute أكثر من :value حروفٍ/حرفًا.',
        'array' => 'يجب أن يحتوي :attribute على أكثر من :value عناصر/عنصر.',
    ],
    'gte' => [
        'numeric' => 'يجب أن تكون قيمة :attribute مساوية أو أكبر من :value.',
        'file' => 'يجب أن يكون حجم الملف :attribute على الأقل :value كيلوبايت.',
        'string' => 'يجب أن يكون طول النص :attribute على الأقل :value حروفٍ/حرفًا.',
        'array' => 'يجب أن يحتوي :attribute على الأقل على :value عُنصرًا/عناصر.',
    ],
    'image' => 'يجب أن يكون :attribute صورةً.',
    'in' => ':attribute غير موجود.',
    'in_array' => ':attribute غير موجود في :other.',
    'integer' => 'يجب أن يكون :attribute عددًا صحيحًا.',
    'ip' => 'يجب أن يكون :attribute عنوان IP صحيحًا.',
    'ipv4' => 'يجب أن يكون :attribute عنوان IPv4 صحيحًا.',
    'ipv6' => 'يجب أن يكون :attribute عنوان IPv6 صحيحًا.',
    'json' => 'يجب أن يكون :attribute نصآ من نوع JSON.',
    'lt' => [
        'numeric' => 'يجب أن تكون قيمة :attribute أصغر من :value.',
        'file' => 'يجب أن يكون حجم الملف :attribute أصغر من :value كيلوبايت.',
        'string' => 'يجب أن يكون طول النّص :attribute أقل من :value حروفٍ/حرفًا.',
        'array' => 'يجب أن يحتوي :attribute على أقل من :value عناصر/عنصر.',
    ],
    'lte' => [
        'numeric' => 'يجب أن تكون قيمة :attribute مساوية أو أصغر من :value.',
        'file' => 'يجب أن لا يتجاوز حجم الملف :attribute :value كيلوبايت.',
        'string' => 'يجب أن لا يتجاوز طول النّص :attribute :value حروفٍ/حرفًا.',
        'array' => 'يجب أن لا يحتوي :attribute على أكثر من :value عناصر/عنصر.',
    ],
    'max' => [
        'numeric' => 'يجب أن تكون قيمة :attribute مساوية أو أصغر من :max.',
        'file' => 'يجب أن لا يتجاوز حجم :attribute :max كيلوبايت.',
        'string' => 'يجب أن لا يتجاوز طول النّص :attribute :max حرفاً.',
        'array' => 'يجب أن لا يحتوي :attribute على أكثر من :max عناصر/عنصر.',
    ],
    'mimes' => 'يجب أن يكون ملفًا من نوع : :values.',
    'mimetypes' => 'يجب أن يكون ملفًا من نوع : :values.',
    'min' => [
        'numeric' => 'يجب أن تكون قيمة :attribute مساوية أو أكبر من :min.',
        'file' => 'يجب أن يكون حجم الملف :attribute على الأقل :min كيلوبايت.',
        'string' => 'يجب أن يكون طول النص :attribute على الأقل :min حروفٍ/حرفًا.',
        'array' => 'يجب أن يحتوي :attribute على الأقل على :min عُنصرًا/عناصر.',
    ],
    'missing_with' => 'يجب أن يكون حقل :attribute مفقوداً عندما يكون حقل :values موجوداً',
    'not_in' => ':attribute موجود.',
    'not_regex' => 'صيغة :attribute غير صحيحة.',
    'numeric' => 'يجب على :attribute أن يكون رقمًا.',
    'present' => 'يجب تقديم :attribute.',
    'regex' => 'صيغة :attribute غير صحيحة.',
    'required' => ':attribute مطلوب.',
    'required_if' => ':attribute مطلوب في حال ما إذا كان :other يساوي :value.',
    'required_unless' => ':attribute مطلوب في حال ما لم يكن :other يساوي :values.',
    'required_with' => ':attribute مطلوب إذا توفّر :values.',
    'required_with_all' => ':attribute مطلوب إذا توفّر :values.',
    'required_without' => ':attribute مطلوب إذا لم يتوفّر :values.',
    'required_without_all' => ':attribute مطلوب إذا لم يتوفّر :values.',
    'same' => 'يجب أن يتطابق :attribute مع :other.',
    'size' => [
        'numeric' => 'يجب أن تكون قيمة :attribute مساوية لـ :size.',
        'file' => 'يجب أن يكون حجم الملف :attribute :size كيلوبايت.',
        'string' => 'يجب أن يحتوي النص :attribute على :size حروفٍ/حرفًا بالضبط.',
        'array' => 'يجب أن يحتوي :attribute على :size عنصرٍ/عناصر بالضبط.',
    ],
    'starts_with' => 'يجب أن يبدأ :attribute بأحد القيم التالية: :values',
    'string' => 'يجب أن يكون :attribute نصًا.',
    'timezone' => 'يجب أن يكون :attribute نطاقًا زمنيًا صحيحًا.',
    'unique' => 'قيمة :attribute مُستخدمة من قبل.',
    'uploaded' => 'فشل في تحميل الـ :attribute.',
    'url' => 'صيغة الرابط :attribute غير صحيحة.',
    'uuid' => ':attribute يجب أن يكون بصيغة UUID سليمة.',
    'enum_value' => ':attribute ليست في القائمة',

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Attributes
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to swap our attribute placeholder
    | with something more reader friendly such as "E-Mail Address" instead
    | of "email". This simply helps us make our message more expressive.
    |
    */

    'attributes' => [
        'name' => 'الاسم',
        'name_ar' => 'اسم الفعالية بالعربي',
        'name_en' => 'اسم الفعالية بالإنجليزي',
        'full_name' => 'الاسم الكامل',
        'job_name' => 'اسم الوظيفة',
        'username' => 'اسم المستخدم',
        'email' => 'البريد الإلكتروني',
        'password' => 'كلمة المرور',
        'password_confirmation' => 'تأكيد كلمة المرور',
        'city' => 'المدينة',
        'phone' => 'الهاتف',
        'mobile' => 'رقم الجوال',
        'gender' => 'الجنس',
        'day' => 'اليوم',
        'month' => 'الشهر',
        'year' => 'السنة',
        'hour' => 'الساعة',
        'minute' => 'الدقيقة',
        'second' => 'الثانية',
        'content' => 'المحتوى',
        'description' => 'الوصف',
        'excerpt' => 'الملخص',
        'date' => 'التاريخ',
        'time' => 'الوقت',
        'available' => 'متاح',
        'avatar' => 'الصورة الشخصية',
        'city_id' => 'المدينة',
        'current_password' => 'كلمة المرور الحالية',
        'new_password' => 'كلمة المرور الجديدة',
        'sort' => 'الترتيب',
        'check_in_type' => 'نوع الدخول',
        'start_at' => 'تاريخ البدء',
        'end_at' => 'تاريخ الانتهاء',
        'category_id' => 'تصنيف الفعالية',
        'country_code' => 'رمز الدولة',
        'picture' => 'الصورة',
        'description_ar' => 'الوصف بالعربي',
        'description_en' => 'الوصف بالإنجليزي',
        'latitude' => 'خط العرض',
        'longitude' => 'خط الطول',
        'location' => 'الموقع',
        'logo' => 'الشعار',
        'banner' => 'اللافتة',
        'primary_color' => 'اللون الأساسي',
        'secondary_color' => 'اللون الثانوي',
        'company' => 'الشركة',
        'job' => 'الوظيفة',
        'bio' => 'النبذة',
        'social_accounts' => [
            '*' => [
                'name' => 'الحساب',
                'url' => 'الرابط',
            ],
        ],
        'picture' => 'الصورة',
        'birth_date' => 'تاريخ الميلاد',
        'country_id' => 'الدولة',
        'newsletter_subscription' => 'الاشتراك في النشرة الإخبارية',
        'fcm_token' => 'رمز الجهاز',
        'id_token' => 'رمز المصادقة',
        'otp' => 'رمز التحقق',
        'city_en' => 'المدينة (بالإنجليزية)',
        'city_ar' => 'المدينة (بالعربية)',
        'location.lat' => 'خط العرض',
        'location.lng' => 'خط الطول',
        'from_city_en' => 'مدينة المغادرة (بالإنجليزية)',
        'from_city_ar' => 'مدينة المغادرة (بالعربية)',
        'to_city_en' => 'مدينة الوصول (بالإنجليزية)',
        'to_city_ar' => 'مدينة الوصول (بالعربية)',
        'from_location.lat' => 'خط عرض نقطة المغادرة',
        'from_location.lng' => 'خط طول نقطة المغادرة',
        'to_location.lat' => 'خط عرض نقطة الوصول',
        'to_location.lng' => 'خط طول نقطة الوصول',
        'departure_datetime' => 'تاريخ ووقت المغادرة',
        'arrival_datetime' => 'تاريخ ووقت الوصول',
        'transportation_type' => 'نوع النقل',
        'trip_type' => 'نوع الرحلة',
        'fazaa_service_type_id' => 'نوع الخدمة',
        'specific_type_id' => 'النوع المحدد',
        'price' => 'السعر',
        'package_price' => 'سعر الكراتين',
        'document_price' => 'سعر المستند',
        'furniture_price' => 'سعر الأثاث',
        'packages_volume' => 'حجم الكراتين',
        'document_volume' => 'حجم المستندات',
        'furniture_volume' => 'حجم الأثاث',
        'is_fragile' => 'مؤشر المواد القابلة للكسر',
        'cancellation_reason' => 'سبب الإلغاء',
        'rejection_reason' => 'سبب الرفض',
        'note' => 'ملاحظة',
        'seats' => 'المقاعد',
        'seats.*' => 'المقعد',
        'seat_id' => 'المقعد',
        'delivery_location' => 'موقع التسليم',
        'service_location' => 'موقع الخدمة',
        'can_ship_packages' => 'إمكانية شحن الطرود',
        'can_ship_documents' => 'إمكانية شحن المستندات',
        'can_ship_furniture' => 'إمكانية شحن الأثاث',
        'shipment_volume' => 'حجم الشحنة',
        'car_id' => 'السيارة',
        'available_packages_volume' => 'حجم الكراتين المتاح',
        'available_document_volume' => 'حجم المستندات المتاح',
        'available_furniture_volume' => 'حجم الأثاث المتاح',
        'rating' => 'التقييم',
        'comment' => 'التعليق',
        'has_fragile_items' => 'يوجد مواد قابلة للكسر',
        'number_of_seats' => 'عدد المقاعد',
        'number_of_available_seats' => 'عدد المقاعد المتاحة',
        'number_of_free_cartons' => 'عدد الكراتين المجانية',
        'allow_smoking' => 'السماح بالتدخين',
        'deliver_to_door' => 'التوصيل إلى الباب',
        'attendance_confirmed_at' => 'وقت تأكيد الحضور',
        'arrival_confirmed_at' => 'وقت تأكيد الوصول',
        'delay_requested_at' => 'وقت طلب التأخير',
        'delayed_at' => 'وقت التأخير',
        'delay_reason' => 'سبب التأخير',
        'delay_note' => 'ملاحظة التأخير',
        'cancelled_at' => 'وقت الإلغاء',
        'cancellation_note' => 'ملاحظة الإلغاء',
        'rejected_at' => 'وقت الرفض',
        'rejection_note' => 'ملاحظة الرفض',
        'read_at' => 'وقت القراءة',
        'read_by_id' => 'تمت القراءة بواسطة المستخدم',
        'trip_service_booking_id' => 'معرف حجز خدمة الرحلة',
        'trip_service_id' => 'معرف خدمة الرحلة',
        'trip_service_booking_ids' => 'معرفات حجوزات خدمة الرحلة',
        'trip_service_bookings' => 'حجوزات خدمة الرحلة',
        'trip_service' => 'خدمة الرحلة',
        'user_id' => 'معرف المستخدم',
        'user' => 'المستخدم',
        'car' => 'السيارة',
        'car_id' => 'معرف السيارة',
        'from_city_id' => 'معرف مدينة المغادرة',
        'to_city_id' => 'معرف مدينة الوصول',
        'from_location_lat' => 'خط عرض المغادرة',
        'from_location_lng' => 'خط طول المغادرة',
        'to_location_lat' => 'خط عرض الوصول',
        'to_location_lng' => 'خط طول الوصول',
        'from_city' => 'مدينة المغادرة',
        'to_city' => 'مدينة الوصول',
        'from_location' => 'موقع المغادرة',
        'to_location' => 'موقع الوصول',
        'additional_services' => 'الخدمات الإضافية',
        'additional_services.*' => 'خدمة إضافية',
        'additional_services.*.id' => 'معرف الخدمة الإضافية',
        'additional_services.*.name' => 'اسم الخدمة الإضافية',
    ],

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | Here you may specify custom validation messages for attributes using the
    | convention "attribute.rule" to name the lines. This makes it quick to
    | specify a specific custom language line for a given attribute rule.
    |
    */

    'custom' => [
        'name' => [
            'regex' => 'يجب أن يحتوي :attribute على حروف فقط.',
            'min' => 'يجب أن يكون طول :attribute على الأقل :min أحرف.',
            'max' => 'حقل :attribute يجب ألا يتجاوز :max حرفاً',
        ],
        'name_ar' => [
            'regex' => 'يجب أن يحتوي :attribute على حروف فقط.',
            'min' => 'يجب أن يكون طول :attribute على الأقل :min أحرف.',
            'max' => 'حقل :attribute يجب ألا يتجاوز :max حرفاً',
        ],
        'name_en' => [
            'regex' => 'يجب أن يحتوي :attribute على حروف فقط.',
            'min' => 'يجب أن يكون طول :attribute على الأقل :min أحرف.',
            'max' => 'حقل :attribute يجب ألا يتجاوز :max حرفاً',
        ],
        'representative_mobile' => [
            'regex' => ':attribute يجب ان يبدا ب 5 ثم يتبع ب 8 ارقام فقط',
            'max' => 'يجب أن لا يتجاوز طول :attribute :max رقمًا',
        ],
        'password' => [
            'min' => 'يجب أن يكون طول :attribute على الأقل :min أحرف.'
        ],
        'current_password' => [
            'min' => 'يجب أن يكون طول :attribute على الأقل :min أحرف.'
        ],
        'new_password' => [
            'min' => 'يجب أن يكون طول :attribute على الأقل :min أحرف.'
        ],
        'username' => [
            'min' => 'يجب أن يكون طول :attribute على الأقل 3 أحرف.',
            'max' => 'يجب أن لا يتجاوز طول :attribute 20 حرفاً.'
        ],
        'avatar' => [
            'max' => 'يجب أن لا يتجاوز حجم :attribute :max كيلوبايت.'
        ],
        'code' => [
            'unique' => 'الرمز موجود مسبقًا',
        ],
        'real_name' => [
            'min' => 'يجب أن يكون طول :attribute على الأقل 8 أحرف.',
            'max' => 'يجب أن لا يتجاوز طول :attribute 27 حرفاً.'
        ],
        'city_id' => [
            'prohibited' => 'لا يمكنك اختيار مدينة وأنت غير مقيم',
            'exists' => 'القيمة المختارة للمدينة غير موجودة',
            'integer' => 'القيمة المختارة للمدينة غير موجودة',
        ],
        'image' => [
            'required' => 'الصورة مطلوبة',
            'file' => 'الصورة يجب أن تكون صورة',
            'max' => 'يجب أن لا يتجاوز حجم الصورة 3 ميجا بايت'
        ],
        'mobile' => [
            'regex' => ':attribute يجب ان يبدا ب 5 ثم يتبع ب 8 ارقام فقط',
            'min_digits' => 'يجب أن يكون طول :attribute على الأقل :min أرقام',
            'max_digits' => 'يجب أن لا يتجاوز طول :attribute :max رقمًا',
        ],
        'email' => [
            'email' => 'البريد الإلكتروني غير صحيح',
            'unique' => 'هذا :attribute موجود بالفعل',
        ],
        'company' => [
            'required' => 'حقل :attribute مطلوب',
            'min' => 'حقل :attribute يجب ألا يقل عن :min أحرف',
            'max' => 'حقل :attribute يجب ألا يتجاوز :max حرفاً'
        ],
        'job' => [
            'required' => 'حقل :attribute مطلوب',
            'min' => 'حقل :attribute يجب ألا يقل عن :min أحرف',
            'max' => 'حقل :attribute يجب ألا يتجاوز :max حرفاً'
        ],
        'bio' => [
            'required' => 'حقل :attribute مطلوب',
            'min' => 'حقل :attribute يجب ألا يقل عن :min أحرف',
            'max' => 'حقل :attribute يجب ألا يتجاوز :max حرفاً'
        ],
        'social_accounts' => [
            '*' => [
                'name' => [
                    'in' => 'اسم :attribute المختار غير موجود',
                ],
                'url' => [
                    'url' => 'صيغة الرابط غير صحيحة'
                ]
            ],
        ],
        'picture' => [
            'max' => [
                'file' => 'يجب أن لا يتجاوز حجم :attribute 2 ميجابايت'
            ]
        ],
        'departure_datetime' => [
            'before_or_equal' => 'يجب أن يكون تاريخ المغادرة خلال الخمسة أيام القادمة.',
            'after_or_equal' => 'لا يمكن أن يكون تاريخ المغادرة في الماضي.',
        ],
    ]
];
