<?php

declare(strict_types=1);

use App\Enums\Complaints\ComplaintStatusEnum;
use App\Enums\Travel\SeatStatusEnum;
use App\Enums\Travel\ShippingServiceTypeEnum;
use App\Enums\Travel\TransportationTypeEnum;
use App\Enums\Travel\TripStatusEnum;
use App\Enums\Travel\TripTypeEnum;

return [
    'trip_type' => [
        TripTypeEnum::FAMILIES() => 'عوائل',
        TripTypeEnum::SINGLES() => 'عزاب',
    ],
    'seat_status' => [
        SeatStatusEnum::AVAILABLE() => 'متاح',
        SeatStatusEnum::RESERVED() => 'محجوز',
    ],
    'shipping_service_type' => [
        ShippingServiceTypeEnum::PACKAGE() => 'حزمة',
        ShippingServiceTypeEnum::DOCUMENT() => 'مستند',
        ShippingServiceTypeEnum::FURNITURE() => 'أثاث',
    ],
    'transportation_type' => [
        TransportationTypeEnum::CAR() => 'سيارة',
        TransportationTypeEnum::FLIGHT() => 'طيران',
    ],
    'status' => [
        TripStatusEnum::ACTIVE() => 'نشط',
        TripStatusEnum::COMPLETED() => 'مكتمل',
        TripStatusEnum::CANCELLED() => 'ملغي',
        ComplaintStatusEnum::PENDING() => 'معلق',
        ComplaintStatusEnum::RESOLVED() => 'تم الحل',
        ComplaintStatusEnum::REJECTED() => 'مرفوض',
    ],
];
