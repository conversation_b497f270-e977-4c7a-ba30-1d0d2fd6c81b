<?php

declare(strict_types=1);

use App\Enums\Complaints\ComplaintStatusEnum;
use App\Enums\Travel\FazaaServiceTypeEnum;
use App\Enums\Travel\SeatStatusEnum;
use App\Enums\Travel\ShippingServiceTypeEnum;
use App\Enums\Travel\TransportationTypeEnum;
use App\Enums\Travel\TripStatusEnum;
use App\Enums\Travel\TripTypeEnum;

return [
    'trip_type' => [
        TripTypeEnum::FAMILIES() => 'Families',
        TripTypeEnum::SINGLES() => 'Singles',
    ],
    'seat_status' => [
        SeatStatusEnum::AVAILABLE() => 'Available',
        SeatStatusEnum::RESERVED() => 'Reserved',
    ],
    'shipping_service_type' => [
        ShippingServiceTypeEnum::PACKAGE() => 'Package',
        ShippingServiceTypeEnum::DOCUMENT() => 'Document',
        ShippingServiceTypeEnum::FURNITURE() => 'Furniture',
    ],
    'transportation_type' => [
        TransportationTypeEnum::CAR() => 'Car',
        TransportationTypeEnum::FLIGHT() => 'Flight',
    ],
    'status' => [
        TripStatusEnum::ACTIVE() => 'Active',
        TripStatusEnum::COMPLETED() => 'Completed',
        TripStatusEnum::CANCELLED() => 'Cancelled',
        ComplaintStatusEnum::PENDING() => 'Pending',
        ComplaintStatusEnum::RESOLVED() => 'Resolved',
        ComplaintStatusEnum::REJECTED() => 'Rejected',
    ],
];
