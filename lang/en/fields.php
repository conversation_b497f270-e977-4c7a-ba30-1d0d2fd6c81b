<?php

return [
    'id' => 'ID',
    'name' => 'Name',
    'email' => 'Email',
    'mobile' => 'Mobile',
    'country' => 'Country',
    'city' => 'City',
    'picture' => 'Picture',
    'latitude' => 'Latitude',
    'longitude' => 'Longitude',
    'language' => 'Language',
    'verified' => 'Verified',
    'services' => 'Services',
    'requests' => 'Requests',

    // User fields
    'user' => 'User',
    'created_at' => 'Created At',
    'updated_at' => 'Updated At',

    // Trip request fields
    'trip_type' => 'Trip Type',
    'departure_datetime' => 'Departure Date & Time',
    'arrival_datetime' => 'Arrival Date & Time',
    'number_of_seats' => 'Number of Seats',
    'price' => 'Price',
    'from_city' => 'From City',
    'to_city' => 'To City',
    'from_latitude' => 'From Latitude',
    'from_longitude' => 'From Longitude',
    'to_latitude' => 'To Latitude',
    'to_longitude' => 'To Longitude',
    'car_type' => 'Car Type',
    'allow_smoking' => 'Allow Smoking',
    'deliver_to_door' => 'Deliver to Door',
    'arrive_destination' => 'Arrive at Destination',
    'note' => 'Note',
    'status' => 'Status',

    // Fazaa request fields
    'service_type' => 'Service Type',
    'specific_type' => 'Specific Type',
    'transportation_type' => 'Transportation Type',
    'service_location' => 'Service Location',

    // Shipping request fields
    'can_ship_packages' => 'Can Ship Packages',
    'can_ship_documents' => 'Can Ship Documents',
    'can_ship_furniture' => 'Can Ship Furniture',
    'packages_volume' => 'Packages Volume',
    'document_volume' => 'Document Volume',
    'furniture_volume' => 'Furniture Volume',
    'is_fragile' => 'Is Fragile',
    'delivery_location' => 'Delivery Location',

    // Status fields
    'cancelled_at' => 'Cancelled At',
    'cancellation_reason' => 'Cancellation Reason',
    'cancellation_note' => 'Cancellation Note',
    'attendance_confirmed_at' => 'Attendance Confirmed At',
    'delayed_at' => 'Delayed At',
    'delay_reason' => 'Delay Reason',
    'delay_note' => 'Delay Note',

    // Read tracking fields
    'read_at' => 'Read At',
    'read_by' => 'Read By',
    'read_status' => 'Read',
    'unread_status' => 'Unread',

    // Tracking fields
    'tracking' => 'Tracking',
    'tracking_number' => 'Tracking Number',
    'service_provider' => 'Service Provider',
    'current_location' => 'Current Location',
    'destination' => 'Destination',
    'duration' => '3 Hours',
    'active' => 'Active',
    'completed' => 'Completed',
    'cancelled' => 'Cancelled',
    'all' => 'All',
    'not_started' => 'Not Started',
    'delayed' => 'Delayed',
    'delayed_more_than_3_hours' => 'Delayed > 3 Hours',
    'view' => 'View',
    'hours' => 'Hours',
    'status_active' => 'Active',
    'status_completed' => 'Completed',
    'status_cancelled' => 'Cancelled',
    'remaining_time_to_arrival' => 'Remaining Time to Arrival',

    // Package fields
    'package_type' => 'Package Type',
    'package_size' => 'Package Size',
    'package_weight' => 'Package Weight',
    'package_quantity' => 'Package Quantity',
    'package_value' => 'Package Value',

    // Complaint fields
    'description' => 'Description',
    'admin_response' => 'Admin Response',
    'handled_by' => 'Handled By',
    'resolved_at' => 'Resolved At',
    'reason_for_rejection' => 'Reason for Rejection',
    'resolve' => 'Resolve',
    'reject' => 'Reject',

    // Rating fields
    'rating' => 'Rating',
    'comment' => 'Comment',
    'driver' => 'Driver',
    'trip_service_booking' => 'Trip Service Booking',
    'service_owner' => 'Service Owner',
    'rating_owner' => 'Rating Owner',
    'service_type' => 'Service Type',
    'available' => 'Available',
    'deleted' => 'Deleted',

    // Booking fields
    'booking_date' => 'Booking Date',
    'trip_service' => 'Trip Service',
    'shipping_service' => 'Shipping Service',
    'fazaa_service' => 'Fazaa Service',

    // Offer fields
    'trip_request' => 'Trip Request',
    'shipping_request' => 'Shipping Request',
    'fazaa_request' => 'Fazaa Request',
    'offered_price' => 'Offered Price',
    'offer_date' => 'Offer Date',
    'accepted' => 'Accepted',
    'rejected' => 'Rejected',
    'pending' => 'Pending',
    'offer_status' => 'Offer Status',
    'package_price' => 'Package Price',
    'document_price' => 'Document Price',
    'furniture_price' => 'Furniture Price',

    // Transport Fields
    'transport' => [
        // Office fields
        'office_name' => 'Office Name',
        'office_email' => 'Office Email',
        'office_phone' => 'Office Phone',
        'office_address' => 'Office Address',
        'office_city' => 'City',
        'office_country' => 'Country',
        'office_description' => 'Description',
        'license_number' => 'License Number',
        'status' => 'Status',
        'name' => 'Name',
        'email' => 'Email',
        'phone' => 'Phone',
        'address' => 'Address',
        'city' => 'City',
        'country' => 'Country',
        'description' => 'Description',
        'created_at' => 'Created At',
        'updated_at' => 'Updated At',

        // User fields
        'full_name' => 'Full Name',
        'email_address' => 'Email Address',
        'password' => 'Password',
        'password_confirmation' => 'Confirm Password',
        'phone_number' => 'Phone Number',
        'role' => 'Role',
        'transportation_office' => 'Transportation Office',

        // Document fields
        'office_logo' => 'Office Logo',
        'license_document' => 'License Document',
        'registration_documents' => 'Registration Document',

        // Status values
        'active' => 'Active',
        'inactive' => 'Inactive',
        'suspended' => 'Suspended',

        // Role values
        'admin' => 'Administrator',
        'manager' => 'Manager',
        'operator' => 'Operator',

        // Filter labels
        'licensed_offices' => 'Licensed Offices',
        'has_license' => 'Has License',

        // Driver fields
        'driver_type' => 'Driver Type',
        'collaborator' => 'Collaborator',
        'employee' => 'Employee',
        'select_driver' => 'Select Driver',
        'license_expiry' => 'License Expiry',
        'emergency_contact' => 'Emergency Contact',
        'driver_information' => 'Driver Information',
        'country' => 'Country',
        'city' => 'City',
        'office_logo' => 'Office Logo',
        'license_document' => 'License Document',
        'mobile' => 'Mobile',
        'description' => 'Description',
        'password' => 'Password',
        'password_confirmation' => 'Password Confirmation',

        // Trip fields
        'trip_type' => 'Trip Type',
        'driver' => 'Driver',
        'car' => 'Car',
        'from_city' => 'From City',
        'to_city' => 'To City',
        'from_latitude' => 'From Latitude',
        'from_longitude' => 'From Longitude',
        'from_location' => 'From Location',
        'to_latitude' => 'To Latitude',
        'to_longitude' => 'To Longitude',
        'to_location' => 'To Location',
        'departure_datetime' => 'Departure Date & Time',
        'arrival_datetime' => 'Arrival Date & Time',
        'price' => 'Price',
        'free_cartons' => 'Free Cartons',
        'allow_smoking' => 'Allow Smoking',
        'deliver_to_door' => 'Deliver to Door',
        'note' => 'Note',
        'available_seats' => 'Available Seats',
        'seat_information' => 'Seat Information',
        'seat_layout' => 'Seat Layout',
        'reserved_seats' => 'Reserved Seats',
        'select_available_seats' => 'Select Available Seats',
        'status' => 'Status',
        'created_at' => 'Created At',

        // Placeholders
        'office_name_placeholder' => 'Enter office name',
        'office_email_placeholder' => '<EMAIL>',
        'office_phone_placeholder' => '+**********',
        'office_address_placeholder' => 'Enter office address',
        'city_placeholder' => 'Enter city',
        'country_placeholder' => 'Enter country',
        'license_number_placeholder' => 'Enter license number',
        'description_placeholder' => 'Enter description',
        'full_name_placeholder' => 'Enter full name',
        'email_placeholder' => '<EMAIL>',
        'password_placeholder' => 'Enter password',
        'password_confirmation_placeholder' => 'Confirm password',
        'phone_placeholder' => '+**********',
    ],
];
