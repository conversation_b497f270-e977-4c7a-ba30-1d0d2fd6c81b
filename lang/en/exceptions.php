<?php

declare(strict_types=1);

return [
    'unauthorized' => 'Unauthorized',
    'unauthenticated' => 'Unauthenticated',
    'record_not_found' => 'Record not found.',
    'route_not_found' => 'Route not found', // 'API route not found',
    'page_not_found' => 'Page not found.',
    'bad_request' => 'Bad request.',
    'login_required' => 'Login required.',
    'forbidden' => 'Forbidden.',
    'internal_server_error' => 'Internal server error.',
    'trip_service' => [
        'booking' => [
            'already_booked_fazaa_service' => 'This Fazaa Service has already been booked',
            'seat_does_not_exist' => 'The selected seat does not exist.',
            'seat_not_available' => 'The selected seat is not available.',
            'fully_booked' => 'This trip service is fully booked',
            'booking_is_not_pending' => 'This booking is not pending',
            'cannot_confirm_cancelled_booking' => 'Cannot confirm attendance for a cancelled booking.',
            'attendance_confirmation_window' => 'Attendance can only be confirmed within 30 minutes before departure time.',
            'attendance_already_confirmed' => 'Attendance has already been confirmed for this trip.',
            'arrival_already_confirmed' => 'Arrival has already been confirmed for this trip.',
            'cannot_confirm_arrival_cancelled_booking' => 'Cannot confirm arrival for a cancelled booking.',
            'cannot_confirm_arrival_rejected_booking' => 'Cannot confirm arrival for a rejected booking.',
            'cannot_confirm_arrival_cancelled_service' => 'Cannot confirm arrival for a booking of a cancelled trip service.',
            'arrival_confirmation_too_early' => 'Arrival can only be confirmed after or within 30 minutes before the expected arrival time.',
            'cannot_notify_late_arrival_cancelled_booking' => 'Cannot request late arrival for a cancelled booking.',
            'cannot_notify_late_arrival_rejected_booking' => 'Cannot request late arrival for a rejected booking.',
            'cannot_notify_late_arrival_cancelled_trip' => 'Cannot request late arrival for a cancelled trip.',
            'cannot_notify_late_arrival_completed_trip' => 'Cannot request late arrival for a trip that has already completed.',
            'already_rated' => 'This trip service has already been rated.',
            'service_is_fully_booked' => 'This trip service is fully booked.',
            'another_booking_already_accepted' => 'Another booking has already been accepted.',
            'no_unpaid_bookings' => 'No unpaid bookings found for this trip service.',
        ],
        'cannot_rate_driver_before_arrival' => 'You cannot rate a driver before the trip service has arrived.',
        'cannot_rate_driver_before_arrival_confirmed' => 'You cannot rate a driver before the arrival is confirmed.',
        'cannot_cancel_within_hour' => 'You cannot cancel a trip service within 1 hour of departure',
        'already_cancelled' => 'This trip service has already been cancelled',
        'attendance_confirmation_window' => 'Attendance can only be confirmed within 30 minutes before departure time.',
        'attendance_already_confirmed' => 'Attendance has already been confirmed for this trip.',
        'cannot_confirm_cancelled_trip' => 'Cannot confirm attendance for a cancelled trip.',
        'cannot_delay_cancelled_trip' => 'Cannot delay a cancelled trip.',
        'cannot_delay_started_trip' => 'Cannot delay a trip that has already started.',
        'cannot_cancel_after_departure' => 'Cannot cancel a trip after departure time.',
        'already_confirmed' => 'This trip service has already been confirmed.',
        'cannot_confirm_cancelled_trip' => 'Cannot confirm attendance for a cancelled trip.',
    ],
    'fazaa_service' => [
        'booking' => [
            'already_booked_fazaa_service' => 'This Fazaa Service has already been booked',
            'booking_is_not_pending' => 'This booking is not pending',
            'attendance_confirmation_window' => 'Attendance can only be confirmed within 30 minutes before departure time.',
            'attendance_already_confirmed' => 'Attendance has already been confirmed for this trip.',
            'cannot_confirm_cancelled_booking' => 'Cannot confirm attendance for a cancelled booking.',
            'cannot_notify_late_arrival_cancelled_booking' => 'Cannot notify late arrival for a cancelled booking.',
        'cannot_notify_late_arrival_rejected_booking' => 'Cannot notify late arrival for a rejected booking.',
        'cannot_notify_late_arrival_cancelled_fazaa' => 'Cannot notify late arrival for a booking of a cancelled fazaa service.',
        'cannot_notify_late_arrival_completed_fazaa' => 'Cannot notify late arrival for a booking of a fazaa service that has already completed.',

        ],
        'cannot_delay_cancelled_service' => 'Cannot delay a cancelled fazaa service.',
        'cannot_delay_started_service' => 'Cannot delay a fazaa service that has already started.',
    ],
    'shipping_service' => [
        'booking' => [
            'fully_booked' => 'Shipping service is fully booked.',
            'not_enough_volume' => 'Not enough volume.',
            'booking_is_not_pending' => 'This booking is not pending',
            'service_is_fully_booked' => 'This shipping service is fully booked.',
            'attendance_confirmation_window' => 'Attendance can only be confirmed within 30 minutes before departure time.',
            'attendance_already_confirmed' => 'Attendance has already been confirmed for this trip.',
            'cannot_confirm_cancelled_booking' => 'Cannot confirm attendance for a cancelled booking.',
            'cannot_notify_late_arrival_cancelled_booking' => 'Cannot notify late arrival for a cancelled booking.',
        'cannot_notify_late_arrival_rejected_booking' => 'Cannot notify late arrival for a rejected booking.',
        'cannot_notify_late_arrival_cancelled_shipping' => 'Cannot notify late arrival for a booking of a cancelled shipping service.',
        'cannot_notify_late_arrival_completed_shipping' => 'Cannot notify late arrival for a booking of a shipping service that has already completed.',

        ],
        'cannot_delay_cancelled_service' => 'Cannot delay a cancelled shipping service.',
        'cannot_delay_started_service' => 'Cannot delay a shipping service that has already started.',
        'cannot_update_cancelled_shipping' => 'Cannot update a cancelled shipping service.',
        'cannot_update_after_departure' => 'Cannot update a shipping service after departure time.',
        'cannot_delay_cancelled_shipping' => 'Cannot delay a cancelled shipping service.',
        'cannot_delay_started_shipping' => 'Cannot delay a shipping service that has already started.',
        'already_cancelled' => 'This shipping service has already been cancelled',
        'cannot_cancel_within_hour' => 'You cannot cancel a shipping service within 1 hour of departure',
    ],
    'trip_request' => [
        'cannot_delay_cancelled_trip' => 'Cannot delay a cancelled trip request.',
        'cannot_delay_started_trip' => 'Cannot delay a trip request that has already started.',
        'booking' => [
            'already_booked_trip_request' => 'This Trip Request has already been booked',
            'another_booking_already_accepted' => 'Another booking is already accepted',
            'booking_is_not_pending' => 'This booking is not pending',
            'attendance_confirmation_window' => 'Attendance can only be confirmed within 30 minutes before departure time.',
            'attendance_already_confirmed' => 'Attendance has already been confirmed for this trip.',
            'cannot_confirm_cancelled_booking' => 'Cannot confirm attendance for a cancelled booking.',
        ],
        'already_cancelled' => 'This trip request has already been cancelled',
        'cannot_cancel_within_hour' => 'You cannot cancel a trip request within 4 hours of departure',
        'cannot_cancel_after_attendance_confirmed' => 'Cannot cancel a trip request after attendance is confirmed',
        'attendance_confirmation_window' => 'Attendance can only be confirmed within 30 minutes before departure time.',
        'attendance_already_confirmed' => 'Attendance has already been confirmed for this trip.',
        'cannot_confirm_cancelled_trip' => 'Cannot confirm attendance for a cancelled trip.',
        'cannot_update_cancelled_trip' => 'Cannot update a cancelled trip request.',
        'cannot_update_after_departure' => 'Cannot update a trip request after departure time.',
        'cannot_delay_cancelled_request' => 'Cannot delay a cancelled trip request.',
        'cannot_delay_started_request' => 'Cannot delay a trip request that has already started.',
    ],
    'fazaa_request' => [
        'booking' => [
            'already_booked_fazaa_request' => 'This Fazaa Request has already been booked',
            'booking_is_not_pending' => 'This booking is not pending',
            'booking_is_not_accepted' => 'This booking is not accepted',
            'attendance_confirmation_window' => 'Attendance can only be confirmed within 30 minutes before departure time.',
            'attendance_already_confirmed' => 'Attendance has already been confirmed for this trip.',
            'cannot_confirm_cancelled_booking' => 'Cannot confirm attendance for a cancelled booking.',
        ],
        'cannot_delay_cancelled_request' => 'Cannot delay a cancelled fazaa request.',
        'cannot_delay_started_request' => 'Cannot delay a fazaa request that has already started.',
        'cannot_confirm_cancelled_request' => 'Cannot confirm attendance for a cancelled fazaa request.',
        'attendance_already_confirmed' => 'Attendance has already been confirmed for this fazaa request.',
        'attendance_confirmation_window_invalid' => 'Attendance can only be confirmed within 30 minutes before departure time.',
    ],
    'shipping_request' => [
        'booking' => [
            'already_booked_shipping_request' => 'This Shipping Request has already been booked',
            'booking_is_not_pending' => 'This booking is not pending',
            'booking_is_not_accepted' => 'This booking is not accepted',
            'attendance_confirmation_window' => 'Attendance can only be confirmed within 30 minutes before departure time.',
            'attendance_already_confirmed' => 'Attendance has already been confirmed for this trip.',
            'cannot_confirm_cancelled_booking' => 'Cannot confirm attendance for a cancelled booking.',
        ],
        'cannot_delay_cancelled_request' => 'Cannot delay a cancelled shipping request.',
        'cannot_delay_started_request' => 'Cannot delay a shipping request that has already started.',
        'cannot_confirm_cancelled_shipping' => 'Cannot confirm attendance for a cancelled shipping request.',
        'attendance_already_confirmed' => 'Attendance has already been confirmed for this shipping request.',
        'attendance_confirmation_window' => 'Attendance can only be confirmed within 30 minutes before departure time.',
        'cannot_update_cancelled_shipping' => 'Cannot update a cancelled shipping request.',
        'cannot_update_after_departure' => 'Cannot update a shipping request after departure time.',
    ],
    'trip_service_booking' => [

    ],
    'shipping_service_booking' => [
        ],
    'fazaa_service_booking' => [
        ],
];
