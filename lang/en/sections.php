<?php

return [
    'trip_request_details' => 'Trip Request Details',
    'fazaa_request_details' => 'Fazaa Request Details',
    'shipping_request_details' => 'Shipping Request Details',
    'location_details' => 'Location Details',
    'shipping_details' => 'Shipping Details',
    'additional_details' => 'Additional Details',
    'status_information' => 'Status Information',
    'tracking_details' => 'Tracking Details',
    'customer_support' => 'Customer Support',
    'complaints' => 'Complaints',
    'booking_details' => 'Booking Details',
    'offer_details' => 'Offer Details',

    // Transport Sections
    'transport' => [
        'transportation_office_information' => 'Transportation Office Information',
        'admin_user_information' => 'Admin User Information',
        'basic_information' => 'Basic Information',
        'location_information' => 'Location Information',
        'additional_information' => 'Additional Information',
        'documents' => 'Documents & Files',
        'quick_actions' => 'Quick Actions',
        'statistics' => 'Statistics',
        'office_info_card' => 'Office Information',
        'quick_actions_card' => 'Quick Actions',
        'statistics_card' => 'Statistics',

        // Section descriptions
        'office_information_description' => 'Basic information about your transportation office',
        'admin_account_description' => 'Create an administrator account for your office',
        'documents_description' => 'Upload your office documents and logo',
        'driver_information' => 'Driver Information',
        'trip_information' => 'Trip Information',
        'route_information' => 'Route Information',
        'schedule_information' => 'Schedule Information',
        'pricing_and_options' => 'Pricing & Options',
        'seat_management' => 'Seat Management',
        'trip_details' => 'Trip Details',
        'seat_layout' => 'Seat Layout',
        'location_details' => 'Location Details',
        'additional_details' => 'Additional Details',
    ],
];
