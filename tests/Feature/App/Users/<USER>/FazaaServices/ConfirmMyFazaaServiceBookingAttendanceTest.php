<?php

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Event;
use App\Events\FazaaServiceBookingAttendanceConfirmedEvent;

beforeEach(function () {
    $this->user = User::first();
    $this->actingAs($this->user, 'api');
});

it('can confirm fazaa service booking attendance within valid time window', function () {
    Event::fake();

    $booking = $this->user->fazaaServiceBookings()
        ->whereNull('attendance_confirmed_at')
        ->whereNull('cancelled_at')
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No available fazaa service booking found for attendance confirmation.');
    }

    // Update the fazaa service departure time to be within the valid window
    $booking->fazaaService->update([
        'departure_datetime' => Carbon::now()->addMinutes(15)
    ]);

    $response = $this->postJson(
        route('app.users.services.fazaas.my-bookings.confirm-attendance', $booking->id)
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $booking->refresh();
    expect($booking->attendance_confirmed_at)->not->toBeNull();

    Event::assertDispatched(FazaaServiceBookingAttendanceConfirmedEvent::class);
});

it('cannot confirm attendance for cancelled booking', function () {
    $booking = $this->user->fazaaServiceBookings()
        ->whereNull('attendance_confirmed_at')
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No fazaa service booking found.');
    }

    // Update booking to be cancelled
    $booking->update([
        'cancelled_at' => Carbon::now()
    ]);

    $response = $this->postJson(
        route('app.users.services.fazaas.my-bookings.confirm-attendance', $booking->id)
    );

    expect($response->status())->toBe(422)
        ->and($response->json('message'))->toBe(__('exceptions.fazaa_service.booking.cannot_confirm_cancelled_booking'));
});

it('cannot confirm attendance when already confirmed', function () {
    $booking = $this->user->fazaaServiceBookings()
        ->whereNull('cancelled_at')
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No fazaa service booking found.');
    }

    // Update booking to be already confirmed
    $booking->update([
        'attendance_confirmed_at' => Carbon::now()
    ]);

    $response = $this->postJson(
        route('app.users.services.fazaas.my-bookings.confirm-attendance', $booking->id)
    );

    expect($response->status())->toBe(422)
        ->and($response->json('message'))->toBe(__('exceptions.fazaa_service.booking.attendance_already_confirmed'));
});

it('cannot confirm attendance outside 30-minute window before departure', function () {
    // Test too early
    $booking = $this->user->fazaaServiceBookings()
        ->whereNull('cancelled_at')
        ->whereNull('attendance_confirmed_at')
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No fazaa service booking found for early confirmation test.');
    }

    // Update departure time to be too early
    $booking->fazaaService->update([
        'departure_datetime' => Carbon::now()->addHours(2)
    ]);

    $response = $this->postJson(
        route('app.users.services.fazaas.my-bookings.confirm-attendance', $booking->id)
    );

    expect($response->status())->toBe(422)
        ->and($response->json('message'))->toBe(__('exceptions.fazaa_service.booking.attendance_confirmation_window'));

    // Test too late
    // Update departure time to be in the past
    $booking->fazaaService->update([
        'departure_datetime' => Carbon::now()->subHour()
    ]);

    $response = $this->postJson(
        route('app.users.services.fazaas.my-bookings.confirm-attendance', $booking->id)
    );

    expect($response->status())->toBe(422)
        ->and($response->json('message'))->toBe(__('exceptions.fazaa_service.booking.attendance_confirmation_window'));
});

it('cannot confirm attendance for non-owned booking', function () {
    $otherUser = User::where('id', '!=', $this->user->id)->first();

    $booking = $otherUser->fazaaServiceBookings()
        ->whereNull('cancelled_at')
        ->whereNull('attendance_confirmed_at')
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No other user\'s fazaa service booking found.');
    }

    $response = $this->postJson(
        route('app.users.services.fazaas.my-bookings.confirm-attendance', $booking->id)
    );

    expect($response->status())->toBe(404);
});
