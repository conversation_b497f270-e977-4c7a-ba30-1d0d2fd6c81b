<?php

use App\Enums\Bookings\BookingStatusEnum;
use App\Events\FazaaServiceBookingCancelledByOwnerEvent;
use App\Models\User;
use Illuminate\Support\Facades\Event;

beforeEach(function () {
    $this->user = User::first();
    $this->actingAs($this->user, 'api');
});

it('can cancel a fazaa service booking', function () {
    Event::fake();

    $fazaaService = $this->user->fazaaServices()->whereHas('bookings', function ($query) {
        $query->where('status', BookingStatusEnum::PENDING());
    })->inRandomOrder()->firstOr(function () {
        $this->markTestSkipped('No pending fazaa service found.');
    });

    $booking = $fazaaService->bookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    $cancellationData = [
        'cancellation_reason' => 'Service no longer available',
        'note' => 'Schedule conflict'
    ];

    $response = $this->postJson(
        route('app.users.services.fazaas.bookings.cancel', [
            'id' => $booking->id,
        ]),
        $cancellationData
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::CANCELLED_BY_OWNER());
    expect($booking->cancellation_reason)->toBe($cancellationData['cancellation_reason']);
    expect($booking->cancellation_note)->toBe($cancellationData['note']);

    Event::assertDispatched(FazaaServiceBookingCancelledByOwnerEvent::class);
});

it('cannot cancel non pending booking', function () {
    $fazaaService = $this->user->fazaaServices()->whereHas('bookings', function ($query) {
        $query->where('status', BookingStatusEnum::ACCEPTED());
    })->inRandomOrder()->firstOr(function () {
        $this->markTestSkipped('No fazaa service found with accepted bookings.');
    });

    $booking = $fazaaService->bookings()
        ->where('status', BookingStatusEnum::ACCEPTED())
        ->first();

    $cancellationData = [
        'cancellation_reason' => 'Service no longer available',
        'note' => 'Schedule conflict'
    ];

    $response = $this->postJson(
        route('app.users.services.fazaas.bookings.cancel', [
            'id' => $booking->id,
        ]),
        $cancellationData
    );

    expect($response->status())->toBe(422);
    expect($response->json('message'))->toBe(__('exceptions.fazaa_service.booking.booking_is_not_pending'));

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::ACCEPTED());
});

it('cannot cancel other users booking', function () {
    $otherUser = User::where('id', '!=', $this->user->id)->first();
    $fazaaService = $otherUser->fazaaServices()
        ->whereHas('bookings', function ($query) {
            $query->where('status', BookingStatusEnum::PENDING());
        })
        ->inRandomOrder()
        ->firstOr(function () {
            $this->markTestSkipped('No pending fazaa service found for other user.');
        });

    $booking = $fazaaService->bookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    $cancellationData = [
        'cancellation_reason' => 'Service no longer available',
        'note' => 'Schedule conflict'
    ];

    $response = $this->postJson(
        route('app.users.services.fazaas.bookings.cancel', [
            'id' => $booking->id,
        ]),
        $cancellationData
    );

    expect($response->status())->toBe(404);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::PENDING());
});

it('validates cancellation reasons', function () {
        $fazaaService = $this->user->fazaaServices()->whereHas('bookings', function ($query) {
            $query->where('status', BookingStatusEnum::PENDING());
        })->inRandomOrder()->firstOr(function () {
        $this->markTestSkipped('No pending fazaa service found.');
    });

    $booking = $fazaaService->bookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    // Test empty reason
    $response = $this->postJson(
        route('app.users.services.fazaas.bookings.cancel', [
            'id' => $booking->id,
        ]),
        ['cancellation_reason' => '']
    );

    expect($response->status())->toBe(422);

    // Test too long reason
    $response = $this->postJson(
        route('app.users.services.fazaas.bookings.cancel', [
            'id' => $booking->id,
        ]),
        [
            'cancellation_reason' => str_repeat('a', 256)
        ]
    );

    expect($response->status())->toBe(422);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::PENDING());
});
