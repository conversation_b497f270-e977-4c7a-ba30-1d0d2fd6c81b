<?php

use App\Models\User;

beforeEach(function () {
    $this->user = User::first();
});

it('can show booking details to fazaa service owner', function () {
    $this->actingAs($this->user, 'api');

    $owner = User::whereHas('fazaaServices.bookings')->firstOr(function () {
        $this->markTestSkipped('No service owner with bookings found.');
    });

    $booking = $owner->fazaaServices()
        ->whereHas('bookings')
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No bookings found for the service owner.');
    }

    $response = $this->actingAs($owner, 'api')
        ->getJson(route('app.users.services.fazaas.bookings.show', [
            'id' => $booking->id
        ]));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);
    expect($response->json('data'))->toHaveKeys([
        'id',
        'note',
        'created_at',
        'updated_at',
        'status',
        'accepted_at',
        'fazaa_service',
    ]);
});

it('cannot show booking details to unauthorized user', function () {
    $this->actingAs($this->user, 'api');

    $owner = User::whereHas('fazaaServices.bookings')->firstOr(function () {
        $this->markTestSkipped('No service owner with bookings found.');
    });

    $unauthorizedUser = User::where('id', '!=', $owner->id)->first();

    $booking = $owner->fazaaServices()
        ->whereHas('bookings')
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No bookings found for the service owner.');
    }

    $response = $this->actingAs($unauthorizedUser, 'api')
        ->getJson(route('app.users.services.fazaas.bookings.show', [
            'id' => $booking->id
        ]));

    expect($response->status())->toBe(404);
});

it('requires authentication', function () {
        $user = User::whereHas('fazaaServiceBookings')->firstOr(function () {
        $this->markTestSkipped('No user with fazaa service bookings found.');
    });

    $booking = $user->fazaaServiceBookings()->first();

    $response = $this->getJson(
        route('app.users.services.fazaas.bookings.show', [
            'id' => $booking->id
        ]),
        ['Accept' => 'application/json']
    );

    expect($response->status())->toBe(401);
});
