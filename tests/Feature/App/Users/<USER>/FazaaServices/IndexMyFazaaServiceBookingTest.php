<?php

use App\Models\User;
use function Pest\Laravel\get;

beforeEach(function () {
    $this->user = User::first();
});

it('can list my fazaa service bookings', function () {
    $this->actingAs($this->user, 'api');

    $user = User::whereHas('fazaaServiceBookings')->firstOr(function () {
        $this->markTestSkipped('No user with fazaa service bookings found.');
    });

    $response = get(route('app.users.services.fazaas.my-bookings.index'));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);
    expect($response->json('data.data.0'))->toHaveKeys([
        'id',
        'status',
        'note',
        'cancellation_reason',
        'rejection_reason',
        'created_at',
        'updated_at',
        'fazaa_service'
    ]);
});

it('paginates my fazaa service bookings', function () {
    $this->actingAs($this->user, 'api');

    $response = get(route('app.users.services.fazaas.my-bookings.index', ['per_page' => 10]));

    expect($response->status())->toBe(200);
    expect($response->json('data.per_page'))->toBe(10);
});

it('cannot see other users fazaa service bookings', function () {
    $this->actingAs($this->user, 'api');

    $otherUser = User::where('id', '!=', $this->user->id)
        ->whereHas('fazaaServiceBookings')
        ->firstOr(function () {
            $this->markTestSkipped('No other user with fazaa service bookings found.');
        });

    $otherUserBookings = $otherUser->fazaaServiceBookings()
        ->with(['fazaaService'])
        ->get();

    $response = get(route('app.users.services.fazaas.my-bookings.index'));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    // Verify that none of the returned bookings belong to the other user
    $returnedBookingIds = collect($response->json('data.data'))->pluck('id');
    $otherUserBookingIds = $otherUserBookings->pluck('id');

    expect($returnedBookingIds->intersect($otherUserBookingIds))->toBeEmpty();
});

it('requires authentication', function () {
    $response = get(route('app.users.services.fazaas.my-bookings.index'), [
        'Accept' => 'application/json'
    ]);

    expect($response->status())->toBe(401);
});
