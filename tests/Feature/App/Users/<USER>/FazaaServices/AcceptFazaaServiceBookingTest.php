<?php

use App\Enums\Bookings\BookingStatusEnum;
use App\Events\FazaaServiceBookingAcceptedEvent;
use App\Models\User;
use Illuminate\Support\Facades\Event;

beforeEach(function () {
    $this->user = User::first();
    $this->actingAs($this->user, 'api');
});

it('can accept a fazaa service booking', function () {
    Event::fake();

    $fazaaService = $this->user->fazaaServices()->whereHas('bookings', function ($query) {
        $query->where('status', BookingStatusEnum::PENDING());
    })->inRandomOrder()->firstOr(function () {
        $this->markTestSkipped('No pending fazaa service found.');
    });

    $booking = $fazaaService->bookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    $response = $this->postJson(
        route('app.users.services.fazaas.bookings.accept', [
            'id' => $booking->id,
        ])
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::ACCEPTED());

    Event::assertDispatched(FazaaServiceBookingAcceptedEvent::class);
});

it('cannot accept non pending booking', function () {
    $fazaaService = $this->user->fazaaServices()->whereHas('bookings', function ($query) {
        $query->where('status', BookingStatusEnum::ACCEPTED());
    })->inRandomOrder()->firstOr(function () {
        $this->markTestSkipped('No fazaa service found with accepted bookings.');
    });

    $booking = $fazaaService->bookings()
        ->where('status', BookingStatusEnum::ACCEPTED())
        ->firstOr(function () {
            $this->markTestSkipped('No accepted booking found.');
        });

    $response = $this->postJson(
        route('app.users.services.fazaas.bookings.accept', [
            'id' => $booking->id,
        ])
    );

    expect($response->status())->toBe(422);
    expect($response->json('message'))->toBe(__('exceptions.fazaa_service.booking.booking_is_not_pending'));

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::ACCEPTED());
});

it('cannot accept other users booking', function () {
    $otherUser = User::where('id', '!=', $this->user->id)->first();
    $fazaaService = $otherUser->fazaaServices()
        ->whereHas('bookings', function ($query) {
            $query->where('status', BookingStatusEnum::PENDING());
        })
        ->inRandomOrder()
        ->firstOr(function () {
            $this->markTestSkipped('No pending fazaa service found for other user.');
        });

    $booking = $fazaaService->bookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    $response = $this->postJson(
        route('app.users.services.fazaas.bookings.accept', [
            'id' => $booking->id,
        ])
    );

    expect($response->status())->toBe(404);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::PENDING());
});
