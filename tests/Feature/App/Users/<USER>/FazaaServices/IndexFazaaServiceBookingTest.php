<?php

use App\Models\User;

beforeEach(function () {
    $this->user = User::first();
    $this->actingAs($this->user, 'api');
});

it('can list fazaa service bookings', function () {
    $fazaaService = $this->user->fazaaServices()
        ->whereHas('bookings')
        ->firstOr(function () {
            $this->markTestSkipped('No fazaa service with bookings found.');
        });

    $response = $this->getJson(route('app.users.services.fazaas.bookings.index', [
        'id' => $fazaaService->id
    ]));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);
    expect($response->json('data.data.0'))->toHaveKeys([
        'id',
        'status',
        'note',
        'created_at',
        'updated_at',
    ]);
});

it('paginates fazaa service bookings', function () {
    $fazaaService = $this->user->fazaaServices()
        ->whereHas('bookings')
        ->firstOr(function () {
            $this->markTestSkipped('No fazaa service with bookings found.');
        });

    $response = $this->getJson(route('app.users.services.fazaas.bookings.index', [
        'id' => $fazaaService->id,
        'per_page' => 10
    ]));

    expect($response->status())->toBe(200);
    expect($response->json('data.per_page'))->toBe(10);
});

it('cannot list other users fazaa service bookings', function () {
    $otherUser = User::where('id', '!=', $this->user->id)
        ->whereHas('fazaaServices.bookings')
        ->firstOr(function () {
            $this->markTestSkipped('No other user with fazaa service bookings found.');
        });

    $fazaaService = $otherUser->fazaaServices()
        ->whereHas('bookings')
        ->first();

    $response = $this->getJson(route('app.users.services.fazaas.bookings.index', [
        'id' => $fazaaService->id
    ]));

    expect($response->status())->toBe(404);
});
