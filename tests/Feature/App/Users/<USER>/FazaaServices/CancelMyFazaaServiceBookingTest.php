<?php

use App\Enums\Bookings\BookingStatusEnum;
use App\Events\FazaaServiceBookingCancelledEvent;
use App\Models\User;
use Illuminate\Support\Facades\Event;

it('can cancel my fazaa service booking', function () {
    $user = User::first();
    $this->actingAs($user, 'api');
    Event::fake();

    $booking = $user->fazaaServiceBookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No pending fazaa service bookings found.');
    }

    $cancellationData = [
        'cancellation_reason' => 'Schedule conflict',
        'note' => 'Changed plans'
    ];

    $response = $this->postJson(
        route('app.users.services.fazaas.my-bookings.cancel', $booking->id),
        $cancellationData
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::CANCELLED());
    expect($booking->cancellation_reason)->toBe($cancellationData['cancellation_reason']);
    expect($booking->cancellation_note)->toBe($cancellationData['note']);

    Event::assertDispatched(FazaaServiceBookingCancelledEvent::class);
});

it('cannot cancel other users booking', function () {
    $user = User::first();
    $this->actingAs($user, 'api');

    $otherUser = User::where('id', '!=', $user->id)->first();

    $booking = $otherUser->fazaaServiceBookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No pending bookings found for other user.');
    }

    $cancellationData = [
        'cancellation_reason' => 'Schedule conflict',
        'note' => 'Changed plans'
    ];

    $response = $this->postJson(
        route('app.users.services.fazaas.my-bookings.cancel', $booking->id),
        $cancellationData
    );

    expect($response->status())->toBe(404);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::PENDING());
});

it('validates cancellation reasons', function () {
    $user = User::first();
    $this->actingAs($user, 'api');

    $booking = $user->fazaaServiceBookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No pending bookings found.');
    }

    // Test empty reason
    $response = $this->postJson(
        route('app.users.services.fazaas.my-bookings.cancel', $booking->id),
        ['cancellation_reason' => '']
    );

    expect($response->status())->toBe(422);

    // Test too long reason
    $response = $this->postJson(
        route('app.users.services.fazaas.my-bookings.cancel', $booking->id),
        [
            'cancellation_reason' => str_repeat('a', 256)
        ]
    );

    expect($response->status())->toBe(422);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::PENDING());
});

it('returns 404 for non-existent booking', function () {
    $user = User::first();
    $this->actingAs($user, 'api');

    $response = $this->postJson(
        route('app.users.services.fazaas.my-bookings.cancel', 999999999),
        [
            'cancellation_reason' => 'Schedule conflict',
            'note' => 'Changed plans'
        ]
    );

    expect($response->status())->toBe(404);
});

it('requires authentication', function () {
    $user = User::whereHas('fazaaServiceBookings')->first();
    $booking = $user->fazaaServiceBookings()->first();

    $response = $this->postJson(
        route('app.users.services.fazaas.my-bookings.cancel', $booking->id),
        [
            'cancellation_reason' => 'Schedule conflict',
            'note' => 'Changed plans'
        ],
        ['Accept' => 'application/json']
    );

    expect($response->status())->toBe(401);
});
