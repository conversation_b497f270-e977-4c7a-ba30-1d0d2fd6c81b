<?php

use App\Models\User;

it('can show booking details to booking owner', function () {
    $user = User::whereHas('fazaaServiceBookings')->firstOr(function () {
        $this->markTestSkipped('No user with fazaa service bookings found.');
    });

    $booking = $user->fazaaServiceBookings()->first();

    $response = $this->actingAs($user, 'api')
        ->getJson(route('app.users.services.fazaas.my-bookings.show', $booking->id));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    expect($response->json('data'))->toHaveKeys([
        'id',
        'status',
        'note',
        'accepted_at',
        'cancellation_reason',
        'rejection_reason',
        'created_at',
        'updated_at',
        'fazaa_service',
    ]);

    expect($response->json('data.fazaa_service'))->toHaveKeys([
        'id',
        'user',
        'from_city',
        'to_city',
        'price',
    ]);
});

it('cannot show booking details to unauthorized user', function () {
    $owner = User::whereHas('fazaaServiceBookings')->firstOr(function () {
        $this->markTestSkipped('No user with fazaa service bookings found.');
    });

    $unauthorizedUser = User::where('id', '!=', $owner->id)->first();
    $booking = $owner->fazaaServiceBookings()->first();

    $response = $this->actingAs($unauthorizedUser, 'api')
        ->getJson(route('app.users.services.fazaas.my-bookings.show', $booking->id));

    expect($response->status())->toBe(404);
});

it('requires authentication', function () {
    $user = User::whereHas('fazaaServiceBookings')->firstOr(function () {
        $this->markTestSkipped('No user with fazaa service bookings found.');
    });

    $booking = $user->fazaaServiceBookings()->first();

    $response = $this->getJson(
        route('app.users.services.fazaas.my-bookings.show', $booking->id),
        ['Accept' => 'application/json']
    );

    expect($response->status())->toBe(401);
});
