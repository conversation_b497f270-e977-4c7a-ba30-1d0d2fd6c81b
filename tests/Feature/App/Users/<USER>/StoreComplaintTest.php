<?php

use App\Enums\Bookings\BookingStatusEnum;
use App\Enums\Bookings\BookingTypeEnum;
use App\Enums\Complaints\ComplaintStatusEnum;
use App\Models\Booking\TripServiceBooking;
use App\Models\Complaint;
use App\Models\User;
use Database\Seeders\ComplaintSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;


beforeEach(function () {
    // Find a user with at least one cancelled booking and one pending booking
    $this->user = User::whereHas('tripServiceBookings', function ($query) {
        $query->where('status', BookingStatusEnum::CANCELLED_BY_OWNER());
    })->whereHas('tripServiceBookings', function ($query) {
        $query->where('status', BookingStatusEnum::PENDING());
    })->first();

    // If no user found with both types, find one with at least cancelled bookings
    if (!$this->user) {
        $this->user = User::whereHas('tripServiceBookings', function ($query) {
            $query->where('status', BookingStatusEnum::CANCELLED_BY_OWNER());
        })->first();
    }

    // If still no user found, find one with any bookings and log a warning
    if (!$this->user) {
        $this->markTestSkipped('No user with necessary booking statuses found.');
    }
});

it('can create a complaint for a trip service booking cancelled by owner', function () {
    $this->actingAs($this->user, 'api');

    $booking = $this->user->tripServiceBookings()
        ->where('status', BookingStatusEnum::CANCELLED_BY_OWNER())
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No cancelled booking found for test user');
    }

    $complaintData = [
        'booking_id' => $booking->id,
        'booking_type' => BookingTypeEnum::TRIP_SERVICE(),
        'description' => 'This is a test complaint about a cancelled booking.',
    ];

    $response = $this->postJson(
        route('app.users.complaints.store'),
        $complaintData
    );

    $response->assertStatus(200)
        ->assertJsonStructure([
            'success',
            'message',
            'data' => [
                'id',
                'description',
                'status',
                'created_at',
                'updated_at',
            ],
            'status_code'
        ]);

    expect(Complaint::where([
        'user_id' => $this->user->id,
        'booking_id' => $booking->id,
        'booking_type' => TripServiceBooking::class,
        'description' => $complaintData['description'],
        'status' => ComplaintStatusEnum::PENDING(),
    ])->exists())->toBeTrue();
});

it('cannot create a complaint for a booking that is not cancelled by owner', function () {
    $this->actingAs($this->user, 'api');

    $booking = $this->user->tripServiceBookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No pending booking found for test user');
    }

    $complaintData = [
        'booking_id' => $booking->id,
        'booking_type' => BookingTypeEnum::TRIP_SERVICE(),
        'description' => 'This is a test complaint about a non-cancelled booking.',
    ];

    $response = $this->postJson(
        route('app.users.complaints.store'),
        $complaintData
    );

    $response->assertStatus(422);

    expect(Complaint::where([
        'user_id' => $this->user->id,
        'booking_id' => $booking->id,
        'description' => $complaintData['description'],
    ])->exists())->toBeFalse();
});

it('cannot create a complaint for a booking that belongs to another user', function () {
    $this->actingAs($this->user, 'api');

    // Find another user with cancelled bookings
    $anotherUser = User::whereHas('tripServiceBookings', function ($query) {
        $query->where('status', BookingStatusEnum::CANCELLED_BY_OWNER())
            ->where('user_id', '!=', $this->user->id);
    })->first();

    if (!$anotherUser) {
        $this->markTestSkipped('No other user with cancelled bookings found for test');
    }

    $booking = $anotherUser->tripServiceBookings()
        ->where('status', BookingStatusEnum::CANCELLED_BY_OWNER())
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No cancelled booking found for other user');
    }

    $complaintData = [
        'booking_id' => $booking->id,
        'booking_type' => BookingTypeEnum::TRIP_SERVICE(),
        'description' => 'This is a test complaint about another user\'s booking.',
    ];

    $response = $this->postJson(
        route('app.users.complaints.store'),
        $complaintData
    );

    expect($response->status())->toBe(404);

    expect(Complaint::where([
        'user_id' => $this->user->id,
        'booking_id' => $booking->id,
        'description' => $complaintData['description'],
    ])->exists())->toBeFalse();
});

it('validates required fields when creating a complaint', function () {
    $this->actingAs($this->user, 'api');

    $response = $this->postJson(
        route('app.users.complaints.store'),
        []
    );

    expect($response->status())->toBe(422);
});

it('validates description length when creating a complaint', function () {
    $this->actingAs($this->user, 'api');

    $booking = $this->user->tripServiceBookings()
        ->where('status', BookingStatusEnum::CANCELLED_BY_OWNER())
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No cancelled booking found for test user');
    }

    $complaintData = [
        'booking_id' => $booking->id,
        'booking_type' => BookingTypeEnum::TRIP_SERVICE(),
        'description' => 'Too short', // Less than 10 characters
    ];

    $response = $this->postJson(
        route('app.users.complaints.store'),
        $complaintData
    );

    expect($response->status())->toBe(422);
});
