<?php

use App\Enums\Bookings\BookingStatusEnum;
use App\Events\TripRequestBookingCancelledByOwnerEvent;
use App\Models\User;
use Illuminate\Support\Facades\Event;

beforeEach(function () {
    $this->user = User::first();
});

it('can cancel a trip request booking', function () {
    $this->actingAs($this->user, 'api');

    Event::fake();

    $tripRequest = $this->user->tripRequests()->whereHas('bookings', function ($query) {
        $query->where('status', BookingStatusEnum::PENDING());
    })->inRandomOrder()->firstOr(function () {
        $this->markTestSkipped('No pending trip request found.');
    });

    $booking = $tripRequest->bookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    $cancellationData = [
        'cancellation_reason' => 'Schedule conflict',
        'note' => 'Changed plans'
    ];

    $response = $this->postJson(
        route('app.users.requests.trips.offers.cancel', [
            'id' => $booking->id,
        ]),
        $cancellationData
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::CANCELLED_BY_OWNER());
    expect($booking->cancellation_reason)->toBe($cancellationData['cancellation_reason']);
    expect($booking->cancellation_note)->toBe($cancellationData['note']);

    Event::assertDispatched(TripRequestBookingCancelledByOwnerEvent::class);
});


it('cannot cancel other users booking', function () {
    $this->actingAs($this->user, 'api');

    $otherUser = User::where('id', '!=', $this->user->id)->first();
    $tripRequest = $otherUser->tripRequests()
        ->whereHas('bookings', function ($query) {
            $query->where('status', BookingStatusEnum::PENDING());
        })
        ->inRandomOrder()
        ->firstOr(function () {
            $this->markTestSkipped('No pending trip request found for other user.');
        });

    $booking = $tripRequest->bookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    $cancellationData = [
        'cancellation_reason' => 'Schedule conflict',
        'note' => 'Changed plans'
    ];

    $response = $this->postJson(
        route('app.users.requests.trips.offers.cancel', [
            'id' => $booking->id,
        ]),
        $cancellationData
    );

    expect($response->status())->toBe(404);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::PENDING());
});

it('validates cancellation reasons', function () {
    $this->actingAs($this->user, 'api');

    $tripRequest = $this->user->tripRequests()->whereHas('bookings', function ($query) {
        $query->where('status', BookingStatusEnum::PENDING());
    })->inRandomOrder()->firstOr(function () {
        $this->markTestSkipped('No pending trip request found.');
    });

    $booking = $tripRequest->bookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    // Test empty reason
    $response = $this->postJson(
        route('app.users.requests.trips.offers.cancel', [
            'id' => $booking->id,
        ]),
        ['cancellation_reason' => '']
    );

    expect($response->status())->toBe(422);

    // Test too long reason
    $response = $this->postJson(
        route('app.users.requests.trips.offers.cancel', [
            'id' => $booking->id,
        ]),
        [
            'cancellation_reason' => str_repeat('a', 256)
        ]
    );

    expect($response->status())->toBe(422);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::PENDING());
});

it('returns 404 for non-existent booking', function () {
    $this->actingAs($this->user, 'api');

    $response = $this->postJson(
        route('app.users.requests.trips.offers.cancel', [
            'id' => 999999999,
        ]),
        [
            'cancellation_reason' => 'Schedule conflict',
            'note' => 'Changed plans'
        ]
    );

    expect($response->status())->toBe(404);
});

it('requires authentication', function () {
    $user = User::whereHas('tripRequests.bookings')->first();
    $booking = $user->tripRequests()->first()->bookings()->first();

    $response = $this->postJson(
        route('app.users.requests.trips.offers.cancel', [
            'id' => $booking->id,
        ]),
        [
            'cancellation_reason' => 'Schedule conflict',
            'note' => 'Changed plans'
        ],
        ['Accept' => 'application/json']
    );

    expect($response->status())->toBe(401);
});
