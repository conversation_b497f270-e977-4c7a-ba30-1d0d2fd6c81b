<?php

use App\Models\User;

it('can show booking details to trip request owner', function () {

    $owner = User::whereHas('tripRequests.bookings')->firstOr(function () {
        $this->markTestSkipped('No request owner with bookings found.');
    });

    $booking = $owner->tripRequests()
        ->with('bookings')
        ->first()
        ->bookings
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No bookings found for trip request owner.');
    }

    $response = $this->actingAs($owner, 'api')
        ->getJson(route('app.users.requests.trips.offers.show', [
            'id' => $booking->id
        ]));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);
    expect($response->json('data'))->toHaveKeys([
        'id',
        'note',
        'price',
        'created_at',
        'updated_at',
        'status',
        'accepted_at',
        'rejection_reason',
        'trip_request',
    ]);
});

it('cannot show booking details to unauthorized user', function () {
    $owner = User::whereHas('tripRequests.bookings')->firstOr(function () {
        $this->markTestSkipped('No request owner with bookings found.');
    });

    $unauthorizedUser = User::where('id', '!=', $owner->id)->first();

    $booking = $owner->tripRequests()
        ->with('bookings')
        ->first()
        ->bookings
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No bookings found for trip request owner.');
    }

    $response = $this->actingAs($unauthorizedUser, 'api')
        ->getJson(route('app.users.requests.trips.offers.show', [
            'id' => $booking->id
        ]));

    expect($response->status())->toBe(404);
});

it('requires authentication', function () {
    $user = User::whereHas('tripRequestBookings')->firstOr(function () {
        $this->markTestSkipped('No user with trip request bookings found.');
    });

    $booking = $user->tripRequestBookings()->first();

    if (!$booking) {
        $this->markTestSkipped('No bookings found for user.');
    }

    $response = $this->getJson(
        route('app.users.requests.trips.offers.show', [
            'id' => $booking->id
        ]),
        ['Accept' => 'application/json']
    );

    expect($response->status())->toBe(401);
});
