<?php

use App\Enums\Bookings\BookingStatusEnum;
use App\Events\TripRequestBookingCancelledEvent;
use App\Models\User;
use Illuminate\Support\Facades\Event;

beforeEach(function () {
    $this->user = User::first();
});

it('can cancel a trip request booking', function () {
    $this->actingAs($this->user, 'api');
    Event::fake();

    $booking = $this->user->tripRequestBookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No pending bookings found.');
    }

    $cancellationData = [
        'cancellation_reason' => 'Schedule conflict',
        'note' => 'Changed plans'
    ];

    $response = $this->postJson(
        route('app.users.requests.trips.my-offers.cancel', [
            'bookingId' => $booking->id
        ]),
        $cancellationData
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::CANCELLED());
    expect($booking->cancellation_reason)->toBe($cancellationData['cancellation_reason']);
    expect($booking->cancellation_note)->toBe($cancellationData['note']);

    Event::assertDispatched(TripRequestBookingCancelledEvent::class);
});

it('cannot cancel other users booking', function () {
    $this->actingAs($this->user, 'api');
    $otherUser = User::where('id', '!=', $this->user->id)->first();

    $booking = $otherUser->tripRequestBookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No pending bookings found for other user.');
    }

    $cancellationData = [
        'cancellation_reason' => 'Schedule conflict',
        'note' => 'Changed plans'
    ];

    $response = $this->postJson(
        route('app.users.requests.trips.my-offers.cancel', [
            'bookingId' => $booking->id
        ]),
        $cancellationData
    );

    expect($response->status())->toBe(404);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::PENDING());
});

it('validates cancellation reasons', function () {
    $this->actingAs($this->user, 'api');
    $booking = $this->user->tripRequestBookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No pending bookings found.');
    }

    // Test empty reason
    $response = $this->postJson(
        route('app.users.requests.trips.my-offers.cancel', [
            'bookingId' => $booking->id
        ]),
        ['cancellation_reason' => '']
    );

    expect($response->status())->toBe(422);

    // Test too long reason
    $response = $this->postJson(
        route('app.users.requests.trips.my-offers.cancel', [
            'bookingId' => $booking->id
        ]),
        [
            'cancellation_reason' => str_repeat('a', 256)
        ]
    );

    expect($response->status())->toBe(422);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::PENDING());
});

it('returns 404 for non-existent booking', function () {
    $this->actingAs($this->user, 'api');
    $response = $this->postJson(
        route('app.users.requests.trips.my-offers.cancel', [
            'bookingId' => 999999999
        ]),
        [
            'cancellation_reason' => 'Schedule conflict',
            'note' => 'Changed plans'
        ]
    );

    expect($response->status())->toBe(404);
});

it('requires authentication', function () {
    $user = User::whereHas('tripRequestBookings')->first();
    $booking = $user->tripRequestBookings()->first();

    $response = $this->postJson(
        route('app.users.requests.trips.my-offers.cancel', [
            'bookingId' => $booking->id
        ]),
        [
            'cancellation_reason' => 'Schedule conflict',
            'note' => 'Changed plans'
        ],
        ['Accept' => 'application/json']
    );

    expect($response->status())->toBe(401);
});
