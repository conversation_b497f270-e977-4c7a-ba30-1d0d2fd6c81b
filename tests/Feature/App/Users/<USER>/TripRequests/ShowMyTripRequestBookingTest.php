<?php

use App\Models\User;

it('can show booking details to booking owner', function () {
    $user = User::whereHas('tripRequestBookings')->firstOr(function () {
        $this->markTestSkipped('No user with trip request bookings found.');
    });

    $booking = $user->tripRequestBookings()->first();

    $response = $this->actingAs($user, 'api')
        ->getJson(route('app.users.requests.trips.my-offers.show', [
            'bookingId' => $booking->id
        ]));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    expect($response->json('data'))->toHaveKeys([
        'id',
        'note',
        'price',
        'created_at',
        'updated_at',
        'status',
        'accepted_at',
        'rejection_reason',
        'trip_request',
    ]);
});

it('cannot show booking details to unauthorized user', function () {
    $owner = User::whereHas('tripRequestBookings')->firstOr(function () {
        $this->markTestSkipped('No user with trip request bookings found.');
    });

    $unauthorizedUser = User::where('id', '!=', $owner->id)->first();
    $booking = $owner->tripRequestBookings()->first();

    $response = $this->actingAs($unauthorizedUser, 'api')
        ->getJson(route('app.users.requests.trips.my-offers.show', [
            'bookingId' => $booking->id
        ]));

    expect($response->status())->toBe(404);
});

it('requires authentication', function () {
    $user = User::whereHas('tripRequestBookings')->firstOr(function () {
        $this->markTestSkipped('No user with trip request bookings found.');
    });

    $booking = $user->tripRequestBookings()->first();

    $response = $this->getJson(
        route('app.users.requests.trips.my-offers.show', [
            'bookingId' => $booking->id
        ]),
        ['Accept' => 'application/json']
    );

    expect($response->status())->toBe(401);
});
