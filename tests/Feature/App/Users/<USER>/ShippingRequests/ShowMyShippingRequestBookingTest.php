<?php

use App\Models\User;
use App\Models\Booking\ShippingRequestBooking;

beforeEach(function () {
    $this->user = User::first();
});

it('can show user shipping request booking', function () {
    $this->actingAs($this->user, 'api');

    $booking = $this->user->shippingRequestBookings()
        ->with(['shippingRequest'])
        ->inRandomOrder()
        ->firstOr(function () {
            $this->markTestSkipped('No shipping request booking found for user.');
        });

    $response = $this->getJson(
        route('app.users.requests.shippings.my-offers.show', [
            'bookingId' => $booking->id,
        ])
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);
    expect($response->json('data'))->toHaveKeys([
        'id',
        'note',
        'package_price',
        'document_price',
        'furniture_price',
        'created_at',
        'updated_at',
        'shipping_request'
    ]);
});

it('cannot show other users shipping request booking', function () {
    $this->actingAs($this->user, 'api');

    $otherUser = User::where('id', '!=', $this->user->id)
        ->whereHas('shippingRequestBookings')
        ->firstOr(function () {
            $this->markTestSkipped('No other user with shipping request bookings found.');
        });

    $booking = $otherUser->shippingRequestBookings()
        ->inRandomOrder()
        ->first();

    $response = $this->getJson(
        route('app.users.requests.shippings.my-offers.show', [
            'bookingId' => $booking->id,
        ])
    );

    expect($response->status())->toBe(404);
});

it('requires authentication', function () {
    $user = User::whereHas('shippingRequestBookings')->first();
    $booking = $user->shippingRequestBookings()->first();

    $response = $this->getJson(
        route('app.users.requests.shippings.my-offers.show', [
            'bookingId' => $booking->id,
        ]),
        ['Accept' => 'application/json']
    );

    expect($response->status())->toBe(401);
});
