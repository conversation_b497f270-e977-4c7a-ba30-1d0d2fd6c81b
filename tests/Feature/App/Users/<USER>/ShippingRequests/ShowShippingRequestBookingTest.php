<?php

use App\Models\User;
use App\Models\Booking\ShippingRequestBooking;

beforeEach(function () {
    $this->user = User::first();
});

it('can show shipping request booking', function () {
    $this->actingAs($this->user, 'api');

    $booking = ShippingRequestBooking::with(['shippingRequest'])
        ->inRandomOrder()
        ->firstOr(function () {
            $this->markTestSkipped('No shipping request booking found.');
        });

    $response = $this->getJson(
        route('app.users.requests.shippings.offers.show', [
            'id' => $booking->id,
        ])
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);
    expect($response->json('data'))->toHaveKeys([
        'id',
        'note',
        'package_price',
        'document_price',
        'furniture_price',
        'created_at',
        'updated_at',
        'accepted_at',
        'shipping_request'
    ]);
});

it('returns 404 for non-existent booking', function () {
    $this->actingAs($this->user, 'api');

    $response = $this->getJson(
        route('app.users.requests.shippings.offers.show', [
            'id' => 999999999,
        ])
    );

    expect($response->status())->toBe(404);
});

it('requires authentication', function () {
    $booking = ShippingRequestBooking::first();

    $response = $this->getJson(
        route('app.users.requests.shippings.offers.show', [
            'id' => $booking->id,
        ]),
        ['Accept' => 'application/json']
    );

    expect($response->status())->toBe(401);
});
