<?php

use App\Enums\Bookings\BookingStatusEnum;
use App\Events\ShippingRequestBookingAccepted;
use App\Models\User;
use Illuminate\Support\Facades\Event;

beforeEach(function () {
    $this->user = User::first();
    $this->actingAs($this->user, 'api');
});

it('can accept a shipping request booking', function () {
    Event::fake();

    $shippingRequest = $this->user->shippingRequests()
        ->whereHas('bookings', function ($query) {
            $query->where('status', BookingStatusEnum::PENDING());
        })
        ->whereDoesntHave('bookings', function ($query) {
            $query->where('status', BookingStatusEnum::ACCEPTED());
        })
        ->inRandomOrder()
        ->firstOr(function () {
            $this->markTestSkipped('No pending shipping request found without accepted bookings.');
        });

    $booking = $shippingRequest->bookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    $response = $this->postJson(
        route('app.users.requests.shippings.offers.accept', [
            'id' => $booking->id,
        ])
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::ACCEPTED());

    Event::assertDispatched(ShippingRequestBookingAccepted::class);
});

it('cannot accept non pending booking', function () {
    $shippingRequest = $this->user->shippingRequests()
        ->whereHas('bookings', function ($query) {
            $query->where('status', BookingStatusEnum::ACCEPTED());
        })
        ->inRandomOrder()
        ->firstOr(function () {
            $this->markTestSkipped('No shipping request found with accepted bookings.');
        });

    $booking = $shippingRequest->bookings()
        ->where('status', BookingStatusEnum::ACCEPTED())
        ->firstOr(function () {
            $this->markTestSkipped('No accepted booking found.');
        });

    $response = $this->postJson(
        route('app.users.requests.shippings.offers.accept', [
            'id' => $booking->id,
        ])
    );

    expect($response->status())->toBe(422);
    expect($response->json('message'))->toBe(__('exceptions.shipping_request.booking.booking_is_not_pending'));

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::ACCEPTED());
});

it('cannot accept other users booking', function () {
    $otherUser = User::where('id', '!=', $this->user->id)->first();
    $shippingRequest = $otherUser->shippingRequests()
        ->whereHas('bookings', function ($query) {
            $query->where('status', BookingStatusEnum::PENDING());
        })
        ->inRandomOrder()
        ->firstOr(function () {
            $this->markTestSkipped('No pending shipping request found for other user.');
        });

    $booking = $shippingRequest->bookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    $response = $this->postJson(
        route('app.users.requests.shippings.offers.accept', [
            'id' => $booking->id,
        ])
    );

    expect($response->status())->toBe(404);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::PENDING());
});

it('cannot accept booking when another booking is already accepted', function () {
    $shippingRequest = $this->user->shippingRequests()
        ->whereHas('bookings', function ($query) {
            $query->where('status', BookingStatusEnum::ACCEPTED());
        })
        ->whereHas('bookings', function ($query) {
            $query->where('status', BookingStatusEnum::PENDING());
        })
        ->inRandomOrder()
        ->firstOr(function () {
            $this->markTestSkipped('No shipping request found with both pending and accepted bookings.');
        });

    $pendingBooking = $shippingRequest->bookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    $response = $this->postJson(
        route('app.users.requests.shippings.offers.accept', [
            'id' => $pendingBooking->id,
        ])
    );

    expect($response->status())->toBe(422);
    expect($response->json('message'))->toBe(__('exceptions.shipping_request.booking.another_booking_already_accepted'));

    $pendingBooking->refresh();
    expect($pendingBooking->status)->toBe(BookingStatusEnum::PENDING());
});
