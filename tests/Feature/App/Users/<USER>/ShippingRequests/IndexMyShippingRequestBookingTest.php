<?php

use App\Models\User;
use App\Models\Booking\ShippingRequestBooking;
use App\Models\Request\ShippingRequest;
use Carbon\Carbon;

use function Pest\Laravel\get;

beforeEach(function () {
    $this->user = User::first();
});

it('can get user shipping request bookings', function () {
    $this->actingAs($this->user, 'api');

    $user = User::whereHas('shippingRequestBookings')->firstOr(function () {
        $this->markTestSkipped('No user with shipping request bookings found.');
    });

    $response = get(route('app.users.requests.shippings.my-offers.index'));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);
    expect($response->json('data.data.0'))->toHaveKeys([
        'id',
        'note',
        'package_price',
        'document_price',
        'furniture_price',
        'created_at',
        'updated_at',
        'shipping_request'
    ]);
});

it('cannot see other users shipping request bookings', function () {
    $this->actingAs($this->user, 'api');

    $otherUser = User::where('id', '!=', $this->user->id)
        ->whereHas('shippingRequestBookings')
        ->firstOr(function () {
            $this->markTestSkipped('No other user with shipping request bookings found.');
        });

    $otherUserBookings = $otherUser->shippingRequestBookings()
        ->with(['shippingRequest'])
        ->get();

    $response = get(route('app.users.requests.shippings.my-offers.index'));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    // Verify that none of the returned bookings belong to the other user
    $returnedBookingIds = collect($response->json('data'))->pluck('id');
    $otherUserBookingIds = $otherUserBookings->pluck('id');

    expect($returnedBookingIds->intersect($otherUserBookingIds))->toBeEmpty();
});

it('requires authentication', function () {
    $response = get(route('app.users.requests.shippings.my-offers.index'), [
        'Accept' => 'application/json'
    ]);

    expect($response->status())->toBe(401);
});
