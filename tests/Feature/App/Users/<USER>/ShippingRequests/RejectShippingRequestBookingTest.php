<?php

use App\Enums\Bookings\BookingStatusEnum;
use App\Events\ShippingRequestBookingRejected;
use App\Models\User;
use Illuminate\Support\Facades\Event;

beforeEach(function () {
    $this->user = User::first();
    $this->actingAs($this->user, 'api');
});

it('can reject a shipping request booking', function () {
    Event::fake();

    $shippingRequest = $this->user->shippingRequests()->whereHas('bookings', function ($query) {
        $query->where('status', BookingStatusEnum::PENDING());
    })->inRandomOrder()->firstOr(function () {
        $this->markTestSkipped('No pending shipping request found.');
    });

    $booking = $shippingRequest->bookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    $rejectionData = [
        'rejection_reason' => 'Schedule conflict',
        'note' => 'Cannot accommodate the request at this time'
    ];

    $response = $this->postJson(
        route('app.users.requests.shippings.offers.reject', [
            'id' => $booking->id,
        ]),
        $rejectionData
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::REJECTED());
    expect($booking->rejection_reason)->toBe($rejectionData['rejection_reason']);
    expect($booking->rejection_note)->toBe($rejectionData['note']);

    Event::assertDispatched(ShippingRequestBookingRejected::class);
});

it('cannot reject non pending booking', function () {
    $shippingRequest = $this->user->shippingRequests()->whereHas('bookings', function ($query) {
        $query->where('status', BookingStatusEnum::REJECTED());
    })->inRandomOrder()->firstOr(function () {
        $this->markTestSkipped('No rejected shipping request found.');
    });

    $booking = $shippingRequest->bookings()
        ->where('status', BookingStatusEnum::REJECTED())
        ->first();

    $response = $this->postJson(
        route('app.users.requests.shippings.offers.reject', [
            'id' => $booking->id,
        ]),
        ['rejection_reason' => 'Schedule conflict']
    );

    expect($response->status())->toBe(422);
    expect($response->json('message'))->toBe(__('exceptions.shipping_request.booking.booking_is_not_pending'));

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::REJECTED());
});

it('cannot reject other users booking', function () {
    $otherUser = User::where('id', '!=', $this->user->id)->first();
    $shippingRequest = $otherUser->shippingRequests()
        ->whereHas('bookings', function ($query) {
            $query->where('status', BookingStatusEnum::PENDING());
        })
        ->inRandomOrder()
        ->firstOr(function () {
            $this->markTestSkipped('No pending shipping request found for other user.');
        });

    $booking = $shippingRequest->bookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    $response = $this->postJson(
        route('app.users.requests.shippings.offers.reject', [
            'id' => $booking->id,
        ]),
        ['rejection_reason' => 'Schedule conflict']
    );

    expect($response->status())->toBe(404);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::PENDING());
});

it('validates rejection reasons', function () {
    $shippingRequest = $this->user->shippingRequests()->whereHas('bookings', function ($query) {
        $query->where('status', BookingStatusEnum::PENDING());
    })->inRandomOrder()->firstOr(function () {
        $this->markTestSkipped('No pending shipping request found.');
    });

    $booking = $shippingRequest->bookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    // Test empty reason
    $response = $this->postJson(
        route('app.users.requests.shippings.offers.reject', [
            'id' => $booking->id
        ]),
        ['rejection_reason' => '']
    );

    expect($response->status())->toBe(422);

    // Test too long reason
    $response = $this->postJson(
        route('app.users.requests.shippings.offers.reject', [
            'id' => $booking->id
        ]),
        [
            'rejection_reason' => str_repeat('a', 256)
        ]
    );

    expect($response->status())->toBe(422);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::PENDING());
});
