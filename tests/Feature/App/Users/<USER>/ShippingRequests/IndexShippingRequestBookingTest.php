<?php

use App\Models\User;
use App\Models\Request\ShippingRequest;
use App\Models\Booking\ShippingRequestBooking;

it('can list shipping request bookings', function () {
    $user = User::first();
    $this->actingAs($user, 'api');

    $shippingRequest = ShippingRequest::whereHas('bookings')
        ->inRandomOrder()
        ->firstOr(function () {
            $this->markTestSkipped('No shipping request with bookings found.');
        });

    $response = $this->getJson(
        route('app.users.requests.shippings.offers.index', [
            'id' => $shippingRequest->id,
        ])
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);
    expect($response->json('data.data.0'))->toHaveKeys([
        'id',
        'note',
        'package_price',
        'document_price',
        'furniture_price',
        'created_at',
        'updated_at',
    ]);
});

it('returns empty array for shipping request without bookings', function () {
    $user = User::first();
    $this->actingAs($user, 'api');

    $shippingRequest = ShippingRequest::doesntHave('bookings')
        ->inRandomOrder()
        ->firstOr(function () {
            $this->markTestSkipped('No shipping request without bookings found.');
        });

    $response = $this->getJson(
        route('app.users.requests.shippings.offers.index', [
            'id' => $shippingRequest->id,
        ])
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);
    expect($response->json('data.data'))->toBeArray()->toBeEmpty();
});

it('returns 404 for non-existent shipping request', function () {
    $user = User::first();
    $this->actingAs($user, 'api');

    $response = $this->getJson(
        route('app.users.requests.shippings.offers.index', [
            'id' => 999999999,
        ])
    );

    expect($response->status())->toBe(404);
});

it('requires authentication', function () {
    $shippingRequest = ShippingRequest::whereHas('bookings')->first();

    $response = $this->getJson(
        route('app.users.requests.shippings.offers.index', [
            'id' => $shippingRequest->id,
        ]),
        ['Accept' => 'application/json']
    );

    expect($response->status())->toBe(401);
});
