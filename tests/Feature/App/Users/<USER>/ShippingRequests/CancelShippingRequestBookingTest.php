<?php

use App\Models\User;
use App\Models\Booking\ShippingRequestBooking;
use App\Events\ShippingRequestBookingCancelled;
use Illuminate\Support\Facades\Event;
use App\Enums\Bookings\BookingStatusEnum;
use App\Events\ShippingRequestBookingCancelledByOwnerEvent;

beforeEach(function () {
    $this->user = User::first();
});

it('can cancel shipping request booking', function () {
    $this->actingAs($this->user, 'api');
    Event::fake();

    $shippingRequest = $this->user->shippingRequests()
        ->whereHas('bookings', function ($query) {
            $query->where('status', BookingStatusEnum::PENDING());
        })
        ->firstOr(function () {
            $this->markTestSkipped('No shipping request with pending bookings found.');
        });

    $booking = $shippingRequest->bookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    $cancellationData = [
        'cancellation_reason' => 'Schedule conflict',
        'note' => 'Changed plans'
    ];

    $response = $this->postJson(
        route('app.users.requests.shippings.offers.cancel', [
            'id' => $booking->id,
        ]),
        $cancellationData
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::CANCELLED_BY_OWNER());
    expect($booking->cancellation_reason)->toBe($cancellationData['cancellation_reason']);
    expect($booking->cancellation_note)->toBe($cancellationData['note']);

    Event::assertDispatched(ShippingRequestBookingCancelledByOwnerEvent::class);
});

it('cannot cancel booking of other users shipping request', function () {
    $this->actingAs($this->user, 'api');

    $otherUser = User::where('id', '!=', $this->user->id)
        ->whereHas('shippingRequests', function ($query) {
            $query->whereHas('bookings', function ($q) {
                $q->where('status', BookingStatusEnum::PENDING());
            });
        })
        ->firstOr(function () {
            $this->markTestSkipped('No other user with shipping requests and pending bookings found.');
        });

    $shippingRequest = $otherUser->shippingRequests()
        ->whereHas('bookings', function ($query) {
            $query->where('status', BookingStatusEnum::PENDING());
        })
        ->first();

    $booking = $shippingRequest->bookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    $cancellationData = [
        'cancellation_reason' => 'Schedule conflict',
        'note' => 'Changed plans'
    ];

    $response = $this->postJson(
        route('app.users.requests.shippings.offers.cancel', [
            'id' => $booking->id,
        ]),
        $cancellationData
    );

    expect($response->status())->toBe(404);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::PENDING());
});

it('validates cancellation reasons', function () {
    $this->actingAs($this->user, 'api');

    $shippingRequest = $this->user->shippingRequests()
        ->whereHas('bookings', function ($query) {
            $query->where('status', BookingStatusEnum::PENDING());
        })
        ->firstOr(function () {
            $this->markTestSkipped('No shipping request with pending bookings found.');
        });

    $booking = $shippingRequest->bookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    // Test empty reason
    $response = $this->postJson(
        route('app.users.requests.shippings.offers.cancel', [
            'id' => $booking->id,
        ]),
        ['cancellation_reason' => '']
    );

    expect($response->status())->toBe(422);

    // Test too long reason
    $response = $this->postJson(
        route('app.users.requests.shippings.offers.cancel', [
            'id' => $booking->id,
        ]),
        [
            'cancellation_reason' => str_repeat('a', 256)
        ]
    );

    expect($response->status())->toBe(422);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::PENDING());
});

it('returns 404 for non-existent booking', function () {
    $this->actingAs($this->user, 'api');

    $response = $this->postJson(
        route('app.users.requests.shippings.offers.cancel', [
            'id' => 999999999,
        ]),
        [
            'cancellation_reason' => 'Schedule conflict',
            'note' => 'Changed plans'
        ]
    );

    expect($response->status())->toBe(404);
});

it('requires authentication', function () {
    $user = User::whereHas('shippingRequests', function ($query) {
        $query->whereHas('bookings');
    })->first();

    $shippingRequest = $user->shippingRequests()
        ->whereHas('bookings')
        ->first();

    $booking = $shippingRequest->bookings()->first();

    $response = $this->postJson(
        route('app.users.requests.shippings.offers.cancel', [
            'id' => $booking->id,
        ]),
        [
            'cancellation_reason' => 'Schedule conflict',
            'note' => 'Changed plans'
        ],
        ['Accept' => 'application/json']
    );

    expect($response->status())->toBe(401);
    expect(ShippingRequestBooking::find($booking->id))->not->toBeNull();
});
