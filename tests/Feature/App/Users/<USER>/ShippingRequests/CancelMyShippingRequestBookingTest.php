<?php

use App\Enums\Bookings\BookingStatusEnum;
use App\Events\ShippingRequestBookingCancelled;
use App\Models\User;
use Illuminate\Support\Facades\Event;

beforeEach(function () {
    $this->user = User::first();
    $this->actingAs($this->user, 'api');
});

it('can cancel a shipping request booking', function () {
    Event::fake();

    $booking = $this->user->shippingRequestBookings()
        ->where('status', '!=', BookingStatusEnum::REJECTED())
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No pending bookings found.');
    }

    $cancellationData = [
        'cancellation_reason' => 'Schedule conflict',
        'note' => 'Had to cancel due to a scheduling issue'
    ];

    $response = $this->postJson(
        route('app.users.requests.shippings.my-offers.cancel', [
            'bookingId' => $booking->id
        ]),
        $cancellationData
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::CANCELLED());
    expect($booking->cancellation_reason)->toBe($cancellationData['cancellation_reason']);
    expect($booking->cancellation_note)->toBe($cancellationData['note']);

    Event::assertDispatched(ShippingRequestBookingCancelled::class);
});

it('cannot cancel other users booking', function () {
    $otherUser = User::where('id', '!=', $this->user->id)->first();

    $booking = $otherUser->shippingRequestBookings()
        ->where('status', '!=', BookingStatusEnum::REJECTED())
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No pending bookings found for other user.');
    }

    $cancellationData = [
        'cancellation_reason' => 'Schedule conflict',
        'note' => 'Had to cancel due to a scheduling issue'
    ];

    $response = $this->postJson(
        route('app.users.requests.shippings.my-offers.cancel', [
            'bookingId' => $booking->id
        ]),
        $cancellationData
    );

    expect($response->status())->toBe(404);

    $booking->refresh();
});

it('validates cancellation reasons', function () {
    $booking = $this->user->shippingRequestBookings()
        ->where('status', '!=', BookingStatusEnum::REJECTED())
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No pending bookings found.');
    }

    // Test empty reason
    $response = $this->postJson(
        route('app.users.requests.shippings.my-offers.cancel', [
            'bookingId' => $booking->id
        ]),
        ['cancellation_reason' => '']
    );

    expect($response->status())->toBe(422);

    // Test too long reason
    $response = $this->postJson(
        route('app.users.requests.shippings.my-offers.cancel', [
            'bookingId' => $booking->id
        ]),
        [
            'cancellation_reason' => str_repeat('a', 256)
        ]
    );

    expect($response->status())->toBe(422);

    $booking->refresh();
});
