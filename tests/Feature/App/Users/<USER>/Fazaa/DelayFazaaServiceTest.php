<?php

use App\Models\User;
use App\Models\Service\FazaaService;
use Carbon\Carbon;

use function Pest\Laravel\patch;

beforeEach(function () {
    $this->user = User::whereHas('fazaaServices')->inRandomOrder()->first();
});

it('can delay a fazaa service', function () {
    $fazaaService = $this->user->fazaaServices()->first();
    $fazaaService->update([
        'departure_datetime' => Carbon::now()->addDay(),
        'arrival_datetime' => Carbon::now()->addDay()->addHours(2),
        'cancelled_at' => null
    ]);

    $newDepartureDateTime = Carbon::now()->addDays(2);
    $newArrivalDateTime = Carbon::now()->addDays(2)->addHours(2);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.services.fazaas.delay', $fazaaService->id),
        [
            'departure_datetime' => $newDepartureDateTime->format('Y-m-d H:i:s'),
            'arrival_datetime' => $newArrivalDateTime->format('Y-m-d H:i:s'),
            'delay_reason' => 'medical_reason',
            'note' => 'Need to delay due to medical appointment'
        ]
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $fazaaService->refresh();
    expect($fazaaService->departure_datetime->format('Y-m-d H:i:s'))->toBe($newDepartureDateTime->format('Y-m-d H:i:s'));
    expect($fazaaService->arrival_datetime->format('Y-m-d H:i:s'))->toBe($newArrivalDateTime->format('Y-m-d H:i:s'));
    expect($fazaaService->delay_reason)->toBe('medical_reason');
    expect($fazaaService->delay_note)->toBe('Need to delay due to medical appointment');
    expect($fazaaService->delayed_at)->not->toBeNull();
});

it('validates delay reasons', function () {
    $fazaaService = $this->user->fazaaServices()->first();
    $fazaaService->update([
        'departure_datetime' => Carbon::now()->addDay(),
        'arrival_datetime' => Carbon::now()->addDay()->addHours(2),
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.services.fazaas.delay', $fazaaService->id),
        [
            'departure_datetime' => Carbon::now()->addDays(2),
            'arrival_datetime' => Carbon::now()->addDays(2)->addHours(2),
            'delay_reason' => ['invalid_reason'],
            'note' => 'Invalid reason test'
        ]
    );

    expect($response->status())->toBe(422);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.services.fazaas.delay', $fazaaService->id),
        [
            'departure_datetime' => Carbon::now()->addDays(2),
            'arrival_datetime' => Carbon::now()->addDays(2)->addHours(2),
            'delay_reason' => '',
            'note' => 'Empty reason test'
        ]
    );

    expect($response->status())->toBe(422);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.services.fazaas.delay', $fazaaService->id),
        [
            'departure_datetime' => Carbon::now()->addDays(2),
            'arrival_datetime' => Carbon::now()->addDays(2)->addHours(2),
            'delay_reason' => str_repeat('a', 256),
            'note' => 'Too long reason test'
        ]
    );

    expect($response->status())->toBe(422);
});

it('cannot delay cancelled fazaa service', function () {
    $fazaaService = $this->user->fazaaServices()->first();
    $fazaaService->update([
        'departure_datetime' => Carbon::now()->addHour(),
        'arrival_datetime' => Carbon::now()->addHours(2),
        'cancelled_at' => Carbon::now()
    ]);

    $newDepartureTime = Carbon::now()->addHours(3);
    $newArrivalTime = Carbon::now()->addHours(4);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.services.fazaas.delay', $fazaaService->id),
        [
            'departure_datetime' => $newDepartureTime->toDateTimeString(),
            'arrival_datetime' => $newArrivalTime->toDateTimeString(),
            'delay_reason' => 'medical_reason',
            'note' => 'Medical appointment'
        ]
    );

    expect($response->status())->toBe(422);
});

it('cannot delay started fazaa service', function () {
    $fazaaService = $this->user->fazaaServices()->first();
    $fazaaService->update([
        'departure_datetime' => Carbon::now()->subHour(),
        'arrival_datetime' => Carbon::now()->addHour(),
        'cancelled_at' => null
    ]);

    $newDepartureTime = Carbon::now()->addHours(3);
    $newArrivalTime = Carbon::now()->addHours(4);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.services.fazaas.delay', $fazaaService->id),
        [
            'departure_datetime' => $newDepartureTime->toDateTimeString(),
            'arrival_datetime' => $newArrivalTime->toDateTimeString(),
            'delay_reason' => 'medical_reason',
            'note' => 'Medical appointment'
        ]
    );

    expect($response->status())->toBe(422);
});

it('cannot delay non-owned fazaa service', function () {
    $otherUser = User::where('id', '!=', $this->user->id)->whereHas('fazaaServices')->inRandomOrder()->first();
    $fazaaService = $otherUser->fazaaServices()->first();
    $fazaaService->update([
        'departure_datetime' => Carbon::now()->addHour(),
        'arrival_datetime' => Carbon::now()->addHours(2),
        'cancelled_at' => null
    ]);

    $newDepartureTime = Carbon::now()->addHours(3);
    $newArrivalTime = Carbon::now()->addHours(4);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.services.fazaas.delay', $fazaaService->id),
        [
            'departure_datetime' => $newDepartureTime->toDateTimeString(),
            'arrival_datetime' => $newArrivalTime->toDateTimeString(),
            'delay_reason' => 'medical_reason',
            'note' => 'Medical appointment'
        ]
    );

    expect($response->status())->toBe(404);
});
