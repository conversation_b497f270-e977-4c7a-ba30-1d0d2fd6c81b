<?php

use App\Models\User;
use App\Models\Service\FazaaService;
use Illuminate\Support\Facades\Event;

beforeEach(function () {
    $this->user = User::first();
});

it('can show my fazaa service details', function () {
    $this->actingAs($this->user, 'api');

    Event::fake();

    $fazaaService = $this->user->fazaaServices()->first();

    if (!$fazaaService) {
        $this->markTestSkipped('No fazaa services found.');
    }

    $response = $this->getJson(
        route('app.users.services.fazaas.show', [
            'id' => $fazaaService->id
        ])
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $serviceData = $response->json('data');
    expect($serviceData)->toHaveKeys([
        'id',
        'fazaa_service_type',
        'specific_type',
        'transportation_type',
        'departure_datetime',
        'arrival_datetime',
        'from_city',
        'from_location',
        'to_city',
        'to_location',
        'service_location',
        'price',
        'note',
    ]);
    if ($serviceData['fazaa_service_type'] === 'receive_shipment') {
        expect($serviceData['shipment_volume'])->not->toBeNull();
    }
});

it('cannot show other users fazaa service details', function () {
    $this->actingAs($this->user, 'api');

    $otherUser = User::where('id', '!=', $this->user->id)->first();

    $fazaaService = $otherUser->fazaaServices()->first();

    if (!$fazaaService) {
        $this->markTestSkipped('No fazaa services found for other user.');
    }

    $response = $this->getJson(
        route('app.users.services.fazaas.show', [
            'id' => $fazaaService->id
        ])
    );

    expect($response->status())->toBe(404);
});

it('returns 404 for non-existent fazaa service', function () {
    $this->actingAs($this->user, 'api');

    $response = $this->getJson(
        route('app.users.services.fazaas.show', [
            'id' => 999999999
        ])
    );

    expect($response->status())->toBe(404);
});

it('requires authentication', function () {
    $fazaaService = FazaaService::first();

    $response = $this->getJson(
        route('app.users.services.fazaas.show', [
            'id' => $fazaaService->id
        ])
    );

    expect($response->status())->toBe(401);
});
