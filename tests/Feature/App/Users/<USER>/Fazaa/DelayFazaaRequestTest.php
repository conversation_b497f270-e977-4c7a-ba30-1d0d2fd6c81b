<?php

use App\Models\User;
use Carbon\Carbon;

use function Pest\Laravel\patch;

beforeEach(function () {
    $this->user = User::whereHas('fazaaRequests')->inRandomOrder()->first();
    $this->token = $this->user->createToken('test-token')->plainTextToken;
});

it('can delay a fazaa request', function () {
    $fazaaRequest = $this->user->fazaaRequests()->first();
    $fazaaRequest->update([
        'departure_datetime' => Carbon::now()->addHour(),
        'arrival_datetime' => Carbon::now()->addHours(2),
        'delay_reason' => 'medical_reason',
        'note' => 'Medical appointment'
    ]);

    $newDepartureTime = Carbon::now()->addHours(3);
    $newArrivalTime = Carbon::now()->addHours(4);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.requests.fazaas.delay', $fazaaRequest->id),
        [
            'departure_datetime' => $newDepartureTime->toDateTimeString(),
            'arrival_datetime' => $newArrivalTime->toDateTimeString(),
            'delay_reason' => 'medical_reason',
            'note' => 'Medical appointment'
        ]
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $fazaaRequest->refresh();
    expect($fazaaRequest->departure_datetime->timestamp)->toBe($newDepartureTime->timestamp);
    expect($fazaaRequest->arrival_datetime->timestamp)->toBe($newArrivalTime->timestamp);
    expect($fazaaRequest->delay_reason)->toBe('medical_reason');
    expect($fazaaRequest->delay_note)->toBe('Medical appointment');
    expect($fazaaRequest->delayed_at)->not->toBeNull();
});

it('validates datetime format when delaying a fazaa request', function () {
    $fazaaRequest = $this->user->fazaaRequests()->first();
    $fazaaRequest->update([
        'departure_datetime' => Carbon::now()->addHour(),
        'arrival_datetime' => Carbon::now()->addHours(2),
    ]);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.requests.fazaas.delay', $fazaaRequest->id),
        [
            'departure_datetime' => 'invalid-date',
            'arrival_datetime' => 'invalid-date',
            'delay_reason' => 'medical_reason',
            'note' => 'Medical appointment'
        ]
    );

    expect($response->status())->toBe(422);
});

it('validates delay reasons', function () {
    $fazaaRequest = $this->user->fazaaRequests()->first();
    $fazaaRequest->update([
        'departure_datetime' => Carbon::now()->addHour(),
        'arrival_datetime' => Carbon::now()->addHours(2),
    ]);

    $newDepartureTime = Carbon::now()->addHours(3);
    $newArrivalTime = Carbon::now()->addHours(4);

    // Test invalid reason
    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.requests.fazaas.delay', $fazaaRequest->id),
        [
            'delay_reason' => 'invalid_reason',
            'note' => 'Invalid reason test'
        ]
    );

    expect($response->status())->toBe(422);

    // Test empty reason
    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.requests.fazaas.delay', $fazaaRequest->id),
        [
            'delay_reason' => '',
            'note' => 'Empty reason test'
        ]
    );

    expect($response->status())->toBe(422);

    // Test too long reason
    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.requests.fazaas.delay', $fazaaRequest->id),
        [
            'delay_reason' => str_repeat('a', 256),
            'note' => 'Too long reason test'
        ]
    );

    expect($response->status())->toBe(422);
});

it('cannot delay cancelled fazaa request', function () {
    $fazaaRequest = $this->user->fazaaRequests()->first();
    $fazaaRequest->update([
        'departure_datetime' => Carbon::now()->addHour(),
        'arrival_datetime' => Carbon::now()->addHours(2),
        'cancelled_at' => Carbon::now()
    ]);

    $newDepartureTime = Carbon::now()->addHours(3);
    $newArrivalTime = Carbon::now()->addHours(4);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.requests.fazaas.delay', $fazaaRequest->id),
        [
            'departure_datetime' => $newDepartureTime->toDateTimeString(),
            'arrival_datetime' => $newArrivalTime->toDateTimeString(),
            'delay_reason' => 'medical_reason',
            'note' => 'Medical appointment'
        ]
    );

    expect($response->status())->toBe(422);
});

it('cannot delay started fazaa request', function () {
    $fazaaRequest = $this->user->fazaaRequests()->first();
    $fazaaRequest->update([
        'departure_datetime' => Carbon::now()->subHour(),
        'arrival_datetime' => Carbon::now()->addHour(),
    ]);

    $newDepartureTime = Carbon::now()->addHours(3);
    $newArrivalTime = Carbon::now()->addHours(4);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.requests.fazaas.delay', $fazaaRequest->id),
        [
            'departure_datetime' => $newDepartureTime->toDateTimeString(),
            'arrival_datetime' => $newArrivalTime->toDateTimeString(),
            'delay_reason' => 'medical_reason',
            'note' => 'Medical appointment'
        ]
    );

    expect($response->status())->toBe(422);
});

it('cannot delay non-owned fazaa request', function () {
    $otherUser = User::where('id', '!=', $this->user->id)->whereHas('fazaaRequests')->inRandomOrder()->first();
    $fazaaRequest = $otherUser->fazaaRequests()->first();
    $fazaaRequest->update([
        'departure_datetime' => Carbon::now()->addHour(),
        'arrival_datetime' => Carbon::now()->addHours(2),
    ]);

    $newDepartureTime = Carbon::now()->addHours(3);
    $newArrivalTime = Carbon::now()->addHours(4);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.requests.fazaas.delay', $fazaaRequest->id),
        [
            'departure_datetime' => $newDepartureTime->toDateTimeString(),
            'arrival_datetime' => $newArrivalTime->toDateTimeString(),
            'delay_reason' => 'medical_reason',
            'note' => 'Medical appointment'
        ]
    );

    expect($response->status())->toBe(404);
});
