<?php

use App\Models\User;
use App\Models\Service\FazaaService;
use Carbon\Carbon;
use App\Enums\Travel\TripStatusEnum;

use function Pest\Laravel\post;

beforeEach(function () {
    $this->user = User::whereHas('fazaaServices')->inRandomOrder()->first();
    $this->token = $this->user->createToken('test-token')->plainTextToken;
});

it('can cancel a fazaa service', function () {
    $fazaaService = $this->user->fazaaServices()->first();
    $fazaaService->update([
        'departure_datetime' => Carbon::now()->addHour(),
        'cancelled_at' => null
    ]);

    $data = [
        'cancellation_reason' => 'test reason',
        'note' => 'Service no longer available'
    ];

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.services.fazaas.cancel', $fazaaService->id),
        $data
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $fazaaService->refresh();
    expect($fazaaService->cancelled_at)->not->toBeNull();
    expect($fazaaService->cancellation_reason)->toBe($data['cancellation_reason']);
    expect($fazaaService->cancellation_note)->toBe($data['note']);
    expect($fazaaService->status)->toBe(TripStatusEnum::CANCELLED());
});

it('cannot cancel already cancelled fazaa service', function () {
    $fazaaService = $this->user->fazaaServices()->first();
    $fazaaService->update([
        'departure_datetime' => Carbon::now()->addHour(),
        'cancelled_at' => Carbon::now()
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.services.fazaas.cancel', $fazaaService->id)
    );

    expect($response->status())->toBe(422);
});

it('cannot cancel started fazaa service', function () {
    $fazaaService = $this->user->fazaaServices()->first();
    $fazaaService->update([
        'departure_datetime' => Carbon::now()->subHour(),
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.services.fazaas.cancel', $fazaaService->id)
    );

    expect($response->status())->toBe(422);
});

it('cannot cancel non-owned fazaa service', function () {
    $otherUser = User::whereHas('fazaaServices')->where('id', '!=', $this->user->id)->inRandomOrder()->first();
    $fazaaService = $otherUser->fazaaServices()->first();
    $fazaaService->update([
        'departure_datetime' => Carbon::now()->addHour(),
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.services.fazaas.cancel', $fazaaService->id)
    );

    expect($response->status())->toBe(404);
});
