<?php

use App\Models\User;
use App\Models\Service\FazaaService;
use Carbon\Carbon;

use function Pest\Laravel\post;

beforeEach(function () {
    $this->user = User::whereHas('fazaaServices')->inRandomOrder()->first();
    $this->token = $this->user->createToken('test-token')->plainTextToken;
});

it('can confirm fazaa service attendance within valid time window', function () {
    $departureTime = Carbon::now()->addMinutes(15);

    $fazaaService = $this->user->fazaaServices()->first();
    $fazaaService->update([
        'departure_datetime' => $departureTime,
        'attendance_confirmed_at' => null,
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.services.fazaas.confirm-attendance', $fazaaService->id)
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $fazaaService->refresh();
    expect($fazaaService->attendance_confirmed_at)->not->toBeNull();
});

it('cannot confirm attendance for cancelled fazaa service', function () {
    $departureTime = Carbon::now()->addMinutes(15);

    $fazaaService = $this->user->fazaaServices()->first();
    $fazaaService->update([
        'departure_datetime' => $departureTime,
        'attendance_confirmed_at' => null,
        'cancelled_at' => Carbon::now()
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.services.fazaas.confirm-attendance', $fazaaService->id)
    );

    expect($response->status())->toBe(422);
    expect($fazaaService->fresh()->attendance_confirmed_at)->toBeNull();
});

it('cannot confirm attendance when already confirmed', function () {
    $departureTime = Carbon::now()->addMinutes(15);

    $fazaaService = $this->user->fazaaServices()->first();
    $fazaaService->update([
        'departure_datetime' => $departureTime,
        'attendance_confirmed_at' => Carbon::now(),
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.services.fazaas.confirm-attendance', $fazaaService->id)
    );

    expect($response->status())->toBe(422);
});

it('cannot confirm attendance outside 30-minute window before departure', function () {
    $departureTime = Carbon::now()->addHours(2);

    $fazaaService = $this->user->fazaaServices()->first();
    $fazaaService->update([
        'departure_datetime' => $departureTime,
        'attendance_confirmed_at' => null,
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.services.fazaas.confirm-attendance', $fazaaService->id)
    );

    expect($response->status())->toBe(422);
    expect($fazaaService->fresh()->attendance_confirmed_at)->toBeNull();
});

it('cannot confirm attendance after departure time', function () {
    $departureTime = Carbon::now()->subHours(1);

    $fazaaService = $this->user->fazaaServices()->first();
    $fazaaService->update([
        'departure_datetime' => $departureTime,
        'attendance_confirmed_at' => null,
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.services.fazaas.confirm-attendance', $fazaaService->id)
    );

    expect($response->status())->toBe(422);
    expect($fazaaService->fresh()->attendance_confirmed_at)->toBeNull();
});

it('cannot confirm attendance for non-owned fazaa service', function () {
    $otherUser = User::where('id', '!=', $this->user->id)->whereHas('fazaaServices')->inRandomOrder()->first();
    $departureTime = Carbon::now()->addMinutes(15);

    $fazaaService = $otherUser->fazaaServices()->first();
    $fazaaService->update([
        'departure_datetime' => $departureTime,
        'attendance_confirmed_at' => null,
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.services.fazaas.confirm-attendance', $fazaaService->id)
    );

    expect($response->status())->toBe(404);
    expect($fazaaService->fresh()->attendance_confirmed_at)->toBeNull();
});
