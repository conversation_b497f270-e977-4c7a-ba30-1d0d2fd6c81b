<?php

use App\Models\User;
use Carbon\Carbon;

use function Pest\Laravel\post;

beforeEach(function () {
    $this->user = User::whereHas('fazaaRequests')->inRandomOrder()->first();
    $this->token = $this->user->createToken('test-token')->plainTextToken;
});

it('can confirm fazaa request attendance within valid time window', function () {
    $departureTime = Carbon::now()->addMinutes(15);

    $fazaaRequest = $this->user->fazaaRequests()->first();
    $fazaaRequest->update([
        'departure_datetime' => $departureTime,
        'attendance_confirmed_at' => null,
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.requests.fazaas.confirm-attendance', $fazaaRequest->id)
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $fazaaRequest->refresh();
    expect($fazaaRequest->attendance_confirmed_at)->not->toBeNull();
});

it('cannot confirm attendance for cancelled fazaa request', function () {
    $departureTime = Carbon::now()->addMinutes(15);

    $fazaaRequest = $this->user->fazaaRequests()->first();
    $fazaaRequest->update([
        'departure_datetime' => $departureTime,
        'attendance_confirmed_at' => null,
        'cancelled_at' => Carbon::now()
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.requests.fazaas.confirm-attendance', $fazaaRequest->id)
    );

    expect($response->status())->toBe(422);
    expect($fazaaRequest->fresh()->attendance_confirmed_at)->toBeNull();
});

it('cannot confirm attendance when already confirmed', function () {
    $departureTime = Carbon::now()->addMinutes(15);

    $fazaaRequest = $this->user->fazaaRequests()->first();
    $fazaaRequest->update([
        'departure_datetime' => $departureTime,
        'attendance_confirmed_at' => Carbon::now(),
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.requests.fazaas.confirm-attendance', $fazaaRequest->id)
    );

    expect($response->status())->toBe(422);
});

it('cannot confirm attendance outside 30-minute window before departure', function () {
    $departureTime = Carbon::now()->addHours(2);

    $fazaaRequest = $this->user->fazaaRequests()->first();
    $fazaaRequest->update([
        'departure_datetime' => $departureTime,
        'attendance_confirmed_at' => null,
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.requests.fazaas.confirm-attendance', $fazaaRequest->id)
    );

    expect($response->status())->toBe(422);
    expect($fazaaRequest->fresh()->attendance_confirmed_at)->toBeNull();
});

it('cannot confirm attendance after departure time', function () {
    $departureTime = Carbon::now()->subHours(1);

    $fazaaRequest = $this->user->fazaaRequests()->first();
    $fazaaRequest->update([
        'departure_datetime' => $departureTime,
        'attendance_confirmed_at' => null,
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.requests.fazaas.confirm-attendance', $fazaaRequest->id)
    );

    expect($response->status())->toBe(422);
    expect($fazaaRequest->fresh()->attendance_confirmed_at)->toBeNull();
});

it('cannot confirm attendance for non-owned fazaa request', function () {
    $otherUser = User::where('id', '!=', $this->user->id)->whereHas('fazaaRequests')->inRandomOrder()->first();
    $departureTime = Carbon::now()->addMinutes(15);

    $fazaaRequest = $otherUser->fazaaRequests()->first();
    $fazaaRequest->update([
        'departure_datetime' => $departureTime,
        'attendance_confirmed_at' => null,
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.requests.fazaas.confirm-attendance', $fazaaRequest->id)
    );

    expect($response->status())->toBe(404);
    expect($fazaaRequest->fresh()->attendance_confirmed_at)->toBeNull();
});
