<?php

use App\Models\User;
use App\Models\Common\FazaaServiceType;
use App\Models\Common\FazaaSpecificType;
use Carbon\Carbon;

use function Pest\Laravel\patch;

beforeEach(function () {
    $this->user = User::whereHas('fazaaRequests')->inRandomOrder()->first();
    $this->token = $this->user->createToken('test-token')->plainTextToken;
});

it('can update a fazaa request', function () {
    $fazaaRequest = $this->user->fazaaRequests()->first();
    $fazaaServiceType = FazaaServiceType::inRandomOrder()->first();
    $specificType = FazaaSpecificType::where('fazaa_service_type_id', $fazaaServiceType->id)->inRandomOrder()->first();

    $fazaaRequest->update([
        'departure_datetime' => Carbon::now()->addHour(),
        'cancelled_at' => null
    ]);

    $newDepartureTime = Carbon::now()->addHours(3);
    $newArrivalTime = Carbon::now()->addHours(4);

    $updateData = [
        'fazaa_service_type_id' => $fazaaServiceType->id,
        'specific_type_id' => $specificType->id,
        'service_location' => 'Updated Location',
        'transportation_type' => 'car',
        'departure_datetime' => $newDepartureTime->toDateTimeString(),
        'arrival_datetime' => $newArrivalTime->toDateTimeString(),
        'from_city_en' => 'Dubai',
        'from_city_ar' => 'دبي',
        'to_city_en' => 'Abu Dhabi',
        'to_city_ar' => 'أبو ظبي',
        'from_location' => [
            'lat' => 25.2048,
            'lng' => 55.2708
        ],
        'to_location' => [
            'lat' => 24.4539,
            'lng' => 54.3773
        ],
        'note' => 'Updated note'
    ];

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.requests.fazaas.update', $fazaaRequest->id),
        $updateData
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $fazaaRequest->refresh();

    expect($fazaaRequest->fazaa_service_type_id)->toBe($fazaaServiceType->id);
    expect($fazaaRequest->specific_type_id)->toBe($specificType->id);
    expect($fazaaRequest->service_location)->toBe('Updated Location');
    expect($fazaaRequest->transportation_type)->toBe('car');
    expect($fazaaRequest->departure_datetime->timestamp)->toBe($newDepartureTime->timestamp);
    expect($fazaaRequest->arrival_datetime->timestamp)->toBe($newArrivalTime->timestamp);
    expect($fazaaRequest->note)->toBe('Updated note');
});

it('cannot update cancelled fazaa request', function () {
    $fazaaRequest = $this->user->fazaaRequests()->first();
    $fazaaRequest->update([
        'departure_datetime' => Carbon::now()->addHour(),
        'cancelled_at' => Carbon::now()
    ]);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.requests.fazaas.update', $fazaaRequest->id),
        [
            'service_location' => 'Updated Location'
        ]
    );

    expect($response->status())->toBe(422);
});

it('cannot update started fazaa request', function () {
    $fazaaRequest = $this->user->fazaaRequests()->first();
    $fazaaRequest->update([
        'departure_datetime' => Carbon::now()->subHour(),
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.requests.fazaas.update', $fazaaRequest->id),
        [
            'service_location' => 'Updated Location'
        ]
    );

    expect($response->status())->toBe(422);
});

it('cannot update non-owned fazaa request', function () {
    $otherUser = User::whereHas('fazaaRequests')->where('id', '!=', $this->user->id)->inRandomOrder()->first();
    $fazaaRequest = $otherUser->fazaaRequests()->first();
    $fazaaRequest->update([
        'departure_datetime' => Carbon::now()->addHour(),
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.requests.fazaas.update', $fazaaRequest->id),
        [
            'service_location' => 'Updated Location'
        ]
    );

    expect($response->status())->toBe(404);
});
