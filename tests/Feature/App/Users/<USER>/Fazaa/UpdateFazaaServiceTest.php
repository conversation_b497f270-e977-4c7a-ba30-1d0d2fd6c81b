<?php

use App\Models\User;
use App\Models\Service\FazaaService;
use App\Models\Common\FazaaServiceType;
use App\Models\Common\FazaaSpecificType;
use Carbon\Carbon;

use function Pest\Laravel\patch;

beforeEach(function () {
    $this->user = User::whereHas('fazaaServices')->inRandomOrder()->first();
    $this->token = $this->user->createToken('test-token')->plainTextToken;
});

it('can update a fazaa service', function () {
    $fazaaService = $this->user->fazaaServices()->first();
    $fazaaServiceType = FazaaServiceType::inRandomOrder()->first();
    $specificType = FazaaSpecificType::where('fazaa_service_type_id', $fazaaServiceType->id)->inRandomOrder()->first();

    $fazaaService->update([
        'departure_datetime' => Carbon::now()->addHour(),
        'cancelled_at' => null
    ]);

    $newDepartureTime = Carbon::now()->addHours(3);
    $newArrivalTime = Carbon::now()->addHours(4);

    $updateData = [
        'fazaa_service_type_id' => $fazaaServiceType->id,
        'specific_type_id' => $specificType->id,
        'service_location' => 'Updated Location',
        'transportation_type' => 'car',
        'departure_datetime' => $newDepartureTime->toDateTimeString(),
        'arrival_datetime' => $newArrivalTime->toDateTimeString(),
        'from_city_en' => 'Dubai',
        'from_city_ar' => 'دبي',
        'to_city_en' => 'Abu Dhabi',
        'to_city_ar' => 'أبو ظبي',
        'from_location' => [
            'lat' => 25.2048,
            'lng' => 55.2708
        ],
        'to_location' => [
            'lat' => 24.4539,
            'lng' => 54.3773
        ],
        'price' => 150.00,
        'note' => 'Updated note'
    ];

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.services.fazaas.update', $fazaaService->id),
        $updateData
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $fazaaService->refresh();
    expect($fazaaService->fazaa_service_type_id)->toBe($fazaaServiceType->id);
    expect($fazaaService->specific_type_id)->toBe($specificType->id);
    expect($fazaaService->service_location)->toBe('Updated Location');
    expect($fazaaService->transportation_type)->toBe('car');
    expect($fazaaService->departure_datetime->timestamp)->toBe($newDepartureTime->timestamp);
    expect($fazaaService->arrival_datetime->timestamp)->toBe($newArrivalTime->timestamp);
    expect($fazaaService->price)->toBe(150.00);
    expect($fazaaService->note)->toBe('Updated note');
});

it('cannot update cancelled fazaa service', function () {
    $fazaaService = $this->user->fazaaServices()->first();
    $fazaaService->update([
        'departure_datetime' => Carbon::now()->addHour(),
        'cancelled_at' => Carbon::now()
    ]);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.services.fazaas.update', $fazaaService->id),
        [
            'service_location' => 'Updated Location'
        ]
    );

    expect($response->status())->toBe(422);
});

it('cannot update started fazaa service', function () {
    $fazaaService = $this->user->fazaaServices()->first();
    $fazaaService->update([
        'departure_datetime' => Carbon::now()->subHour(),
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.services.fazaas.update', $fazaaService->id),
        [
            'service_location' => 'Updated Location'
        ]
    );

    expect($response->status())->toBe(422);
});

it('cannot update non-owned fazaa service', function () {
    $otherUser = User::where('id', '!=', $this->user->id)->whereHas('fazaaServices')->inRandomOrder()->first();
    $fazaaService = $otherUser->fazaaServices()->first();
    $fazaaService->update([
        'departure_datetime' => Carbon::now()->addHour(),
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.services.fazaas.update', $fazaaService->id),
        [
            'service_location' => 'Updated Location'
        ]
    );

    expect($response->status())->toBe(404);
});
