<?php

use App\Models\User;
use App\Models\Request\FazaaRequest;
use Illuminate\Support\Facades\Event;

beforeEach(function () {
    $this->user = User::first();
});

it('can show my fazaa request details', function () {
    $this->actingAs($this->user, 'api');

    Event::fake();

    $fazaaRequest = $this->user->fazaaRequests()->first();

    if (!$fazaaRequest) {
        $this->markTestSkipped('No fazaa requests found.');
    }

    $response = $this->getJson(
        route('app.users.requests.fazaas.show', [
            'id' => $fazaaRequest->id
        ])
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $requestData = $response->json('data');
    expect($requestData)->toHaveKeys([
        'id',
        'fazaa_service_type',
        'specific_type',
        'transportation_type',
        'departure_datetime',
        'arrival_datetime',
        'from_city',
        'from_location',
        'to_city',
        'to_location',
        'service_location',
        'note',
    ]);
});

it('cannot show other users fazaa request details', function () {
    $this->actingAs($this->user, 'api');

    $otherUser = User::where('id', '!=', $this->user->id)->first();

    $fazaaRequest = $otherUser->fazaaRequests()->first();

    if (!$fazaaRequest) {
        $this->markTestSkipped('No fazaa requests found for other user.');
    }

    $response = $this->getJson(
        route('app.users.requests.fazaas.show', [
            'id' => $fazaaRequest->id
        ])
    );

    expect($response->status())->toBe(404);
});

it('returns 404 for non-existent fazaa request', function () {
    $this->actingAs($this->user, 'api');

    $response = $this->getJson(
        route('app.users.requests.fazaas.show', [
            'id' => 999999999
        ])
    );

    expect($response->status())->toBe(404);
});

it('requires authentication', function () {
    $fazaaRequest = FazaaRequest::first();

    $response = $this->getJson(
        route('app.users.requests.fazaas.show', [
            'id' => $fazaaRequest->id
        ])
    );

    expect($response->status())->toBe(401);
});
