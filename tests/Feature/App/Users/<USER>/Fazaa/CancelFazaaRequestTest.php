<?php

use App\Models\User;
use Carbon\Carbon;

use function Pest\Laravel\post;

beforeEach(function () {
    $this->user = User::whereHas('fazaaRequests')->inRandomOrder()->first();
    $this->token = $this->user->createToken('test-token')->plainTextToken;
});

it('can cancel a fazaa request', function () {
    $fazaaRequest = $this->user->fazaaRequests()->first();
    $fazaaRequest->update([
        'departure_datetime' => Carbon::now()->addHour(),
        'cancelled_at' => null
    ]);

    $data = [
        'cancellation_reason' => 'test reason',
        'note' => 'Need to cancel due to schedule change'
    ];

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.requests.fazaas.cancel', $fazaaRequest->id),
        $data
    );


    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $fazaaRequest->refresh();
    expect($fazaaRequest->cancelled_at)->not->toBeNull();
    expect($fazaaRequest->cancellation_reason)->toBe($data['cancellation_reason']);
    expect($fazaaRequest->cancellation_note)->toBe($data['note']);
});

it('cannot cancel already cancelled fazaa request', function () {
    $fazaaRequest = $this->user->fazaaRequests()->first();
    $fazaaRequest->update([
        'departure_datetime' => Carbon::now()->addHour(),
        'cancelled_at' => Carbon::now()
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.requests.fazaas.cancel', $fazaaRequest->id),
        [
            'cancellation_reason' => 'test reason',
            'note' => 'Need to cancel due to schedule change'
        ]
    );

    expect($response->status())->toBe(422);
});

it('cannot cancel started fazaa request', function () {
    $fazaaRequest = $this->user->fazaaRequests()->first();
    $fazaaRequest->update([
        'departure_datetime' => Carbon::now()->subHour(),
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.requests.fazaas.cancel', $fazaaRequest->id)
    );

    expect($response->status())->toBe(422);
});

it('cannot cancel non-owned fazaa request', function () {
    $otherUser = User::whereHas('fazaaRequests')->where('id', '!=', $this->user->id)->inRandomOrder()->first();
    $fazaaRequest = $otherUser->fazaaRequests()->first();
    $fazaaRequest->update([
        'departure_datetime' => Carbon::now()->addHour(),
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.requests.fazaas.cancel', $fazaaRequest->id),
        [
            'cancellation_reason' => 'test reason',
            'note' => 'Need to cancel due to schedule change'
        ]
    );

    expect($response->status())->toBe(404);
});

it('cannot cancel with empty reason', function () {
    $fazaaRequest = $this->user->fazaaRequests()->first();
    $fazaaRequest->update([
        'departure_datetime' => Carbon::now()->addHour(),
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.requests.fazaas.cancel', $fazaaRequest->id),
        ['cancellation_reason' => '']
    );

    expect($response->status())->toBe(422);
});

it('cannot cancel with too long reason', function () {
    $fazaaRequest = $this->user->fazaaRequests()->first();
    $fazaaRequest->update([
        'departure_datetime' => Carbon::now()->addHour(),
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.requests.fazaas.cancel', $fazaaRequest->id),
        [
            'cancellation_reason' => str_repeat('a', 256)
        ]
    );

    expect($response->status())->toBe(422);
});
