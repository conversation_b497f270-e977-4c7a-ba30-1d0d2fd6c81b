<?php

use App\Models\User;
use App\Models\Service\TripService;
use Carbon\Carbon;

use function Pest\Laravel\post;

beforeEach(function () {
    $this->user = User::whereHas('tripServices')->inRandomOrder()->first();
    $this->token = $this->user->createToken('test-token')->plainTextToken;
});

it('can confirm trip service attendance within valid time window', function () {
    $departureTime = Carbon::now()->addMinutes(15);

    $tripService = $this->user->tripServices()->first();
    $tripService->update([
        'departure_datetime' => $departureTime,
        'attendance_confirmed_at' => null,
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.services.trips.confirm-attendance', $tripService->id)
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $tripService->refresh();
    expect($tripService->attendance_confirmed_at)->not->toBeNull();
});

it('cannot confirm attendance for cancelled trip service', function () {
    $departureTime = Carbon::now()->addMinutes(15);

    $tripService = $this->user->tripServices()->first();
    $tripService->update([
        'departure_datetime' => $departureTime,
        'attendance_confirmed_at' => null,
        'cancelled_at' => Carbon::now()
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.services.trips.confirm-attendance', $tripService->id)
    );

    expect($response->status())->toBe(422);
    expect($tripService->fresh()->attendance_confirmed_at)->toBeNull();
});

it('cannot confirm attendance when already confirmed', function () {
    $departureTime = Carbon::now()->addMinutes(15);

    $tripService = $this->user->tripServices()->first();
    $tripService->update([
        'departure_datetime' => $departureTime,
        'attendance_confirmed_at' => Carbon::now(),
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.services.trips.confirm-attendance', $tripService->id)
    );

    expect($response->status())->toBe(422);
});

it('cannot confirm attendance outside 30-minute window before departure', function () {
    $departureTime = Carbon::now()->addHours(2);

    $tripService = $this->user->tripServices()->first();
    $tripService->update([
        'departure_datetime' => $departureTime,
        'attendance_confirmed_at' => null,
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.services.trips.confirm-attendance', $tripService->id)
    );

    expect($response->status())->toBe(422);
    expect($tripService->fresh()->attendance_confirmed_at)->toBeNull();
});

it('cannot confirm attendance after departure time', function () {
    $departureTime = Carbon::now()->subHours(1);

    $tripService = $this->user->tripServices()->first();
    $tripService->update([
        'departure_datetime' => $departureTime,
        'attendance_confirmed_at' => null,
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.services.trips.confirm-attendance', $tripService->id)
    );

    expect($response->status())->toBe(422);
    expect($tripService->fresh()->attendance_confirmed_at)->toBeNull();
});

it('cannot confirm attendance for non-owned trip service', function () {
    $otherUser = User::whereHas('tripServices')->where('id', '!=', $this->user->id)->inRandomOrder()->first();
    $departureTime = Carbon::now()->addMinutes(15);

    $tripService = $otherUser->tripServices()->first();
    $tripService->update([
        'departure_datetime' => $departureTime,
        'attendance_confirmed_at' => null,
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.services.trips.confirm-attendance', $tripService->id)
    );

    expect($response->status())->toBe(404);
    expect($tripService->fresh()->attendance_confirmed_at)->toBeNull();
});
