<?php

use App\Models\User;
use App\Models\Service\TripService;
use Carbon\Carbon;

use function Pest\Laravel\post;

beforeEach(function () {
    $this->user = User::whereHas('tripServices')->inRandomOrder()->first();
    $this->token = $this->user->createToken('test-token')->plainTextToken;
});

it('can confirm trip service arrival', function () {
    $tripService = $this->user->tripServices()->first();
    $tripService->update([
        'arrival_confirmed_at' => null,
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.services.trips.confirm-arrival', $tripService->id)
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $tripService->refresh();
    expect($tripService->arrival_confirmed_at)->not->toBeNull();
});

it('cannot confirm arrival for already confirmed trip service', function () {
    $tripService = $this->user->tripServices()->first();
    $tripService->update([
        'arrival_confirmed_at' => Carbon::now(),
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.services.trips.confirm-arrival', $tripService->id)
    );

    expect($response->status())->toBe(422);
});

it('cannot confirm arrival for cancelled trip service', function () {
    $tripService = $this->user->tripServices()->first();
    $tripService->update([
        'arrival_confirmed_at' => null,
        'cancelled_at' => Carbon::now()
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.services.trips.confirm-arrival', $tripService->id)
    );

    expect($response->status())->toBe(422);
    expect($tripService->fresh()->arrival_confirmed_at)->toBeNull();
});

it('cannot confirm arrival for non-owned trip service', function () {
    $otherUser = User::whereHas('tripServices')->where('id', '!=', $this->user->id)->inRandomOrder()->first();

    $tripService = $otherUser->tripServices()->first();
    $tripService->update([
        'arrival_confirmed_at' => null,
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.services.trips.confirm-arrival', $tripService->id)
    );

    expect($response->status())->toBe(404);
    expect($tripService->fresh()->arrival_confirmed_at)->toBeNull();
});

it('requires authentication', function () {
    $tripService = TripService::first();

    $response = $this->postJson(
        route('app.users.services.trips.confirm-arrival', $tripService->id)
    );

    expect($response->status())->toBe(401);
});