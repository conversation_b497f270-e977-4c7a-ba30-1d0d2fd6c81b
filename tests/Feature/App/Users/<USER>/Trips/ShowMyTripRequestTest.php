<?php

use App\Models\User;
use App\Models\Request\TripRequest;
use Illuminate\Support\Facades\Event;

beforeEach(function () {
    $this->user = User::first();
});

it('can show my trip request details', function () {
    $this->actingAs($this->user, 'api');

    Event::fake();

    $tripRequest = $this->user->tripRequests()->first();

    if (!$tripRequest) {
        $this->markTestSkipped('No trip requests found.');
    }

    $response = $this->getJson(
        route('app.users.requests.trips.show', [
            'id' => $tripRequest->id
        ])
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $requestData = $response->json('data');
    expect($requestData)->toHaveKeys([
        'id',
        'trip_type',
        'departure_datetime',
        'arrival_datetime',
        'number_of_seats',
        'from_city',
        'from_location',
        'to_city',
        'to_location',
        'price',
        'allow_smoking',
        'deliver_to_door',
        'seats',
        'additional_services',
    ]);
});

it('cannot show other users trip request details', function () {
    $this->actingAs($this->user, 'api');

    $otherUser = User::where('id', '!=', $this->user->id)->first();

    $tripRequest = $otherUser->tripRequests()->first();

    if (!$tripRequest) {
        $this->markTestSkipped('No trip requests found for other user.');
    }

    $response = $this->getJson(
        route('app.users.requests.trips.show', [
            'id' => $tripRequest->id
        ])
    );

    expect($response->status())->toBe(404);
});

it('returns 404 for non-existent trip request', function () {
    $this->actingAs($this->user, 'api');

    $response = $this->getJson(
        route('app.users.requests.trips.show', [
            'id' => 999999999
        ])
    );

    expect($response->status())->toBe(404);
});

it('requires authentication', function () {
    $tripRequest = TripRequest::first();

    $response = $this->getJson(
        route('app.users.requests.trips.show', [
            'id' => $tripRequest->id
        ])
    );

    expect($response->status())->toBe(401);
});
