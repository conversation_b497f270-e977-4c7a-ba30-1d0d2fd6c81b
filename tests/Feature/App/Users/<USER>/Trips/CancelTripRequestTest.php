<?php

use App\Models\User;
use Carbon\Carbon;
use App\Enums\Travel\TripStatusEnum;

it('can cancel a trip request', function () {
    $user = User::first();
    $tripRequest = $user->tripRequests()
        ->whereNull('cancelled_at')
        ->where('departure_datetime', '>', Carbon::now()->addHours(5))
        ->firstOr(function () {
            $this->markTestSkipped('No available trip request found for cancellation.');
        });

    $tripRequest->update([
        'departure_datetime' => Carbon::now()->addHours(5),
        'attendance_confirmed_at' => null,
        'cancelled_at' => null
    ]);

    $cancellationReasons = 'Changed travel plans';
    $note = 'Emergency situation occurred';

    $response = $this->actingAs($user, 'api')
        ->postJson(route('app.users.requests.trips.cancel', $tripRequest->id), [
            'cancellation_reason' => $cancellationReasons,
            'note' => $note
        ]);

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $tripRequest->refresh();
    expect($tripRequest->cancelled_at)->not->toBeNull()
        ->and($tripRequest->cancellation_reason)->toBe($cancellationReasons)
        ->and($tripRequest->cancellation_note)->toBe($note);
});

it('cannot cancel an already cancelled trip request', function () {
    $user = User::first();
    $tripRequest = $user->tripRequests()
        ->whereNotNull('cancelled_at')
        ->firstOr(function () {
            $this->markTestSkipped('No cancelled trip request found.');
        });

    $response = $this->actingAs($user, 'api')
        ->postJson(route('app.users.requests.trips.cancel', $tripRequest->id), [
            'cancellation_reason' => 'Changed plans'
        ]);

    expect($response->status())->toBe(422)
        ->and($response->json('message'))->toBe(__('exceptions.trip_request.already_cancelled'));
});


it('cannot cancel another user\'s trip request', function () {
    $user = User::first();
    $otherUser = User::where('id', '!=', $user->id)->first();
    $tripRequest = $otherUser->tripRequests()
        ->whereNull('cancelled_at')
        ->firstOr(function () {
            $this->markTestSkipped('No trip request found for other user.');
        });

    $response = $this->actingAs($user, 'api')
        ->postJson(route('app.users.requests.trips.cancel', $tripRequest->id), [
            'cancellation_reason' => 'Changed plans'
        ]);

    expect($response->status())->toBe(404);
});

it('validates cancellation reasons', function () {
    $user = User::first();
    $tripRequest = $user->tripRequests()
        ->whereNull('cancelled_at')
        ->where('departure_datetime', '>', Carbon::now()->addHours(5))
        ->firstOr(function () {
            $this->markTestSkipped('No available trip request found for validation test.');
        });

    // Test empty reason
    $response = $this->actingAs($user, 'api')
        ->postJson(route('app.users.requests.trips.cancel', $tripRequest->id), [
            'cancellation_reason' => '',
            'note' => ''
        ]);
    expect($response->status())->toBe(422);

    // Test too long reason
    $response = $this->actingAs($user, 'api')
        ->postJson(route('app.users.requests.trips.cancel', $tripRequest->id), [
            'cancellation_reason' => str_repeat('a', 256),
            'note' => str_repeat('a', 256)
        ]);
    expect($response->status())->toBe(422);
});
