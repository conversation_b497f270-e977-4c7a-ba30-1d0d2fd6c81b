<?php

use App\Models\User;
use Carbon\Carbon;

beforeEach(function () {
    $this->user = User::first();
    $this->token = $this->user->createToken('test-token')->plainTextToken;
});

it('can delay a trip service with valid data', function () {
    $tripService = $this->user->tripServices()->first();
    $tripService->update([
        'departure_datetime' => Carbon::now()->addDays(2),
        'arrival_datetime' => Carbon::now()->addDays(2)->addHours(3),
        'cancelled_at' => null
    ]);

    $newDepartureTime = Carbon::now()->addDays(3);
    $newArrivalTime = Carbon::now()->addDays(3)->addHours(3);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.services.trips.delay', $tripService->id),
        [
            'departure_datetime' => $newDepartureTime->format('Y-m-d H:i:s'),
            'arrival_datetime' => $newArrivalTime->format('Y-m-d H:i:s'),
            'delay_reason' => 'medical_reason',
            'note' => 'Need to delay due to medical appointment'
        ]
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $tripService->refresh();
    expect($tripService->departure_datetime->format('Y-m-d H:i:s'))->toBe($newDepartureTime->format('Y-m-d H:i:s'));
    expect($tripService->arrival_datetime->format('Y-m-d H:i:s'))->toBe($newArrivalTime->format('Y-m-d H:i:s'));
    expect($tripService->delay_reason)->toBe('medical_reason');
    expect($tripService->delay_note)->toBe('Need to delay due to medical appointment');
    expect($tripService->delayed_at)->not->toBeNull();
});

it('validates delay reasons', function () {
    $tripService = $this->user->tripServices()->first();
    $tripService->update([
        'departure_datetime' => Carbon::now()->addDays(2),
        'arrival_datetime' => Carbon::now()->addDays(2)->addHours(3),
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.services.trips.delay', $tripService->id),
        [
            'departure_datetime' => Carbon::now()->addDays(3),
            'arrival_datetime' => Carbon::now()->addDays(3)->addHours(3),
            'delay_reason' => ['invalid_reason'],
            'note' => 'Invalid reason test'
        ]
    );

    expect($response->status())->toBe(422);
});

it('requires at least one delay reason', function () {
    $tripService = $this->user->tripServices()->first();
    $tripService->update([
        'departure_datetime' => Carbon::now()->addDays(2),
        'arrival_datetime' => Carbon::now()->addDays(2)->addHours(3),
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.services.trips.delay', $tripService->id),
        [
            'departure_datetime' => Carbon::now()->addDays(3),
            'arrival_datetime' => Carbon::now()->addDays(3)->addHours(3),
            'delay_reason' => '',
            'note' => 'Empty reason test'
        ]
    );

    expect($response->status())->toBe(422);
});

it('cannot delay a cancelled trip service', function () {
    $tripService = $this->user->tripServices()->first();
    $tripService->update([
        'departure_datetime' => Carbon::now()->addDays(2),
        'arrival_datetime' => Carbon::now()->addDays(2)->addHours(3),
        'cancelled_at' => Carbon::now()
    ]);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.services.trips.delay', $tripService->id),
        [
            'departure_datetime' => Carbon::now()->addDays(3),
            'arrival_datetime' => Carbon::now()->addDays(3)->addHours(3),
            'delay_reason' => 'medical_reason',
            'note' => 'Test note'
        ]
    );

    expect($response->status())->toBe(422);
});

it('cannot delay a started trip', function () {
    $tripService = $this->user->tripServices()->first();
    $tripService->update([
        'departure_datetime' => Carbon::now()->subHour(),
        'arrival_datetime' => Carbon::now()->addHours(2),
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.services.trips.delay', $tripService->id),
        [
            'departure_datetime' => Carbon::now()->addHours(1),
            'arrival_datetime' => Carbon::now()->addHours(4),
            'delay_reason' => 'medical_reason',
            'note' => 'Test note'
        ]
    );

    expect($response->status())->toBe(422);
});

it('validates arrival time is after departure time', function () {
    $tripService = $this->user->tripServices()->first();
    $tripService->update([
        'departure_datetime' => Carbon::now()->addDays(2),
        'arrival_datetime' => Carbon::now()->addDays(2)->addHours(3),
        'cancelled_at' => null
    ]);

    $departureTime = Carbon::now()->addDays(3);
    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.services.trips.delay', $tripService->id),
        [
            'departure_datetime' => $departureTime->format('Y-m-d H:i:s'),
            'arrival_datetime' => $departureTime->subHour()->format('Y-m-d H:i:s'),
        ]
    );

    expect($response->status())->toBe(422);
});

it('validates departure time is in the future', function () {
    $tripService = $this->user->tripServices()->first();
    $tripService->update([
        'departure_datetime' => Carbon::now()->addDays(2),
        'arrival_datetime' => Carbon::now()->addDays(2)->addHours(3),
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.services.trips.delay', $tripService->id),
        [
            'departure_datetime' => Carbon::now()->subDay(),
            'arrival_datetime' => Carbon::now()->addHours(2),
        ]
    );

    expect($response->status())->toBe(422);
});

it('cannot delay another users trip service', function () {
    $otherUser = User::where('id', '!=', $this->user->id)->first();
    $tripService = $otherUser->tripServices()->first();

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.services.trips.delay', $tripService->id),
        [
            'departure_datetime' => Carbon::now()->addDays(3),
            'arrival_datetime' => Carbon::now()->addDays(3)->addHours(3),
            'delay_reason' => 'medical_reason',
            'note' => 'Test note'
        ]
    );

    expect($response->status())->toBe(404);
});

it('validates too long delay reason', function () {
    $tripService = $this->user->tripServices()->first();
    $tripService->update([
        'departure_datetime' => Carbon::now()->addDays(2),
        'arrival_datetime' => Carbon::now()->addDays(2)->addHours(3),
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.services.trips.delay', $tripService->id),
        [
            'departure_datetime' => Carbon::now()->addDays(3),
            'arrival_datetime' => Carbon::now()->addDays(3)->addHours(3),
            'delay_reason' => str_repeat('a', 256),
            'note' => 'Too long reason test'
        ]
    );

    expect($response->status())->toBe(422);
});
