<?php

use App\Models\User;
use App\Models\Request\TripRequest;
use Carbon\Carbon;

use function Pest\Laravel\post;

beforeEach(function () {
    $this->user = User::whereHas('tripRequests')->inRandomOrder()->first();
    $this->token = $this->user->createToken('test-token')->plainTextToken;
});

it('can confirm trip request attendance within valid time window', function () {
    $departureTime = Carbon::now()->addMinutes(15);

    $tripRequest = $this->user->tripRequests()->first();
    $tripRequest->update([
        'departure_datetime' => $departureTime,
        'attendance_confirmed_at' => null,
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.requests.trips.confirm-attendance', $tripRequest->id)
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $tripRequest->refresh();
    expect($tripRequest->attendance_confirmed_at)->not->toBeNull();
});

it('cannot confirm attendance for cancelled trip request', function () {
    $departureTime = Carbon::now()->addMinutes(15);

    $tripRequest = $this->user->tripRequests()->firstOr(function () {
        $this->markTestSkipped('No trip request found.');
    });


    $tripRequest->update([
        'departure_datetime' => $departureTime,
        'attendance_confirmed_at' => null,
        'cancelled_at' => Carbon::now()
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.requests.trips.confirm-attendance', $tripRequest->id)
    );

    expect($response->status())->toBe(422);
    expect($tripRequest->fresh()->attendance_confirmed_at)->toBeNull();
});

it('cannot confirm attendance when already confirmed', function () {
    $departureTime = Carbon::now()->addMinutes(15);

    $tripRequest = $this->user->tripRequests()->first();
    $tripRequest->update([
        'departure_datetime' => $departureTime,
        'attendance_confirmed_at' => Carbon::now(),
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.requests.trips.confirm-attendance', $tripRequest->id)
    );

    expect($response->status())->toBe(422);
});

it('cannot confirm attendance outside 30-minute window before departure', function () {
    $departureTime = Carbon::now()->addHours(2);

    $tripRequest = $this->user->tripRequests()->first();
    $tripRequest->update([
        'departure_datetime' => $departureTime,
        'attendance_confirmed_at' => null,
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.requests.trips.confirm-attendance', $tripRequest->id)
    );

    expect($response->status())->toBe(422);
    expect($tripRequest->fresh()->attendance_confirmed_at)->toBeNull();
});
