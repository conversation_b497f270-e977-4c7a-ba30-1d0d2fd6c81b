<?php

use App\Models\User;
use App\Models\Service\TripService;
use Illuminate\Support\Facades\Event;

beforeEach(function () {
    $this->user = User::first();
});

it('can show my trip service details', function () {
    $this->actingAs($this->user, 'api');

    Event::fake();

    $tripService = $this->user->tripServices()->first();

    if (!$tripService) {
        $this->markTestSkipped('No trip services found.');
    }

    $response = $this->getJson(
        route('app.users.services.trips.show', [
            'id' => $tripService->id
        ])
    );

    expect($response->status())->toBe(200);

    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $serviceData = $response->json('data');
    expect($serviceData)->toHaveKeys([
        'id',
        'trip_type',
        'departure_datetime',
        'arrival_datetime',
        'number_of_seats',
        'number_of_available_seats',
        'number_of_free_cartons',
        'from_city',
        'to_city',
        'price',
        'allow_smoking',
        'deliver_to_door',
        'additional_services',
        'car',
        'seats',
        'note',
    ]);
});

it('cannot show other users trip service details', function () {
    $this->actingAs($this->user, 'api');

    $otherUser = User::where('id', '!=', $this->user->id)->first();

    $tripService = $otherUser->tripServices()->first();

    if (!$tripService) {
        $this->markTestSkipped('No trip services found for other user.');
    }

    $response = $this->getJson(
        route('app.users.services.trips.show', [
            'id' => $tripService->id
        ])
    );

    expect($response->status())->toBe(404);
});

it('returns 404 for non-existent trip service', function () {
    $this->actingAs($this->user, 'api');

    $response = $this->getJson(
        route('app.users.services.trips.show', [
            'id' => 999999999
        ])
    );

    expect($response->status())->toBe(404);
});

it('requires authentication', function () {
    $tripService = TripService::first();

    $response = $this->getJson(
        route('app.users.services.trips.show', [
            'id' => $tripService->id
        ])
    );

    expect($response->status())->toBe(401);
});
