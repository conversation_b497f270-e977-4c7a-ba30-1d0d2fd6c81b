<?php

use App\Models\User;
use App\Models\Booking\TripRequestBooking;
use App\Models\Request\TripRequest;
use Carbon\Carbon;

use function Pest\Laravel\get;

beforeEach(function () {
    $this->user = User::first();
});

it('can get user trip request bookings', function () {
    $this->actingAs($this->user, 'api');

    $user = User::whereHas('tripRequestBookings')->firstOr(function () {
        $this->markTestSkipped('No user with trip request bookings found.');
    });

    $response = get(route('app.users.requests.trips.my-offers.index'));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    expect($response->json('data.data.0'))->toHaveKeys([
        'id',
        'note',
        'price',
        'status',
        'rejection_reason',
        'created_at',
        'updated_at',
        'trip_request'
    ]);
});

it('cannot see other users trip request bookings', function () {
    $this->actingAs($this->user, 'api');

    $otherUser = User::where('id', '!=', $this->user->id)
        ->whereHas('tripRequestBookings')
        ->firstOr(function () {
            $this->markTestSkipped('No other user with trip request bookings found.');
        });

    $otherUserBookings = $otherUser->tripRequestBookings()
        ->with(['tripRequest'])
        ->get();

    $response = get(route('app.users.requests.trips.my-offers.index'));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    // Verify that none of the returned bookings belong to the other user
    $returnedBookingIds = collect($response->json('data'))->pluck('id');
    $otherUserBookingIds = $otherUserBookings->pluck('id');

    expect($returnedBookingIds->intersect($otherUserBookingIds))->toBeEmpty();
});

it('requires authentication', function () {
    $response = get(route('app.users.requests.trips.my-offers.index'), [
        'Accept' => 'application/json'
    ]);

    expect($response->status())->toBe(401);
});
