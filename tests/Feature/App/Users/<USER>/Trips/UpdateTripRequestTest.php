<?php

use App\Models\User;
use App\Models\Request\TripRequest;
use App\Enums\Travel\TripTypeEnum;
use Carbon\Carbon;

use function Pest\Laravel\patch;

beforeEach(function () {
    $user = User::inRandomOrder()->has('tripRequests')->first();
    $this->token = $user->createToken('test-token')->plainTextToken;
    $this->user = $user;
});

it('can update a trip request', function () {
    $tripRequest = $this->user->tripRequests()->first();
    $tripRequest->update([
        'cancelled_at' => null,
        'departure_datetime' => Carbon::now()->addDay(),
        'arrival_datetime' => Carbon::now()->addDay()->addHours(2),
    ]);

    $updateData = [
        'price' => fake()->randomFloat(2, 1, 1000),
        'note' => fake()->sentence(),
        'allow_smoking' => fake()->boolean(),
        'deliver_to_door' => fake()->boolean(),
        'trip_type' => TripTypeEnum::SINGLES(),
        'from_city_en' => 'Riyadh',
        'from_city_ar' => 'الرياض',
        'to_city_en' => 'Jeddah',
        'to_city_ar' => 'جدة',
        'from_location' => [
            'lat' => fake()->latitude(),
            'lng' => fake()->longitude(),
        ],
        'to_location' => [
            'lat' => fake()->latitude(),
            'lng' => fake()->longitude(),
        ],
        'seats' => ['A1', 'B2', 'C3'],
    ];

    $response = patch(
        route('app.users.requests.trips.update', $tripRequest->id),
        $updateData,
        ['Authorization' => 'Bearer ' . $this->token]
    );

    $response->assertOk();
    $response->assertJsonStructure([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $tripRequest->refresh();

    expect($tripRequest->price)->toBe($updateData['price']);
    expect($tripRequest->note)->toBe($updateData['note']);
    expect($tripRequest->allow_smoking)->toBe($updateData['allow_smoking']);
    expect($tripRequest->deliver_to_door)->toBe($updateData['deliver_to_door']);
    expect($tripRequest->trip_type)->toBe($updateData['trip_type']);

    expect($tripRequest->seats()->count())->toBe(count($updateData['seats']));
    foreach ($updateData['seats'] as $seatNumber) {
        expect($tripRequest->seats()->where('number', $seatNumber)->exists())->toBeTrue();
    }
});

it('cannot update another user\'s trip request', function () {
    $otherUser = User::where('id', '!=', $this->user->id)->first();
    $tripRequest = $otherUser->tripRequests()->first();

    $updateData = [
        'price' => fake()->randomFloat(2, 1, 1000),
        'note' => fake()->sentence(),
    ];

    $response = patch(
        route('app.users.requests.trips.update', $tripRequest->id),
        $updateData,
        ['Authorization' => 'Bearer ' . $this->token]
    );

    $response->assertStatus(404);
});

it('validates required fields when updating a trip request', function () {
    $tripRequest = $this->user->tripRequests()->inRandomOrder()->first();
    $tripRequest->update([
        'cancelled_at' => null,
        'departure_datetime' => Carbon::now()->addDay(),
    ]);

    $response = patch(
        route('app.users.requests.trips.update', $tripRequest->id),
        [
            'price' => 'invalid-price',
            'from_location' => 'invalid-location',
            'seats' => 'invalid-seats'
        ],
        ['Authorization' => 'Bearer ' . $this->token]
    );

    $response->assertStatus(422);
});

it('cannot update a cancelled trip request', function () {
    $tripRequest = $this->user->tripRequests()->inRandomOrder()->first();
    $tripRequest->update([
        'cancelled_at' => Carbon::now(),
        'departure_datetime' => Carbon::now()->addDay(),
    ]);

    $updateData = [
        'price' => fake()->randomFloat(2, 1, 1000),
        'note' => fake()->sentence(),
    ];

    $response = patch(
        route('app.users.requests.trips.update', $tripRequest->id),
        $updateData,
        ['Authorization' => 'Bearer ' . $this->token]
    );

    $response->assertStatus(422);
    $response->assertJsonFragment([
        'message' => __('exceptions.trip_request.cannot_update_cancelled_trip')
    ]);
});

it('cannot update a trip request after departure', function () {
    $tripRequest = $this->user->tripRequests()->inRandomOrder()->first();
    $tripRequest->update([
        'cancelled_at' => null,
        'departure_datetime' => Carbon::now()->subHour()
    ]);

    $updateData = [
        'price' => fake()->randomFloat(2, 1, 1000),
        'note' => fake()->sentence(),
    ];

    $response = patch(
        route('app.users.requests.trips.update', $tripRequest->id),
        $updateData,
        ['Authorization' => 'Bearer ' . $this->token]
    );

    $response->assertStatus(422);
    $response->assertJsonFragment([
        'message' => __('exceptions.trip_request.cannot_update_after_departure')
    ]);
});
