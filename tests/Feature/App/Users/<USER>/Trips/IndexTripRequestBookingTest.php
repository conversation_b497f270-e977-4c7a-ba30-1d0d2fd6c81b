<?php

use App\Models\User;
use App\Models\Request\TripRequest;

use function Pest\Laravel\get;

beforeEach(function () {
    $this->user = User::first();
});

it('can list trip request bookings', function () {
    $this->actingAs($this->user, 'api');

    $tripRequest = $this->user->tripRequests()
        ->whereHas('bookings')
        ->firstOr(function () {
            $this->markTestSkipped('No trip request with bookings found.');
        });

    $response = get(route('app.users.requests.trips.offers.index', $tripRequest->id));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);
    expect($response->json('data.data.0'))->toHaveKeys([
        'id',
        'note',
        'price',
        'status',
        'created_at',
        'updated_at',
    ]);
});

it('cannot list bookings of other users trip requests', function () {
    $this->actingAs($this->user, 'api');

    $otherUser = User::where('id', '!=', $this->user->id)
        ->whereHas('tripRequests.bookings')
        ->firstOr(function () {
            $this->markTestSkipped('No other user with trip request bookings found.');
        });

    $otherUserTripRequest = $otherUser->tripRequests()
        ->whereHas('bookings')
        ->first();

    $response = get(route('app.users.requests.trips.offers.index', $otherUserTripRequest->id));

    expect($response->status())->toBe(404);
});

it('requires authentication', function () {
    $tripRequest = TripRequest::whereHas('bookings')->firstOr(function () {
        $this->markTestSkipped('No trip request with bookings found.');
    });

    $response = get(route('app.users.requests.trips.offers.index', $tripRequest->id), [
        'Accept' => 'application/json'
    ]);

    expect($response->status())->toBe(401);
});
