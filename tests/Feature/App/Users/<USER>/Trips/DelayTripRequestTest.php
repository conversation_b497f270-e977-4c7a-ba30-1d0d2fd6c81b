<?php

use App\Models\User;
use Carbon\Carbon;

beforeEach(function () {
    $this->user = User::first();
    $this->token = $this->user->createToken('test-token')->plainTextToken;
});

it('can delay a trip request with valid dates', function () {
    $tripRequest = $this->user->tripRequests()->first();
    $tripRequest->update([
        'departure_datetime' => Carbon::now()->addDays(2),
        'arrival_datetime' => Carbon::now()->addDays(2)->addHours(3),
        'cancelled_at' => null
    ]);

    $newDepartureTime = Carbon::now()->addDays(3);
    $newArrivalTime = Carbon::now()->addDays(3)->addHours(3);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.requests.trips.delay', $tripRequest->id),
        [
            'departure_datetime' => $newDepartureTime->format('Y-m-d H:i:s'),
            'arrival_datetime' => $newArrivalTime->format('Y-m-d H:i:s'),
            'delay_reason' => 'medical_reason',
            'note' => 'Medical appointment'
        ]
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $tripRequest->refresh();
    expect($tripRequest->departure_datetime->format('Y-m-d H:i:s'))->toBe($newDepartureTime->format('Y-m-d H:i:s'));
    expect($tripRequest->arrival_datetime->format('Y-m-d H:i:s'))->toBe($newArrivalTime->format('Y-m-d H:i:s'));
    expect($tripRequest->delay_reason)->toBe('medical_reason');
    expect($tripRequest->delay_note)->toBe('Medical appointment');
    expect($tripRequest->delayed_at)->not->toBeNull();
});

it('validates datetime format when delaying a trip request', function () {
    $tripRequest = $this->user->tripRequests()->first();
    $tripRequest->update([
        'departure_datetime' => Carbon::now()->addDays(2),
        'arrival_datetime' => Carbon::now()->addDays(2)->addHours(3),
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.requests.trips.delay', $tripRequest->id),
        [
            'departure_datetime' => 'invalid-date',
            'arrival_datetime' => 'invalid-date',
            'delay_reason' => 'medical_reason',
            'note' => 'Medical appointment'
        ]
    );

    expect($response->status())->toBe(422);
});

it('validates delay reasons', function () {
    $tripRequest = $this->user->tripRequests()->first();
    $tripRequest->update([
        'departure_datetime' => Carbon::now()->addDays(2),
        'arrival_datetime' => Carbon::now()->addDays(2)->addHours(3),
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.requests.trips.delay', $tripRequest->id),
        [
            'departure_datetime' => Carbon::now()->addDays(3)->format('Y-m-d H:i:s'),
            'arrival_datetime' => Carbon::now()->addDays(3)->addHours(3)->format('Y-m-d H:i:s'),
            'delay_reason' => ['invalid_reason'],
            'note' => 'Test note'
        ]
    );

    expect($response->status())->toBe(422);
});

it('cannot delay a cancelled trip request', function () {
    $tripRequest = $this->user->tripRequests()->first();
    $tripRequest->update([
        'departure_datetime' => Carbon::now()->addDays(2),
        'arrival_datetime' => Carbon::now()->addDays(2)->addHours(3),
        'cancelled_at' => Carbon::now()
    ]);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.requests.trips.delay', $tripRequest->id),
        [
            'departure_datetime' => Carbon::now()->addDays(3),
            'arrival_datetime' => Carbon::now()->addDays(3)->addHours(3),
            'delay_reason' => 'medical_reason',
            'note' => 'Medical appointment'
        ]
    );

    expect($response->status())->toBe(422);
});

it('cannot delay a started trip request', function () {
    $tripRequest = $this->user->tripRequests()->first();
    $tripRequest->update([
        'departure_datetime' => Carbon::now()->subHour(),
        'arrival_datetime' => Carbon::now()->addHours(2),
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.requests.trips.delay', $tripRequest->id),
        [
            'departure_datetime' => Carbon::now()->addHours(1),
            'arrival_datetime' => Carbon::now()->addHours(4),
            'delay_reason' => 'medical_reason',
            'note' => 'Medical appointment'
        ]
    );

    expect($response->status())->toBe(422);
});
