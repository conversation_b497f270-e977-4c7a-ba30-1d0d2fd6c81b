<?php

use App\Enums\Bookings\BookingStatusEnum;
use App\Events\FazaaRequestBookingRejected;
use App\Models\User;
use App\Models\Booking\FazaaRequestBooking;
use Illuminate\Support\Facades\Event;

beforeEach(function () {
    $this->user = User::first();
    $this->actingAs($this->user, 'api');
});

it('can reject a fazaa request booking', function () {
    Event::fake();

    $booking = FazaaRequestBooking::whereHas('fazaaRequest', function ($query) {
        $query->where('user_id', $this->user->id);
    })
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No pending bookings found.');
    }

    $rejectionData = [
        'rejection_reason' => 'Not suitable',
        'note' => 'Had to cancel due to an emergency',
    ];

    $response = $this->postJson(
        route('app.users.requests.fazaas.offers.reject', [
            'id' => $booking->id
        ]),
        $rejectionData
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::REJECTED());
    expect($booking->rejection_reason)->toBe($rejectionData['rejection_reason']);
    expect($booking->rejection_note)->toBe($rejectionData['note']);

    Event::assertDispatched(FazaaRequestBookingRejected::class);
});

it('cannot reject non pending booking', function () {
    $booking = FazaaRequestBooking::whereHas('fazaaRequest', function ($query) {
        $query->where('user_id', $this->user->id);
    })
        ->where('status', '!=', BookingStatusEnum::PENDING())
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No non-pending bookings found.');
    }

    $rejectionData = [
        'rejection_reason' => 'Not suitable',
        'note' => 'Had to cancel due to an emergency',
    ];

    $response = $this->postJson(
        route('app.users.requests.fazaas.offers.reject', [
            'id' => $booking->id
        ]),
        $rejectionData
    );

    expect($response->status())->toBe(422);
    expect($response->json('message'))->toBe(__('exceptions.fazaa_request.booking.booking_is_not_pending'));

    $booking->refresh();
    expect($booking->status)->not->toBe(BookingStatusEnum::PENDING());
});

it('cannot reject other users booking', function () {
    $otherUser = User::where('id', '!=', $this->user->id)->first();

    $booking = FazaaRequestBooking::whereHas('fazaaRequest', function ($query) use ($otherUser) {
        $query->where('user_id', $otherUser->id);
    })
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No pending bookings found for other user.');
    }

    $rejectionData = [
        'rejection_reason' => 'Not suitable',
        'note' => 'Had to cancel due to an emergency',
    ];

    $response = $this->postJson(
        route('app.users.requests.fazaas.offers.reject', [
            'id' => $booking->id
        ]),
        $rejectionData
    );

    expect($response->status())->toBe(404);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::PENDING());
});

it('validates rejection reasons', function () {
    $booking = FazaaRequestBooking::whereHas('fazaaRequest', function ($query) {
        $query->where('user_id', $this->user->id);
    })
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No pending bookings found.');
    }

    // Test empty reason
    $response = $this->postJson(
        route('app.users.requests.fazaas.offers.reject', [
            'id' => $booking->id
        ]),
        ['rejection_reason' => '']
    );

    expect($response->status())->toBe(422);

    // Test too long reason
    $response = $this->postJson(
        route('app.users.requests.fazaas.offers.reject', [
            'id' => $booking->id
        ]),
        [
            'rejection_reason' => str_repeat('a', 256)
        ]
    );

    expect($response->status())->toBe(422);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::PENDING());
});
