<?php

use App\Enums\Bookings\BookingStatusEnum;
use App\Events\FazaaRequestBookingCancelled;
use App\Models\User;
use App\Models\Booking\FazaaRequestBooking;
use Illuminate\Support\Facades\Event;

beforeEach(function () {
    $this->user = User::first();
    $this->actingAs($this->user, 'api');
});

it('can cancel a fazaa request booking', function () {
    Event::fake();

    $booking = FazaaRequestBooking::whereHas('fazaaRequest', function ($query) {
        $query->where('user_id', $this->user->id);
    })
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No pending bookings found.');
    }

    $cancellationData = [
        'cancellation_reason' => 'Schedule conflict',
        'note' => 'Need to cancel due to schedule change'
    ];

    $response = $this->postJson(
        route('app.users.requests.fazaas.offers.cancel', [
            'id' => $booking->id
        ]),
        $cancellationData
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::CANCELLED_BY_OWNER());
    expect($booking->cancellation_reason)->toBe($cancellationData['cancellation_reason']);
    expect($booking->cancellation_note)->toBe($cancellationData['note']);

    Event::assertDispatched(FazaaRequestBookingCancelled::class);
});

it('cannot cancel other users booking', function () {
    $otherUser = User::where('id', '!=', $this->user->id)->first();

    $booking = FazaaRequestBooking::whereHas('fazaaRequest', function ($query) use ($otherUser) {
        $query->where('user_id', $otherUser->id);
    })
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No pending bookings found for other user.');
    }

    $cancellationData = [
        'cancellation_reason' => 'Schedule conflict',
        'note' => 'Need to cancel due to schedule change',
    ];

    $response = $this->postJson(
        route('app.users.requests.fazaas.offers.cancel', [
            'id' => $booking->id
        ]),
        $cancellationData
    );

    expect($response->status())->toBe(404);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::PENDING());
});

it('validates cancellation reasons', function () {
    $booking = FazaaRequestBooking::whereHas('fazaaRequest', function ($query) {
        $query->where('user_id', $this->user->id);
    })
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No pending bookings found.');
    }

    // Test empty reason
    $response = $this->postJson(
        route('app.users.requests.fazaas.offers.cancel', [
            'id' => $booking->id
        ]),
        ['cancellation_reason' => '']
    );

    expect($response->status())->toBe(422);

    // Test too long reason
    $response = $this->postJson(
        route('app.users.requests.fazaas.offers.cancel', [
            'id' => $booking->id
        ]),
        [
            'cancellation_reason' => str_repeat('a', 256)
        ]
    );

    expect($response->status())->toBe(422);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::PENDING());
});

it('cannot cancel a non-pending booking', function () {
    $booking = FazaaRequestBooking::whereHas('fazaaRequest', function ($query) {
        $query->where('user_id', $this->user->id);
    })
        ->where('status', '!=', BookingStatusEnum::PENDING())
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No non-pending bookings found.');
    }

    $cancellationData = [
        'cancellation_reason' => 'Schedule conflict',
        'note' => 'Need to cancel due to schedule change'
    ];

    $response = $this->postJson(
        route('app.users.requests.fazaas.offers.cancel', [
            'id' => $booking->id
        ]),
        $cancellationData
    );

    expect($response->status())->toBe(422);
});
