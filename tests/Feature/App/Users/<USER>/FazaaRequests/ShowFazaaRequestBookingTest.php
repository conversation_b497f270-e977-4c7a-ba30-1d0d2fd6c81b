<?php

use App\Models\User;
use App\Models\Booking\FazaaRequestBooking;
use Illuminate\Support\Facades\Event;

beforeEach(function () {
    $this->user = User::first();
    $this->actingAs($this->user, 'api');
});

it('can show fazaa request booking details', function () {
    Event::fake();

    $booking = FazaaRequestBooking::whereHas('fazaaRequest', function ($query) {
        $query->where('user_id', $this->user->id);
    })->first();

    if (!$booking) {
        $this->markTestSkipped('No fazaa request bookings found.');
    }

    $response = $this->getJson(
        route('app.users.requests.fazaas.offers.show', [
            'id' => $booking->id
        ])
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $bookingData = $response->json('data');
    expect($bookingData)->toHaveKeys([
        'id',
        'fazaa_request',
        'status',
        'note',
        'rejection_reason',
        'cancellation_reason',
        'cancelled_at',
        'accepted_at',
        'created_at',
        'updated_at',
    ]);

    expect($bookingData['fazaa_request'])->toHaveKeys([
        'id',
        'fazaa_service_type',
        'specific_type',
        'transportation_type',
        'departure_datetime',
        'arrival_datetime',
        'from_city',
        'to_city',
        'service_location',
        'user',
    ]);
});

it('cannot show other users fazaa request booking details', function () {
    $otherUser = User::where('id', '!=', $this->user->id)->first();

    $booking = FazaaRequestBooking::whereHas('fazaaRequest', function ($query) use ($otherUser) {
        $query->where('user_id', $otherUser->id);
    })->first();

    if (!$booking) {
        $this->markTestSkipped('No fazaa request bookings found for other user.');
    }

    $response = $this->getJson(
        route('app.users.requests.fazaas.offers.show', [
            'id' => $booking->id
        ])
    );

    expect($response->status())->toBe(404);
});
