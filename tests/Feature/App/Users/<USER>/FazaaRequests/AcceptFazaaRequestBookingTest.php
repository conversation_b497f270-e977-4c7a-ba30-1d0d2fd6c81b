<?php

use App\Enums\Bookings\BookingStatusEnum;
use App\Events\FazaaRequestBookingAccepted;
use App\Models\User;
use App\Models\Booking\FazaaRequestBooking;
use Illuminate\Support\Facades\Event;

beforeEach(function () {
    $this->user = User::first();
    $this->actingAs($this->user, 'api');
});

it('can accept a fazaa request booking', function () {
    Event::fake();

    $booking = FazaaRequestBooking::whereHas('fazaaRequest', function ($query) {
        $query->where('user_id', $this->user->id);
    })->where('status', BookingStatusEnum::PENDING())
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No pending bookings found.');
    }

    $response = $this->postJson(
        route('app.users.requests.fazaas.offers.accept', [
            'id' => $booking->id
        ])
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::ACCEPTED());

    Event::assertDispatched(FazaaRequestBookingAccepted::class);
});

it('cannot accept other users booking', function () {
    $otherUser = User::where('id', '!=', $this->user->id)->first();

    $booking = FazaaRequestBooking::whereHas('fazaaRequest', function ($query) use ($otherUser) {
        $query->where('user_id', $otherUser->id);
    })
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No pending bookings found for other user.');
    }

    $response = $this->postJson(
        route('app.users.requests.fazaas.offers.accept', [
            'id' => $booking->id
        ])
    );

    expect($response->status())->toBe(404);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::PENDING());
});

it('cannot accept a non-pending booking', function () {
    $booking = FazaaRequestBooking::whereHas('fazaaRequest', function ($query) {
        $query->where('user_id', $this->user->id);
    })
        ->where('status', '!=', BookingStatusEnum::PENDING())
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No non-pending bookings found.');
    }

    $response = $this->postJson(
        route('app.users.requests.fazaas.offers.accept', [
            'id' => $booking->id
        ])
    );

    expect($response->status())->toBe(422);
});
