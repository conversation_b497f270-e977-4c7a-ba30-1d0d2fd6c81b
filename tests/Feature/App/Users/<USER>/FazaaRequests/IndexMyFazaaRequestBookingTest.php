<?php

use App\Models\User;
use Illuminate\Support\Facades\Event;

beforeEach(function () {
    $this->user = User::first();
    $this->actingAs($this->user, 'api');
});

it('can list my fazaa request bookings', function () {
    Event::fake();

    $response = $this->getJson(
        route('app.users.requests.fazaas.my-offers.index')
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $bookingsData = $response->json('data.data');
    foreach ($bookingsData as $booking) {
        expect($booking)->toHaveKeys([
            'id',
            'fazaa_request',
            'status',
            'note',
            'rejection_reason',
            'cancellation_reason',
            'cancelled_at',
            'created_at',
            'updated_at',
        ]);

        expect($booking['fazaa_request'])->toHaveKeys([
            'id',
            'fazaa_service_type',
            'specific_type',
            'transportation_type',
            'departure_datetime',
            'arrival_datetime',
            'from_city',
            'to_city',
            'service_location',
            'user',
        ]);
    }
});
