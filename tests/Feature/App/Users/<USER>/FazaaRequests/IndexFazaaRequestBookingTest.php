<?php

use App\Models\User;
use App\Models\Request\FazaaRequest;
use Illuminate\Support\Facades\Event;

beforeEach(function () {
    $this->user = User::first();
    $this->actingAs($this->user, 'api');
});

it('can list fazaa request bookings', function () {
    Event::fake();

    $fazaaRequest = FazaaRequest::where('user_id', $this->user->id)->first();

    if (!$fazaaRequest) {
        $this->markTestSkipped('No fazaa requests found.');
    }

    $response = $this->getJson(
        route('app.users.requests.fazaas.offers.index', [
            'id' => $fazaaRequest->id
        ])
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    expect($response->json('data.data.0'))->toHaveKeys([
        'id',
        'status',
        'note',
        'created_at',
        'updated_at',
    ]);
});

it('cannot list other users fazaa request bookings', function () {
    $otherUser = User::where('id', '!=', $this->user->id)->first();

    $fazaaRequest = FazaaRequest::where('user_id', $otherUser->id)->first();

    if (!$fazaaRequest) {
        $this->markTestSkipped('No fazaa requests found for other user.');
    }

    $response = $this->getJson(
        route('app.users.requests.fazaas.offers.index', [
            'id' => $fazaaRequest->id
        ])
    );

    expect($response->status())->toBe(404);
});
