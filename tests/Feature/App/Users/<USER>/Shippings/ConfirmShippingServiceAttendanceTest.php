<?php

use App\Models\User;
use App\Models\Service\ShippingService;
use Carbon\Carbon;

use function Pest\Laravel\post;

beforeEach(function () {
    $this->user = User::whereHas('shippingServices')->inRandomOrder()->first();
    $this->token = $this->user->createToken('test-token')->plainTextToken;
});

it('can confirm shipping service attendance within valid time window', function () {
    $departureTime = Carbon::now()->addMinutes(15);

    $shippingService = $this->user->shippingServices()->first();
    $shippingService->update([
        'departure_datetime' => $departureTime,
        'attendance_confirmed_at' => null,
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.services.shippings.confirm-attendance', $shippingService->id)
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $shippingService->refresh();
    expect($shippingService->attendance_confirmed_at)->not->toBeNull();
});

it('cannot confirm attendance for cancelled shipping service', function () {
    $departureTime = Carbon::now()->addMinutes(15);

    $shippingService = $this->user->shippingServices()->first();
    $shippingService->update([
        'departure_datetime' => $departureTime,
        'attendance_confirmed_at' => null,
        'cancelled_at' => Carbon::now()
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.services.shippings.confirm-attendance', $shippingService->id)
    );

    expect($response->status())->toBe(422);
    expect($shippingService->fresh()->attendance_confirmed_at)->toBeNull();
});

it('cannot confirm attendance when already confirmed', function () {
    $departureTime = Carbon::now()->addMinutes(15);

    $shippingService = $this->user->shippingServices()->first();
    $shippingService->update([
        'departure_datetime' => $departureTime,
        'attendance_confirmed_at' => Carbon::now(),
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.services.shippings.confirm-attendance', $shippingService->id)
    );

    expect($response->status())->toBe(422);
});

it('cannot confirm attendance outside 30-minute window before departure', function () {
    $departureTime = Carbon::now()->addHours(2);

    $shippingService = $this->user->shippingServices()->first();
    $shippingService->update([
        'departure_datetime' => $departureTime,
        'attendance_confirmed_at' => null,
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.services.shippings.confirm-attendance', $shippingService->id)
    );

    expect($response->status())->toBe(422);
    expect($shippingService->fresh()->attendance_confirmed_at)->toBeNull();
});

it('cannot confirm attendance after departure time', function () {
    $departureTime = Carbon::now()->subHours(1);

    $shippingService = $this->user->shippingServices()->first();
    $shippingService->update([
        'departure_datetime' => $departureTime,
        'attendance_confirmed_at' => null,
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.services.shippings.confirm-attendance', $shippingService->id)
    );

    expect($response->status())->toBe(422);
    expect($shippingService->fresh()->attendance_confirmed_at)->toBeNull();
});

it('cannot confirm attendance for non-owned shipping service', function () {
    $otherUser = User::where('id', '!=', $this->user->id)->inRandomOrder()->first();
    $departureTime = Carbon::now()->addMinutes(15);

    $shippingService = $otherUser->shippingServices()->first();
    $shippingService->update([
        'departure_datetime' => $departureTime,
        'attendance_confirmed_at' => null,
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.services.shippings.confirm-attendance', $shippingService->id)
    );

    expect($response->status())->toBe(404);
    expect($shippingService->fresh()->attendance_confirmed_at)->toBeNull();
});
