<?php

use App\Models\User;
use App\Enums\Travel\TransportationTypeEnum;
use Carbon\Carbon;

use function Pest\Laravel\patch;

beforeEach(function () {
    $user = User::inRandomOrder()->has('shippingRequests')->first();
    $this->token = $user->createToken('test-token')->plainTextToken;
    $this->user = $user;
});

it('can update a shipping request', function () {
    $shippingRequest = $this->user->shippingRequests()->first();
    $shippingRequest->update([
        'cancelled_at' => null,
        'departure_datetime' => Carbon::now()->addDay(),
        'arrival_datetime' => Carbon::now()->addDay()->addHours(2),
    ]);

    $updateData = [
        'transportation_type' => fake()->randomElement(TransportationTypeEnum::values()),
        'note' => fake()->sentence(),
        'is_fragile' => fake()->boolean(),
        'from_city_en' => 'Riyadh',
        'from_city_ar' => 'الرياض',
        'to_city_en' => 'Jeddah',
        'to_city_ar' => 'جدة',
        'from_location' => [
            'lat' => fake()->latitude(),
            'lng' => fake()->longitude(),
        ],
        'to_location' => [
            'lat' => fake()->latitude(),
            'lng' => fake()->longitude(),
        ],
        'delivery_location' => fake()->address(),
    ];

    // Handle shipping options conditionally
    $canShipPackages = fake()->boolean();
    if ($canShipPackages) {
        $updateData['packages_volume'] = fake()->numberBetween(1, 100);
    }
    $canShipDocuments = fake()->boolean();
    if ($canShipDocuments) {
        $updateData['document_volume'] = fake()->numberBetween(1, 100);
    }
    $canShipFurniture = fake()->boolean();
    if ($canShipFurniture) {
        $updateData['furniture_volume'] = fake()->numberBetween(1, 100);
    }

    $updateData['can_ship_packages'] = $canShipPackages;
    $updateData['can_ship_documents'] = $canShipDocuments;
    $updateData['can_ship_furniture'] = $canShipFurniture;

    $response = patch(
        route('app.users.requests.shippings.update', $shippingRequest->id),
        $updateData,
        ['Authorization' => 'Bearer ' . $this->token]
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $responseData = $response->json('data');

    // Verify response structure and data types
    expect($responseData)->toHaveKeys([
        'id',
        'transportation_type',
        'can_ship_packages',
        'can_ship_documents',
        'can_ship_furniture',
        'departure_datetime',
        'arrival_datetime',
        'from_city',
        'to_city',
        'from_location',
        'to_location',
        'packages_volume',
        'document_volume',
        'furniture_volume',
        'delivery_location',
        'is_fragile',
        'note'
    ]);

    // Verify the boolean fields
    expect($responseData['can_ship_packages'])->toBe($updateData['can_ship_packages']);
    expect($responseData['can_ship_documents'])->toBe($updateData['can_ship_documents']);
    expect($responseData['can_ship_furniture'])->toBe($updateData['can_ship_furniture']);
    expect($responseData['is_fragile'])->toBe($updateData['is_fragile']);

    // Verify the text fields
    expect($responseData['note'])->toBe($updateData['note']);
    expect($responseData['delivery_location'])->toBe($updateData['delivery_location']);

    // Verify city data structure
    expect($responseData['from_city'])->toHaveKeys(['id', 'name', 'current_weather']);
    expect($responseData['to_city'])->toHaveKeys(['id', 'name', 'current_weather']);

    // Verify location structure
    expect($responseData['from_location'])->toHaveKeys(['lat', 'lng']);
    expect($responseData['to_location'])->toHaveKeys(['lat', 'lng']);

    // Verify volumes conditionally
    if ($canShipPackages) {
        expect($responseData['packages_volume'])->toBe($updateData['packages_volume']);
    }
    if ($canShipDocuments) {
        expect($responseData['document_volume'])->toBe($updateData['document_volume']);
    }
    if ($canShipFurniture) {
        expect($responseData['furniture_volume'])->toBe($updateData['furniture_volume']);
    }
});

it('cannot update another user\'s shipping request', function () {
    $otherUser = User::where('id', '!=', $this->user->id)->first();
    $shippingRequest = $otherUser->shippingRequests()->first();

    $updateData = [
        'note' => fake()->sentence(),
        'transportation_type' => 'car',
    ];

    $response = patch(
        route('app.users.requests.shippings.update', $shippingRequest->id),
        $updateData,
        ['Authorization' => 'Bearer ' . $this->token]
    );

    expect($response->status())->toBe(404);
});

it('validates required fields when updating a shipping request', function () {
    $shippingRequest = $this->user->shippingRequests()->first();
    $shippingRequest->update([
        'cancelled_at' => null,
        'departure_datetime' => Carbon::now()->addDay(),
    ]);

    $response = patch(
        route('app.users.requests.shippings.update', $shippingRequest->id),
        [
            'from_location' => 'invalid-location',
            'packages_volume' => 'invalid-volume',
            'transportation_type' => 'INVALID_TYPE'
        ],
        ['Authorization' => 'Bearer ' . $this->token]
    );

    $response->assertStatus(422);
});

it('cannot update a cancelled shipping request', function () {
    $shippingRequest = $this->user->shippingRequests()->first();
    $shippingRequest->update([
        'cancelled_at' => Carbon::now(),
        'departure_datetime' => Carbon::now()->addDay(),
    ]);

    $updateData = [
        'transportation_type' => 'car',
        'note' => fake()->sentence(),
    ];

    $response = patch(
        route('app.users.requests.shippings.update', $shippingRequest->id),
        $updateData,
        ['Authorization' => 'Bearer ' . $this->token]
    );

    $response->assertStatus(422);
    $response->assertJsonFragment([
        'message' => __('exceptions.shipping_request.cannot_update_cancelled_shipping')
    ]);
});

it('cannot update a shipping request after departure', function () {
    $shippingRequest = $this->user->shippingRequests()->first();
    $shippingRequest->update([
        'cancelled_at' => null,
        'departure_datetime' => Carbon::now()->subHour()
    ]);

    $updateData = [
        'transportation_type' => 'car',
        'note' => fake()->sentence(),
    ];

    $response = patch(
        route('app.users.requests.shippings.update', $shippingRequest->id),
        $updateData,
        ['Authorization' => 'Bearer ' . $this->token]
    );

    expect($response->status())->toBe(422);
    expect($response->json('message'))->toBe(__('exceptions.shipping_request.cannot_update_after_departure'));
});
