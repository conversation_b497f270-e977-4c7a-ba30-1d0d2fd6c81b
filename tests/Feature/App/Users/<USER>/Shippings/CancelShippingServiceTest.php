<?php

use App\Models\User;
use App\Models\Service\ShippingService;
use Carbon\Carbon;

use function Pest\Laravel\post;

beforeEach(function () {
    $this->user = User::whereHas('shippingServices')->inRandomOrder()->first();
    $this->token = $this->user->createToken('test-token')->plainTextToken;
});

it('can cancel a shipping service', function () {
    $shippingService = $this->user->shippingServices()->first();
    $shippingService->update([
        'departure_datetime' => Carbon::now()->addDay(),
        'cancelled_at' => null
    ]);

    $data = [
        'cancellation_reason' => 'Service no longer available',
        'note' => 'Need to cancel due to vehicle maintenance'
    ];

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.services.shippings.cancel', $shippingService->id),
        $data
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $shippingService->refresh();
    expect($shippingService->cancelled_at)->not->toBeNull();
    expect($shippingService->cancellation_reason)->toBe($data['cancellation_reason']);
    expect($shippingService->cancellation_note)->toBe($data['note']);
});

it('cannot cancel an already cancelled shipping service', function () {
    $shippingService = $this->user->shippingServices()->first();
    $shippingService->update([
        'departure_datetime' => Carbon::now()->addDay(),
        'cancelled_at' => Carbon::now()
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.services.shippings.cancel', $shippingService->id),
        [
            'cancellation_reason' => 'Test cancellation reason'
        ]
    );

    expect($response->status())->toBe(422);
    expect($response->json('message'))->toBe(__('exceptions.shipping_service.already_cancelled'));
});

it('cannot cancel a shipping service within 4 hours of departure', function () {
    $shippingService = $this->user->shippingServices()->first();
    $shippingService->update([
        'departure_datetime' => Carbon::now()->addHours(3),
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.services.shippings.cancel', $shippingService->id),
        [
            'cancellation_reason' => 'Test cancellation reason'
        ]
    );

    expect($response->status())->toBe(422);
    expect($response->json('message'))->toBe(__('exceptions.shipping_service.cannot_cancel_within_hour'));
});

it('cannot cancel another user\'s shipping service', function () {
    $otherUser = User::whereHas('shippingServices')->where('id', '!=', $this->user->id)->inRandomOrder()->first();
    $shippingService = $otherUser->shippingServices()->first();
    $shippingService->update([
        'departure_datetime' => Carbon::now()->addDay(),
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.services.shippings.cancel', $shippingService->id),
        [
            'cancellation_reason' => 'Test cancellation reason'
        ]
    );

    expect($response->status())->toBe(404);
});

it('validates cancellation reasons when canceling a shipping service', function () {
    $shippingService = $this->user->shippingServices()->first();
    $shippingService->update([
        'departure_datetime' => Carbon::now()->addDay(),
        'cancelled_at' => null
    ]);

    // Test empty reason
    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.services.shippings.cancel', $shippingService->id),
        ['cancellation_reason' => '']
    );

    expect($response->status())->toBe(422);

    // Test too long reason
    $response = $this->actingAs($this->user, 'api')->post(
        route('app.users.services.shippings.cancel', $shippingService->id),
        [
            'cancellation_reason' => str_repeat('a', 256)
        ]
    );

    expect($response->status())->toBe(422);
});
