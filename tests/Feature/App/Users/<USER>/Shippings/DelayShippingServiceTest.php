<?php

use App\Models\User;
use App\Models\Service\ShippingService;
use Carbon\Carbon;

use function Pest\Laravel\patch;

beforeEach(function () {
    $this->user = User::whereHas('shippingServices')->inRandomOrder()->first();
    $this->token = $this->user->createToken('test-token')->plainTextToken;
});

it('can delay a shipping service', function () {
    $shippingService = $this->user->shippingServices()->first();
    $shippingService->update([
        'departure_datetime' => Carbon::now()->addDay(),
        'arrival_datetime' => Carbon::now()->addDay()->addHours(2),
        'cancelled_at' => null
    ]);

    $newDepartureDateTime = Carbon::now()->addDays(2);
    $newArrivalDateTime = Carbon::now()->addDays(2)->addHours(2);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.services.shippings.delay', $shippingService->id),
        [
            'departure_datetime' => $newDepartureDateTime->format('Y-m-d H:i:s'),
            'arrival_datetime' => $newArrivalDateTime->format('Y-m-d H:i:s'),
            'delay_reason' => 'medical_reason',
            'note' => 'Need to delay due to medical appointment'
        ]
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $shippingService->refresh();
    expect($shippingService->departure_datetime->format('Y-m-d H:i:s'))->toBe($newDepartureDateTime->format('Y-m-d H:i:s'));
    expect($shippingService->arrival_datetime->format('Y-m-d H:i:s'))->toBe($newArrivalDateTime->format('Y-m-d H:i:s'));
    expect($shippingService->delay_reason)->toBe('medical_reason');
    expect($shippingService->delay_note)->toBe('Need to delay due to medical appointment');
    expect($shippingService->delayed_at)->not->toBeNull();
});

it('cannot delay a cancelled shipping service', function () {
    $shippingService = $this->user->shippingServices()->first();
    $shippingService->update([
        'departure_datetime' => Carbon::now()->addDay(),
        'arrival_datetime' => Carbon::now()->addDay()->addHours(2),
        'cancelled_at' => Carbon::now()
    ]);

    $newDepartureDateTime = Carbon::now()->addDays(2);
    $newArrivalDateTime = Carbon::now()->addDays(2)->addHours(2);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.services.shippings.delay', $shippingService->id),
        [
            'departure_datetime' => $newDepartureDateTime->format('Y-m-d H:i:s'),
            'arrival_datetime' => $newArrivalDateTime->format('Y-m-d H:i:s'),
            'delay_reason' => 'medical_reason',
            'note' => 'Need to delay due to medical appointment'
        ]
    );

    expect($response->status())->toBe(422);
    expect($response->json('message'))->toBe(__('exceptions.shipping_service.cannot_delay_cancelled_shipping'));
});

it('cannot delay a shipping service after departure', function () {
    $shippingService = $this->user->shippingServices()->first();
    $shippingService->update([
        'departure_datetime' => Carbon::now()->subHour(),
        'arrival_datetime' => Carbon::now()->addHour(),
        'cancelled_at' => null
    ]);

    $newDepartureDateTime = Carbon::now()->addDays(2);
    $newArrivalDateTime = Carbon::now()->addDays(2)->addHours(2);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.services.shippings.delay', $shippingService->id),
        [
            'departure_datetime' => $newDepartureDateTime->format('Y-m-d H:i:s'),
            'arrival_datetime' => $newArrivalDateTime->format('Y-m-d H:i:s'),
            'delay_reason' => 'medical_reason',
            'note' => 'Need to delay due to medical appointment'
        ]
    );

    expect($response->status())->toBe(422);
    expect($response->json('message'))->toBe(__('exceptions.shipping_service.cannot_delay_started_shipping'));
});

it('cannot delay another user\'s shipping service', function () {
    $otherUser = User::where('id', '!=', $this->user->id)->whereHas('shippingServices')->inRandomOrder()->first();
    $shippingService = $otherUser->shippingServices()->first();

    $shippingService->update([
        'departure_datetime' => Carbon::now()->addDay(),
        'arrival_datetime' => Carbon::now()->addDay()->addHours(2),
        'cancelled_at' => null
    ]);

    $newDepartureDateTime = Carbon::now()->addDays(2);
    $newArrivalDateTime = Carbon::now()->addDays(2)->addHours(2);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.services.shippings.delay', $shippingService->id),
        [
            'departure_datetime' => $newDepartureDateTime->format('Y-m-d H:i:s'),
            'arrival_datetime' => $newArrivalDateTime->format('Y-m-d H:i:s'),
            'delay_reason' => 'medical_reason',
            'note' => 'Need to delay due to medical appointment'
        ]
    );

    expect($response->status())->toBe(404);
});

it('validates datetime format when delaying a shipping service', function () {
    $shippingService = $this->user->shippingServices()->first();
    $shippingService->update([
        'departure_datetime' => Carbon::now()->addDay(),
        'arrival_datetime' => Carbon::now()->addDay()->addHours(2),
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.services.shippings.delay', $shippingService->id),
        [
            'departure_datetime' => 'invalid-date',
            'arrival_datetime' => 'invalid-date',
            'delay_reason' => 'medical_reason',
            'note' => 'Need to delay due to medical appointment'
        ]
    );

    expect($response->status())->toBe(422);
});

it('validates delay reasons', function () {
    $shippingService = $this->user->shippingServices()->first();
    $shippingService->update([
        'departure_datetime' => Carbon::now()->addDay(),
        'arrival_datetime' => Carbon::now()->addDay()->addHours(2),
        'cancelled_at' => null
    ]);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.services.shippings.delay', $shippingService->id),
        [
            'departure_datetime' => Carbon::now()->addDays(2),
            'arrival_datetime' => Carbon::now()->addDays(2)->addHours(2),
            'delay_reason' => ['invalid_reason'],
            'note' => 'Invalid reason test'
        ]
    );

    expect($response->status())->toBe(422);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.services.shippings.delay', $shippingService->id),
        [
            'departure_datetime' => Carbon::now()->addDays(2),
            'arrival_datetime' => Carbon::now()->addDays(2)->addHours(2),
            'delay_reason' => '',
            'note' => 'Empty reason test'
        ]
    );

    expect($response->status())->toBe(422);

    $response = $this->actingAs($this->user, 'api')->patch(
        route('app.users.services.shippings.delay', $shippingService->id),
        [
            'departure_datetime' => Carbon::now()->addDays(2),
            'arrival_datetime' => Carbon::now()->addDays(2)->addHours(2),
            'delay_reason' => str_repeat('a', 256),
            'note' => 'Too long reason test'
        ]
    );

    expect($response->status())->toBe(422);
});
