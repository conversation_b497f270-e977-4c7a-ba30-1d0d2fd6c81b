<?php

use App\Models\User;
use App\Models\Service\ShippingService;
use Illuminate\Support\Facades\Event;

beforeEach(function () {
    $this->user = User::first();
});

it('can show my shipping service details', function () {
    $this->actingAs($this->user, 'api');

    Event::fake();

    $shippingService = $this->user->shippingServices()->first();

    if (!$shippingService) {
        $this->markTestSkipped('No shipping services found.');
    }

    $response = $this->getJson(
        route('app.users.services.shippings.show', [
            'id' => $shippingService->id
        ])
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $serviceData = $response->json('data');
    expect($serviceData)->toHaveKeys([
        'id',
        'transportation_type',
        'can_ship_packages',
        'can_ship_documents',
        'can_ship_furniture',
        'departure_datetime',
        'arrival_datetime',
        'packages_volume',
        'available_packages_volume',
        'document_volume',
        'available_document_volume',
        'furniture_volume',
        'available_furniture_volume',
        'from_city',
        'to_city',
        'package_price',
        'document_price',
        'furniture_price',
        'car',
    ]);

    if ($serviceData['can_ship_packages']) {
        expect($serviceData['packages_volume'])->toBeGreaterThan(0);
        expect($serviceData['package_price'])->toBeGreaterThan(0);
    }
    if ($serviceData['can_ship_documents']) {
        expect($serviceData['document_volume'])->toBeGreaterThan(0);
        expect($serviceData['document_price'])->toBeGreaterThan(0);
    }
    if ($serviceData['can_ship_furniture']) {
        expect($serviceData['furniture_volume'])->toBeGreaterThan(0);
        expect($serviceData['furniture_price'])->toBeGreaterThan(0);
    }
});

it('cannot show other users shipping service details', function () {
    $this->actingAs($this->user, 'api');
    $otherUser = User::where('id', '!=', $this->user->id)->first();

    $shippingService = $otherUser->shippingServices()->first();

    if (!$shippingService) {
        $this->markTestSkipped('No shipping services found for other user.');
    }

    $response = $this->getJson(
        route('app.users.services.shippings.show', [
            'id' => $shippingService->id
        ])
    );

    expect($response->status())->toBe(404);
});

it('returns 404 for non-existent shipping service', function () {
    $this->actingAs($this->user, 'api');

    $response = $this->getJson(
        route('app.users.services.shippings.show', [
            'id' => 999999999
        ])
    );

    expect($response->status())->toBe(404);
});

it('requires authentication', function () {
    $shippingService = ShippingService::first();

    $response = $this->getJson(
        route('app.users.services.shippings.show', [
            'id' => $shippingService->id
        ])
    );

    expect($response->status())->toBe(401);
});
