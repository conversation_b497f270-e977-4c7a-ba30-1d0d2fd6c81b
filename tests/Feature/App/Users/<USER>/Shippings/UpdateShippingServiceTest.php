<?php

use App\Models\User;
use App\Enums\Travel\TransportationTypeEnum;
use Carbon\Carbon;

use function Pest\Laravel\patch;

beforeEach(function () {
    $user = User::inRandomOrder()->has('shippingServices')->first();
    $this->token = $user->createToken('test-token')->plainTextToken;
    $this->user = $user;
});

it('can update a shipping service', function () {
    $shippingService = $this->user->shippingServices()->first();

    if (!$shippingService) {
        $this->markTestSkipped('No shipping service found');
    }

    $shippingService->update([
        'cancelled_at' => null,
        'departure_datetime' => Carbon::now()->addDay(),
        'arrival_datetime' => Carbon::now()->addDay()->addHours(2),
    ]);


    $transportationType = fake()->randomElement(TransportationTypeEnum::values());
    $updateData = [
        'transportation_type' => $transportationType,
        'from_city_en' => 'Riyadh',
        'from_city_ar' => 'الرياض',
        'to_city_en' => 'Jeddah',
        'to_city_ar' => 'جدة',
        'from_location' => [
            'lat' => fake()->latitude(),
            'lng' => fake()->longitude(),
        ],
        'to_location' => [
            'lat' => fake()->latitude(),
            'lng' => fake()->longitude(),
        ],
    ];

    // Handle shipping options conditionally
    $canShipPackages = fake()->boolean();
    if ($canShipPackages) {
        $updateData['packages_volume'] = fake()->numberBetween(1, 100);
        $updateData['package_price'] = fake()->numberBetween(1, 1000);
    } else {
        $updateData['packages_volume'] = null;
        $updateData['package_price'] = null;
    }

    $canShipDocuments = fake()->boolean();
    if ($canShipDocuments) {
        $updateData['document_volume'] = fake()->numberBetween(1, 100);
        $updateData['document_price'] = fake()->numberBetween(1, 1000);
    } else {
        $updateData['document_volume'] = null;
        $updateData['document_price'] = null;
    }

    $canShipFurniture = $transportationType !== TransportationTypeEnum::FLIGHT() ? fake()->boolean() : false;
    if ($canShipFurniture) {
        $updateData['furniture_volume'] = fake()->numberBetween(1, 100);
        $updateData['furniture_price'] = fake()->numberBetween(1, 1000);
    } else {
        $updateData['furniture_volume'] = null;
        $updateData['furniture_price'] = null;
    }

    $updateData['can_ship_packages'] = $canShipPackages;
    $updateData['can_ship_documents'] = $canShipDocuments;
    $updateData['can_ship_furniture'] = $canShipFurniture;

    // Ensure at least one shipping type is enabled
    if (!$canShipPackages && !$canShipDocuments && !$canShipFurniture) {
        $updateData['can_ship_packages'] = true;
        $updateData['packages_volume'] = fake()->numberBetween(1, 100);
        $updateData['package_price'] = fake()->numberBetween(1, 1000);
    }

    $response = patch(
        route('app.users.services.shippings.update', $shippingService->id),
        $updateData,
        ['Authorization' => 'Bearer ' . $this->token]
    );

    expect($response->status())->toBe(200);

    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $responseData = $response->json('data');

    // Verify response structure and data types
    expect($responseData)->toHaveKeys([
        'id',
        'transportation_type',
        'can_ship_packages',
        'can_ship_documents',
        'can_ship_furniture',
        'departure_datetime',
        'arrival_datetime',
        'from_city',
        'to_city',
        'from_location',
        'to_location',
        'packages_volume',
        'document_volume',
        'furniture_volume',
    ]);

    // Verify the boolean fields
    expect($responseData['can_ship_packages'])->toBe($updateData['can_ship_packages']);
    expect($responseData['can_ship_documents'])->toBe($updateData['can_ship_documents']);
    expect($responseData['can_ship_furniture'])->toBe($updateData['can_ship_furniture']);

    // Verify city data structure
    expect($responseData['from_city'])->toHaveKeys(['id', 'name', 'current_weather']);
    expect($responseData['to_city'])->toHaveKeys(['id', 'name', 'current_weather']);

    // Verify location structure
    expect($responseData['from_location'])->toHaveKeys(['lat', 'lng']);
    expect($responseData['to_location'])->toHaveKeys(['lat', 'lng']);

    // Verify volumes conditionally
    if ($canShipPackages) {
        expect($responseData['packages_volume'])->toBe($updateData['packages_volume']);
    }
    if ($canShipDocuments) {
        expect($responseData['document_volume'])->toBe($updateData['document_volume']);
    }
    if ($canShipFurniture) {
        expect($responseData['furniture_volume'])->toBe($updateData['furniture_volume']);
    }
});

it('cannot update another user\'s shipping service', function () {
    $otherUser = User::where('id', '!=', $this->user->id)->first();
    $shippingService = $otherUser->shippingServices()->first();

    $updateData = [
        'transportation_type' => fake()->randomElement(TransportationTypeEnum::values()),
    ];

    $response = patch(
        route('app.users.services.shippings.update', $shippingService->id),
        $updateData,
        ['Authorization' => 'Bearer ' . $this->token]
    );

    expect($response->status())->toBe(404);
});

it('validates required fields when updating a shipping service', function () {
    $shippingService = $this->user->shippingServices()->first();
    $shippingService->update([
        'cancelled_at' => null,
        'departure_datetime' => Carbon::now()->addDay(),
    ]);

    $response = patch(
        route('app.users.services.shippings.update', $shippingService->id),
        [
            'from_location' => 'invalid-location',
            'transportation_type' => 'INVALID_TYPE'
        ],
        ['Authorization' => 'Bearer ' . $this->token]
    );

    expect($response->status())->toBe(422);
});

it('cannot update a cancelled shipping service', function () {
    $shippingService = $this->user->shippingServices()->first();
    $shippingService->update([
        'cancelled_at' => Carbon::now(),
        'departure_datetime' => Carbon::now()->addDay(),
    ]);

    $updateData = [
        'transportation_type' => fake()->randomElement(TransportationTypeEnum::values()),
    ];

    $response = patch(
        route('app.users.services.shippings.update', $shippingService->id),
        $updateData,
        ['Authorization' => 'Bearer ' . $this->token]
    );

    expect($response->status())->toBe(422);
    expect($response->json('message'))->toBe(__('exceptions.shipping_service.cannot_update_cancelled_shipping'));
});

it('cannot update a shipping service after departure', function () {
    $shippingService = $this->user->shippingServices()->first();
    $shippingService->update([
        'cancelled_at' => null,
        'departure_datetime' => Carbon::now()->subHour()
    ]);

    $updateData = [
        'transportation_type' => fake()->randomElement(TransportationTypeEnum::values()),
    ];

    $response = patch(
        route('app.users.services.shippings.update', $shippingService->id),
        $updateData,
        ['Authorization' => 'Bearer ' . $this->token]
    );

    expect($response->status())->toBe(422);
    expect($response->json('message'))->toBe(__('exceptions.shipping_service.cannot_update_after_departure'));
});
