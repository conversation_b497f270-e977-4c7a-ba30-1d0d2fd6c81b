<?php

use App\Models\User;
use App\Enums\Travel\TripStatusEnum;
use Carbon\Carbon;

use function Pest\Laravel\get;


beforeEach(function () {
    $user = User::first();
    $this->token = $user->createToken('test-token')->plainTextToken;
});

it('can access my shipping requests', function () {
    $response = get(route('app.users.requests.shippings.index'), [
        'Authorization' => 'Bearer ' . $this->token
    ]);

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $requestData = $response->json('data.data');
    foreach ($requestData as $request) {
        expect($request)->toHaveKeys([
            'id',
            'transportation_type',
            'can_ship_packages',
            'can_ship_documents',
            'can_ship_furniture',
            'departure_datetime',
            'arrival_datetime',
            'from_city',
            'from_location',
            'to_city',
            'to_location',
            'packages_volume',
            'document_volume',
            'furniture_volume',
            'delivery_location',
            'is_fragile',
            'note',
        ]);
    }
});

it('can filter my shipping requests by status', function () {
    $statuses = [
        TripStatusEnum::ACTIVE(),
        TripStatusEnum::COMPLETED(),
        TripStatusEnum::CANCELLED(),
    ];

    foreach ($statuses as $status) {
        $response = get(route('app.users.requests.shippings.index', [
            'status' => $status
        ]), [
            'Authorization' => 'Bearer ' . $this->token
        ]);

        expect($response->status())->toBe(200);
        expect($response->json())->toHaveKeys([
            'success',
            'message',
            'data',
            'status_code'
        ]);
    }
});

it('requires authentication for my shipping requests', function () {
    $response = get(route('app.users.requests.shippings.index'));
    expect($response->status())->toBe(401);
});
