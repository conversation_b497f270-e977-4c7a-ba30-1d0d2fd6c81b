<?php

use App\Models\User;
use Carbon\Carbon;
use App\Enums\Travel\TripStatusEnum;

it('can cancel a shipping request', function () {
    $user = User::first();
    $shippingRequest = $user->shippingRequests()
        ->whereNull('cancelled_at')
        ->where('departure_datetime', '>', Carbon::now()->addHours(4))
        ->firstOr(function () {
            $this->markTestSkipped('No available shipping request found for cancellation.');
        });

    $cancellationReasons = 'Changed shipping plans';
    $note = 'Need to cancel due to schedule change';

    $response = $this->actingAs($user, 'api')
        ->postJson(route('app.users.requests.shippings.cancel', $shippingRequest->id), [
            'cancellation_reason' => $cancellationReasons,
            'note' => $note
        ]);

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $shippingRequest->refresh();
    expect($shippingRequest->cancelled_at)->not->toBeNull()
        ->and($shippingRequest->cancellation_reason)->toBe($cancellationReasons)
        ->and($shippingRequest->cancellation_note)->toBe($note);
});

it('cannot cancel an already cancelled shipping request', function () {
    $user = User::first();
    $shippingRequest = $user->shippingRequests()
        ->whereNotNull('cancelled_at')
        ->firstOr(function () {
            $this->markTestSkipped('No cancelled shipping request found.');
        });

    $response = $this->actingAs($user, 'api')
        ->postJson(route('app.users.requests.shippings.cancel', $shippingRequest->id), [
            'cancellation_reason' => 'Changed plans'
        ]);

    expect($response->status())->toBe(422)
        ->and($response->json('message'))->toBe(__('exceptions.shipping_request.already_cancelled'));
});

it('cannot cancel another user\'s shipping request', function () {
    $user = User::first();
    $otherUser = User::where('id', '!=', $user->id)->first();
    $shippingRequest = $otherUser->shippingRequests()
        ->whereNull('cancelled_at')
        ->firstOr(function () {
            $this->markTestSkipped('No shipping request found for other user.');
        });

    $response = $this->actingAs($user, 'api')
        ->postJson(route('app.users.requests.shippings.cancel', $shippingRequest->id), [
            'cancellation_reason' => 'Changed plans'
        ]);

    expect($response->status())->toBe(404);
});

it('validates cancellation reasons', function () {
    $user = User::first();
    $shippingRequest = $user->shippingRequests()
        ->whereNull('cancelled_at')
        ->where('departure_datetime', '>', Carbon::now()->addHours(4))
        ->firstOr(function () {
            $this->markTestSkipped('No available shipping request found for validation test.');
        });

    $data = [
        'cancellation_reason' => 'Changed plans',
        'note' => 'Need to cancel due to schedule change'
    ];

    // Test empty reason
    $response = $this->actingAs($user, 'api')
        ->postJson(route('app.users.requests.shippings.cancel', $shippingRequest->id), [
            'cancellation_reason' => '',
            'note' => $data['note']
        ]);
    expect($response->status())->toBe(422);

    // Test too long reason
    $response = $this->actingAs($user, 'api')
        ->postJson(route('app.users.requests.shippings.cancel', $shippingRequest->id), [
            'cancellation_reason' => str_repeat('a', 256),
            'note' => $data['note']
        ]);

    expect($response->status())->toBe(422);
});
