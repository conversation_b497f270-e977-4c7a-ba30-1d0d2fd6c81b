<?php

use App\Models\User;
use Carbon\Carbon;

it('can delay a shipping request', function () {
    $user = User::first();
    $shippingRequest = $user->shippingRequests()
        ->whereNull('cancelled_at')
        ->where('departure_datetime', '>', Carbon::now())
        ->firstOr(function () {
            $this->markTestSkipped('No available shipping request found for delay.');
        });

    $newDepartureDateTime = Carbon::now()->addDays(2);
    $newArrivalDateTime = $newDepartureDateTime->copy()->addHours(2);

    $response = $this->actingAs($user, 'api')
        ->patchJson(route('app.users.requests.shippings.delay', $shippingRequest->id), [
            'departure_datetime' => $newDepartureDateTime->toDateTimeString(),
            'arrival_datetime' => $newArrivalDateTime->toDateTimeString(),
            'delay_reason' => 'medical_reason',
            'note' => 'Need to delay due to medical appointment'
        ]);

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $shippingRequest->refresh();
    expect($shippingRequest->departure_datetime->toDateTimeString())->toBe($newDepartureDateTime->toDateTimeString())
        ->and($shippingRequest->arrival_datetime->toDateTimeString())->toBe($newArrivalDateTime->toDateTimeString());
    expect($shippingRequest->delay_reason)->toBe('medical_reason');
    expect($shippingRequest->delay_note)->toBe('Need to delay due to medical appointment');
    expect($shippingRequest->delayed_at)->not->toBeNull();
});

it('cannot delay a cancelled shipping request', function () {
    $user = User::first();
    $shippingRequest = $user->shippingRequests()
        ->whereNotNull('cancelled_at')
        ->firstOr(function () {
            $this->markTestSkipped('No cancelled shipping request found.');
        });

    $newDepartureDateTime = Carbon::now()->addDays(2);
    $newArrivalDateTime = $newDepartureDateTime->copy()->addHours(2);

    $response = $this->actingAs($user, 'api')
        ->patchJson(route('app.users.requests.shippings.delay', $shippingRequest->id), [
            'departure_datetime' => $newDepartureDateTime->toDateTimeString(),
            'arrival_datetime' => $newArrivalDateTime->toDateTimeString(),
            'delay_reason' => 'medical_reason',
            'note' => 'Need to delay due to medical appointment'
        ]);

    expect($response->status())->toBe(422)
        ->and($response->json('message'))->toBe(__('exceptions.shipping_request.cannot_delay_cancelled_shipping'));
});

it('cannot delay a started shipping request', function () {
    $user = User::first();
    $shippingRequest = $user->shippingRequests()
        ->whereNull('cancelled_at')
        ->where('departure_datetime', '<=', Carbon::now())
        ->firstOr(function () {
            $this->markTestSkipped('No started shipping request found.');
        });

    $newDepartureDateTime = Carbon::now()->addDays(2);
    $newArrivalDateTime = $newDepartureDateTime->copy()->addHours(2);

    $response = $this->actingAs($user, 'api')
        ->patchJson(route('app.users.requests.shippings.delay', $shippingRequest->id), [
            'departure_datetime' => $newDepartureDateTime->toDateTimeString(),
            'arrival_datetime' => $newArrivalDateTime->toDateTimeString(),
            'delay_reason' => 'medical_reason',
            'note' => 'Need to delay due to medical appointment'
        ]);

    expect($response->status())->toBe(422)
        ->and($response->json('message'))->toBe(__('exceptions.shipping_request.cannot_delay_started_shipping'));
});

it('validates delay request data', function () {
    $user = User::first();
    $shippingRequest = $user->shippingRequests()
        ->whereNull('cancelled_at')
        ->where('departure_datetime', '>', Carbon::now())
        ->firstOr(function () {
            $this->markTestSkipped('No available shipping request found for validation test.');
        });

    // Test missing departure_datetime
    $response = $this->actingAs($user, 'api')
        ->patchJson(route('app.users.requests.shippings.delay', $shippingRequest->id), [
            'arrival_datetime' => Carbon::now()->addDays(2)->toDateTimeString(),
        ]);
    expect($response->status())->toBe(422);

    // Test missing arrival_datetime
    $response = $this->actingAs($user, 'api')
        ->patchJson(route('app.users.requests.shippings.delay', $shippingRequest->id), [
            'departure_datetime' => Carbon::now()->addDays(2)->toDateTimeString(),
        ]);
    expect($response->status())->toBe(422);

    // Test arrival_datetime before departure_datetime
    $response = $this->actingAs($user, 'api')
        ->patchJson(route('app.users.requests.shippings.delay', $shippingRequest->id), [
            'departure_datetime' => Carbon::now()->addDays(2)->toDateTimeString(),
            'arrival_datetime' => Carbon::now()->addDay()->toDateTimeString(),
        ]);
    expect($response->status())->toBe(422);
});


it('validates delay reasons', function () {
    $user = User::first();
    $shippingRequest = $user->shippingRequests()
        ->whereNull('cancelled_at')
        ->where('departure_datetime', '>', Carbon::now())
        ->firstOr(function () {
            $this->markTestSkipped('No available shipping request found for validation test.');
        });

    // Test invalid reason
    $response = $this->actingAs($user, 'api')
        ->patchJson(route('app.users.requests.shippings.delay', $shippingRequest->id), [
            'delay_reason' => 'invalid_reason',
            'note' => 'Invalid reason test'
        ]);
    expect($response->status())->toBe(422);

    // Test empty reason
    $response = $this->actingAs($user, 'api')
        ->patchJson(route('app.users.requests.shippings.delay', $shippingRequest->id), [
            'delay_reason' => '',
            'note' => 'Empty reason test'
        ]);
    expect($response->status())->toBe(422);

    // Test too long reason
    $response = $this->actingAs($user, 'api')
        ->patchJson(route('app.users.requests.shippings.delay', $shippingRequest->id), [
            'delay_reason' => str_repeat('a', 256),
            'note' => 'Too long reason test'
        ]);
    expect($response->status())->toBe(422);
});
