<?php

use App\Models\User;
use Carbon\Carbon;

it('can confirm shipping request attendance', function () {
    $user = User::first();
    $shippingRequest = $user->shippingRequests()
        ->whereNull('cancelled_at')
        ->whereNull('attendance_confirmed_at')
        ->where('departure_datetime', '>', Carbon::now())
        ->where('departure_datetime', '<=', Carbon::now()->addMinutes(30))
        ->firstOr(function () {
            $this->markTestSkipped('No available shipping request found for attendance confirmation.');
        });

    $response = $this->actingAs($user, 'api')
        ->postJson(route('app.users.requests.shippings.confirm-attendance', $shippingRequest->id));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $shippingRequest->refresh();
    expect($shippingRequest->attendance_confirmed_at)->not->toBeNull();
});

it('cannot confirm attendance for a cancelled shipping request', function () {
    $user = User::first();
    $shippingRequest = $user->shippingRequests()
        ->whereNotNull('cancelled_at')
        ->firstOr(function () {
            $this->markTestSkipped('No cancelled shipping request found.');
        });

    $response = $this->actingAs($user, 'api')
        ->postJson(route('app.users.requests.shippings.confirm-attendance', $shippingRequest->id));

    expect($response->status())->toBe(422)
        ->and($response->json('message'))->toBe(__('exceptions.shipping_request.cannot_confirm_cancelled_shipping'));
});

it('cannot confirm attendance outside the confirmation window', function () {
    $user = User::first();

    // Test too early
    $shippingRequest = $user->shippingRequests()
        ->whereNull('cancelled_at')
        ->whereNull('attendance_confirmed_at')
        ->where('departure_datetime', '>', Carbon::now()->addMinutes(30))
        ->firstOr(function () {
            $this->markTestSkipped('No shipping request found for early confirmation test.');
        });

    $response = $this->actingAs($user, 'api')
        ->postJson(route('app.users.requests.shippings.confirm-attendance', $shippingRequest->id));

    expect($response->status())->toBe(422)
        ->and($response->json('message'))->toBe(__('exceptions.shipping_request.attendance_confirmation_window'));

    // Test too late
    $shippingRequest = $user->shippingRequests()
        ->whereNull('cancelled_at')
        ->whereNull('attendance_confirmed_at')
        ->where('departure_datetime', '<', Carbon::now())
        ->firstOr(function () {
            $this->markTestSkipped('No shipping request found for late confirmation test.');
        });

    $response = $this->actingAs($user, 'api')
        ->postJson(route('app.users.requests.shippings.confirm-attendance', $shippingRequest->id));

    expect($response->status())->toBe(422)
        ->and($response->json('message'))->toBe(__('exceptions.shipping_request.attendance_confirmation_window'));
});

it('cannot confirm attendance when already confirmed', function () {
    $user = User::first();
    $shippingRequest = $user->shippingRequests()
        ->whereNull('cancelled_at')
        ->whereNotNull('attendance_confirmed_at')
        ->firstOr(function () {
            $this->markTestSkipped('No shipping request with confirmed attendance found.');
        });

    $response = $this->actingAs($user, 'api')
        ->postJson(route('app.users.requests.shippings.confirm-attendance', $shippingRequest->id));

    expect($response->status())->toBe(422)
        ->and($response->json('message'))->toBe(__('exceptions.shipping_request.attendance_already_confirmed'));
});
