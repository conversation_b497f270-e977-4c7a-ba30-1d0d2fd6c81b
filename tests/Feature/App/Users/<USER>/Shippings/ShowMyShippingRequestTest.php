<?php

use App\Models\User;
use App\Models\Request\ShippingRequest;
use Illuminate\Support\Facades\Event;

beforeEach(function () {
    $this->user = User::first();
});

it('can show my shipping request details', function () {
    $this->actingAs($this->user, 'api');
    Event::fake();

    $shippingRequest = $this->user->shippingRequests()->first();

    if (!$shippingRequest) {
        $this->markTestSkipped('No shipping requests found.');
    }

    $response = $this->getJson(
        route('app.users.requests.shippings.show', [
            'id' => $shippingRequest->id
        ])
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $requestData = $response->json('data');
    expect($requestData)->toHaveKeys([
        'id',
        'transportation_type',
        'can_ship_packages',
        'can_ship_documents',
        'can_ship_furniture',
        'departure_datetime',
        'arrival_datetime',
        'from_city',
        'from_location',
        'to_city',
        'to_location',
        'packages_volume',
        'document_volume',
        'furniture_volume',
        'delivery_location',
        'is_fragile',
        'note',
    ]);

    if ($requestData['can_ship_packages']) {
        expect($requestData['packages_volume'])->toBeGreaterThan(0);
    }
    if ($requestData['can_ship_documents']) {
        expect($requestData['document_volume'])->toBeGreaterThan(0);
    }
    if ($requestData['can_ship_furniture']) {
        expect($requestData['furniture_volume'])->toBeGreaterThan(0);
    }
});

it('cannot show other users shipping request details', function () {
    $this->actingAs($this->user, 'api');
   
    $otherUser = User::where('id', '!=', $this->user->id)->first();

    $shippingRequest = $otherUser->shippingRequests()->first();

    if (!$shippingRequest) {
        $this->markTestSkipped('No shipping requests found for other user.');
    }

    $response = $this->getJson(
        route('app.users.requests.shippings.show', [
            'id' => $shippingRequest->id
        ])
    );

    expect($response->status())->toBe(404);
});

it('returns 404 for non-existent shipping request', function () {
    $this->actingAs($this->user, 'api');

    $response = $this->getJson(
        route('app.users.requests.shippings.show', [
            'id' => 999999999
        ])
    );

    expect($response->status())->toBe(404);
});

it('requires authentication', function () {
    $shippingRequest = ShippingRequest::first();

    $response = $this->getJson(
        route('app.users.requests.shippings.show', [
            'id' => $shippingRequest->id
        ])
    );

    expect($response->status())->toBe(401);
});
