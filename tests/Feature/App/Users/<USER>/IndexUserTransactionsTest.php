<?php

use App\Enums\Bookings\BookingStatusEnum;
use App\Models\User;
use function Pest\Laravel\get;

beforeEach(function () {
    $this->user = User::whereHas('tripServices', function ($query) {
        $query->whereHas('bookings', function ($bookingQuery) {
            $bookingQuery->where('status', BookingStatusEnum::ACCEPTED());
        });
    })->firstOr(function () {
        $this->markTestSkipped('No user with trip services having accepted bookings found.');
    });
});

it('can get user transactions', function () {
    $this->actingAs($this->user, 'api');

    $response = get(route('app.users.transactions.index'));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);
    expect($response->json('data.data.0'))->toHaveKeys([
        'trip_service_id',
        'from_city',
        'to_city',
        'departure_datetime',
        'arrival_datetime',
        'booking_count',
        'total',
        'profit',
        'commission',
        'tax'
    ]);
});

it('calculates commission and profit correctly', function () {
    $this->actingAs($this->user, 'api');

    $response = get(route('app.users.transactions.index'));

    expect($response->status())->toBe(200);

    $transactions = $response->json('data.data');

    if (count($transactions) > 0) {
        foreach ($transactions as $transaction) {
            // Verify that commission is 10% of total
            expect($transaction['commission'])->toBe($transaction['total'] * 0.10);

            // Verify that profit is total minus commission
            expect($transaction['profit'])->toBe($transaction['total'] - $transaction['commission']);

            // Verify tax is zero as per requirements
            expect($transaction['tax'])->toBe(0);
        }
    }
});

it('paginates transactions', function () {
    $this->actingAs($this->user, 'api');

    $perPage = 5;
    $response = get(route('app.users.transactions.index', [
        'per_page' => $perPage
    ]));

    expect($response->status())->toBe(200);
    expect($response->json('data.per_page'))->toBe($perPage);
    expect(count($response->json('data.data')))->toBeLessThanOrEqual($perPage);
});

it('only shows trip services with accepted bookings in transactions', function () {
    $this->actingAs($this->user, 'api');

    // Create a user with trip services that have accepted bookings
    $user = User::whereHas('tripServices', function ($query) {
        $query->whereHas('bookings', function ($bookingQuery) {
            $bookingQuery->where('status', BookingStatusEnum::ACCEPTED());
        });
    })->firstOr(function () {
        $this->markTestSkipped('No user with trip services having accepted bookings found.');
    });

    // Get the user's trip services with accepted bookings count
    $tripServicesWithAcceptedBookingsCount = $user->tripServices()
        ->whereHas('bookings', function ($query) use ($user) {
            $query->where('user_id', $user->id)
                  ->where('status', BookingStatusEnum::ACCEPTED());
        })
        ->count();

    $response = get(route('app.users.transactions.index', ['per_page' => 100]));

    expect($response->status())->toBe(200);

    // The number of transactions should match the number of trip services with accepted bookings
    $transactionsCount = count($response->json('data.data'));
    expect($transactionsCount)->toBeLessThanOrEqual($tripServicesWithAcceptedBookingsCount);
});

it('cannot access transactions when unauthenticated', function () {
    $response = get(route('app.users.transactions.index'));

    expect($response->status())->toBe(401);
});
