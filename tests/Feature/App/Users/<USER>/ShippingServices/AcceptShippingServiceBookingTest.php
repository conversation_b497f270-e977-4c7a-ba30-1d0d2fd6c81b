<?php

use App\Enums\Bookings\BookingStatusEnum;
use App\Events\ShippingServiceBookingAccepted;
use App\Models\User;
use Illuminate\Support\Facades\Event;

beforeEach(function () {
    $this->user = User::first();
    $this->actingAs($this->user, 'api');
});

it('can accept a shipping service booking', function () {
    Event::fake();

    $shippingService = $this->user->shippingServices()
        ->whereHas('bookings', function ($query) {
            $query->where('status', BookingStatusEnum::PENDING());
        })
        ->whereDoesntHave('bookings', function ($query) {
            $query->where('status', BookingStatusEnum::ACCEPTED());
        })
        ->inRandomOrder()
        ->firstOr(function () {
            $this->markTestSkipped('No pending shipping service found without accepted bookings.');
        });

    $booking = $shippingService->bookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    $response = $this->postJson(
        route('app.users.services.shippings.bookings.accept', [
            'id' => $booking->id,
        ])
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::ACCEPTED());

    Event::assertDispatched(ShippingServiceBookingAccepted::class);
});

it('cannot accept non pending booking', function () {
    $shippingService = $this->user->shippingServices()
        ->whereHas('bookings', function ($query) {
            $query->where('status', BookingStatusEnum::ACCEPTED());
        })
        ->inRandomOrder()
        ->firstOr(function () {
            $this->markTestSkipped('No shipping service found with accepted bookings.');
        });

    $booking = $shippingService->bookings()
        ->where('status', BookingStatusEnum::ACCEPTED())
        ->firstOr(function () {
            $this->markTestSkipped('No accepted booking found.');
        });

    $response = $this->postJson(
        route('app.users.services.shippings.bookings.accept', [
            'id' => $booking->id,
        ])
    );

    expect($response->status())->toBe(422);
    expect($response->json('message'))->toBe(__('exceptions.shipping_service.booking.booking_is_not_pending'));

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::ACCEPTED());
});

it('cannot accept booking when shipping service is fully booked', function () {
    $shippingService = $this->user->shippingServices()
        ->where('available_packages_volume', 0)
        ->where('available_document_volume', 0)
        ->where('available_furniture_volume', 0)
        ->whereHas('bookings', function ($query) {
            $query->where('status', BookingStatusEnum::PENDING());
        })
        ->inRandomOrder()
        ->firstOr(function () {
            $this->markTestSkipped('No fully booked shipping service found with pending bookings.');
        });

    $pendingBooking = $shippingService->bookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    $response = $this->postJson(
        route('app.users.services.shippings.bookings.accept', [
            'id' => $pendingBooking->id,
        ])
    );

    expect($response->status())->toBe(422);
    expect($response->json('message'))->toBe(__('exceptions.shipping_service.booking.service_is_fully_booked'));

    $pendingBooking->refresh();
    expect($pendingBooking->status)->toBe(BookingStatusEnum::PENDING());
});
