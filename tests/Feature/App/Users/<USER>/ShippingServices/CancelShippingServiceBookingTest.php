<?php

use App\Models\User;
use App\Models\Booking\ShippingServiceBooking;
use Illuminate\Support\Facades\Event;
use App\Enums\Bookings\BookingStatusEnum;
use App\Events\ShippingServiceBookingCancelledByOwner;

beforeEach(function () {
    $this->user = User::first();
});

it('can cancel shipping service booking', function () {
    $this->actingAs($this->user, 'api');
    Event::fake();

    $shippingService = $this->user->shippingServices()
        ->whereHas('bookings', function ($query) {
            $query->where('status', BookingStatusEnum::PENDING());
        })
        ->firstOr(function () {
            $this->markTestSkipped('No shipping service with pending bookings found.');
        });

    $booking = $shippingService->bookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    $cancellationData = [
        'cancellation_reason' => 'Schedule conflict',
        'note' => 'Need to cancel due to unexpected delay'
    ];

    $response = $this->postJson(
        route('app.users.services.shippings.bookings.cancel', [
            'id' => $booking->id,
        ]),
        $cancellationData
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::CANCELLED_BY_OWNER());
    expect($booking->cancellation_reason)->toBe($cancellationData['cancellation_reason']);
    expect($booking->cancellation_note)->toBe($cancellationData['note']);

    Event::assertDispatched(ShippingServiceBookingCancelledByOwner::class);
});

it('cannot cancel booking of other users shipping service', function () {
    $this->actingAs($this->user, 'api');

    $otherUser = User::where('id', '!=', $this->user->id)
        ->whereHas('shippingServices', function ($query) {
            $query->whereHas('bookings', function ($q) {
                $q->where('status', BookingStatusEnum::PENDING());
            });
        })
        ->firstOr(function () {
            $this->markTestSkipped('No other user with shipping services and pending bookings found.');
        });

    $shippingService = $otherUser->shippingServices()
        ->whereHas('bookings', function ($query) {
            $query->where('status', BookingStatusEnum::PENDING());
        })
        ->first();

    $booking = $shippingService->bookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No pending bookings found for other user.');
    }

    $cancellationData = [
        'cancellation_reason' => 'Schedule conflict',
        'note' => 'Need to cancel due to unexpected delay'
    ];

    $response = $this->postJson(
        route('app.users.services.shippings.bookings.cancel', [
            'id' => $booking->id,
        ]),
        $cancellationData
    );

    expect($response->status())->toBe(404);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::PENDING());
});

it('validates cancellation reasons', function () {
    $this->actingAs($this->user, 'api');

    $shippingService = $this->user->shippingServices()
        ->whereHas('bookings', function ($query) {
            $query->where('status', BookingStatusEnum::PENDING());
        })
        ->firstOr(function () {
            $this->markTestSkipped('No shipping service with pending bookings found.');
        });

    $booking = $shippingService->bookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    // Test empty reason
    $response = $this->postJson(
        route('app.users.services.shippings.bookings.cancel', [
            'id' => $booking->id
        ]),
        ['cancellation_reason' => '']
    );

    expect($response->status())->toBe(422);

    // Test too long reason
    $response = $this->postJson(
        route('app.users.services.shippings.bookings.cancel', [
            'id' => $booking->id
        ]),
        [
            'cancellation_reason' => str_repeat('a', 256)
        ]
    );

    expect($response->status())->toBe(422);

    $booking->refresh();
    $originalStatus = $booking->getOriginal('status');
    expect($booking->status)->toBe($originalStatus);
});

it('returns 404 for non-existent booking', function () {
    $this->actingAs($this->user, 'api');

    $response = $this->postJson(
        route('app.users.services.shippings.bookings.cancel', [
            'id' => 999999999,
        ]),
        ['cancellation_reason' => 'reason 1']
    );

    expect($response->status())->toBe(404);
});

it('requires authentication', function () {
    $user = User::whereHas('shippingServices', function ($query) {
        $query->whereHas('bookings');
    })->first();

    $shippingService = $user->shippingServices()
        ->whereHas('bookings')
        ->first();

    $booking = $shippingService->bookings()->first();

    $response = $this->postJson(
        route('app.users.services.shippings.bookings.cancel', [
            'id' => $booking->id,
        ]),
        ['cancellation_reason' => 'reason 1'],
        ['Accept' => 'application/json']
    );

    expect($response->status())->toBe(401);
    expect(ShippingServiceBooking::find($booking->id))->not->toBeNull();
});
