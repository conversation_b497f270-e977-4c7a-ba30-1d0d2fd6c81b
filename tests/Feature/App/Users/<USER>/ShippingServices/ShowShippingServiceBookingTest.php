<?php

use App\Models\User;
use App\Models\Booking\ShippingServiceBooking;

beforeEach(function () {
    $this->user = User::first();
});

it('can show shipping service booking', function () {
    $this->actingAs($this->user, 'api');

    $booking = $this->user->shippingServices()
        ->first()
        ->bookings
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No bookings found for the service owner.');
    }

    $response = $this->getJson(
        route('app.users.services.shippings.bookings.show', [
            'id' => $booking->id,
        ])
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);
    expect($response->json('data'))->toHaveKeys([
        'id',
        'note',
        'packages_volume',
        'document_volume',
        'furniture_volume',
        'created_at',
        'accepted_at',
    ]);
});

it('returns 404 for non-existent booking', function () {
    $this->actingAs($this->user, 'api');

    $response = $this->getJson(
        route('app.users.services.shippings.bookings.show', [
            'id' => 999999999,
        ])
    );

    expect($response->status())->toBe(404);
});

it('requires authentication', function () {
    $booking = ShippingServiceBooking::first();

    $response = $this->getJson(
        route('app.users.services.shippings.bookings.show', [
            'id' => $booking->id,
        ]),
        ['Accept' => 'application/json']
    );

    expect($response->status())->toBe(401);
});
