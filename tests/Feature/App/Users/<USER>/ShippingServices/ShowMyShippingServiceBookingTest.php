<?php

use App\Models\User;
use App\Models\Booking\ShippingServiceBooking;

beforeEach(function () {
    $this->user = User::first();
});

it('can show user shipping service booking', function () {
    $this->actingAs($this->user, 'api');

    $booking = $this->user->shippingServiceBookings()
        ->with(['shippingService'])
        ->inRandomOrder()
        ->firstOr(function () {
            $this->markTestSkipped('No shipping service booking found for user.');
        });

    $response = $this->getJson(
        route('app.users.services.shippings.my-bookings.show', [
            'bookingId' => $booking->id,
        ])
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);
    expect($response->json('data'))->toHaveKeys([
        'id',
        'note',
        'packages_volume',
        'document_volume',
        'furniture_volume',
        'created_at',
        'updated_at',
        'shipping_service'
    ]);
});

it('cannot show other users shipping service booking', function () {
    $this->actingAs($this->user, 'api');

    $otherUser = User::where('id', '!=', $this->user->id)
        ->whereHas('shippingServiceBookings')
        ->firstOr(function () {
            $this->markTestSkipped('No other user with shipping service bookings found.');
        });

    $booking = $otherUser->shippingServiceBookings()
        ->inRandomOrder()
        ->first();

    $response = $this->getJson(
        route('app.users.services.shippings.my-bookings.show', [
            'bookingId' => $booking->id,
        ])
    );

    expect($response->status())->toBe(404);
});

it('requires authentication', function () {
    $user = User::whereHas('shippingServiceBookings')->first();
    $booking = $user->shippingServiceBookings()->first();

    $response = $this->getJson(
        route('app.users.services.shippings.my-bookings.show', [
            'bookingId' => $booking->id,
        ]),
        ['Accept' => 'application/json']
    );

    expect($response->status())->toBe(401);
});
