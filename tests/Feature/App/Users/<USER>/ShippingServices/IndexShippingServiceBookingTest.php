<?php

use App\Models\User;
use App\Models\Service\ShippingService;

it('can list shipping service bookings', function () {
    $user = User::first();
    $this->actingAs($user, 'api');

    $shippingService = ShippingService::whereHas('bookings')
        ->inRandomOrder()
        ->firstOr(function () {
            $this->markTestSkipped('No shipping service with bookings found.');
        });

    $response = $this->getJson(
        route('app.users.services.shippings.bookings.index', [
            'id' => $shippingService->id,
        ])
    );

    expect($response->status())->toBe(200);

    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);
    expect($response->json('data.data.0'))->toHaveKeys([
        'id',
        'note',
        'packages_volume',
        'document_volume',
        'furniture_volume',
        'created_at',
        'updated_at',
    ]);
});

it('returns empty array for shipping service without bookings', function () {
    $user = User::first();
    $this->actingAs($user, 'api');

    $shippingService = ShippingService::doesntHave('bookings')
        ->inRandomOrder()
        ->firstOr(function () {
            $this->markTestSkipped('No shipping service without bookings found.');
        });

    $response = $this->getJson(
        route('app.users.services.shippings.bookings.index', [
            'id' => $shippingService->id,
        ])
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);
    expect($response->json('data.data'))->toBeArray()->toBeEmpty();
});

it('returns 404 for non-existent shipping service', function () {
    $user = User::first();
    $this->actingAs($user, 'api');

    $response = $this->getJson(
        route('app.users.services.shippings.bookings.index', [
            'id' => 999999999,
        ])
    );

    expect($response->status())->toBe(404);
});

it('requires authentication', function () {
    $shippingService = ShippingService::whereHas('bookings')->first();

    $response = $this->getJson(
        route('app.users.services.shippings.bookings.index', [
            'id' => $shippingService->id,
        ]),
        ['Accept' => 'application/json']
    );

    expect($response->status())->toBe(401);
});
