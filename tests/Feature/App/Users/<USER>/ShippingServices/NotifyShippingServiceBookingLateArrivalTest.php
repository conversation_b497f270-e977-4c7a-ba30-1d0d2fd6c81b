<?php

use App\Events\ShippingServiceBookingLateArrivalNotified;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Event;

beforeEach(function () {
    $this->user = User::first();
    $this->actingAs($this->user, 'api');
});

it('can notify late arrival for shipping service booking', function () {
    Event::fake();

    $booking = $this->user->shippingServiceBookings()
        ->whereNull('cancelled_at')
        ->whereNull('rejected_at')
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No available shipping service booking found for late arrival notification.');
    }

    // Ensure the shipping service is not cancelled
    $booking->shippingService->update([
        'cancelled_at' => null
    ]);

    // Ensure the shipping service is in the future
    $booking->shippingService->update([
        'departure_datetime' => Carbon::now()->addHour(),
        'arrival_datetime' => Carbon::now()->addHours(2)
    ]);

    $response = $this->postJson(
        route('app.users.services.shippings.my-bookings.notify-late-arrival', $booking->id),
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $booking->refresh();
    expect($booking->late_arrival_notified_at)->not->toBeNull();

    Event::assertDispatched(ShippingServiceBookingLateArrivalNotified::class);
});

it('cannot notify late arrival for cancelled booking', function () {
    $booking = $this->user->shippingServiceBookings()
        ->whereNull('rejected_at')
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No shipping service booking found.');
    }

    // Update booking to be cancelled
    $booking->update([
        'cancelled_at' => Carbon::now()
    ]);

    $response = $this->postJson(
        route('app.users.services.shippings.my-bookings.notify-late-arrival', $booking->id),
    );

    expect($response->status())->toBe(422)
        ->and($response->json('message'))->toBe(__('exceptions.shipping_service.booking.cannot_notify_late_arrival_cancelled_booking'));
});

it('cannot notify late arrival for rejected booking', function () {
    $booking = $this->user->shippingServiceBookings()
        ->whereNull('cancelled_at')
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No shipping service booking found.');
    }

    // Update booking to be rejected
    $booking->update([
        'rejected_at' => Carbon::now()
    ]);

    $response = $this->postJson(
        route('app.users.services.shippings.my-bookings.notify-late-arrival', $booking->id),
    );

    expect($response->status())->toBe(422)
        ->and($response->json('message'))->toBe(__('exceptions.shipping_service.booking.cannot_notify_late_arrival_rejected_booking'));
});

it('cannot notify late arrival for cancelled shipping service', function () {
    $booking = $this->user->shippingServiceBookings()
        ->whereNull('cancelled_at')
        ->whereNull('rejected_at')
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No shipping service booking found.');
    }

    // Update shipping service to be cancelled
    $booking->shippingService->update([
        'cancelled_at' => Carbon::now()
    ]);

    $response = $this->postJson(
        route('app.users.services.shippings.my-bookings.notify-late-arrival', $booking->id),
    );

    expect($response->status())->toBe(422)
        ->and($response->json('message'))->toBe(__('exceptions.shipping_service.booking.cannot_notify_late_arrival_cancelled_shipping'));
});

it('cannot notify late arrival for completed shipping', function () {
    $booking = $this->user->shippingServiceBookings()
        ->whereNull('cancelled_at')
        ->whereNull('rejected_at')
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No shipping service booking found.');
    }

    // Update shipping service to be in the past (completed)
    $booking->shippingService->update([
        'departure_datetime' => Carbon::now()->subHours(3),
        'arrival_datetime' => Carbon::now()->subHour()
    ]);


    $response = $this->postJson(
        route('app.users.services.shippings.my-bookings.notify-late-arrival', $booking->id),
    );

    expect($response->status())->toBe(422)
        ->and($response->json('message'))->toBe(__('exceptions.shipping_service.booking.cannot_notify_late_arrival_completed_shipping'));
});

it('cannot notify late arrival for non-owned booking', function () {
    $otherUser = User::where('id', '!=', $this->user->id)->first();

    $booking = $otherUser->shippingServiceBookings()
        ->whereNull('cancelled_at')
        ->whereNull('rejected_at')
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No other user\'s shipping service booking found.');
    }

    $response = $this->postJson(
        route('app.users.services.shippings.my-bookings.notify-late-arrival', $booking->id),
    );

    expect($response->status())->toBe(404);
});
