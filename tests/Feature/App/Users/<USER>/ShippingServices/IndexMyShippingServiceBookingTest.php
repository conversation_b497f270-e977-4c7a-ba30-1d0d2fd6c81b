<?php

use App\Models\User;
use App\Models\Booking\ShippingServiceBooking;
use App\Models\Service\ShippingService;
use Carbon\Carbon;

use function Pest\Laravel\get;

beforeEach(function () {
    $this->user = User::first();
});

it('can get user shipping service bookings', function () {
    $this->actingAs($this->user, 'api');

    $user = User::whereHas('shippingServiceBookings')->firstOr(function () {
        $this->markTestSkipped('No user with shipping service bookings found.');
    });

    $response = get(route('app.users.services.shippings.my-bookings.index'));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);
    expect($response->json('data.data.0'))->toHaveKeys([
        'id',
        'note',
        'packages_volume',
        'document_volume',
        'furniture_volume',
        'created_at',
        'updated_at',
        'shipping_service'
    ]);
});

it('cannot see other users shipping service bookings', function () {
    $this->actingAs($this->user, 'api');

    $otherUser = User::where('id', '!=', $this->user->id)
        ->whereHas('shippingServiceBookings')
        ->firstOr(function () {
            $this->markTestSkipped('No other user with shipping service bookings found.');
        });

    $otherUserBookings = $otherUser->shippingServiceBookings()
        ->with(['shippingService'])
        ->get();

    $response = get(route('app.users.services.shippings.my-bookings.index'));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    // Verify that none of the returned bookings belong to the other user
    $returnedBookingIds = collect($response->json('data'))->pluck('id');
    $otherUserBookingIds = $otherUserBookings->pluck('id');

    expect($returnedBookingIds->intersect($otherUserBookingIds))->toBeEmpty();
});

it('requires authentication', function () {
    $response = get(route('app.users.services.shippings.my-bookings.index'), [
        'Accept' => 'application/json'
    ]);

    expect($response->status())->toBe(401);
});
