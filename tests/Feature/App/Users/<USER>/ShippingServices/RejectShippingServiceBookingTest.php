<?php

use App\Enums\Bookings\BookingStatusEnum;
use App\Events\ShippingServiceBookingRejected;
use App\Models\User;
use Illuminate\Support\Facades\Event;

beforeEach(function () {
    $this->user = User::first();
});

it('can reject a shipping service booking', function () {
    $this->actingAs($this->user, 'api');

    Event::fake();

    $shippingService = $this->user->shippingServices()->whereHas('bookings', function ($query) {
        $query->where('status', BookingStatusEnum::PENDING());
    })->inRandomOrder()->firstOr(function () {
        $this->markTestSkipped('No pending shipping service found.');
    });

    $booking = $shippingService->bookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    $rejectionData = [
        'rejection_reason' => 'Schedule conflict',
        'note' => 'Price too high'
    ];

    $response = $this->postJson(
        route('app.users.services.shippings.bookings.reject', [
            'id' => $booking->id,
        ]),
        $rejectionData
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::REJECTED());
    expect($booking->rejection_reason)->toBe($rejectionData['rejection_reason']);
    expect($booking->rejection_note)->toBe($rejectionData['note']);

    Event::assertDispatched(ShippingServiceBookingRejected::class);
});

it('cannot reject non pending booking', function () {
    $this->actingAs($this->user, 'api');

    $shippingService = $this->user->shippingServices()->whereHas('bookings', function ($query) {
        $query->where('status', BookingStatusEnum::REJECTED());
    })->inRandomOrder()->firstOr(function () {
        $this->markTestSkipped('No rejected shipping service found.');
    });

    $booking = $shippingService->bookings()
        ->where('status', BookingStatusEnum::REJECTED())
        ->first();

    $rejectionData = [
        'rejection_reason' => 'Schedule conflict',
        'note' => 'Price too high'
    ];

    $response = $this->postJson(
        route('app.users.services.shippings.bookings.reject', [
            'id' => $booking->id,
        ]),
        $rejectionData
    );

    expect($response->status())->toBe(422);
    expect($response->json('message'))->toBe(__('exceptions.shipping_service.booking.booking_is_not_pending'));

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::REJECTED());
});

it('cannot reject other users booking', function () {
    $this->actingAs($this->user, 'api');

    $otherUser = User::where('id', '!=', $this->user->id)->first();
    $shippingService = $otherUser->shippingServices()
        ->whereHas('bookings', function ($query) {
            $query->where('status', BookingStatusEnum::PENDING());
        })
        ->inRandomOrder()
        ->firstOr(function () {
            $this->markTestSkipped('No pending shipping service found for other user.');
        });

    $booking = $shippingService->bookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    $rejectionData = [
        'rejection_reason' => 'Schedule conflict',
        'note' => 'Price too high'
    ];

    $response = $this->postJson(
        route('app.users.services.shippings.bookings.reject', [
            'id' => $booking->id,
        ]),
        $rejectionData
    );

    expect($response->status())->toBe(404);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::PENDING());
});

it('validates rejection reasons', function () {
    $this->actingAs($this->user, 'api');

    $shippingService = $this->user->shippingServices()->whereHas('bookings', function ($query) {
        $query->where('status', BookingStatusEnum::PENDING());
    })->inRandomOrder()->firstOr(function () {
        $this->markTestSkipped('No pending shipping service found.');
    });

    $booking = $shippingService->bookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    // Test empty reason
    $response = $this->postJson(
        route('app.users.services.shippings.bookings.reject', [
            'id' => $booking->id,
        ]),
        ['rejection_reason' => '']
    );

    expect($response->status())->toBe(422);

    // Test too long reason
    $response = $this->postJson(
        route('app.users.services.shippings.bookings.reject', [
            'id' => $booking->id,
        ]),
        [
            'rejection_reason' => str_repeat('a', 256)
        ]
    );

    expect($response->status())->toBe(422);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::PENDING());
});

it('returns 404 for non-existent booking', function () {
    $this->actingAs($this->user, 'api');

    $response = $this->postJson(
        route('app.users.services.shippings.bookings.reject', [
            'id' => 999999999,
        ]),
        [
            'rejection_reason' => 'Schedule conflict',
            'note' => 'Price too high'
        ]
    );

    expect($response->status())->toBe(404);
});

it('requires authentication', function () {
    $shippingService = $this->user->shippingServices()->whereHas('bookings', function ($query) {
        $query->where('status', BookingStatusEnum::PENDING());
    })->inRandomOrder()->firstOr(function () {
        $this->markTestSkipped('No pending shipping service found.');
    });

    $booking = $shippingService->bookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    $response = $this->postJson(
        route('app.users.services.shippings.bookings.reject', [
            'id' => $booking->id,
        ]),
        [
            'rejection_reason' => 'Schedule conflict',
            'note' => 'Price too high'
        ],
        ['Accept' => 'application/json']
    );

    expect($response->status())->toBe(401);
});
