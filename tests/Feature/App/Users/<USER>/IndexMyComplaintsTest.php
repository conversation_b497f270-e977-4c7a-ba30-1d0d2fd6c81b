<?php

use App\Enums\Bookings\BookingStatusEnum;
use App\Enums\Bookings\BookingTypeEnum;
use App\Enums\Complaints\ComplaintStatusEnum;
use App\Models\Booking\TripServiceBooking;
use App\Models\Complaint;
use App\Models\User;
use Database\Seeders\ComplaintSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;


beforeEach(function () {
    $this->user = User::first();
});

it('can list user complaints', function () {
    $this->actingAs($this->user, 'api');

    $complaintsCount = Complaint::where('user_id', $this->user->id)->count();

    $response = $this->getJson(route('app.users.complaints.index'));

    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $complaintData = $response->json('data.data');
    foreach ($complaintData as $complaint) {
        expect($complaint)->toHaveKeys([
            'id',
            'description',
            'status',
            'created_at',
            'updated_at',
        ]);
    }

    $responseData = json_decode($response->getContent(), true);
    expect(count($responseData['data']['data']))->toBeGreaterThanOrEqual(1);
    expect($responseData['data']['total'])->toBeGreaterThanOrEqual($complaintsCount);
});

it('returns empty array when user has no complaints', function () {
    $newUser = User::factory()->create();
    $this->actingAs($newUser, 'api');

    $response = $this->getJson(route('app.users.complaints.index'));

    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $responseData = json_decode($response->getContent(), true);
    expect($responseData['data']['data'])->toBeArray()->toBeEmpty();
    expect($responseData['data']['total'])->toBe(0);
});

it('paginates complaints correctly', function () {
    $this->actingAs($this->user, 'api');

    $complaintsCount = Complaint::where('user_id', $this->user->id)->count();
    if ($complaintsCount <= 10) {
        return $this->markTestSkipped('User does not have more than 10 complaints.');
    }

    $response = $this->getJson(route('app.users.complaints.index', ['per_page' => 5]));

    $response->assertStatus(200);

    $responseData = json_decode($response->getContent(), true);
    expect($responseData['data']['per_page'])->toBe(5);
    expect(count($responseData['data']['data']))->toBe(5);
    expect($responseData['data']['total'])->toBeGreaterThanOrEqual(10);
    expect($responseData['data']['last_page'])->toBeGreaterThanOrEqual(2);
});
