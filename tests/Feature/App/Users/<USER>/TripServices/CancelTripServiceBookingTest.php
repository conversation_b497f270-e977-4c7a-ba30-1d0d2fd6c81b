<?php

use App\Enums\Bookings\BookingStatusEnum;
use App\Enums\Travel\SeatStatusEnum;
use App\Events\TripServiceBookingCancelledByOwnerEvent;
use App\Models\User;
use Illuminate\Support\Facades\Event;

beforeEach(function () {
    $this->user = User::first();
});

it('can cancel a trip service booking', function () {
    $this->actingAs($this->user, 'api');

    Event::fake();

    $tripService = $this->user->tripServices()
        ->whereHas('bookings', function ($query) {
            $query->where('status', BookingStatusEnum::PENDING());
        })
        ->firstOr(function () {
            $this->markTestSkipped('No trip services with pending bookings found.');
        });

    $booking = $tripService->bookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    $initialAvailableSeats = $booking->tripService->number_of_available_seats;

    $cancellationData = [
        'cancellation_reason' => 'Schedule conflict',
        'note' => 'Vehicle maintenance required'
    ];

    $response = $this->postJson(
        route('app.users.services.trips.bookings.cancel', $booking->id),
        $cancellationData
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $booking->refresh();
    $booking->tripService->refresh();

    expect($booking->status)->toBe(BookingStatusEnum::CANCELLED_BY_OWNER());
    expect($booking->cancellation_reason)->toBe($cancellationData['cancellation_reason']);
    expect($booking->cancellation_note)->toBe($cancellationData['note']);

    foreach ($booking->seats as $bookedSeat) {
        $bookedSeat->seat->refresh();
        expect($bookedSeat->seat->status)->toBe(SeatStatusEnum::AVAILABLE());
    }

    expect($booking->tripService->number_of_available_seats)->toBe($initialAvailableSeats + $booking->seats->count());

    Event::assertDispatched(TripServiceBookingCancelledByOwnerEvent::class);
});

it('cannot cancel other users booking', function () {
    $this->actingAs($this->user, 'api');

    $otherUser = User::where('id', '!=', $this->user->id)->first();

    $tripService = $otherUser->tripServices()
        ->whereHas('bookings', function ($query) {
            $query->where('status', BookingStatusEnum::PENDING());
        })
        ->firstOr(function () {
            $this->markTestSkipped('No trip services with pending bookings found for other user.');
        });

    $booking = $tripService->bookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    $cancellationData = [
        'cancellation_reason' => 'Schedule conflict',
        'note' => 'Vehicle maintenance required'
    ];

    $response = $this->postJson(
        route('app.users.services.trips.bookings.cancel', $booking->id),
        $cancellationData
    );

    expect($response->status())->toBe(404);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::PENDING());
});

it('validates cancellation reasons', function () {
    $this->actingAs($this->user, 'api');

    $tripService = $this->user->tripServices()
        ->whereHas('bookings', function ($query) {
            $query->where('status', BookingStatusEnum::PENDING());
        })
        ->firstOr(function () {
            $this->markTestSkipped('No trip services with pending bookings found.');
        });

    $booking = $tripService->bookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    // Test empty reason
    $response = $this->postJson(
        route('app.users.services.trips.bookings.cancel', $booking->id),
        ['cancellation_reason' => '']
    );

    expect($response->status())->toBe(422);

    // Test too long reason
    $response = $this->postJson(
        route('app.users.services.trips.bookings.cancel', $booking->id),
        [
            'cancellation_reason' => str_repeat('a', 256)
        ]
    );

    expect($response->status())->toBe(422);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::PENDING());
});

it('returns 404 for non-existent booking', function () {
    $this->actingAs($this->user, 'api');

    $response = $this->postJson(
        route('app.users.services.trips.bookings.cancel', 999999999),
        [
            'cancellation_reason' => 'Schedule conflict',
            'note' => 'Vehicle maintenance required'
        ]
    );

    expect($response->status())->toBe(404);
});

it('requires authentication', function () {
    $user = User::whereHas('tripServices.bookings')->first();
    $booking = $user->tripServices()->first()->bookings()->first();

    $response = $this->postJson(
        route('app.users.services.trips.bookings.cancel', $booking->id),
        [
            'cancellation_reason' => 'Schedule conflict',
            'note' => 'Vehicle maintenance required'
        ],
        ['Accept' => 'application/json']
    );

    expect($response->status())->toBe(401);
});
