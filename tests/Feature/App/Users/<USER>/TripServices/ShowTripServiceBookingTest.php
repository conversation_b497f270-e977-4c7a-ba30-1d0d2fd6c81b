<?php

use App\Models\User;

it('can show booking details to trip service owner', function () {
    $owner = User::whereHas('tripServices.bookings')->firstOr(function () {
        $this->markTestSkipped('No service owner with bookings found.');
    });

    $booking = $owner->tripServices()
        ->with('bookings')
        ->first()
        ->bookings
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No bookings found for the service owner.');
    }

    $response = $this->actingAs($owner, 'api')
        ->getJson(route('app.users.services.trips.bookings.show', [
            'id' => $booking->id
        ]));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);
    expect($response->json('data'))->toHaveKeys([
        'id',
        'note',
        'created_at',
        'updated_at',
        'has_fragile_items',
        'accepted_at',
        'cancelled_at',
        'cancellation_reason',
        'rejected_at',
        'rejection_reason',
        'seats',
        'status',
        'attendance_confirmed_at',
        'delay_requested_at',
        'trip_service',
        'user'
    ]);
});

it('cannot show booking details to unauthorized user', function () {
    $owner = User::whereHas('tripServices.bookings')->firstOr(function () {
        $this->markTestSkipped('No service owner with bookings found.');
    });

    $unauthorizedUser = User::where('id', '!=', $owner->id)->first();

    $booking = $owner->tripServices()
        ->with('bookings')
        ->first()
        ->bookings
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No bookings found for the service owner.');
    }

    $response = $this->actingAs($unauthorizedUser, 'api')
        ->getJson(route('app.users.services.trips.bookings.show', [
            'id' => $booking->id
        ]));

    expect($response->status())->toBe(404);
});

it('requires authentication', function () {
    $user = User::whereHas('tripServiceBookings')->firstOr(function () {
        $this->markTestSkipped('No user with trip service bookings found.');
    });

    $booking = $user->tripServiceBookings()->first();

    $response = $this->getJson(
        route('app.users.services.trips.bookings.show', [
            'id' => $booking->id
        ]),
        ['Accept' => 'application/json']
    );

    expect($response->status())->toBe(401);
});
