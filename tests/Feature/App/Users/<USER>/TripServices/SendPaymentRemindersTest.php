<?php

use App\Models\User;
use App\Models\Booking\TripServiceBooking;
use App\Enums\Bookings\BookingStatusEnum;
use App\Notifications\TripServicePaymentReminderNotification;
use Illuminate\Support\Facades\Notification;

beforeEach(function () {
    $this->user = User::first();
});

it('can send payment reminders to unpaid travelers', function () {
    Notification::fake();
    $this->actingAs($this->user, 'api');

    $tripService = $this->user->tripServices()
        ->whereHas('bookings', function ($query) {
            $query->where('status', BookingStatusEnum::ACCEPTED())
                ->whereNull('paid_at');
        })
        ->firstOr(function () {
            $this->markTestSkipped('No trip services with unpaid accepted bookings found.');
        });

    $unpaidBookings = $tripService->bookings()
        ->where('status', BookingStatusEnum::ACCEPTED())
        ->whereNull('paid_at')
        ->get();

    if ($unpaidBookings->count() < 1) {
        $this->markTestSkipped('No unpaid bookings found for testing.');
    }

    $response = $this->postJson(
        route('app.users.services.trips.send-payment-reminders', $tripService->id)
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    // Check notifications were sent
    $unpaidTravelers = $unpaidBookings->map(fn ($booking) => $booking->user);
    Notification::assertSentTo(
        $unpaidTravelers,
        TripServicePaymentReminderNotification::class
    );
});

it('returns success message when no unpaid bookings exist', function () {
    Notification::fake();
    $this->actingAs($this->user, 'api');

    // Find trip service with no unpaid bookings
    $tripService = $this->user->tripServices()
        ->whereDoesntHave('bookings', function ($query) {
            $query->where('status', BookingStatusEnum::ACCEPTED())
                ->whereNull('paid_at');
        })
        ->first();

    if (!$tripService) {
        // If we can't find one, mark any unpaid bookings as paid for a test
        $tripService = $this->user->tripServices()
            ->whereHas('bookings', function ($query) {
                $query->where('status', BookingStatusEnum::ACCEPTED());
            })
            ->first();

        if (!$tripService) {
            $this->markTestSkipped('No suitable trip services found for testing.');
        }

        // Mark all bookings as paid
        $tripService->bookings()
            ->where('status', BookingStatusEnum::ACCEPTED())
            ->whereNull('paid_at')
            ->update(['paid_at' => now()]);
    }

    $response = $this->postJson(
        route('app.users.services.trips.send-payment-reminders', $tripService->id)
    );

    expect($response->status())->toBe(200);
    expect($response->json('message'))->toBe(__('exceptions.trip_service.bookings.no_unpaid_bookings'));

    // No notifications should be sent
    Notification::assertNothingSent();
});

it('cannot send payment reminders for another user\'s trip service', function () {
    Notification::fake();
    $this->actingAs($this->user, 'api');

    $otherUser = User::where('id', '!=', $this->user->id)->first();

    $tripService = $otherUser->tripServices()
        ->whereHas('bookings', function ($query) {
            $query->whereNull('paid_at');
        })
        ->firstOr(function () {
            $this->markTestSkipped('No trip services with unpaid bookings found for other user.');
        });

    $response = $this->postJson(
        route('app.users.services.trips.send-payment-reminders', $tripService->id)
    );

    expect($response->status())->toBe(404);

    // No notifications should be sent
    Notification::assertNothingSent();
});

it('returns 404 for non-existent trip service', function () {
    Notification::fake();
    $this->actingAs($this->user, 'api');

    $response = $this->postJson(
        route('app.users.services.trips.send-payment-reminders', 999999999)
    );

    expect($response->status())->toBe(404);

    // No notifications should be sent
    Notification::assertNothingSent();
});

it('requires authentication', function () {
    Notification::fake();

    $user = User::whereHas('tripServices.bookings')->first();
    $tripService = $user->tripServices()->first();

    $response = $this->postJson(
        route('app.users.services.trips.send-payment-reminders', $tripService->id),
        [],
        ['Accept' => 'application/json']
    );

    expect($response->status())->toBe(401);

    // No notifications should be sent
    Notification::assertNothingSent();
});
