<?php

use App\Models\User;

it('can show booking details to booking owner', function () {
    $user = User::whereHas('tripServiceBookings')->firstOr(function () {
        $this->markTestSkipped('No user with trip service bookings found.');
    });

    $booking = $user->tripServiceBookings()->first();

    $response = $this->actingAs($user, 'api')
        ->getJson(route('app.users.services.trips.my-bookings.show', $booking->id));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    expect($response->json('data'))->toHaveKeys([
        'id',
        'status',
        'note',
        'has_fragile_items',
        'accepted_at',
        'cancellation_reason',
        'rejection_reason',
        'created_at',
        'updated_at',
        'trip_service',
        'seats'
    ]);

    expect($response->json('data.trip_service'))->toHaveKeys([
        'id',
        'trip_type',
        'departure_datetime',
        'arrival_datetime',
        'number_of_seats',
        'number_of_available_seats',
        'from_city',
        'to_city',
        'price',
        'allow_smoking',
        'deliver_to_door',
        'car'
    ]);
});

it('cannot show booking details to unauthorized user', function () {
    $owner = User::whereHas('tripServiceBookings')->firstOr(function () {
        $this->markTestSkipped('No user with trip service bookings found.');
    });

    $unauthorizedUser = User::where('id', '!=', $owner->id)->first();
    $booking = $owner->tripServiceBookings()->first();

    $response = $this->actingAs($unauthorizedUser, 'api')
        ->getJson(route('app.users.services.trips.my-bookings.show', $booking->id));

    expect($response->status())->toBe(404);
});

it('requires authentication', function () {
    $user = User::whereHas('tripServiceBookings')->firstOr(function () {
        $this->markTestSkipped('No user with trip service bookings found.');
    });

    $booking = $user->tripServiceBookings()->first();

    $response = $this->getJson(
        route('app.users.services.trips.my-bookings.show', $booking->id),
        ['Accept' => 'application/json']
    );

    expect($response->status())->toBe(401);
});
