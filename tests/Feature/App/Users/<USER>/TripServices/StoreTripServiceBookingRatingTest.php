<?php

use App\Models\Booking\TripServiceBooking;
use App\Models\Rating;
use App\Models\Service\TripService;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Str;

beforeEach(function () {
    $this->user = User::first();
    $this->actingAs($this->user, 'api');
});

it('can rate a driver after trip service is completed', function () {
    $booking = $this->user->tripServiceBookings()
        ->whereNull('cancelled_at')
        ->whereNull('rejected_at')
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No available trip service booking found for rating.');
    }

    // Ensure the trip service is completed (in the past)
    $booking->tripService->update([
        'departure_datetime' => Carbon::now()->subHours(3),
        'arrival_datetime' => Carbon::now()->subHour()
    ]);

    $booking->update([
        'arrival_confirmed_at' => Carbon::now()->subMinutes(30) // Simulate arrival confirmation
    ]);

    // Ensure no existing rating
    Rating::where('trip_service_booking_id', $booking->id)->delete();

    $ratingData = [
        'rating' => 4,
        'comment' => 'The driver was very professional and friendly.'
    ];

    $response = $this->postJson(
        route('app.users.services.trips.my-bookings.rate.store', $booking->id),
        $ratingData
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    expect($response->json('data'))->toHaveKeys([
        'id',
        'trip_service_booking_id',
        'rating',
        'comment',
        'created_at',
        'updated_at'
    ]);

    expect($response->json('data.rating'))->toBe(4);
    expect($response->json('data.comment'))->toBe('The driver was very professional and friendly.');

    $this->assertDatabaseHas('ratings', [
        'trip_service_booking_id' => $booking->id,
        'user_id' => $this->user->id,
        'driver_id' => $booking->tripService->user_id,
        'rating' => 4
    ]);
});

it('cannot rate a driver before trip arrival', function () {
    $booking = $this->user->tripServiceBookings()
        ->whereNull('cancelled_at')
        ->whereNull('rejected_at')
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No available trip service booking found for rating.');
    }

    // Clear any existing ratings for this booking
    Rating::where('trip_service_booking_id', $booking->id)->delete();

    // Ensure the trip service is not completed (in the future)
    $booking->tripService->update([
        'departure_datetime' => Carbon::now()->subHour(),
        'arrival_datetime' => Carbon::now()->addHour()
    ]);

    $ratingData = [
        'rating' => 4,
        'comment' => 'The driver was very professional and friendly.'
    ];

    $response = $this->postJson(
        route('app.users.services.trips.my-bookings.rate.store', $booking->id),
        $ratingData
    );

    expect($response->status())->toBe(422)
        ->and($response->json('message'))->toBe(__('exceptions.trip_service.cannot_rate_driver_before_arrival'));

    $this->assertDatabaseMissing('ratings', [
        'trip_service_booking_id' => $booking->id
    ]);
});

it('cannot rate a driver twice for same booking', function () {
    $booking = $this->user->tripServiceBookings()
        ->whereNull('cancelled_at')
        ->whereNull('rejected_at')
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No available trip service booking found for rating.');
    }

    // Ensure the trip service is completed (in the past)
    $booking->tripService->update([
        'departure_datetime' => Carbon::now()->subHours(3),
        'arrival_datetime' => Carbon::now()->subHour()
    ]);

    // Create an initial rating
    Rating::updateOrCreate(
        [
            'trip_service_booking_id' => $booking->id,
            'user_id' => $this->user->id,
            'driver_id' => $booking->tripService->user_id
        ],
        [
            'rating' => 5,
            'comment' => 'Great service!'
        ]
    );

    $ratingData = [
        'rating' => 4.0,
        'comment' => 'Trying to submit another rating.'
    ];

    $response = $this->postJson(
        route('app.users.services.trips.my-bookings.rate.store', $booking->id),
        $ratingData
    );

    expect($response->status())->toBe(422)
        ->and($response->json('message'))->toBe(__('exceptions.trip_service.booking.already_rated'));

    // Verify only one rating exists
    $ratingsCount = Rating::where('trip_service_booking_id', $booking->id)->count();
    expect($ratingsCount)->toBe(1);
});

it('cannot rate a booking that doesnt belong to the user', function () {
    $otherUser = User::where('id', '!=', $this->user->id)->first();

    $booking = $otherUser->tripServiceBookings()
        ->whereNull('cancelled_at')
        ->whereNull('rejected_at')
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No other user\'s trip service booking found.');
    }

    $ratingData = [
        'rating' => 4,
        'comment' => 'The driver was very professional and friendly.'
    ];

    $response = $this->postJson(
        route('app.users.services.trips.my-bookings.rate.store', $booking->id),
        $ratingData
    );

    expect($response->status())->toBe(404);
});

it('validates rating must be between 1 and 5', function () {
    $booking = $this->user->tripServiceBookings()
        ->whereNull('cancelled_at')
        ->whereNull('rejected_at')
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No available trip service booking found for rating.');
    }

    // Ensure the trip service is completed (in the past)
    $booking->tripService->update([
        'departure_datetime' => Carbon::now()->subHours(3),
        'arrival_datetime' => Carbon::now()->subHour()
    ]);

    // Remove existing ratings
    Rating::where('trip_service_booking_id', $booking->id)->delete();

    // Test invalid values
    foreach ([0, 5.5, 6, -1] as $invalidRating) {
        $ratingData = [
            'rating' => $invalidRating,
            'comment' => 'Test comment'
        ];

        $response = $this->postJson(
            route('app.users.services.trips.my-bookings.rate.store', $booking->id),
            $ratingData
        );

        expect($response->status())->toBe(422);
        // The AbstractFormRequest returns a message, not a structured errors object
        expect($response->json('message'))->not->toBeEmpty();
    }

    // Test a valid value for confirmation
    $ratingData = [
        'rating' => 4,
        'comment' => 'Test comment'
    ];

    $response = $this->postJson(
        route('app.users.services.trips.my-bookings.rate.store', $booking->id),
        $ratingData
    );

    expect($response->status())->toBe(200);
});

it('allows comment to be optional when rating', function () {
    $booking = $this->user->tripServiceBookings()
        ->whereNull('cancelled_at')
        ->whereNull('rejected_at')
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No available trip service booking found for rating.');
    }

    // Ensure the trip service is completed (in the past)
    $booking->tripService->update([
        'departure_datetime' => Carbon::now()->subHours(3),
        'arrival_datetime' => Carbon::now()->subHour()
    ]);

    // Remove existing ratings
    Rating::where('trip_service_booking_id', $booking->id)->delete();

    $ratingData = [
        'rating' => 4
    ];

    $response = $this->postJson(
        route('app.users.services.trips.my-bookings.rate.store', $booking->id),
        $ratingData
    );

    expect($response->status())->toBe(200);

    $this->assertDatabaseHas('ratings', [
        'trip_service_booking_id' => $booking->id,
        'rating' => 4,
    ]);
});

it('requires authentication', function () {
    $this->refreshApplication();

    $user = User::first();
    $booking = TripServiceBooking::first();

    if (!$booking) {
        $this->markTestSkipped('No trip service booking found.');
    }

    $ratingData = [
        'rating' => 4,
        'comment' => 'Test comment'
    ];

    $response = $this->postJson(
        route('app.users.services.trips.my-bookings.rate.store', $booking->id),
        $ratingData,
        ['Accept' => 'application/json']
    );

    expect($response->status())->toBe(401);
});
