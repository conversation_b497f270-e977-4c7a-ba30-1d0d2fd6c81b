<?php

use App\Enums\Bookings\BookingStatusEnum;
use App\Events\TripServiceBookingAccepted;
use App\Models\User;
use Illuminate\Support\Facades\Event;

beforeEach(function () {
    $this->user = User::first();
    $this->actingAs($this->user, 'api');
});

it('can accept a trip service booking', function () {
    Event::fake();

    $tripService = $this->user->tripServices()->whereHas('bookings', function ($query) {
        $query->where('status', BookingStatusEnum::PENDING());
    })->whereDoesntHave('bookings', function ($query) {
        $query->where('status', BookingStatusEnum::ACCEPTED());
    })->inRandomOrder()->firstOr(function () {
        $this->markTestSkipped('No pending trip service found without accepted bookings.');
    });

    $booking = $tripService->bookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    $response = $this->postJson(
        route('app.users.services.trips.bookings.accept', [
            'id' => $booking->id,
        ])
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::ACCEPTED());

    Event::assertDispatched(TripServiceBookingAccepted::class);
});

it('cannot accept non pending booking', function () {
    $tripService = $this->user->tripServices()->whereHas('bookings', function ($query) {
        $query->where('status', BookingStatusEnum::ACCEPTED());
    })->inRandomOrder()->firstOr(function () {
        $this->markTestSkipped('No trip service found with accepted bookings.');
    });

    $booking = $tripService->bookings()
        ->where('status', BookingStatusEnum::ACCEPTED())
        ->firstOr(function () {
            $this->markTestSkipped('No accepted booking found.');
        });

    $response = $this->postJson(
        route('app.users.services.trips.bookings.accept', [
            'id' => $booking->id,
        ])
    );

    expect($response->status())->toBe(422);
    expect($response->json('message'))->toBe(__('exceptions.trip_service.booking.booking_is_not_pending'));

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::ACCEPTED());
});

it('cannot accept other users booking', function () {
    $otherUser = User::where('id', '!=', $this->user->id)->first();
    $tripService = $otherUser->tripServices()
        ->whereHas('bookings', function ($query) {
            $query->where('status', BookingStatusEnum::PENDING());
        })
        ->inRandomOrder()
        ->firstOr(function () {
            $this->markTestSkipped('No pending trip service found for other user.');
        });

    $booking = $tripService->bookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    $response = $this->postJson(
        route('app.users.services.trips.bookings.accept', [
            'id' => $booking->id,
        ])
    );

    expect($response->status())->toBe(404);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::PENDING());
});

it('cannot accept booking when another booking is already accepted', function () {
    $tripService = $this->user->tripServices()
        ->whereHas('bookings', function ($query) {
            $query->where('status', BookingStatusEnum::ACCEPTED());
        })
        ->whereHas('bookings', function ($query) {
            $query->where('status', BookingStatusEnum::PENDING());
        })
        ->inRandomOrder()
        ->firstOr(function () {
            $this->markTestSkipped('No trip service found with both pending and accepted bookings.');
        });

    $pendingBooking = $tripService->bookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    $response = $this->postJson(
        route('app.users.services.trips.bookings.accept', [
            'id' => $pendingBooking->id,
        ])
    );

    expect($response->status())->toBe(422);
    expect($response->json('message'))->toBe(__('exceptions.trip_service.booking.another_booking_already_accepted'));

    $pendingBooking->refresh();
    expect($pendingBooking->status)->toBe(BookingStatusEnum::PENDING());
});
