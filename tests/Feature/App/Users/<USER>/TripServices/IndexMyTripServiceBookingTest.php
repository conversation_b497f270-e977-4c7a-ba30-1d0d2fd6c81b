<?php

use App\Enums\Bookings\BookingStatusEnum;
use App\Models\User;
use function Pest\Laravel\get;

beforeEach(function () {
    $this->user = User::first();
});

it('can get user trip service bookings', function () {
    $this->actingAs($this->user, 'api');

    $user = User::whereHas('tripServiceBookings')->firstOr(function () {
        $this->markTestSkipped('No user with trip service bookings found.');
    });

    $response = get(route('app.users.services.trips.my-bookings.index'));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);
    expect($response->json('data.data.0'))->toHaveKeys([
        'id',
        'status',
        'note',
        'has_fragile_items',
        'cancellation_reason',
        'rejection_reason',
        'created_at',
        'updated_at',
        'trip_service',
        'seats'
    ]);
});

it('can filter trip service bookings by status', function () {
    $this->actingAs($this->user, 'api');

    // Test each status type
    $statuses = [
        BookingStatusEnum::PENDING(),
        BookingStatusEnum::ACCEPTED(),
        BookingStatusEnum::REJECTED()
    ];

    foreach ($statuses as $status) {
        $response = get(route('app.users.services.trips.my-bookings.index', [
            'status' => $status
        ]));
        expect($response->status())->toBe(200);

        $bookings = $response->json('data.data');
        if (count($bookings) > 0) {
            foreach ($bookings as $booking) {
                expect($booking['status'])->toBe($status);
            }
        }
    }
});

it('paginates trip service bookings', function () {
    $this->actingAs($this->user, 'api');

    $perPage = 5;
    $response = get(route('app.users.services.trips.my-bookings.index', [
        'per_page' => $perPage
    ]));

    expect($response->status())->toBe(200);
    expect($response->json('data.per_page'))->toBe($perPage);
    expect(count($response->json('data.data')))->toBeLessThanOrEqual($perPage);
});

it('cannot see other users trip service bookings', function () {
    $this->actingAs($this->user, 'api');

    $otherUser = User::where('id', '!=', $this->user->id)
        ->whereHas('tripServiceBookings')
        ->firstOr(function () {
            $this->markTestSkipped('No other user with trip service bookings found.');
        });

    $otherUserBookings = $otherUser->tripServiceBookings()
        ->with(['tripService'])
        ->get();

    $response = get(route('app.users.services.trips.my-bookings.index'));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    // Verify that none of the returned bookings belong to the other user
    $returnedBookingIds = collect($response->json('data.data'))->pluck('id');
    $otherUserBookingIds = $otherUserBookings->pluck('id');

    expect($returnedBookingIds->intersect($otherUserBookingIds))->toBeEmpty();
});

it('requires authentication', function () {
    $response = get(route('app.users.services.trips.my-bookings.index'), [
        'Accept' => 'application/json'
    ]);

    expect($response->status())->toBe(401);
});
