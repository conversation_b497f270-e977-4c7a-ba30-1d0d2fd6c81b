<?php

use App\Models\User;
use App\Models\Booking\TripServiceBooking;
use App\Enums\Bookings\BookingStatusEnum;
use Illuminate\Support\Facades\Event;

beforeEach(function () {
    $this->user = User::whereHas('tripServices', function ($query) {
        $query->whereHas('bookings', function ($query) {
            $query->where('status', BookingStatusEnum::ACCEPTED())
                ->whereNull('paid_at');
        });
    })->first();
});

it('can mark all trip service bookings as paid', function () {
    $this->actingAs($this->user, 'api');

    $tripService = $this->user->tripServices()
        ->whereHas('bookings', function ($query) {
            $query->where('status', BookingStatusEnum::ACCEPTED())
                ->whereNull('paid_at');
        })
        ->firstOr(function () {
            $this->markTestSkipped('No trip services with unpaid accepted bookings found.');
        });

    $unpaidCount = $tripService->bookings()
        ->where('status', BookingStatusEnum::ACCEPTED())
        ->whereNull('paid_at')
        ->count();

    if ($unpaidCount < 1) {
        $this->markTestSkipped('No unpaid bookings found for testing.');
    }

    $response = $this->postJson(
        route('app.users.services.trips.mark-all-as-paid', $tripService->id)
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'status_code'
    ]);

    // Verify all bookings were updated
    $remainingUnpaid = $tripService->bookings()
        ->where('status', BookingStatusEnum::ACCEPTED())
        ->whereNull('paid_at')
        ->count();

    expect($remainingUnpaid)->toBe(0);
});

it('cannot mark bookings as paid for another user\'s trip service', function () {
    $this->actingAs($this->user, 'api');

    $otherUser = User::where('id', '!=', $this->user->id)->first();

    $tripService = $otherUser->tripServices()
        ->whereHas('bookings', function ($query) {
            $query->where('status', BookingStatusEnum::ACCEPTED())
                ->whereNull('paid_at');
        })
        ->firstOr(function () {
            $this->markTestSkipped('No trip services with unpaid bookings found for other user.');
        });

    $unpaidCountBefore = $tripService->bookings()
        ->where('status', BookingStatusEnum::ACCEPTED())
        ->whereNull('paid_at')
        ->count();

    if ($unpaidCountBefore < 1) {
        $this->markTestSkipped('No unpaid bookings found for testing.');
    }

    $response = $this->postJson(
        route('app.users.services.trips.mark-all-as-paid', $tripService->id)
    );

    expect($response->status())->toBe(404);

    // Verify bookings were not updated
    $unpaidCountAfter = $tripService->bookings()
        ->where('status', BookingStatusEnum::ACCEPTED())
        ->whereNull('paid_at')
        ->count();

    expect($unpaidCountAfter)->toBe($unpaidCountBefore);
});

it('returns 404 for non-existent trip service', function () {
    $this->actingAs($this->user, 'api');

    $response = $this->postJson(
        route('app.users.services.trips.mark-all-as-paid', 999999999)
    );

    expect($response->status())->toBe(404);
});

it('requires authentication', function () {
    $user = User::whereHas('tripServices.bookings')->first();
    $tripService = $user->tripServices()->first();

    $response = $this->postJson(
        route('app.users.services.trips.mark-all-as-paid', $tripService->id),
        [],
        ['Accept' => 'application/json']
    );

    expect($response->status())->toBe(401);
});
