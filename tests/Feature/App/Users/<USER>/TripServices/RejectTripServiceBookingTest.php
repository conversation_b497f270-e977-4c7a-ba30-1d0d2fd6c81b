<?php

use App\Enums\Bookings\BookingStatusEnum;
use App\Events\TripServiceBookingRejected;
use App\Models\User;
use Illuminate\Support\Facades\Event;

beforeEach(function () {
    $this->user = User::first();
    $this->actingAs($this->user, 'api');
});

it('can reject a trip service booking', function () {
    Event::fake();

    $tripService = $this->user->tripServices()->whereHas('bookings', function ($query) {
        $query->where('status', BookingStatusEnum::PENDING());
    })->inRandomOrder()->firstOr(function () {
        $this->markTestSkipped('No pending trip service found.');
    });

    $booking = $tripService->bookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    $rejectionData = [
        'rejection_reason' => 'Schedule conflict',
        'note' => 'Need to cancel due to schedule change',
    ];

    $response = $this->postJson(
        route('app.users.services.trips.bookings.reject', [
            'id' => $booking->id,
        ]),
        $rejectionData
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::REJECTED());
    expect($booking->rejection_reason)->toEqual($rejectionData['rejection_reason']);
    expect($booking->rejection_note)->toEqual($rejectionData['note']);

    Event::assertDispatched(TripServiceBookingRejected::class);
});

it('cannot reject non pending booking', function () {
    $tripService = $this->user->tripServices()->whereHas('bookings', function ($query) {
        $query->where('status', BookingStatusEnum::REJECTED());
    })->inRandomOrder()->firstOr(function () {
        $this->markTestSkipped('No rejected trip service found.');
    });

    $booking = $tripService->bookings()
        ->where('status', BookingStatusEnum::REJECTED())
        ->first();

    $response = $this->postJson(
        route('app.users.services.trips.bookings.reject', [
            'id' => $booking->id,
        ]),
        [
            'rejection_reason' => 'Schedule conflict',
            'note' => 'Need to cancel due to schedule change',
        ]
    );

    expect($response->status())->toBe(422);
    expect($response->json('message'))->toBe(__('exceptions.trip_service.booking.booking_is_not_pending'));

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::REJECTED());
});

it('cannot reject other users booking', function () {
    $otherUser = User::where('id', '!=', $this->user->id)->first();
    $tripService = $otherUser->tripServices()
        ->whereHas('bookings', function ($query) {
            $query->where('status', BookingStatusEnum::PENDING());
        })
        ->inRandomOrder()
        ->firstOr(function () {
            $this->markTestSkipped('No pending trip service found for other user.');
        });

    $booking = $tripService->bookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    $response = $this->postJson(
        route('app.users.services.trips.bookings.reject', [
            'id' => $booking->id,
        ]),
        [
            'rejection_reason' => 'Schedule conflict',
            'note' => 'Need to cancel due to schedule change',
        ]
    );

    expect($response->status())->toBe(404);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::PENDING());
});

it('validates rejection reasons', function () {
    $tripService = $this->user->tripServices()->whereHas('bookings', function ($query) {
        $query->where('status', BookingStatusEnum::PENDING());
    })->inRandomOrder()->firstOr(function () {
        $this->markTestSkipped('No pending trip service found.');
    });

    $booking = $tripService->bookings()
        ->where('status', BookingStatusEnum::PENDING())
        ->first();

    // Test empty rejection reason
    $response = $this->postJson(
        route('app.users.services.trips.bookings.reject', [
            'id' => $booking->id,
        ]),
        [
            'rejection_reason' => '',
            'note' => 'Need to cancel due to schedule change',
        ]
    );

    expect($response->status())->toBe(422);

    // Test too long rejection reason
    $response = $this->postJson(
        route('app.users.services.trips.bookings.reject', [
            'id' => $booking->id,
        ]),
        [
            'rejection_reason' => str_repeat('a', 256),
            'note' => 'Need to cancel due to schedule change',
        ]
    );

    expect($response->status())->toBe(422);

    $booking->refresh();
    expect($booking->status)->toBe(BookingStatusEnum::PENDING());
});
