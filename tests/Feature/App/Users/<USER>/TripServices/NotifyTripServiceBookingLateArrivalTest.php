<?php

use App\Events\TripServiceBookingLateArrivalNotified;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Event;

beforeEach(function () {
    $this->user = User::first();
    $this->actingAs($this->user, 'api');
});

it('can notify late arrival for trip service booking', function () {
    Event::fake();

    $booking = $this->user->tripServiceBookings()
        ->whereNull('cancelled_at')
        ->whereNull('rejected_at')
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No available trip service booking found for late arrival notification.');
    }

    // Ensure the trip service is not cancelled
    $booking->tripService->update([
        'cancelled_at' => null
    ]);

    // Ensure the trip service is in the future
    $booking->tripService->update([
        'departure_datetime' => Carbon::now()->addHour(),
        'arrival_datetime' => Carbon::now()->addHours(2)
    ]);

    $response = $this->postJson(
        route('app.users.services.trips.my-bookings.notify-late-arrival', $booking->id),
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $booking->refresh();
    expect($booking->late_arrival_notified_at)->not->toBeNull();

    Event::assertDispatched(TripServiceBookingLateArrivalNotified::class);
});

it('cannot notify late arrival for cancelled booking', function () {
    $booking = $this->user->tripServiceBookings()
        ->whereNull('rejected_at')
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No trip service booking found.');
    }

    // Update booking to be cancelled
    $booking->update([
        'cancelled_at' => Carbon::now()
    ]);

    $response = $this->postJson(
        route('app.users.services.trips.my-bookings.notify-late-arrival', $booking->id),
    );

    expect($response->status())->toBe(422)
        ->and($response->json('message'))->toBe(__('exceptions.trip_service.booking.cannot_notify_late_arrival_cancelled_booking'));
});

it('cannot notify late arrival for rejected booking', function () {
    $booking = $this->user->tripServiceBookings()
        ->whereNull('cancelled_at')
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No trip service booking found.');
    }

    // Update booking to be rejected
    $booking->update([
        'rejected_at' => Carbon::now()
    ]);

    $response = $this->postJson(
        route('app.users.services.trips.my-bookings.notify-late-arrival', $booking->id),
    );

    expect($response->status())->toBe(422)
        ->and($response->json('message'))->toBe(__('exceptions.trip_service.booking.cannot_notify_late_arrival_rejected_booking'));
});

it('cannot notify late arrival for cancelled trip service', function () {
    $booking = $this->user->tripServiceBookings()
        ->whereNull('cancelled_at')
        ->whereNull('rejected_at')
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No trip service booking found.');
    }

    // Update trip service to be cancelled
    $booking->tripService->update([
        'cancelled_at' => Carbon::now()
    ]);

    $response = $this->postJson(
        route('app.users.services.trips.my-bookings.notify-late-arrival', $booking->id),
    );

    expect($response->status())->toBe(422)
        ->and($response->json('message'))->toBe(__('exceptions.trip_service.booking.cannot_notify_late_arrival_cancelled_trip'));
});

it('cannot notify late arrival for completed trip', function () {
    $booking = $this->user->tripServiceBookings()
        ->whereNull('cancelled_at')
        ->whereNull('rejected_at')
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No trip service booking found.');
    }

    // Update trip service to be in the past (completed)
    $booking->tripService->update([
        'departure_datetime' => Carbon::now()->subHours(3),
        'arrival_datetime' => Carbon::now()->subHour()
    ]);


    $response = $this->postJson(
        route('app.users.services.trips.my-bookings.notify-late-arrival', $booking->id),
    );

    expect($response->status())->toBe(422)
        ->and($response->json('message'))->toBe(__('exceptions.trip_service.booking.cannot_notify_late_arrival_completed_trip'));
});

it('cannot notify late arrival for non-owned booking', function () {
    $otherUser = User::where('id', '!=', $this->user->id)->first();

    $booking = $otherUser->tripServiceBookings()
        ->whereNull('cancelled_at')
        ->whereNull('rejected_at')
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No other user\'s trip service booking found.');
    }

    $response = $this->postJson(
        route('app.users.services.trips.my-bookings.notify-late-arrival', $booking->id),
    );

    expect($response->status())->toBe(404);
});
