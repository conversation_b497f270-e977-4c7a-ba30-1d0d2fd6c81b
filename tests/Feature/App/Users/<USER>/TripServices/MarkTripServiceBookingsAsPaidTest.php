<?php

use App\Models\User;
use App\Models\Booking\TripServiceBooking;
use App\Enums\Bookings\BookingStatusEnum;
use Illuminate\Support\Facades\Event;

beforeEach(function () {
    $this->user = User::first();
});

it('can mark trip service bookings as paid', function () {
    $this->actingAs($this->user, 'api');

    $tripService = $this->user->tripServices()
        ->whereHas('bookings', function ($query) {
            $query->where('status', BookingStatusEnum::ACCEPTED())
                ->whereNull('paid_at');
        })
        ->firstOr(function () {
            $this->markTestSkipped('No trip services with unpaid accepted bookings found.');
        });

    $bookings = $tripService->bookings()
        ->where('status', BookingStatusEnum::ACCEPTED())
        ->whereNull('paid_at')
        ->take(2)
        ->get();

    if ($bookings->count() < 1) {
        $this->markTestSkipped('Not enough unpaid bookings found for testing.');
    }

    $bookingIds = $bookings->pluck('id')->toArray();

    $response = $this->postJson(
        route('app.users.services.trips.mark-as-paid', $tripService->id),
        [
            'booking_ids' => $bookingIds
        ]
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'status_code'
    ]);

    // Verify bookings were updated
    foreach ($bookingIds as $bookingId) {
        $booking = TripServiceBooking::find($bookingId);
        expect($booking->paid_at)->not->toBeNull();
    }
});

it('cannot mark bookings as paid for another user\'s trip service', function () {
    $this->actingAs($this->user, 'api');

    $otherUser = User::where('id', '!=', $this->user->id)->first();

    $tripService = $otherUser->tripServices()
        ->whereHas('bookings', function ($query) {
            $query->whereNull('paid_at');
        })
        ->firstOr(function () {
            $this->markTestSkipped('No trip services with unpaid bookings found for other user.');
        });

    $bookingIds = $tripService->bookings()
        ->whereNull('paid_at')
        ->take(2)
        ->pluck('id')
        ->toArray();

    if (empty($bookingIds)) {
        $this->markTestSkipped('No unpaid bookings found for testing.');
    }

    $response = $this->postJson(
        route('app.users.services.trips.mark-as-paid', $tripService->id),
        [
            'booking_ids' => $bookingIds
        ]
    );

    expect($response->status())->toBe(404);

    // Verify bookings were not updated
    foreach ($bookingIds as $bookingId) {
        $booking = TripServiceBooking::find($bookingId);
        expect($booking->paid_at)->toBeNull();
    }
});

it('validates booking_ids input', function () {
    $this->actingAs($this->user, 'api');

    $tripService = $this->user->tripServices()->first();

    if (!$tripService) {
        $this->markTestSkipped('No trip services found.');
    }

    // Test empty booking_ids
    $response = $this->postJson(
        route('app.users.services.trips.mark-as-paid', $tripService->id),
        ['booking_ids' => []]
    );

    expect($response->status())->toBe(422);

    // Test non-array booking_ids
    $response = $this->postJson(
        route('app.users.services.trips.mark-as-paid', $tripService->id),
        ['booking_ids' => 'not-an-array']
    );

    expect($response->status())->toBe(422);
});

it('returns 404 for non-existent trip service', function () {
    $this->actingAs($this->user, 'api');

    $response = $this->postJson(
        route('app.users.services.trips.mark-as-paid', 999999999),
        [
            'booking_ids' => ['123456789']
        ]
    );

    expect($response->status())->toBe(404);
});

it('requires authentication', function () {
    $user = User::whereHas('tripServices.bookings')->first();
    $tripService = $user->tripServices()->first();
    $bookingIds = $tripService->bookings()->pluck('id')->toArray();

    $response = $this->postJson(
        route('app.users.services.trips.mark-as-paid', $tripService->id),
        [
            'booking_ids' => $bookingIds
        ],
        ['Accept' => 'application/json']
    );

    expect($response->status())->toBe(401);
});
