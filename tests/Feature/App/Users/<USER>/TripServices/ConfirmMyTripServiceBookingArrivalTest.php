<?php

use App\Events\TripServiceBookingArrivalConfirmedEvent;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Event;

beforeEach(function () {
    $this->user = User::first();
    $this->actingAs($this->user, 'api');
});

it('can confirm trip service booking arrival within valid time window', function () {
    Event::fake();

    $booking = $this->user->tripServiceBookings()
        ->whereNull('arrival_confirmed_at')
        ->whereNull('cancelled_at')
        ->whereNull('rejected_at')
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No available trip service booking found for arrival confirmation.');
    }

    // Update the trip service arrival time to be within the valid window
    $booking->tripService->update([
        'arrival_datetime' => Carbon::now()->subMinutes(15),
        'cancelled_at' => null,
    ]);

    $response = $this->postJson(
        route('app.users.services.trips.my-bookings.confirm-arrival', $booking->id)
    );


    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $booking->refresh();
    expect($booking->arrival_confirmed_at)->not->toBeNull();

    Event::assertDispatched(TripServiceBookingArrivalConfirmedEvent::class);
});

it('cannot confirm arrival for cancelled booking', function () {
    $booking = $this->user->tripServiceBookings()
        ->whereNull('arrival_confirmed_at')
        ->whereNull('rejected_at')
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No trip service booking found.');
    }

    // Update booking to be cancelled
    $booking->update([
        'cancelled_at' => Carbon::now()
    ]);

    $response = $this->postJson(
        route('app.users.services.trips.my-bookings.confirm-arrival', $booking->id)
    );

    expect($response->status())->toBe(422);
});

it('cannot confirm arrival for rejected booking', function () {
    $booking = $this->user->tripServiceBookings()
        ->whereNull('arrival_confirmed_at')
        ->whereNull('cancelled_at')
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No trip service booking found.');
    }

    // Update booking to be rejected
    $booking->update([
        'rejected_at' => Carbon::now()
    ]);

    $response = $this->postJson(
        route('app.users.services.trips.my-bookings.confirm-arrival', $booking->id)
    );

    expect($response->status())->toBe(422);
});

it('cannot confirm arrival for cancelled service', function () {
    $booking = $this->user->tripServiceBookings()
        ->whereNull('arrival_confirmed_at')
        ->whereNull('cancelled_at')
        ->whereNull('rejected_at')
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No trip service booking found.');
    }

    // Update service to be cancelled
    $booking->tripService->update([
        'cancelled_at' => Carbon::now()
    ]);

    $response = $this->postJson(
        route('app.users.services.trips.my-bookings.confirm-arrival', $booking->id)
    );

    expect($response->status())->toBe(422);
});

it('cannot confirm arrival when already confirmed', function () {
    $booking = $this->user->tripServiceBookings()
        ->whereNull('cancelled_at')
        ->whereNull('rejected_at')
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No trip service booking found.');
    }

    // Update booking to be already confirmed
    $booking->update([
        'arrival_confirmed_at' => Carbon::now()
    ]);

    $response = $this->postJson(
        route('app.users.services.trips.my-bookings.confirm-arrival', $booking->id)
    );

    expect($response->status())->toBe(422);
});

it('cannot confirm arrival too early', function () {
    $booking = $this->user->tripServiceBookings()
        ->whereNull('cancelled_at')
        ->whereNull('rejected_at')
        ->whereNull('arrival_confirmed_at')
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No trip service booking found for early confirmation test.');
    }

    // Update arrival time to be too early
    $booking->tripService->update([
        'arrival_datetime' => Carbon::now()->addHours(2)
    ]);

    $response = $this->postJson(
        route('app.users.services.trips.my-bookings.confirm-arrival', $booking->id)
    );

    expect($response->status())->toBe(422);
});

it('cannot confirm arrival for non-owned booking', function () {
    $otherUser = User::where('id', '!=', $this->user->id)->first();

    $booking = $otherUser->tripServiceBookings()
        ->whereNull('cancelled_at')
        ->whereNull('rejected_at')
        ->whereNull('arrival_confirmed_at')
        ->first();

    if (!$booking) {
        $this->markTestSkipped('No other user\'s trip service booking found.');
    }

    $response = $this->postJson(
        route('app.users.services.trips.my-bookings.confirm-arrival', $booking->id)
    );

    expect($response->status())->toBe(404);
});
