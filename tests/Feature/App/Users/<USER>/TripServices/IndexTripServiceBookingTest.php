<?php

use App\Models\User;
use App\Models\Service\TripService;

beforeEach(function () {
    $this->user = User::first();
});

it('can list trip service bookings', function () {
    $this->actingAs($this->user, 'api');

    $tripService = $this->user->tripServices()
        ->whereHas('bookings')
        ->firstOr(function () {
            $this->markTestSkipped('No trip service with bookings found.');
        });

    $response = $this->getJson(route('app.users.services.trips.bookings.index', [
        'id' => $tripService->id
    ]));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data.current_page',
        'data.data',
        'data.first_page_url',
        'data.from',
        'data.last_page',
        'data.last_page_url',
        'data.links',
        'data.next_page_url',
        'data.path',
        'data.per_page',
        'data.prev_page_url',
        'data.to',
        'data.total',
        'status_code'
    ]);

    $bookingsData = $response->json('data.data');
    expect($bookingsData)->toBeArray();

    foreach ($bookingsData as $booking) {
        expect($booking)->toHaveKeys([
            'id',
            'note',
            'created_at',
            'updated_at',
            'status',
            'seats_count'
        ]);
        //verify that the seats_count is the same as the number of seats in the booking
        expect($booking['seats_count'])->toBe(count($tripService->bookings()->find($booking['id'])->seats));
    }
});

it('respects per_page parameter', function () {
    $this->actingAs($this->user, 'api');

    $perPage = 5;
    $tripService = $this->user->tripServices()
        ->whereHas('bookings')
        ->firstOr(function () {
            $this->markTestSkipped('No trip service with bookings found.');
        });

    $response = $this->getJson(route('app.users.services.trips.bookings.index', [
        'id' => $tripService->id,
        'per_page' => $perPage
    ]));

    expect($response->status())->toBe(200);
    expect($response->json('data.per_page'))->toBe($perPage);
    expect(count($response->json('data.data')))->toBeLessThanOrEqual($perPage);
});

it('cannot list bookings of other users trip service', function () {
    $this->actingAs($this->user, 'api');

    $otherUser = User::where('id', '!=', $this->user->id)->first();

    $tripService = $otherUser->tripServices()
        ->whereHas('bookings')
        ->firstOr(function () {
            $this->markTestSkipped('No trip service with bookings found for other user.');
        });

    $response = $this->getJson(route('app.users.services.trips.bookings.index', [
        'id' => $tripService->id
    ]));

    expect($response->status())->toBe(404);
});

it('requires authentication', function () {
    $tripService = TripService::whereHas('bookings')->first();

    if (!$tripService) {
        $this->markTestSkipped('No trip service with bookings found.');
    }

    $response = $this->getJson(route('app.users.services.trips.bookings.index', [
        'id' => $tripService->id
    ]));

    expect($response->status())->toBe(401);
});
