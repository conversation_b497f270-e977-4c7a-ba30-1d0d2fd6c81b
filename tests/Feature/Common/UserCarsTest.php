<?php

use App\Models\Common\Car;
use App\Models\Common\CarType;
use App\Models\User;
use function Pest\Laravel\get;
use function Pest\Laravel\post;

beforeEach(function () {
    $user = User::first();
    $this->token = $user->createToken('test-token')->plainTextToken;
});

it('can access the my cars index route', function () {
    $response = get(route('app.users.cars.index'), [
        'Authorization' => 'Bearer ' . $this->token
    ]);

    expect($response->status())->toBe(200);

    $responseData = $response->json('data.data');

    expect($responseData)->toBeArray();

    foreach ($responseData as $car) {
        expect($car)->toHaveKeys([
            'id',
            'model',
            'plate_number',
            'type.id',
            'type.name',
            'type.number_of_seats'
        ]);

        expect($car['id'])->not->toBeNull();
        expect($car['model'])->not->toBeNull();
        expect($car['plate_number'])->not->toBeNull();
        expect($car['type']['name'])->not->toBeNull();
        expect($car['type']['number_of_seats'])->not->toBeNull();
    }
});

it('can store a car', function () {
    $carType = CarType::inRandomOrder()->first();

    $carData = [
        'model' => fake()->numberBetween(1980, now()->year),
        'plate_number' => fake()->regexify('[A-Z]{3}[0-9]{4}'),
        'type_id' => $carType->id,
    ];

    $response = post(route('app.users.cars.store'), $carData, [
        'Authorization' => 'Bearer ' . $this->token
    ]);

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $carData = $response->json('data');

    expect($carData)->toHaveKeys([
        'id',
        'model',
        'plate_number',
        'type.id',
        'type.name',
        'type.number_of_seats'
    ]);

    expect($carData['id'])->not->toBeNull();
    expect($carData['model'])->not->toBeNull();
    expect($carData['plate_number'])->not->toBeNull();
    expect($carData['type']['name'])->not->toBeNull();
    expect($carData['type']['number_of_seats'])->not->toBeNull();
});
