<?php

namespace Tests\Feature\Common;

use App\Models\Common\CarType;
use App\Models\Common\FazaaServiceType;

use function Pest\Laravel\get;

it('can access the cities index route', function () {
    $response = get(route('app.common.cities.index'));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys(['success', 'message', 'data', 'status_code']);
    expect($response->json('data'))->each(fn ($city) =>
        expect($city->value)->toHaveKeys(['id', 'name'])
    );
});

it('can access the additional services index route', function () {
    $response = get(route('app.common.additional-services.show'));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys(['success', 'message', 'data', 'status_code']);
    expect($response->json('data'))->each(fn ($service) =>
        expect($service->value)->toHaveKeys(['id', 'name'])
    );
});

it('can access the car types index route', function () {
    $response = get(route('app.common.car-types.index'));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys(['success', 'message', 'data', 'status_code']);
    expect($response->json('data'))->each(fn ($carType) =>
        expect($carType->value)->toHaveKeys(['id', 'name', 'number_of_seats'])
    );
});

it('can access the seats index route', function () {
    $carType = CarType::inRandomOrder()->first();
    $response = get(route('app.common.car-types.seats.index', ['id' => $carType->id]));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys(['success', 'message', 'data', 'status_code']);
    expect($response->json('data'))->each(fn ($seat) =>
        expect($seat->value)->toHaveKeys(['number', 'status'])
    );
});

it('can access the fazaa service types index route', function () {
    $response = get(route('app.common.fazaas-service-types.index'));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys(['success', 'message', 'data', 'status_code']);
    expect($response->json('data'))->each(function ($serviceType) {
        $serviceType = $serviceType->value;
        expect($serviceType)->toHaveKeys(['id', 'name', 'key']);
        expect($serviceType['id'])->toBeNumeric();
        expect($serviceType['name'])->toBeString();
        expect($serviceType['key'])->toBeString();
    });
});

it('can access the fazaa specific types index route', function () {
    $response = get(route('app.common.fazaas-specific-types.index'));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys(['success', 'message', 'data', 'status_code']);
    expect($response->json('data'))->each(fn ($specificType) =>
        expect($specificType->value)->toHaveKeys(['id', 'name'])
    );
});

it('can access the countries index route', function () {
    $response = get(route('app.common.countries.index'));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys(['success', 'message', 'data', 'status_code']);
    expect($response->json('data'))->each(fn ($country) =>
        expect($country->value)->toHaveKeys(['name', 'code', 'mobile_code'])
    );
});
