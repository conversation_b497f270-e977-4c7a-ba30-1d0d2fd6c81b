<?php

namespace Tests\Feature\Requests\Shipping;

use App\Enums\Travel\TransportationTypeEnum;
use App\Enums\Travel\TransportServiceTypeEnum;
use App\Models\Request\ShippingRequest;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;

use function Pest\Laravel\delete;
use function Pest\Laravel\get;
use function Pest\Laravel\post;


beforeEach(function () {
    $user = User::first();
    $this->token = $user->createToken('test-token')->plainTextToken;
});

it('can access the my shipping requests index route', function () {
    $response = get(route('app.users.requests.shippings.index'), [
        'Authorization' => 'Bearer ' . $this->token
    ]);

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $requestData = $response->json('data.data');
    foreach ($requestData as $request) {
        expect($request)->toHaveKeys([
            'id',
            'transportation_type',
            'can_ship_packages',
            'can_ship_documents',
            'can_ship_furniture',
            'departure_datetime',
            'arrival_datetime',
            'from_city',
            'from_location',
            'to_city',
            'to_location',
            'packages_volume',
            'document_volume',
            'furniture_volume',
            'delivery_location',
            'is_fragile',
            'note',
        ]);

        expect($request['id'])->not->toBeNull();
        expect($request['departure_datetime'])->not->toBeNull();
        expect($request['arrival_datetime'])->not->toBeNull();
        expect($request['from_city']['id'])->not->toBeNull();
        expect($request['from_city']['name'])->not->toBeNull();
        expect($request['to_city']['id'])->not->toBeNull();
        expect($request['to_city']['name'])->not->toBeNull();
        expect($request['from_location']['lat'])->not->toBeNull();
        expect($request['from_location']['lng'])->not->toBeNull();
        expect($request['to_location']['lat'])->not->toBeNull();
        expect($request['to_location']['lng'])->not->toBeNull();
        expect($request['delivery_location'])->not->toBeNull();
        expect($request['is_fragile'])->toBeBool();
        expect($request['packages_volume'])->when($request['can_ship_packages'], fn() => expect($request['packages_volume'])->toBeGreaterThan(0));
        expect($request['document_volume'])->when($request['can_ship_documents'], fn() => expect($request['document_volume'])->toBeGreaterThan(0));
        expect($request['furniture_volume'])->when($request['can_ship_furniture'], fn() => expect($request['furniture_volume'])->toBeGreaterThan(0));
    }
});

it('can store a shipping request', function () {
    $requestData = [
        'transportation_type' => fake()->randomElement(TransportationTypeEnum::values()),
        'departure_datetime' => now()->addDays(fake()->numberBetween(1, 30))->toDateTimeString(),
        'arrival_datetime' => now()->addDays(fake()->numberBetween(31, 60))->toDateTimeString(),
        'from_city_en' => 'Riyadh',
        'from_city_ar' => 'الرياض',
        'to_city_en' => 'Jeddah',
        'to_city_ar' => 'جدة',
        'from_location' => [
            'lat' => fake()->latitude(),
            'lng' => fake()->longitude(),
        ],
        'to_location' => [
            'lat' => fake()->latitude(),
            'lng' => fake()->longitude(),
        ],
        'price' => fake()->numberBetween(50, 500),
        'is_fragile' => fake()->boolean(),
        'note' => fake()->sentence(),
        'delivery_location' => fake()->address(),
    ];

    $canShipPackages = fake()->boolean();
    if ($canShipPackages) {
        $requestData['packages_volume'] = fake()->numberBetween(1, 100);
    }
    $canShipDocuments = fake()->boolean();
    if ($canShipDocuments) {
        $requestData['document_volume'] = fake()->numberBetween(1, 100);
    }
    $canShipFurniture = fake()->boolean();
    if ($canShipFurniture) {
        $requestData['furniture_volume'] = fake()->numberBetween(1, 100);
    }

    $requestData['can_ship_packages'] = $canShipPackages;
    $requestData['can_ship_documents'] = $canShipDocuments;
    $requestData['can_ship_furniture'] = $canShipFurniture;

    $response = post(route('app.users.requests.shippings.store'), $requestData, [
        'Authorization' => 'Bearer ' . $this->token
    ]);

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $responseData = $response->json('data');

    expect($responseData)->toHaveKeys([
        'id',
        'transportation_type',
        'can_ship_packages',
        'can_ship_documents',
        'can_ship_furniture',
        'departure_datetime',
        'arrival_datetime',
        'from_city',
        'from_location',
        'to_city',
        'to_location',
        'packages_volume',
        'document_volume',
        'furniture_volume',
        'delivery_location',
        'is_fragile',
        'note',
    ]);

    expect($responseData['id'])->not->toBeNull();
    expect($responseData['transportation_type'])->toBe(__('enums.transportation_type.' . $requestData['transportation_type'], locale: App::getLocale()));
    expect($responseData['can_ship_packages'])->toBe($requestData['can_ship_packages']);
    expect($responseData['can_ship_documents'])->toBe($requestData['can_ship_documents']);
    expect($responseData['can_ship_furniture'])->toBe($requestData['can_ship_furniture']);
    expect($responseData['packages_volume'])->when($requestData['can_ship_packages'], fn() => expect($responseData['packages_volume'])->toBeGreaterThan(0));
    expect($responseData['document_volume'])->when($requestData['can_ship_documents'], fn() => expect($responseData['document_volume'])->toBeGreaterThan(0));
    expect($responseData['furniture_volume'])->when($requestData['can_ship_furniture'], fn() => expect($responseData['furniture_volume'])->toBeGreaterThan(0));
    expect(Carbon::parse($responseData['departure_datetime'])->toDateTimeString())->toBe(Carbon::parse($requestData['departure_datetime'])->toDateTimeString());
    expect(Carbon::parse($responseData['arrival_datetime'])->toDateTimeString())->toBe(Carbon::parse($requestData['arrival_datetime'])->toDateTimeString());
    expect($responseData['from_city']['name'])->toBe($requestData['from_city_' . app()->getLocale()]);
    expect($responseData['to_city']['name'])->toBe($requestData['to_city_' . app()->getLocale()]);
    expect($responseData['is_fragile'])->toBe($requestData['is_fragile']);
    expect($responseData['note'])->toBe($requestData['note']);
    expect($responseData['from_location']['lat'])->toBe($requestData['from_location']['lat']);
    expect($responseData['from_location']['lng'])->toBe($requestData['from_location']['lng']);
    expect($responseData['to_location']['lat'])->toBe($requestData['to_location']['lat']);
    expect($responseData['to_location']['lng'])->toBe($requestData['to_location']['lng']);
});

it('can access the shipping requests index route', function () {
    $response = get(route('app.requests.shippings.index'));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $requestData = $response->json('data.data');
    foreach ($requestData as $request) {
        expect($request)->toHaveKeys([
            'id',
            'can_ship_packages',
            'can_ship_documents',
            'can_ship_furniture',
            'departure_datetime',
            'arrival_datetime',
            'packages_volume',
            'document_volume',
            'furniture_volume',
            'from_city',
            'to_city',
            'delivery_location',
            'user',

        ]);
        expect($request['id'])->not->toBeNull();
        expect($request['can_ship_packages'])->toBeBool();
        expect($request['can_ship_documents'])->toBeBool();
        expect($request['can_ship_furniture'])->toBeBool();
        expect($request['departure_datetime'])->not->toBeNull();
        expect($request['arrival_datetime'])->not->toBeNull();
        expect($request['packages_volume'])->when($request['can_ship_packages'], fn() => expect($request['packages_volume'])->toBeGreaterThan(0));
        expect($request['document_volume'])->when($request['can_ship_documents'], fn() => expect($request['document_volume'])->toBeGreaterThan(0));
        expect($request['furniture_volume'])->when($request['can_ship_furniture'], fn() => expect($request['furniture_volume'])->toBeGreaterThan(0));
        expect($request['from_city']['id'])->not->toBeNull();
        expect($request['from_city']['name'])->not->toBeNull();
        expect($request['to_city']['id'])->not->toBeNull();
        expect($request['to_city']['name'])->not->toBeNull();
        expect($request['delivery_location'])->not->toBeNull();
        expect($request['user']['id'])->not->toBeNull();
        expect($request['user']['name'])->not->toBeNull();
    }
});

it('can show a shipping request', function () {
    $shippingRequest = ShippingRequest::inRandomOrder()->first();
    $response = get(route('app.requests.shippings.show', $shippingRequest->id));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $responseData = $response->json('data');

    expect($responseData)->toHaveKeys([
        'id',
        'transportation_type',
        'can_ship_packages',
        'can_ship_documents',
        'can_ship_furniture',
        'departure_datetime',
        'arrival_datetime',
        'from_city',
        'to_city',
        'from_location',
        'to_location',
        'packages_volume',
        'document_volume',
        'furniture_volume',
        'delivery_location',
        'is_fragile',
        'note',
        'user',
    ]);

    expect($responseData['id'])->toBe($shippingRequest->id);
    expect($responseData['transportation_type'])->toBe($shippingRequest->transportation_type_formatted);
    expect($responseData['can_ship_packages'])->toBe($shippingRequest->can_ship_packages);
    expect($responseData['can_ship_documents'])->toBe($shippingRequest->can_ship_documents);
    expect($responseData['can_ship_furniture'])->toBe($shippingRequest->can_ship_furniture);
    expect(Carbon::parse($responseData['departure_datetime'])->toDateTimeString())->toBe(Carbon::parse($shippingRequest->departure_datetime)->toDateTimeString());
    expect(Carbon::parse($responseData['arrival_datetime'])->toDateTimeString())->toBe(Carbon::parse($shippingRequest->arrival_datetime)->toDateTimeString());
    expect($responseData['from_city']['id'])->toBe($shippingRequest->fromCity->id);
    expect($responseData['to_city']['id'])->toBe($shippingRequest->toCity->id);
    expect($responseData['from_location']['lat'])->toBe($shippingRequest->from_location_lat);
    expect($responseData['from_location']['lng'])->toBe($shippingRequest->from_location_lng);
    expect($responseData['to_location']['lat'])->toBe($shippingRequest->to_location_lat);
    expect($responseData['to_location']['lng'])->toBe($shippingRequest->to_location_lng);
    expect($responseData['packages_volume'])->toBe($shippingRequest->packages_volume);
    expect($responseData['document_volume'])->toBe($shippingRequest->document_volume);
    expect($responseData['furniture_volume'])->toBe($shippingRequest->furniture_volume);
    expect($responseData['delivery_location'])->toBe($shippingRequest->delivery_location);
    expect($responseData['is_fragile'])->toBe($shippingRequest->is_fragile);
    expect($responseData['note'])->toBe($shippingRequest->note);
    expect($responseData['user']['id'])->toBe($shippingRequest->user->id);
});

it('can book a shipping request', function () {
    $shippingRequest = ShippingRequest::query()->available()->upcoming()->inRandomOrder()->first();
    if (!$shippingRequest) {
        $this->markTestSkipped('No available upcoming shipping requests found.');
    }

    $bookingData = [
        'note' => fake()->sentence(),
    ];

    if ($shippingRequest->can_ship_packages) {
        $bookingData['package_price'] = fake()->numberBetween(1, 100);
    }
    if ($shippingRequest->can_ship_documents) {
        $bookingData['document_price'] = fake()->numberBetween(1, 100);
    }
    if ($shippingRequest->can_ship_furniture) {
        $bookingData['furniture_price'] = fake()->numberBetween(1, 100);
    }

    $response = post(route('app.requests.shippings.book.store', $shippingRequest->id), $bookingData, [
        'Authorization' => 'Bearer ' . $this->token
    ]);

    expect($response->status())->toBe(200);

    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $bookingData = $response->json('data');

    expect($bookingData)->toHaveKeys([
        'id',
        'note',
        'created_at',
        'updated_at',
        'shipping_request',
        'package_price',
        'document_price',
        'furniture_price',
    ]);

    expect($bookingData['shipping_request']['id'])->toBe($shippingRequest->id);
    expect($bookingData['note'])->toBe($bookingData['note']);
    expect($bookingData['package_price'])->when($shippingRequest->can_ship_packages, fn() => expect($bookingData['package_price'])->toBeGreaterThan(0));
    expect($bookingData['document_price'])->when($shippingRequest->can_ship_documents, fn() => expect($bookingData['document_price'])->toBeGreaterThan(0));
    expect($bookingData['furniture_price'])->when($shippingRequest->can_ship_furniture, fn() => expect($bookingData['furniture_price'])->toBeGreaterThan(0));
});

it('can bookmark and unbookmark a shipping request', function () {
    $shippingRequest = ShippingRequest::inRandomOrder()->first();

    // Test bookmarking
    $response = post(route('app.users.bookmarks.store'), [
        'bookmarkable_type' => TransportServiceTypeEnum::SHIPPING_REQUEST(),
        'bookmarkable_id' => $shippingRequest->id
    ], [
        'Authorization' => 'Bearer ' . $this->token
    ]);

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    /** @var User $user */
    $user = Auth::user();

    expect($user->bookmarks()->where([
        'bookmarkable_type' => ShippingRequest::class,
        'bookmarkable_id' => $shippingRequest->id
    ])->exists())->toBeTrue();

    // Test unbookmarking by toggling again
    $response = delete(route('app.users.bookmarks.destroy'), [
        'bookmarkable_type' => TransportServiceTypeEnum::SHIPPING_REQUEST(),
        'bookmarkable_id' => $shippingRequest->id
    ], [
        'Authorization' => 'Bearer ' . $this->token
    ]);

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);
    expect($user->bookmarks()->where([
        'bookmarkable_type' => ShippingRequest::class,
        'bookmarkable_id' => $shippingRequest->id
    ])->exists())->toBeFalse();
});
