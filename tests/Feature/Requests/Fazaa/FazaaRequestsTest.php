<?php

namespace Tests\Feature\Requests\Fazaa;

use App\Enums\Travel\TransportationTypeEnum;
use App\Enums\Travel\TransportServiceTypeEnum;
use App\Models\Common\FazaaServiceType;
use App\Models\Common\FazaaSpecificType;
use App\Models\Request\FazaaRequest;
use App\Models\User;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;

use function Pest\Laravel\delete;
use function Pest\Laravel\get;
use function Pest\Laravel\post;

beforeEach(function () {
    $user = User::first();
    $this->token = $user->createToken('test-token')->plainTextToken;
});

it('can access the my fazaa requests index route', function () {
    $response = get(route('app.users.requests.fazaas.index'), [
        'Authorization' => 'Bearer ' . $this->token
    ]);

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $requestData = $response->json('data.data');
    foreach ($requestData as $request) {
        expect($request)->toHaveKeys([
            'id',
            'fazaa_service_type',
            'specific_type',
            'transportation_type',
            'departure_datetime',
            'arrival_datetime',
            'from_city',
            'from_location',
            'to_city',
            'to_location',
            'service_location',
            'note',
        ]);

        expect($request['id'])->not->toBeNull();
        expect($request['fazaa_service_type'])->not->toBeNull();
        expect($request['specific_type'])->not->toBeNull();
        expect($request['transportation_type'])->not->toBeNull();
        expect($request['departure_datetime'])->not->toBeNull();
        expect($request['arrival_datetime'])->not->toBeNull();
        expect($request['from_city']['id'])->not->toBeNull();
        expect($request['from_city']['name'])->not->toBeNull();
        expect($request['from_location']['lat'])->not->toBeNull();
        expect($request['from_location']['lng'])->not->toBeNull();
        expect($request['to_city']['id'])->not->toBeNull();
        expect($request['to_city']['name'])->not->toBeNull();
        expect($request['to_location']['lat'])->not->toBeNull();
        expect($request['to_location']['lng'])->not->toBeNull();
        expect($request['service_location'])->not->toBeNull();
    }
});

it('can store a fazaa request', function () {
    $fazaaServiceType = FazaaServiceType::inRandomOrder()->first();

    $requestData = [
        'fazaa_service_type_id' => $fazaaServiceType->id,
        'specific_type_id' => FazaaSpecificType::where('fazaa_service_type_id', $fazaaServiceType->id)->inRandomOrder()->first()->id,
        'transportation_type' => fake()->randomElement(TransportationTypeEnum::values()),
        'from_city_en' => 'Riyadh',
        'from_city_ar' => 'الرياض',
        'from_location' => [
            'lat' => 24.7136,
            'lng' => 46.6753
        ],
        'to_city_en' => 'Jeddah',
        'to_city_ar' => 'جدة',
        'to_location' => [
            'lat' => 21.4858,
            'lng' => 39.1925
        ],
        'departure_datetime' => now()->addDays(7)->toDateTimeString(),
        'arrival_datetime' => now()->addDays(7)->addHours(5)->toDateTimeString(),
        'service_location' => fake()->address(),
        'note' => fake()->sentence(),
    ];

    $response = post(route('app.users.requests.fazaas.store'), $requestData, [
        'Authorization' => 'Bearer ' . $this->token
    ]);

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $responseData = $response->json('data');

    expect($responseData)->toHaveKeys([
        'id',
        'fazaa_service_type',
        'specific_type',
        'transportation_type',
        'departure_datetime',
        'arrival_datetime',
        'from_city',
        'from_location',
        'to_city',
        'to_location',
        'service_location',
        'note',
    ]);

    expect($responseData['id'])->not->toBeNull();
    expect($responseData['fazaa_service_type'])->toBe($fazaaServiceType->name);
    expect($responseData['transportation_type'])->toBe(__('enums.transportation_type.' . $requestData['transportation_type'], locale: App::getLocale()));
    expect(Carbon::parse($responseData['departure_datetime'])->toDateTimeString())->toBe(Carbon::parse($requestData['departure_datetime'])->toDateTimeString());
    expect(Carbon::parse($responseData['arrival_datetime'])->toDateTimeString())->toBe(Carbon::parse($requestData['arrival_datetime'])->toDateTimeString());
    expect($responseData['from_city']['name'])->toBe($requestData['from_city_' . App::getLocale()]);
    expect($responseData['from_location']['lat'])->toBe($requestData['from_location']['lat']);
    expect($responseData['from_location']['lng'])->toBe($requestData['from_location']['lng']);
    expect($responseData['to_city']['name'])->toBe($requestData['to_city_' . App::getLocale()]);
    expect($responseData['to_location']['lat'])->toBe($requestData['to_location']['lat']);
    expect($responseData['to_location']['lng'])->toBe($requestData['to_location']['lng']);
    expect($responseData['service_location'])->toBe($requestData['service_location']);
    expect($responseData['note'])->toBe($requestData['note']);
});

it('can access the fazaa requests index route', function () {
    $response = get(route('app.requests.fazaas.index'));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $requestData = $response->json('data.data');
    foreach ($requestData as $request) {
        expect($request)->toHaveKeys([
            'id',
            'fazaa_service_type',
            'specific_type',
            'transportation_type',
            'departure_datetime',
            'arrival_datetime',
            'from_city',
            'to_city',
            'service_location',
            'user',
        ]);

        expect($request['id'])->not->toBeNull();
        expect($request['fazaa_service_type'])->not->toBeNull();
        expect($request['specific_type'])->not->toBeNull();
        expect($request['transportation_type'])->not->toBeNull();
        expect($request['departure_datetime'])->not->toBeNull();
        expect($request['arrival_datetime'])->not->toBeNull();
        expect($request['from_city']['id'])->not->toBeNull();
        expect($request['from_city']['name'])->not->toBeNull();
        expect($request['to_city']['id'])->not->toBeNull();
        expect($request['to_city']['name'])->not->toBeNull();
        expect($request['service_location'])->not->toBeNull();
        expect($request['user']['id'])->not->toBeNull();
        expect($request['user']['name'])->not->toBeNull();
    }
});

it('can show a fazaa request', function () {
    $fazaaRequest = FazaaRequest::inRandomOrder()->first();
    $response = get(route('app.requests.fazaas.show', $fazaaRequest->id));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $responseData = $response->json('data');

    expect($responseData)->toHaveKeys([
        'id',
        'fazaa_service_type',
        'specific_type',
        'transportation_type',
        'departure_datetime',
        'arrival_datetime',
        'from_city',
        'from_location',
        'to_city',
        'to_location',
        'service_location',
        'user',
        'note',
    ]);

    expect($responseData['id'])->toBe($fazaaRequest->id);
    expect($responseData['fazaa_service_type'])->not->toBeNull();
    expect($responseData['specific_type'])->not->toBeNull();
    expect($responseData['transportation_type'])->not->toBeNull();
    expect($responseData['departure_datetime'])->not->toBeNull();
    expect($responseData['arrival_datetime'])->not->toBeNull();
    expect($responseData['from_city']['id'])->not->toBeNull();
    expect($responseData['from_city']['name'])->not->toBeNull();
    expect($responseData['from_location']['lat'])->not->toBeNull();
    expect($responseData['from_location']['lng'])->not->toBeNull();
    expect($responseData['to_city']['id'])->not->toBeNull();
    expect($responseData['to_city']['name'])->not->toBeNull();
    expect($responseData['to_location']['lat'])->not->toBeNull();
    expect($responseData['to_location']['lng'])->not->toBeNull();
    expect($responseData['service_location'])->not->toBeNull();
    expect($responseData['user']['id'])->not->toBeNull();
    expect($responseData['user']['name'])->not->toBeNull();
});

it('can book a fazaa request', function () {
    $this->withoutExceptionHandling();

    $fazaaRequest = FazaaRequest::inRandomOrder()->upcoming()->available()->first();

    if (!$fazaaRequest) {
        $this->markTestSkipped('No available nor upcoming fazaa requests found.');
    }

    $requestData = [
        'note' => fake()->sentence(),
        'price' => fake()->randomFloat(2, 100, 1000),
    ];

    $response = post(route('app.requests.fazaas.book.store', $fazaaRequest->id), $requestData, [
        'Authorization' => 'Bearer ' . $this->token
    ]);

    expect($response->status())->toBe(200);


    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $responseData = $response->json('data');

    expect($responseData)->toHaveKeys([
        'id',
        'note',
        'price',
        'created_at',
        'updated_at',
        'fazaa_request',
    ]);

    expect($responseData['id'])->not->toBeNull();
    expect($responseData['note'])->toBe($requestData['note']);
    expect($responseData['price'])->toBe($requestData['price']);
    expect($responseData['created_at'])->not->toBeNull();
    expect($responseData['updated_at'])->not->toBeNull();
    expect($responseData['fazaa_request']['id'])->toBe($fazaaRequest->id);
    expect($responseData['fazaa_request']['fazaa_service_type'])->not->toBeNull();
    expect($responseData['fazaa_request']['specific_type'])->not->toBeNull();
    expect($responseData['fazaa_request']['transportation_type'])->not->toBeNull();
    expect($responseData['fazaa_request']['departure_datetime'])->not->toBeNull();
    expect($responseData['fazaa_request']['arrival_datetime'])->not->toBeNull();
    expect($responseData['fazaa_request']['from_city']['id'])->not->toBeNull();
    expect($responseData['fazaa_request']['from_city']['name'])->not->toBeNull();
    expect($responseData['fazaa_request']['to_city']['id'])->not->toBeNull();
    expect($responseData['fazaa_request']['to_city']['name'])->not->toBeNull();
    expect($responseData['fazaa_request']['service_location'])->not->toBeNull();
    expect($responseData['fazaa_request']['user']['id'])->not->toBeNull();
    expect($responseData['fazaa_request']['user']['name'])->not->toBeNull();
});

it('can bookmark and unbookmark a fazaa request', function () {
    $fazaaRequest = FazaaRequest::inRandomOrder()->first();

    $response = post(route('app.users.bookmarks.store'), [
        'bookmarkable_type' => TransportServiceTypeEnum::FAZAA_REQUEST(),
        'bookmarkable_id' => $fazaaRequest->id
    ], [
        'Authorization' => 'Bearer ' . $this->token
    ]);

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    /** @var User $user */
    $user = Auth::user();

    expect($user->bookmarks()->where([
        'bookmarkable_type' => FazaaRequest::class,
        'bookmarkable_id' => $fazaaRequest->id
    ])->exists())->toBeTrue();

    $response = delete(route('app.users.bookmarks.destroy'), [
        'bookmarkable_type' => TransportServiceTypeEnum::FAZAA_REQUEST(),
        'bookmarkable_id' => $fazaaRequest->id
    ], [
        'Authorization' => 'Bearer ' . $this->token
    ]);

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);
    expect($user->bookmarks()->where([
        'bookmarkable_type' => FazaaRequest::class,
        'bookmarkable_id' => $fazaaRequest->id
    ])->exists())->toBeFalse();
});
