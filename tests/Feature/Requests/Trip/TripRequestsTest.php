<?php

namespace Tests\Feature\Requests\Trip;

use App\Enums\Travel\TransportServiceTypeEnum;
use App\Enums\Travel\TripTypeEnum;
use App\Models\Common\CarType;
use App\Models\Request\TripRequest;
use App\Models\User;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;

use function Pest\Laravel\delete;
use function Pest\Laravel\get;
use function Pest\Laravel\post;


beforeEach(function () {
    $user = User::first();
    $this->token = $user->createToken('test-token')->plainTextToken;
});

it('can access the my trip requests index route', function () {
    $response = get(route('app.users.requests.trips.index'), [
        'Authorization' => 'Bearer ' . $this->token
    ]);

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $requestData = $response->json('data.data');
    foreach ($requestData as $request) {
        expect($request)->toHaveKeys([
            'id',
            'trip_type',
            'departure_datetime',
            'arrival_datetime',
            'number_of_seats',
            'from_city',
            'from_location',
            'to_city',
            'to_location',
            'price',
            'allow_smoking',
            'deliver_to_door',
            'arrival_datetime',
        ]);

        expect($request['id'])->not->toBeNull();
        expect($request['trip_type'])->not->toBeNull();
        expect($request['departure_datetime'])->not->toBeNull();
        expect($request['arrival_datetime'])->not->toBeNull();
        expect($request['number_of_seats'])->toBeGreaterThan(0);
        expect($request['from_city']['id'])->not->toBeNull();
        expect($request['from_city']['name'])->not->toBeNull();
        expect($request['from_location']['lat'])->not->toBeNull();
        expect($request['from_location']['lng'])->not->toBeNull();
        expect($request['to_city']['id'])->not->toBeNull();
        expect($request['to_city']['name'])->not->toBeNull();
        expect($request['to_location']['lat'])->not->toBeNull();
        expect($request['to_location']['lng'])->not->toBeNull();
        expect($request['price'])->toBeGreaterThan(0);
    }
});

it('can store a trip request', function () {
    $requestData = [
        'trip_type' => fake()->randomElement(TripTypeEnum::values()),
        'from_city_en' => 'Riyadh',
        'from_city_ar' => 'الرياض',
        'from_location' => [
            'lat' => 24.7136,
            'lng' => 46.6753
        ],
        'to_city_en' => 'Jeddah',
        'to_city_ar' => 'جدة',
        'to_location' => [
            'lat' => 21.4858,
            'lng' => 39.1925
        ],
        'departure_datetime' => now()->addDays(7)->toDateTimeString(),
        'arrival_datetime' => now()->addDays(7)->addHours(5)->toDateTimeString(),
        'number_of_seats' => fake()->numberBetween(1, 4),
        'price' => fake()->numberBetween(50, 500),
        'allow_smoking' => fake()->boolean(),
        'deliver_to_door' => fake()->boolean(),
        'car_type_id' => CarType::where('number_of_seats', '>', 3)->inRandomOrder()->first()->id,
        'seats' => [
            'A3',
            'B1',
            'B2',
            'B3',
        ],
        'arrival_destination' => fake()->address(),
    ];

    $response = post(route('app.users.requests.trips.store'), $requestData, [
        'Authorization' => 'Bearer ' . $this->token
    ]);

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'

    ]);

    $responseData = $response->json('data');

    expect($responseData)->toHaveKeys([
        'id',
        'trip_type',
        'departure_datetime',
        'arrival_datetime',
        'number_of_seats',
        'from_city',
        'from_location',
        'to_city',
        'to_location',
        'price',
        'allow_smoking',
        'deliver_to_door',
        'arrival_datetime',
    ]);

    expect($responseData['id'])->not->toBeNull();
    expect($responseData['trip_type'])->toBe(__('enums.trip_type.' . $requestData['trip_type'], locale: App::getLocale()));
    expect(Carbon::parse($responseData['departure_datetime'])->toDateTimeString())->toBe(Carbon::parse($requestData['departure_datetime'])->toDateTimeString());
    expect(Carbon::parse($responseData['arrival_datetime'])->toDateTimeString())->toBe(Carbon::parse($requestData['arrival_datetime'])->toDateTimeString());
    expect($responseData['number_of_seats'])->toBe(count($requestData['seats']));
    expect($responseData['from_city']['name'])->toBe($requestData['from_city_' . App::getLocale()]);
    expect($responseData['from_location']['lat'])->toBe($requestData['from_location']['lat']);
    expect($responseData['from_location']['lng'])->toBe($requestData['from_location']['lng']);
    expect($responseData['to_city']['name'])->toBe($requestData['to_city_' . App::getLocale()]);
    expect($responseData['to_location']['lat'])->toBe($requestData['to_location']['lat']);
    expect($responseData['to_location']['lng'])->toBe($requestData['to_location']['lng']);
    expect($responseData['price'])->toBe($requestData['price']);
    expect($responseData['allow_smoking'])->toBe($requestData['allow_smoking']);
    expect($responseData['deliver_to_door'])->toBe($requestData['deliver_to_door']);
});

it('can access the trip requests index route', function () {
    $response = get(route('app.requests.trips.index'));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $requestData = $response->json('data.data');
    foreach ($requestData as $request) {
        expect($request)->toHaveKeys([
            'id',
            'trip_type',
            'departure_datetime',
            'arrival_datetime',
            'number_of_seats',
            'from_city',
            'to_city',
            'price',
            'allow_smoking',
            'deliver_to_door',
            'user',
        ]);

        expect($request['id'])->not->toBeNull();
        expect($request['trip_type'])->not->toBeNull();
        expect($request['departure_datetime'])->not->toBeNull();
        expect($request['arrival_datetime'])->not->toBeNull();
        expect($request['number_of_seats'])->toBeGreaterThan(0);
        expect($request['from_city']['id'])->not->toBeNull();
        expect($request['from_city']['name'])->not->toBeNull();
        expect($request['to_city']['id'])->not->toBeNull();
        expect($request['to_city']['name'])->not->toBeNull();
        expect($request['price'])->toBeGreaterThan(0);
        expect($request['user']['id'])->not->toBeNull();
        expect($request['user']['name'])->not->toBeNull();
    }
});

it('can show a trip request', function () {
    $tripRequest = TripRequest::inRandomOrder()->first();
    $response = get(route('app.requests.trips.show', $tripRequest->id));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $responseData = $response->json('data');

    expect($responseData)->toHaveKeys([
        'id',
        'trip_type',
        'departure_datetime',
        'arrival_datetime',
        'number_of_seats',
        'from_city',
        'from_location',
        'to_city',
        'to_location',
        'price',
        'allow_smoking',
        'deliver_to_door',
        'arrival_datetime',
        'user',
        'car_type',
    ]);

    expect($responseData['id'])->toBe($tripRequest->id);
    expect($responseData['trip_type'])->not->toBeNull();
    expect($responseData['departure_datetime'])->not->toBeNull();
    expect($responseData['arrival_datetime'])->not->toBeNull();
    expect($responseData['number_of_seats'])->toBeGreaterThan(0);
    expect($responseData['from_city']['id'])->not->toBeNull();
    expect($responseData['from_city']['name'])->not->toBeNull();
    expect($responseData['from_location']['lat'])->not->toBeNull();
    expect($responseData['from_location']['lng'])->not->toBeNull();
    expect($responseData['to_city']['id'])->not->toBeNull();
    expect($responseData['to_city']['name'])->not->toBeNull();
    expect($responseData['to_location']['lat'])->not->toBeNull();
    expect($responseData['to_location']['lng'])->not->toBeNull();
    expect($responseData['price'])->toBeGreaterThan(0);
    expect($responseData['user']['id'])->not->toBeNull();
    expect($responseData['user']['name'])->not->toBeNull();
    expect($responseData['car_type']['id'])->not->toBeNull();
    expect($responseData['car_type']['name'])->not->toBeNull();
});

it('can book a trip request', function () {
    $tripRequest = TripRequest::inRandomOrder()->upcoming()->available()->first();

    if (!$tripRequest) {
        $this->markTestSkipped('No available nor upcoming trip requests found.');
    }

    $requestData = [
        'price' => fake()->numberBetween(50, 500),
        'note' => fake()->sentence(),
    ];

    $response = post(route('app.requests.trips.book.store', $tripRequest->id), $requestData, [
        'Authorization' => 'Bearer ' . $this->token
    ]);

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $responseData = $response->json('data');

    expect($responseData)->toHaveKeys([
        'id',
        'price',
        'note',
        'created_at',
        'updated_at',
        'trip_request',
    ]);

    expect($responseData['id'])->not->toBeNull();
    expect($responseData['price'])->toBe($requestData['price']);
    expect($responseData['note'])->toBe($requestData['note']);
    expect($responseData['created_at'])->not->toBeNull();
    expect($responseData['updated_at'])->not->toBeNull();
    expect($responseData['trip_request']['id'])->toBe($tripRequest->id);
    expect($responseData['trip_request']['trip_type'])->not->toBeNull();
    expect($responseData['trip_request']['departure_datetime'])->not->toBeNull();
    expect($responseData['trip_request']['arrival_datetime'])->not->toBeNull();
    expect($responseData['trip_request']['number_of_seats'])->toBeGreaterThan(0);
    expect($responseData['trip_request']['from_city']['id'])->not->toBeNull();
    expect($responseData['trip_request']['from_city']['name'])->not->toBeNull();
    expect($responseData['trip_request']['to_city']['id'])->not->toBeNull();
    expect($responseData['trip_request']['to_city']['name'])->not->toBeNull();
    expect($responseData['trip_request']['price'])->toBeGreaterThan(0);
    expect($responseData['trip_request']['user']['id'])->not->toBeNull();
    expect($responseData['trip_request']['user']['name'])->not->toBeNull();
});

it('can bookmark and unbookmark a trip request', function () {
    $tripRequest = TripRequest::inRandomOrder()->first();

    $response = post(route('app.users.bookmarks.store'), [
        'bookmarkable_type' => TransportServiceTypeEnum::TRIP_REQUEST(),
        'bookmarkable_id' => $tripRequest->id
    ], [
        'Authorization' => 'Bearer ' . $this->token
    ]);

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    /** @var User $user */
    $user = Auth::user();

    expect($user->bookmarks()->where([
        'bookmarkable_type' => TripRequest::class,
        'bookmarkable_id' => $tripRequest->id
    ])->exists())->toBeTrue();

    $response = delete(route('app.users.bookmarks.destroy'), [
        'bookmarkable_type' => TransportServiceTypeEnum::TRIP_REQUEST(),
        'bookmarkable_id' => $tripRequest->id
    ], [
        'Authorization' => 'Bearer ' . $this->token
    ]);

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);
    expect($user->bookmarks()->where([
        'bookmarkable_type' => TripRequest::class,
        'bookmarkable_id' => $tripRequest->id
    ])->exists())->toBeFalse();
});
