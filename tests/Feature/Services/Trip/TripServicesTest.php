<?php

use App\Enums\Bookings\BookingStatusEnum;
use App\Enums\Travel\SeatStatusEnum;
use App\Enums\Travel\TransportServiceTypeEnum;
use App\Enums\Travel\TripTypeEnum;
use App\Models\Booking\TripServiceBooking;
use App\Models\Service\TripService;
use App\Models\Service\AdditionalService;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

use function Pest\Laravel\delete;
use function Pest\Laravel\get;
use function Pest\Laravel\post;
use function Pest\Laravel\patch;

beforeEach(function () {
    $user = User::first();
    $this->token = $user->createToken('test-token')->plainTextToken;
});

it('can access the my trip services index route', function () {
    $response = get(route('app.users.services.trips.index'), [
        'Authorization' => 'Bearer ' . $this->token
    ]);

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data.current_page',
        'data.data',
        'data.first_page_url',
        'data.from',
        'data.last_page',
        'data.last_page_url',
        'data.links',
        'data.next_page_url',
        'data.path',
        'data.per_page',
        'data.prev_page_url',
        'data.to',
        'data.total',
        'status_code'
    ]);

    $tripDataArray = $response->json('data.data');

    foreach ($tripDataArray as $tripData) {
        expect($tripData)->toHaveKeys([
            'id',
            'trip_type',
            'departure_datetime',
            'arrival_datetime',
            'number_of_seats',
            'number_of_available_seats',
            'number_of_free_cartons',
            'from_city.id',
            'from_city.name',
            'from_city.current_weather',
            'from_location.lat',
            'from_location.lng',
            'to_city.id',
            'to_city.name',
            'to_city.current_weather',
            'to_location.lat',
            'to_location.lng',
            'price',
            'allow_smoking',
            'deliver_to_door',
            'additional_services',
            'car.id',
            'car.model',
            'car.plate_number',
            'car.type.id',
            'car.type.name',
            'car.type.number_of_seats',
            'seats',
            'note',
        ]);

        expect($tripData['trip_type'])->not->toBeNull();
        expect($tripData['from_city']['name'])->not->toBeNull();
        expect($tripData['to_city']['name'])->not->toBeNull();
        expect($tripData['number_of_seats'])->not->toBeNull();
        expect($tripData['price'])->not->toBeNull();
        expect($tripData['car']['type']['name'])->not->toBeNull();
        expect($tripData['car']['model'])->not->toBeNull();
        expect($tripData['car']['plate_number'])->not->toBeNull();
        expect($tripData['seats'])->not->toBeNull();
    }
});

it('can store a trip service', function () {
    $user = User::first();

    $car = $user->cars()->inRandomOrder()->first();
    $response = get(route('app.common.car-types.seats.index', ['id' => $car->type->id]), [
        'Authorization' => 'Bearer ' . $this->token
    ]);
    $seats = $response->json('data');
    $carSeatsCount = $car->type->number_of_seats;

    // Filter out seats A1 and A2
    $filteredSeats = collect($seats)->filter(function ($seat) {
        return $seat['number'] != 'A1' && $seat['number'] != 'A2';
    });

    $selectedSeats = $filteredSeats
        ->take(min($carSeatsCount, random_int(2, count($filteredSeats))))
        ->pluck('number')
        ->toArray();

    $requestData = [
        'trip_type' => fake()->randomElement(TripTypeEnum::values()),
        'from_city_en' => 'Riyadh',
        'from_city_ar' => 'الرياض',
        'from_location' => [
            'lat' => 24.7136,
            'lng' => 46.6753
        ],
        'to_city_en' => 'Jeddah',
        'to_city_ar' => 'جدة',
        'to_location' => [
            'lat' => 21.4858,
            'lng' => 39.1925
        ],
        'departure_datetime' => now()->addDays(4),
        'arrival_datetime' => now()->addDays(4)->addHours(6),
        'price' => 500,
        'number_of_free_cartons' => 2,
        'allow_smoking' => 'false',
        'deliver_to_door' => 'true',
        'car_id' => $car->id,
        'seats' => $selectedSeats,
        'additional_services' => AdditionalService::inRandomOrder()->take(2)->pluck('id')->toArray(),
        'note' => fake()->sentence(),
    ];


    $response = $this->postJson(route('app.users.services.trips.store'), $requestData, [
        'Authorization' => 'Bearer ' . $this->token
    ]);
    dump($response->json());

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data.id',
        'data.trip_type',
        'data.departure_datetime',
        'data.arrival_datetime',
        'data.number_of_seats',
        'data.number_of_available_seats',
        'data.number_of_free_cartons',
        'data.from_city.id',
        'data.from_city.name',
        'data.from_city.current_weather',
        'data.from_location.lat',
        'data.from_location.lng',
        'data.to_city.id',
        'data.to_city.name',
        'data.to_city.current_weather',
        'data.to_location.lat',
        'data.to_location.lng',
        'data.price',
        'data.allow_smoking',
        'data.deliver_to_door',
        'data.additional_services',
        'data.car.id',
        'data.car.model',
        'data.car.plate_number',
        'data.car.type.id',
        'data.car.type.name',
        'data.car.type.number_of_seats',
        'data.seats',
        'status_code'
    ]);

    $tripData = $response->json('data');

    expect($tripData['trip_type'])->not->toBeNull();
    expect($tripData['from_city']['name'])->not->toBeNull();
    expect($tripData['to_city']['name'])->not->toBeNull();
    expect($tripData['number_of_seats'])->not->toBeNull();
    expect($tripData['price'])->not->toBeNull();
    expect($tripData['car']['type']['name'])->not->toBeNull();
    expect($tripData['car']['model'])->not->toBeNull();
    expect($tripData['car']['plate_number'])->not->toBeNull();
    expect($tripData['seats'])->not->toBeNull();

    expect($tripData['number_of_available_seats'])->toBeLessThanOrEqual($car->type->number_of_seats);

    foreach ($tripData['seats'] as $seat) {
        expect($seat['status'])->toBe(SeatStatusEnum::AVAILABLE());
        expect($seat['number'])->toBeIn($selectedSeats);
    }

    $availableSeatsCount = collect($tripData['seats'])->where('status', SeatStatusEnum::AVAILABLE())->count();

    expect($availableSeatsCount)->toBe($tripData['number_of_available_seats']);
});

it('can access the trip services index route', function () {
    $response = get(route('app.services.trips.index'));
    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data.current_page',
        'data.data',
        'data.first_page_url',
        'data.from',
        'data.last_page',
        'data.last_page_url',
        'data.links',
        'data.next_page_url',
        'data.path',
        'data.per_page',
        'data.prev_page_url',
        'data.to',
        'data.total',
        'status_code'
    ]);

    $tripDataArray = $response->json('data.data');

    foreach ($tripDataArray as $tripData) {
        expect($tripData)->toHaveKeys([
            'id',
            'trip_type',
            'departure_datetime',
            'arrival_datetime',
            'number_of_seats',
            'number_of_available_seats',
            'from_city.id',
            'from_city.name',
            'from_city.current_weather',
            'to_city.id',
            'to_city.name',
            'to_city.current_weather',
            'price',
        ]);

        expect($tripData['trip_type'])->not->toBeNull();
        expect($tripData['from_city']['name'])->not->toBeNull();
        expect($tripData['to_city']['name'])->not->toBeNull();
        expect($tripData['number_of_seats'])->not->toBeNull();
        expect($tripData['price'])->not->toBeNull();
    }
});

it('can show a trip service', function () {
    $tripService = TripService::inRandomOrder()->first();
    $response = get(route('app.services.trips.show', $tripService->id));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data.id',
        'data.trip_type',
        'data.departure_datetime',
        'data.arrival_datetime',
        'data.number_of_seats',
        'data.number_of_available_seats',
        'data.number_of_free_cartons',
        'data.from_city',
        'data.to_city',
        'data.price',
        'data.allow_smoking',
        'data.deliver_to_door',
        'data.additional_services',
        'data.car',
        'data.available_seats',
        'data.unavailable_seats',
        'status_code',
        'data.note',
        'data.user.id',
        'data.user.rating',
    ]);

    expect($response->json('data.available_seats'))->toBeArray();
});

it('can book a trip service', function () {
    $tripService = TripService::query()->upcoming()->hasAvailableSeats()->inRandomOrder()->first();

    if (!$tripService) {
        $this->markTestSkipped('No available seats found for booking.');
        return;
    }

    $seats = $tripService->seats()->where('status', SeatStatusEnum::AVAILABLE())->take(random_int(1, $tripService->number_of_available_seats))->get();

    $requestData = [
        'seats' => $seats->pluck('number')->toArray(),
        'has_fragile_items' => fake()->boolean(),
        'note' => fake()->sentence(),
    ];

    $response = post(route('app.services.trips.book.store', $tripService->id), $requestData, [
        'Authorization' => 'Bearer ' . $this->token
    ]);

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data.id',
        'data.note',
        'data.created_at',
        'data.updated_at',
        'data.seats',
        'data.trip_service.id',
        'data.trip_service.trip_type',
        'data.trip_service.departure_datetime',
        'data.trip_service.arrival_datetime',
        'data.trip_service.number_of_seats',
        'data.trip_service.number_of_available_seats',
        'data.trip_service.number_of_free_cartons',
        'data.trip_service.from_city.id',
        'data.trip_service.from_city.name',
        'data.trip_service.from_city.current_weather',
        'data.trip_service.from_location.lat',
        'data.trip_service.from_location.lng',
        'data.trip_service.to_city.id',
        'data.trip_service.to_city.name',
        'data.trip_service.to_city.current_weather',
        'data.trip_service.to_location.lat',
        'data.trip_service.to_location.lng',
        'data.trip_service.price',
        'data.trip_service.allow_smoking',
        'data.trip_service.deliver_to_door',
        'data.trip_service.user.id',
        'data.trip_service.user.name',
        'data.trip_service.additional_services',
        'data.trip_service.car.id',
        'data.trip_service.car.model',
        'data.trip_service.car.plate_number',
        'data.trip_service.car.type.id',
        'data.trip_service.car.type.name',
        'data.trip_service.car.type.number_of_seats',
        'data.trip_service.available_seats',
        'data.trip_service.unavailable_seats',
        'status_code',
        'data.note',
    ]);

    expect($response->json('data.trip_service.number_of_available_seats'))->toBe($tripService->number_of_available_seats - count($requestData['seats']));

    // Check if the seats have become reserved
    foreach ($requestData['seats'] as $seatNumber) {
        $seat = $tripService->seats()->where('number', $seatNumber)->first();
        expect($seat->status)->toBe(SeatStatusEnum::RESERVED());
    }
});

it('can bookmark and unbookmark a trip service', function () {
    $tripService = TripService::inRandomOrder()->first();

    // Test bookmarking
    $response = post(route('app.users.bookmarks.store'), [
        'bookmarkable_type' => TransportServiceTypeEnum::TRIP_SERVICE(),
        'bookmarkable_id' => $tripService->id
    ], [
        'Authorization' => 'Bearer ' . $this->token
    ]);

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    /** @var User $user */
    $user = Auth::user();

    expect($user->bookmarks()->where([
        'bookmarkable_type' => TripService::class,
        'bookmarkable_id' => $tripService->id
    ])->exists())->toBeTrue();

    // Test unbookmarking by toggling again
    $response = delete(route('app.users.bookmarks.destroy'), [
        'bookmarkable_type' => TransportServiceTypeEnum::TRIP_SERVICE(),
        'bookmarkable_id' => $tripService->id
    ], [
        'Authorization' => 'Bearer ' . $this->token
    ]);

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);
    expect($user->bookmarks()->where([
        'bookmarkable_type' => TripService::class,
        'bookmarkable_id' => $tripService->id
    ])->exists())->toBeFalse();
});
