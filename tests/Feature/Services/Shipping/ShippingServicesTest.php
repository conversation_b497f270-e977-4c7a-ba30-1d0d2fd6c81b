<?php

use App\Enums\Travel\TransportationTypeEnum;
use App\Models\Service\ShippingService;
use App\Models\User;

use function Pest\Laravel\delete;
use function Pest\Laravel\get;
use function Pest\Laravel\post;
use App\Enums\Travel\TransportServiceTypeEnum;
use Illuminate\Support\Facades\Auth;

beforeEach(function () {
    $user = User::first();
    $this->token = $user->createToken('test-token')->plainTextToken;
});

it('can access the my shipping services index route', function () {
    $response = get(route('app.users.services.shippings.index'), [
        'Authorization' => 'Bearer ' . $this->token
    ]);

    expect($response->status())->toBe(200);

    $responseData = $response->json('data.data');

    expect($responseData)->toBeArray();

    foreach ($responseData as $shippingService) {
        expect($shippingService)->toHaveKeys([
            'id',
            'can_ship_packages',
            'can_ship_documents',
            'can_ship_furniture',
            'departure_datetime',
            'arrival_datetime',
            'packages_volume',
            'available_packages_volume',
            'document_volume',
            'available_document_volume',
            'furniture_volume',
            'available_furniture_volume',
            'from_city',
            'to_city',
            'from_location',
            'to_location',
            'package_price',
            'document_price',
            'furniture_price',
            'transportation_type',
            'from_city.id',
            'from_city.name',
            'from_city.current_weather',
            'to_city.id',
            'to_city.name',
            'to_city.current_weather',
            'from_location.lat',
            'from_location.lng',
            'to_location.lat',
            'to_location.lng'
        ]);

        if ($shippingService['transportation_type'] === TransportationTypeEnum::CAR()) {
            expect($shippingService)->toHaveKeys([
                'car.id',
                'car.model',
                'car.plate_number',
                'car.type',
                'car.type.id',
                'car.type.name',
                'car.type.number_of_seats'
            ]);
        } elseif ($shippingService['transportation_type'] === TransportationTypeEnum::FLIGHT()) {
            expect($shippingService['car'])->toBeNull();
        }

        expect($shippingService['id'])->not->toBeNull();
        expect($shippingService['can_ship_packages'] || $shippingService['can_ship_documents'] || $shippingService['can_ship_furniture'])->toBeTrue();

        expect($shippingService['departure_datetime'])->not->toBeNull();
        expect($shippingService['arrival_datetime'])->not->toBeNull();

        if ($shippingService['can_ship_packages']) {
            expect($shippingService['packages_volume'])->not->toBeNull();
            expect($shippingService['available_packages_volume'])->not->toBeNull();
            expect($shippingService['package_price'])->not->toBeNull();
        }
        if ($shippingService['can_ship_documents']) {
            expect($shippingService['document_volume'])->not->toBeNull();
            expect($shippingService['available_document_volume'])->not->toBeNull();
            expect($shippingService['document_price'])->not->toBeNull();
        }
        if ($shippingService['can_ship_furniture']) {
            expect($shippingService['furniture_volume'])->not->toBeNull();
            expect($shippingService['available_furniture_volume'])->not->toBeNull();
            expect($shippingService['furniture_price'])->not->toBeNull();
        }

        expect($shippingService['transportation_type'])->not->toBeNull();
    }
});

it('can store a shipping service', function () {
    $user = User::first();

    $transportationType = fake()->randomElement(TransportationTypeEnum::values());

    $shippingService = ShippingService::factory()->make([
        'transportation_type' => $transportationType,
        'from_city_en' => 'Riyadh',
        'from_city_ar' => 'الرياض',
        'to_city_en' => 'Jeddah',
        'to_city_ar' => 'جدة',
        'from_location' => [
            'lat' => fake()->latitude(),
            'lng' => fake()->longitude()
        ],
        'to_location' => [
            'lat' => fake()->latitude(),
            'lng' => fake()->longitude()
        ],
    ])->toArray();

    if (TransportationTypeEnum::CAR() === $transportationType) {
        $shippingService['car_id'] = $user->cars()->inRandomOrder()->first()->id;
    } elseif (TransportationTypeEnum::FLIGHT() === $transportationType) {
        $shippingService['can_ship_furniture'] = false;
        $shippingService['furniture_volume'] = null;
        $shippingService['furniture_price'] = null;
    }

    $response = post(route('app.users.services.shippings.store'), $shippingService, [
        'Authorization' => 'Bearer ' . $this->token
    ]);

    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data.id',
        'data.can_ship_packages',
        'data.can_ship_documents',
        'data.can_ship_furniture',
        'data.departure_datetime',
        'data.arrival_datetime',
        'data.packages_volume',
        'data.available_packages_volume',
        'data.document_volume',
        'data.available_document_volume',
        'data.furniture_volume',
        'data.available_furniture_volume',
        'data.from_city.id',
        'data.from_city.name',
        'data.from_city.current_weather',
        'data.from_location.lat',
        'data.from_location.lng',
        'data.to_city.id',
        'data.to_city.name',
        'data.to_city.current_weather',
        'data.to_location.lat',
        'data.to_location.lng',
        'data.package_price',
        'data.document_price',
        'data.furniture_price',
        'status_code'
    ]);

    if (TransportationTypeEnum::CAR() === $transportationType) {
        expect($response->json())->toHaveKeys([
            'data.car.id',
            'data.car.model',
            'data.car.plate_number',
            'data.car.type.id',
            'data.car.type.name',
            'data.car.type.number_of_seats'
        ]);
    } elseif (TransportationTypeEnum::FLIGHT() === $transportationType) {
        expect($response->json('data.car'))->toBeNull();
    }

    $shippingData = $response->json('data');

    if (TransportationTypeEnum::FLIGHT() === $transportationType) {
        expect($shippingData['can_ship_furniture'])->toBeFalse();
        expect($shippingData['furniture_volume'])->toBeNull();
        expect($shippingData['furniture_price'])->toBeNull();
    } else {
        expect($shippingData['can_ship_packages'] || $shippingData['can_ship_documents'] || $shippingData['can_ship_furniture'])->toBeTrue();
    }

    expect($shippingData['from_city']['name'])->not->toBeNull();
    expect($shippingData['to_city']['name'])->not->toBeNull();

    if ($shippingData['can_ship_packages']) {
        expect($shippingData['packages_volume'])->not->toBeNull();
        expect($shippingData['package_price'])->not->toBeNull();
    }
    if ($shippingData['can_ship_documents']) {
        expect($shippingData['document_volume'])->not->toBeNull();
        expect($shippingData['document_price'])->not->toBeNull();
    }
    if ($shippingData['can_ship_furniture']) {
        expect($shippingData['furniture_volume'])->not->toBeNull();
        expect($shippingData['furniture_price'])->not->toBeNull();
    }

    if (TransportationTypeEnum::CAR() === $transportationType) {
        expect($shippingData['car']['type']['name'])->not->toBeNull();
        expect($shippingData['car']['model'])->not->toBeNull();
        expect($shippingData['car']['plate_number'])->not->toBeNull();
    }
});

it('can access the shipping services index route', function () {
    $response = get(route('app.services.shippings.index'));

    expect($response->status())->toBe(200);

    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data.current_page',
        'data.data',
        'data.first_page_url',
        'data.from',
        'data.last_page',
        'data.last_page_url',
        'data.links',
        'data.next_page_url',
        'data.path',
        'data.per_page',
        'data.prev_page_url',
        'data.to',
        'data.total',
        'status_code'
    ]);

    $responseData = $response->json('data.data');
    expect($responseData)->toBeArray();

    foreach ($responseData as $shippingService) {
        expect($shippingService)->toHaveKeys([
            'id',
            'transportation_type',
            'can_ship_packages',
            'can_ship_documents',
            'can_ship_furniture',
            'departure_datetime',
            'arrival_datetime',
            'packages_volume',
            'available_packages_volume',
            'document_volume',
            'available_document_volume',
            'furniture_volume',
            'available_furniture_volume',
            'from_city',
            'to_city',
            'package_price',
            'document_price',
            'furniture_price',
            'user'
        ]);

        expect($shippingService['can_ship_packages'] || $shippingService['can_ship_documents'] || $shippingService['can_ship_furniture'])->toBeTrue();


        if ($shippingService['can_ship_packages']) {
            expect($shippingService['packages_volume'])->toBeGreaterThan(0);
            expect($shippingService['packages_volume'])->toBeGreaterThanOrEqual($shippingService['available_packages_volume']);
            expect($shippingService['package_price'])->toBeGreaterThan(0);
        }
        if ($shippingService['can_ship_documents']) {
            expect($shippingService['document_volume'])->toBeGreaterThan(0);
            expect($shippingService['document_volume'])->toBeGreaterThanOrEqual($shippingService['available_document_volume']);
            expect($shippingService['document_price'])->toBeGreaterThan(0);
        }
        if ($shippingService['can_ship_furniture']) {
            expect($shippingService['furniture_volume'])->toBeGreaterThan(0);
            expect($shippingService['furniture_volume'])->toBeGreaterThanOrEqual($shippingService['available_furniture_volume']);
            expect($shippingService['furniture_price'])->toBeGreaterThan(0);
        }
    }
});

it('can show a shipping service', function () {
    $shippingService = ShippingService::inRandomOrder()->first();
    $response = get(route('app.services.shippings.show', $shippingService->id));

    expect($response->status())->toBe(200);

    $expectedKeys = [
        'success',
        'message',
        'data.id',
        'data.transportation_type',
        'data.can_ship_packages',
        'data.can_ship_documents',
        'data.can_ship_furniture',
        'data.departure_datetime',
        'data.arrival_datetime',
        'data.packages_volume',
        'data.available_packages_volume',
        'data.document_volume',
        'data.available_document_volume',
        'data.furniture_volume',
        'data.available_furniture_volume',
        'data.from_city',
        'data.to_city',
        'data.package_price',
        'data.document_price',
        'data.furniture_price',
        'data.user.id',
        'data.user.name',
        'status_code'
    ];

    expect($response->json())->toHaveKeys($expectedKeys);

    $shippingData = $response->json('data');

    if ($shippingData['transportation_type'] === TransportationTypeEnum::CAR()) {
        expect($shippingData)->toHaveKey('car');
        expect($shippingData['car']['model'])->not->toBeNull();
        expect($shippingData['car']['plate_number'])->not->toBeNull();
        expect($shippingData['car'])->toHaveKeys(['id', 'model', 'plate_number', 'type']);
        expect($shippingData['car']['type'])->toHaveKeys(['id', 'name', 'number_of_seats']);
    } elseif ($shippingData['transportation_type'] === TransportationTypeEnum::FLIGHT()) {
        expect($shippingData['car'])->toBeNull();
    }


    if ($shippingData['can_ship_packages']) {
        expect($shippingData['packages_volume'])->toBeGreaterThan(0);
        expect($shippingData['packages_volume'])->toBeGreaterThanOrEqual($shippingData['available_packages_volume']);
        expect($shippingData['package_price'])->toBeGreaterThan(0);
    }
    if ($shippingData['can_ship_documents']) {
        expect($shippingData['document_volume'])->toBeGreaterThan(0);
        expect($shippingData['document_volume'])->toBeGreaterThanOrEqual($shippingData['available_document_volume']);
        expect($shippingData['document_price'])->toBeGreaterThan(0);
    }
    if ($shippingData['can_ship_furniture']) {
        expect($shippingData['furniture_volume'])->toBeGreaterThan(0);
        expect($shippingData['furniture_volume'])->toBeGreaterThanOrEqual($shippingData['available_furniture_volume']);
        expect($shippingData['furniture_price'])->toBeGreaterThan(0);
    }
});

it('can book a shipping service', function () {
    $shippingService = ShippingService::upcoming()->where(function($query) {
        $query->whereRaw('available_packages_volume > 0')
            ->orWhereRaw('available_document_volume > 0')
            ->orWhereRaw('available_furniture_volume > 0');
    })->inRandomOrder()->first();

    if (!$shippingService) {
        $this->markTestSkipped('No available shipping service found.');
    }

    $volumes = [];
    if ($shippingService->can_ship_packages && $shippingService->available_packages_volume > 0) {
        $volumes['packages_volume'] = fake()->numberBetween(1, $shippingService->available_packages_volume);
    }
    if ($shippingService->can_ship_documents && $shippingService->available_document_volume > 0) {
        $volumes['document_volume'] = fake()->numberBetween(1, $shippingService->available_document_volume);
    }
    if ($shippingService->can_ship_furniture && $shippingService->available_furniture_volume > 0) {
        $volumes['furniture_volume'] = fake()->numberBetween(1, $shippingService->available_furniture_volume);
    }

    $requestData = array_merge([
        'note' => fake()->sentence(),
        'is_fragile' => fake()->boolean(),
    ], $volumes);

    if (empty($volumes)) {
        $this->markTestSkipped('No available volumes to book.');
    }

    $response = post(route('app.services.shippings.book.store', $shippingService->id), $requestData, [
        'Authorization' => 'Bearer ' . $this->token
    ]);
    expect($response->status())->toBe(200);

    $expectedKeys = [
        'success',
        'message',
        'data.id',
        'data.note',
        'data.packages_volume',
        'data.document_volume',
        'data.furniture_volume',
        'data.created_at',
        'data.updated_at',
        'data.shipping_service.id',
        'data.shipping_service.can_ship_packages',
        'data.shipping_service.can_ship_documents',
        'data.shipping_service.can_ship_furniture',
        'data.shipping_service.departure_datetime',
        'data.shipping_service.arrival_datetime',
        'data.shipping_service.packages_volume',
        'data.shipping_service.available_packages_volume',
        'data.shipping_service.document_volume',
        'data.shipping_service.available_document_volume',
        'data.shipping_service.furniture_volume',
        'data.shipping_service.available_furniture_volume',
        'data.shipping_service.from_city.id',
        'data.shipping_service.from_city.name',
        'data.shipping_service.from_city.current_weather',
        'data.shipping_service.from_location.lat',
        'data.shipping_service.from_location.lng',
        'data.shipping_service.to_city.id',
        'data.shipping_service.to_city.name',
        'data.shipping_service.to_city.current_weather',
        'data.shipping_service.to_location.lat',
        'data.shipping_service.to_location.lng',
        'data.shipping_service.package_price',
        'data.shipping_service.document_price',
        'data.shipping_service.furniture_price',
        'data.shipping_service.user.id',
        'data.shipping_service.user.name',
        'status_code'
    ];

    if ($shippingService->transportation_type === TransportationTypeEnum::CAR()) {
        expect($response->json())->toHaveKeys($expectedKeys);
        expect($response->json('data.shipping_service.car'))->not->toBeNull();
    } elseif ($shippingService->transportation_type === TransportationTypeEnum::FLIGHT()) {
        expect($response->json('data.shipping_service.car'))->toBeNull();
    }
});

it('can bookmark and unbookmark a shipping service', function () {
    $shippingService = ShippingService::inRandomOrder()->first();

    // Test bookmarking
    $response = post(route('app.users.bookmarks.store'), [
        'bookmarkable_type' => TransportServiceTypeEnum::SHIPPING_SERVICE(),
        'bookmarkable_id' => $shippingService->id
    ], [
        'Authorization' => 'Bearer ' . $this->token
    ]);

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    /** @var User $user */
    $user = Auth::user();

    expect($user->bookmarks()->where([
        'bookmarkable_type' => ShippingService::class,
        'bookmarkable_id' => $shippingService->id
    ])->exists())->toBeTrue();

    // Test unbookmarking by toggling again
    $response = delete(route('app.users.bookmarks.destroy'), [
        'bookmarkable_type' => TransportServiceTypeEnum::SHIPPING_SERVICE(),
        'bookmarkable_id' => $shippingService->id
    ], [
        'Authorization' => 'Bearer ' . $this->token
    ]);

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);
    expect($user->bookmarks()->where([
        'bookmarkable_type' => ShippingService::class,
        'bookmarkable_id' => $shippingService->id
    ])->exists())->toBeFalse();
});
