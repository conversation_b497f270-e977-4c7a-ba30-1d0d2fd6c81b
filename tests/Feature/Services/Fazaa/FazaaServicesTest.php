<?php

namespace Tests\Feature\Services\Fazaa;

use App\Enums\Travel\TransportationTypeEnum;
use App\Enums\Travel\TransportServiceTypeEnum;
use App\Models\Common\FazaaServiceType;
use App\Models\Common\FazaaSpecificType;
use App\Models\Service\FazaaService;
use function Pest\Laravel\delete;
use function Pest\Laravel\get;
use function Pest\Laravel\post;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

beforeEach(function () {
    $user = User::first();
    $this->token = $user->createToken('test-token')->plainTextToken;
});

it('can access the my fazaa services index route', function () {
    $response = get(route('app.users.services.fazaas.index'), [
        'Authorization' => 'Bearer ' . $this->token
    ]);

    expect($response->status())->toBe(200);

    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $serviceData = $response->json('data.data');
    foreach ($serviceData as $service) {
        expect($service)->toHaveKeys([
            'id',
            'fazaa_service_type',
            'specific_type',
            'transportation_type',
            'departure_datetime',
            'arrival_datetime',
            'from_city',
            'from_location',
            'to_city',
            'to_location',
            'service_location',
            'price',
            'note',
        ]);

        if ($service['fazaa_service_type']['key'] === 'receive_shipment') {
            expect($service['shipment_volume'])->not->toBeNull();
        }

        expect($service['id'])->not->toBeNull();
        expect($service['fazaa_service_type'])->not->toBeNull();
        expect($service['fazaa_service_type']['id'])->not->toBeNull();
        expect($service['fazaa_service_type']['name'])->not->toBeNull();
        expect($service['fazaa_service_type']['key'])->not->toBeNull();
        expect($service['transportation_type'])->not->toBeNull();
        expect($service['departure_datetime'])->not->toBeNull();
        expect($service['arrival_datetime'])->not->toBeNull();
        expect($service['from_city']['id'])->not->toBeNull();
        expect($service['from_city']['name'])->not->toBeNull();
        expect($service['from_city']['current_weather'])->not->toBeNull();
        expect($service['from_location']['lat'])->not->toBeNull();
        expect($service['from_location']['lng'])->not->toBeNull();
        expect($service['to_city']['id'])->not->toBeNull();
        expect($service['to_city']['name'])->not->toBeNull();
        expect($service['to_city']['current_weather'])->not->toBeNull();
        expect($service['to_location']['lat'])->not->toBeNull();
        expect($service['to_location']['lng'])->not->toBeNull();
        expect($service['service_location'])->not->toBeNull();
        expect($service['price'])->not->toBeNull();
    }
});

it('can store a fazaa service', function () {
    $fazaaServiceType = FazaaServiceType::inRandomOrder()->first();

    $response = post(route('app.users.services.fazaas.store'), [
        'fazaa_service_type_id' => $fazaaServiceType->id,
        'specific_type_id' => FazaaSpecificType::where('fazaa_service_type_id', $fazaaServiceType->id)->inRandomOrder()->first()->id,
        'transportation_type' => TransportationTypeEnum::CAR(),
        'from_city_en' => 'Riyadh',
        'from_city_ar' => 'الرياض',
        'from_location' => [
            'lat' => 24.7136,
            'lng' => 46.6753
        ],
        'to_city_en' => 'Jeddah',
        'to_city_ar' => 'جدة',
        'to_location' => [
            'lat' => 21.4858,
            'lng' => 39.1925
        ],
        'departure_datetime' => now()->addDays(7)->toDateTimeString(),
        'arrival_datetime' => now()->addDays(7)->addHours(6)->toDateTimeString(),
        'service_location' => '123 Main St, Riyadh',
        'description' => 'Full house cleaning service',
        'price' => 150,
        'note' => 'Please be on time.',
        'shipment_volume' => $fazaaServiceType->key === 'receive_shipment' ? rand(1, 15) : null,
    ], [
        'Authorization' => 'Bearer ' . $this->token
    ]);

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $serviceData = $response->json('data');

    expect($serviceData)->toHaveKeys([
        'id',
        'fazaa_service_type',
        'specific_type',
        'transportation_type',
        'from_city',
        'from_location',
        'to_city',
        'to_location',
        'departure_datetime',
        'arrival_datetime',
        'service_location',
        'price',
        'note',
    ]);

    if ($fazaaServiceType->key === 'receive_shipment') {
        expect($serviceData['shipment_volume'])->not->toBeNull();
    }

    expect($serviceData['id'])->not->toBeNull();
    expect($serviceData['fazaa_service_type'])->not->toBeNull();
    expect($serviceData['specific_type'])->not->toBeNull();
    expect($serviceData['transportation_type'])->not->toBeNull();
    expect($serviceData['from_city']['id'])->not->toBeNull();
    expect($serviceData['from_city']['name'])->not->toBeNull();
    expect($serviceData['from_location']['lat'])->not->toBeNull();
    expect($serviceData['from_location']['lng'])->not->toBeNull();
    expect($serviceData['to_city']['id'])->not->toBeNull();
    expect($serviceData['to_city']['name'])->not->toBeNull();
    expect($serviceData['to_location']['lat'])->not->toBeNull();
    expect($serviceData['to_location']['lng'])->not->toBeNull();
    expect($serviceData['departure_datetime'])->not->toBeNull();
    expect($serviceData['arrival_datetime'])->not->toBeNull();
    expect($serviceData['service_location'])->not->toBeNull();
    expect($serviceData['price'])->not->toBeNull();
});

it('can access the fazaa services index route', function () {
    $response = get(route('app.services.fazaas.index'));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $serviceData = $response->json('data.data');

    foreach ($serviceData as $service) {
        expect($service)->toHaveKeys([
            'id',
            'fazaa_service_type',
            'specific_type',
            'transportation_type',
            'departure_datetime',
            'arrival_datetime',
            'from_city',
            'to_city',
            'service_location',
            'price',
        ]);

        expect($service['id'])->not->toBeNull();
        expect($service['fazaa_service_type'])->not->toBeNull();
        expect($service['specific_type'])->not->toBeNull();
        expect($service['transportation_type'])->not->toBeNull();
        expect($service['departure_datetime'])->not->toBeNull();
        expect($service['arrival_datetime'])->not->toBeNull();
        expect($service['from_city']['id'])->not->toBeNull();
        expect($service['from_city']['name'])->not->toBeNull();
        expect($service['from_city']['current_weather'])->not->toBeNull();
        expect($service['to_city']['id'])->not->toBeNull();
        expect($service['to_city']['name'])->not->toBeNull();
        expect($service['to_city']['current_weather'])->not->toBeNull();
        expect($service['service_location'])->not->toBeNull();
        expect($service['price'])->not->toBeNull();
    }
});

it('can show a fazaa service', function () {
    $fazaaService = FazaaService::inRandomOrder()->first();
    $response = get(route('app.services.fazaas.show', $fazaaService->id));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $serviceData = $response->json('data');

    expect($serviceData)->toHaveKeys([
        'id',
        'fazaa_service_type',
        'specific_type',
        'transportation_type',
        'from_city',
        'from_location',
        'to_city',
        'to_location',
        'departure_datetime',
        'arrival_datetime',
        'service_location',
        'price',
        'note',
    ]);

    if ($fazaaService->fazaaServiceType->key === 'receive_shipment') {
        expect($serviceData['shipment_volume'])->not->toBeNull();
    }

    expect($serviceData['id'])->not->toBeNull();
    expect($serviceData['fazaa_service_type'])->not->toBeNull();
    expect($serviceData['transportation_type'])->not->toBeNull();
    expect($serviceData['from_city']['id'])->not->toBeNull();
    expect($serviceData['from_city']['name'])->not->toBeNull();
    expect($serviceData['from_city']['current_weather'])->not->toBeNull();
    expect($serviceData['from_location']['lat'])->not->toBeNull();
    expect($serviceData['from_location']['lng'])->not->toBeNull();
    expect($serviceData['to_city']['id'])->not->toBeNull();
    expect($serviceData['to_city']['name'])->not->toBeNull();
    expect($serviceData['to_city']['current_weather'])->not->toBeNull();
    expect($serviceData['to_location']['lat'])->not->toBeNull();
    expect($serviceData['to_location']['lng'])->not->toBeNull();
    expect($serviceData['departure_datetime'])->not->toBeNull();
    expect($serviceData['arrival_datetime'])->not->toBeNull();
    expect($serviceData['service_location'])->not->toBeNull();
    expect($serviceData['price'])->not->toBeNull();
});

it('can book a fazaa service', function () {
    $fazaaService = FazaaService::upcoming()->notBooked()->inRandomOrder()->first();

    if (!$fazaaService) {
        $this->markTestSkipped('No available Fazaa service to book.');
        return;
    }

    $requestData = [
        'note' => fake()->sentence(),
    ];

    $response = post(route('app.services.fazaas.book.store', $fazaaService->id), $requestData, [
        'Authorization' => 'Bearer ' . $this->token
    ]);

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $serviceData = $response->json('data');

    expect($serviceData)->toHaveKeys([
        'id',
        'note',
        'created_at',
        'updated_at',
        'fazaa_service',
    ]);

    expect($serviceData['id'])->not->toBeNull();
    expect($serviceData['created_at'])->not->toBeNull();
    expect($serviceData['updated_at'])->not->toBeNull();
    expect($serviceData['fazaa_service']['id'])->not->toBeNull();
    expect($serviceData['fazaa_service']['fazaa_service_type'])->not->toBeNull();
    expect($serviceData['fazaa_service']['transportation_type'])->not->toBeNull();
    expect($serviceData['fazaa_service']['departure_datetime'])->not->toBeNull();
    expect($serviceData['fazaa_service']['arrival_datetime'])->not->toBeNull();
    expect($serviceData['fazaa_service']['from_city']['id'])->not->toBeNull();
    expect($serviceData['fazaa_service']['from_city']['name'])->not->toBeNull();
    expect($serviceData['fazaa_service']['from_city']['current_weather'])->not->toBeNull();
    expect($serviceData['fazaa_service']['from_location']['lat'])->not->toBeNull();
    expect($serviceData['fazaa_service']['from_location']['lng'])->not->toBeNull();
    expect($serviceData['fazaa_service']['to_city']['id'])->not->toBeNull();
    expect($serviceData['fazaa_service']['to_city']['name'])->not->toBeNull();
    expect($serviceData['fazaa_service']['to_city']['current_weather'])->not->toBeNull();
    expect($serviceData['fazaa_service']['to_location']['lat'])->not->toBeNull();
    expect($serviceData['fazaa_service']['to_location']['lng'])->not->toBeNull();
    expect($serviceData['fazaa_service']['service_location'])->not->toBeNull();
    expect($serviceData['fazaa_service']['price'])->not->toBeNull();
    expect($serviceData['fazaa_service']['user']['id'])->not->toBeNull();
    expect($serviceData['fazaa_service']['user']['name'])->not->toBeNull();
});

it('can bookmark and unbookmark a fazaa service', function () {
    $fazaaService = FazaaService::inRandomOrder()->first();

    // Test bookmarking
    $response = post(route('app.users.bookmarks.store'), [
        'bookmarkable_type' => TransportServiceTypeEnum::FAZAA_SERVICE(),
        'bookmarkable_id' => $fazaaService->id
    ], [
        'Authorization' => 'Bearer ' . $this->token
    ]);

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    /** @var User $user */
    $user = Auth::user();

    expect($user->bookmarks()->where([
        'bookmarkable_type' => FazaaService::class,
        'bookmarkable_id' => $fazaaService->id
    ])->exists())->toBeTrue();

    // Test unbookmarking by toggling again
    $response = delete(route('app.users.bookmarks.destroy'), [
        'bookmarkable_type' => TransportServiceTypeEnum::FAZAA_SERVICE(),
        'bookmarkable_id' => $fazaaService->id
    ], [
        'Authorization' => 'Bearer ' . $this->token
    ]);

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);
    expect($user->bookmarks()->where([
        'bookmarkable_type' => FazaaService::class,
        'bookmarkable_id' => $fazaaService->id
    ])->exists())->toBeFalse();
});
