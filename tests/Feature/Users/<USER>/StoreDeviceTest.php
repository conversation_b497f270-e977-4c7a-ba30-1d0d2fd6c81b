<?php

use App\Models\User;
use Laravel\Sanctum\Sanctum;

use function Pest\Laravel\postJson;

beforeEach(function () {
    $this->user = User::first();
});

it('can store device when authenticated', function () {
    $this->actingAs($this->user, 'api');

    $response = postJson(route('app.users.devices.store'), [
        'fcm_token' => 'test-fcm-token',
    ]);

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'status_code'
    ]);

    $this->assertDatabaseHas('devices', [
        'token' => 'test-fcm-token',
        'notifiable_type' => User::class,
        'notifiable_id' => $this->user->id,
    ]);
});


it('cannot store device without authentication', function () {
    $response = postJson(route('app.users.devices.store'), [
        'fcm_token' => 'test-fcm-token',
    ]);

    expect($response->status())->toBe(401);
});

it('requires fcm_token for storing device', function () {
    $this->actingAs($this->user, 'api');

    $response = postJson(route('app.users.devices.store'), []);

    expect($response->status())->toBe(422);
});

