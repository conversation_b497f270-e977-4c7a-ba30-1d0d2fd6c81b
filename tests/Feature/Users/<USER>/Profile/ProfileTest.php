<?php

namespace Tests\Feature\Users\Accounts\Profile;

use App\Enums\Bookings\BookingStatusEnum;
use App\Models\Common\City;
use App\Models\Common\Country;
use App\Models\User;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

use function Pest\Laravel\get;
use function Pest\Laravel\patch;
use function Pest\Laravel\post;

beforeEach(function () {
    $user = User::first();
    $this->token = $user->createToken('test-token')->plainTextToken;
});

it('can show user profile', function () {
    $response = get(route('app.users.account.profile.show'), [
        'Authorization' => 'Bearer ' . $this->token,
        'Accept' => 'application/json'
    ]);

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $userData = $response->json('data');
    expect($userData)->toHaveKeys([
        'id',
        'name',
        'email',
        'mobile',
        'picture',
        'city',
        'location',
        'gender',
        'birth_date',
        'country',
        'whatsapp_number',
        'is_profile_completed',
        'total_income',
    ]);
    expect($userData)->toHaveKeys([
        'services_count',
        'requests_count',
        'trip_services_count',
        'fazaa_services_count',
        'shipping_services_count',
        'trip_requests_count',
        'shipping_requests_count',
        'fazaa_requests_count',
    ]);

    /** @var User $user */
    $user = Auth::user();

    // Calculate expected total income for verification
    $expectedTotalIncome = $user->tripServiceBookings()
        ->where('status', BookingStatusEnum::ACCEPTED())
        ->join('trip_services', 'trip_service_bookings.trip_service_id', '=', 'trip_services.id')
        ->sum(DB::raw('trip_services.price * 0.9')); // 90% of price (after 10% commission)

    $trip_services_count = $user->tripServices()->active()->count() + $user->tripRequestBookings()->active()->count();
    $fazaa_services_count = $user->fazaaServices()->active()->count() + $user->fazaaRequestBookings()->active()->count();
    $shipping_services_count = $user->shippingServices()->active()->count() + $user->shippingRequestBookings()->active()->count();
    $trip_requests_count = $user->tripRequests()->active()->count() + $user->tripServiceBookings()->active()->count();
    $shipping_requests_count = $user->shippingRequests()->active()->count() + $user->shippingServiceBookings()->active()->count();
    $fazaa_requests_count = $user->fazaaRequests()->active()->count() + $user->fazaaServiceBookings()->active()->count();

    $services_count = $trip_services_count + $fazaa_services_count + $shipping_services_count;
    $requests_count = $trip_requests_count + $shipping_requests_count + $fazaa_requests_count;

   expect($userData['total_income'])->toBe($expectedTotalIncome);
    expect($userData['services_count'])->toBe($services_count);
    expect($userData['requests_count'])->toBe($requests_count);
    expect($userData['trip_services_count'])->toBe($trip_services_count);
    expect($userData['fazaa_services_count'])->toBe($fazaa_services_count);
    expect($userData['shipping_services_count'])->toBe($shipping_services_count);
    expect($userData['trip_requests_count'])->toBe($trip_requests_count);
    expect($userData['shipping_requests_count'])->toBe($shipping_requests_count);
    expect($userData['fazaa_requests_count'])->toBe($fazaa_requests_count);
});

it('cannot access profile without authentication', function () {
    $response = get(route('app.users.account.profile.show'), [
        'Accept' => 'application/json'
    ]);

    expect($response->status())->toBe(401);
});

it('can update user profile', function () {
    Storage::fake('public');
    $city = City::inRandomOrder()->first();

    $updateData = [
        'name' => fake()->name(),
        'email' => fake()->unique()->safeEmail(),
        'mobile' => fake()->unique()->phoneNumber(),
        'picture' => UploadedFile::fake()->image('picture.jpg'),
        'city_en' => $city->name_en,
        'city_ar' => $city->name_ar,
        'location' => [
            'lat' => (string) fake()->latitude(),
            'lng' => (string) fake()->longitude(),
        ],
        'gender' => fake()->randomElement(['male', 'female']),
        'birth_date' => fake()->date(),
        'country_id' => Country::first()->id,
        'whatsapp_number' => fake()->unique()->phoneNumber(),
    ];

    $response = patch(route('app.users.account.profile.update'), $updateData, [
        'Authorization' => 'Bearer ' . $this->token,
        'Accept' => 'application/json'
    ]);

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $userData = $response->json('data');
    expect($userData)->toHaveKeys([
        'id',
        'name',
        'email',
        'mobile',
        'picture',
        'city',
        'location',
        'gender',
        'birth_date',
        'country',
        'total_income',
    ]);

    expect($userData['name'])->toBe($updateData['name']);
    expect($userData['email'])->toBe($updateData['email']);
    expect($userData['mobile'])->toBe($updateData['mobile']);
    expect($userData['whatsapp_number'])->toBe($updateData['whatsapp_number']);
    expect($userData['is_profile_completed'])->toBe(true);

    if (isset($userData['city'])) {
        expect($userData['city']['id'])->toBe($city->id);
    }

    if (isset($userData['location'])) {
        expect($userData['location']['lat'])->toBe($updateData['location']['lat']);
        expect($userData['location']['lng'])->toBe($updateData['location']['lng']);
    }

    if (isset($userData['country'])) {
        expect($userData['country']['id'])->toBe($updateData['country_id']);
    }

    // /** @var \Illuminate\Support\Facades\Storage $storage */
    // $storage = Storage::disk('public');
    // $storage->assertExists($userData['picture']);
});
