<?php

use App\Models\User;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Notification;

use function Pest\Laravel\get;
use function Pest\Laravel\patch;

beforeEach(function () {
    $this->user = User::first();
});

it('can get user notifications', function () {
    $this->actingAs($this->user, 'api');

    $response = get(route('app.users.notifications.index'));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data.current_page',
        'data.data',
        'data.first_page_url',
        'data.from',
        'data.last_page',
        'data.last_page_url',
        'data.links',
        'data.next_page_url',
        'data.path',
        'data.per_page',
        'data.prev_page_url',
        'data.to',
        'data.total',
        'status_code'
    ]);

    $notifications = $response->json('data.data');
    if (count($notifications) > 0) {
        foreach ($notifications as $notification) {
            expect($notification)->toHaveKeys([
                'id',
                'data',
                'read_at',
                'created_at',
                'data.type',
                'data.title',
                'data.content',
            ]);
        }
    }
});

it('can get unread notifications count', function () {
    $this->actingAs($this->user, 'api');

    $unreadCount = $this->user->unreadNotifications()->count();

    $response = get(route('app.users.notifications.unread-count'));

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'data.unread_count',
        'status_code'
    ]);

    expect($response->json('data.unread_count'))->toBe($unreadCount);
});

it('can mark a notification as read', function () {
    $this->actingAs($this->user, 'api');
    $user = User::first();

    $notification = $user->notifications()->whereNull('read_at')->first();
    expect($notification)->not()->toBeNull();

    $response = patch(
        route('app.users.notifications.mark-as-read', ['notificationId' => $notification->id])
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $this->assertDatabaseHas('notifications', [
        'id' => $notification->id,
        'read_at' => now()->toDateTimeString()
    ]);
});

it('can mark all notifications as read', function () {
    $this->actingAs($this->user, 'api');

    $response = patch(
        route('app.users.notifications.mark-all-as-read'),
    );

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data',
        'status_code'
    ]);

    $this->assertDatabaseMissing('notifications', [
        'notifiable_id' => $this->user->id,
        'read_at' => null
    ]);
});

it('cannot access notifications without authentication', function () {
    $response = get(route('app.users.notifications.index'));
    expect($response->status())->toBe(401);

    $response = get(route('app.users.notifications.unread-count'));
    expect($response->status())->toBe(401);

    $response = patch(
        route('app.users.notifications.mark-as-read', ['notificationId' => 1]),
    );
    expect($response->status())->toBe(401);

    $response = patch(
        route('app.users.notifications.mark-all-as-read'),
    );
    expect($response->status())->toBe(401);
});

it('cannot mark non-existent notification as read', function () {
    $this->actingAs($this->user, 'api');
    $response = patch(
        route('app.users.notifications.mark-as-read', ['notificationId' => Str::uuid()]),
    );

    expect($response->status())->toBe(404);
});
