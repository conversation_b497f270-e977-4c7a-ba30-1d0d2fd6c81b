<?php

use App\Enums\Travel\SeatStatusEnum;
use App\Enums\Travel\TransportServiceTypeEnum;
use App\Enums\Travel\TripTypeEnum;
use App\Models\Service\TripService;
use App\Models\Service\AdditionalService;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

use function Pest\Laravel\delete;
use function Pest\Laravel\get;
use function Pest\Laravel\post;

beforeEach(function () {
    $user = User::first();
    $this->token = $user->createToken('test-token')->plainTextToken;
});

it('can get user bookmarks ', function () {
    $response = get(route('app.users.bookmarks.index'), [
        'Authorization' => 'Bearer ' . $this->token
    ]);

    expect($response->status())->toBe(200);
    expect($response->json())->toHaveKeys([
        'success',
        'message',
        'data.current_page',
        'data.data',
        'data.first_page_url',
        'data.from',
        'data.last_page',
        'data.last_page_url',
        'data.links',
        'data.next_page_url',
        'data.path',
        'data.per_page',
        'data.prev_page_url',
        'data.to',
        'data.total',
        'status_code'
    ]);

    $bookmarkedTrips = $response->json('data.data');

    /** @var User $user */
    $user = Auth::user();

    $bookmarkedUserTrips = $user->bookmarks()->pluck('bookmarkable_id')->toArray();

    foreach ($bookmarkedTrips as $bookmarkedTrip) {
        expect($bookmarkedTrip)->toHaveKeys([
            'bookmarkable_type',
            'bookmarkable_id',
            'bookmarkable',
            'created_at'
        ]);
        expect($bookmarkedTrip['bookmarkable_id'])->toBeIn($bookmarkedUserTrips);
    }
});
