worker_processes 1;
error_log stderr warn;
pid /run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Define custom log format to include reponse times
    log_format main_timed '$remote_addr - $remote_user [$time_local] "$request" '
                          '$status $body_bytes_sent "$http_referer" '
                          '"$http_user_agent" "$http_x_forwarded_for" '
                          '$request_time $upstream_response_time $pipe $upstream_cache_status';

    access_log /dev/stdout main_timed;
    error_log /dev/stderr notice;

    keepalive_timeout 65;

    # Max body size
    client_max_body_size 192M;

    # Write temporary files to /tmp so they can be created as a non-privileged user
    client_body_temp_path /tmp/client_temp;
    proxy_temp_path /tmp/proxy_temp_path;
    fastcgi_temp_path /tmp/fastcgi_temp;
    uwsgi_temp_path /tmp/uwsgi_temp;
    scgi_temp_path /tmp/scgi_temp;

    # Default server definition
    server {
        listen [::]:80 default_server;
        listen 80 default_server;
        server_name _;
        root /var/www/html/public;

        # When redirecting from /url to /url/, use non-absolute redirects to avoid issues with
        # protocol and ports (eg. when running the Docker service on 80 but serving in production on 443)
        # https://stackoverflow.com/a/49638652
        # absolute_redirect off;

        # sendfile off;

        index index.php index.html;

        # Add support for "WebP Converter for Media" WordPress plugin
        # https://wordpress.org/plugins/webp-converter-for-media/
        # location ~ ^/wp-content/(?<path>.+)\.(?<ext>jpe?g|png|gif)$ {
        #     if ($http_accept !~* "image/webp") {
        #         break;
        #     }

        #     expires 180d;
        #     add_header Vary Accept;
        #     try_files /wp-content/uploads-webpc/$path.$ext.webp $uri =404;
        # }

        location / {
            # First attempt to serve request as file, then
            # as directory, then fall back to index.php
            try_files $uri $uri/ /index.php?$args;
        }

        # Add a location block for static assets
        location ~* \.(css|js|jpg|jpeg|png|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            access_log off;
            add_header Cache-Control "public";
        }

        # ELB Healthcheck
        # location /healthcheck {
        #         default_type application/json;
        #         return 200 '{"nginx": "healthy"}';
        # }
        # Redirect server error pages to the static page /50x.html
        # error_page 500 502 503 504 /50x.html;
        # location = /50x.html {
        #     root /var/www/html/public;
        # }

        # Pass the PHP scripts to PHP-FPM listening on 127.0.0.1:9000
        location ~ \.php$ {
            try_files $uri =404;

            fastcgi_buffers 16 16k;
            fastcgi_buffer_size 32k;

            fastcgi_split_path_info ^(.+\.php)(/.+)$;
            fastcgi_pass 127.0.0.1:9000;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
            fastcgi_param SCRIPT_NAME $fastcgi_script_name;
            fastcgi_index index.php;
            include fastcgi_params;
        }

        # location ~* \.(?:jpg|jpeg|gif|png|ico|cur|gz|svg|svgz|mp4|ogg|ogv|webm|htc|css|js|webp|mpeg|pdf|eot|otf|ttf|woff|woff2)$ {
        #     expires 1y;
        #     access_log off;
        #     add_header Cache-Control "public";
        #     # CORS
        #     location ~* \.(eot|otf|ttf|woff|woff2|webp)$ {
        #         add_header Access-Control-Allow-Origin *;
        #     }
        # }


        # Deny access to . files, for security
        location ~ /\. {
            log_not_found off;
            deny all;
        }

        # Allow fpm ping and status from localhost
        location ~ ^/(fpm-status|fpm-ping)$ {
            access_log off;
            allow 127.0.0.1;
            deny all;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
            include fastcgi_params;
            fastcgi_pass 127.0.0.1:9000;
        }
    }

    gzip on;
    gzip_proxied any;
    gzip_types
        text/plain
        text/css
        text/js
        text/xml
        text/javascript
        application/javascript
        application/x-javascript
        application/json
        application/xml
        application/xml+rss
        application/rss+xml
        image/svg+xml/javascript;
    gzip_vary on;
    gzip_disable "msie6";

    # Include other server configs
    include /etc/nginx/conf.d/*.conf;
}
