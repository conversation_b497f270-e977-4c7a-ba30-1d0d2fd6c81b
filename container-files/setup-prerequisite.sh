#!/bin/sh

apt install -y postgresql postgresql-contrib redis

service postgresql start

service redis-server start

psql -U postgres -c "ALTER ROLE postgres WITH PASSWORD 'password';"
psql -U postgres -c "CREATE DATABASE pogsha_db;"

chmod -R 777 storage/logs
chmod -R 777 bootstrap/cache/

php artisan log-viewer:publish
php artisan storage:link

php artisan config:clear
php artisan optimize:clear
php artisan optimize

yes 'yes' | php artisan migrate:fresh --seed

